# SalonFlow Geliştirme Süreci

**Tarih:** 5 Ağustos 2024
**Saat:** 17:00

## <PERSON>l Bakış

B<PERSON>, SalonFlow projesinin geliştirme sürecini açıklamaktadır. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>, kod yazma, test etme, gözden geçirme ve dağıtım adımlarını içermektedir.

## Geliştirme Ortamı

### Gereksinimler

- Node.js 18.0.0 veya üzeri
- npm 9.0.0 veya üzeri
- Git
- Supabase CLI
- VS Code (önerilen)

### Ortam Kurulumu

1. Projeyi klonlayın:
   ```bash
   git clone https://github.com/username/salonflow.git
   cd salonflow
   ```

2. Bağımlılıkları yükleyin:
   ```bash
   npm install
   ```

3. Ortam değişkenlerini ayarlayın:
   ```bash
   cp .env.example .env.local
   ```

4. Geliştirme sunucusunu başlatın:
   ```bash
   npm run dev
   ```

## Geliştirme İş Akışı

### 1. Görev Oluşturma

Yeni bir özellik veya hata düzeltmesi geliştirmeden önce, bir görev oluşturulmalıdır. Görevler, aşağıdaki bilgileri içermelidir:

- Görev başlığı
- Görev açıklaması
- Öncelik (Düşük, Orta, Yüksek)
- Tahmini tamamlanma süresi
- İlgili bileşenler veya modüller

### 2. Branch Oluşturma

Her görev için yeni bir branch oluşturulmalıdır. Branch adları, aşağıdaki formatta olmalıdır:

```
<type>/<task-id>-<short-description>
```

Örnek: `feature/123-add-subscription-management`

Tip türleri:
- `feature`: Yeni özellik
- `bugfix`: Hata düzeltmesi
- `hotfix`: Acil hata düzeltmesi
- `refactor`: Kod yeniden düzenleme
- `docs`: Dokümantasyon değişiklikleri

### 3. Kod Yazma

Kod yazarken, aşağıdaki kurallara uyulmalıdır:

- **Kod Standartları**: Projenin kod standartlarına uyun.
- **Tip Güvenliği**: TypeScript tip tanımlamalarını doğru şekilde kullanın.
- **Bileşen Yapısı**: Bileşenleri küçük ve yeniden kullanılabilir tutun.
- **Performans**: Gereksiz render'ları önleyin ve performans optimizasyonları yapın.
- **Erişilebilirlik**: WCAG 2.1 AA standartlarına uyun.

### 4. Test Etme

Her özellik veya hata düzeltmesi, aşağıdaki test türleriyle test edilmelidir:

- **Birim Testleri**: Bileşenlerin ve fonksiyonların birim testleri
- **Entegrasyon Testleri**: Bileşenler arası etkileşimlerin testleri
- **E2E Testleri**: Kullanıcı senaryolarının uçtan uca testleri

Testleri çalıştırmak için:

```bash
npm run test        # Birim testleri çalıştır
npm run test:e2e    # E2E testleri çalıştır
```

### 5. Kod Gözden Geçirme

Kod gözden geçirme süreci, aşağıdaki adımları içermektedir:

1. Pull Request (PR) oluşturun.
2. PR açıklamasında, yapılan değişiklikleri ve test sonuçlarını belirtin.
3. En az bir geliştirici tarafından gözden geçirilmesini bekleyin.
4. Geri bildirimlere göre gerekli düzeltmeleri yapın.
5. Tüm gözden geçirme kriterleri karşılandığında, PR'ı birleştirin.

### 6. Dağıtım

Dağıtım süreci, aşağıdaki adımları içermektedir:

1. `main` branch'ine birleştirilen değişiklikler, otomatik olarak test ortamına dağıtılır.
2. Test ortamında testler başarılı olursa, değişiklikler staging ortamına dağıtılır.
3. Staging ortamında onaylandıktan sonra, değişiklikler production ortamına dağıtılır.

## Veritabanı Değişiklikleri

Veritabanı değişiklikleri, aşağıdaki adımları içermektedir:

1. SQL dosyasını `docs/sql/` klasörüne ekleyin.
2. SQL dosyasını Supabase Studio'da çalıştırın.
3. TypeScript tip tanımlamalarını güncelleyin.
4. Değişiklikleri test edin.

## Dokümantasyon

Her yeni özellik veya önemli değişiklik için, dokümantasyon güncellenmelidir:

1. Teknik dokümantasyon: `docs/documentation/technical/` klasöründe
2. Kullanıcı dokümantasyonu: `docs/documentation/user/` klasöründe
3. Geliştirme dokümantasyonu: `docs/documentation/development/` klasöründe

## İlerleme Takibi

Geliştirme ilerlemesi, `docs/progress/` klasöründeki dosyalarda takip edilir. Her önemli değişiklik veya ilerleme, bu klasöre eklenmelidir.

## Sürüm Yönetimi

SalonFlow, [Semantic Versioning](https://semver.org/) kullanmaktadır:

- **Major (X.0.0)**: Geriye dönük uyumlu olmayan değişiklikler
- **Minor (0.X.0)**: Geriye dönük uyumlu yeni özellikler
- **Patch (0.0.X)**: Geriye dönük uyumlu hata düzeltmeleri

## Yardım ve Destek

Geliştirme süreciyle ilgili sorularınız için, aşağıdaki kaynaklara başvurabilirsiniz:

- **Dokümantasyon**: `docs/` klasöründeki dokümantasyonlar
- **Takım İletişimi**: Slack kanalları
- **Teknik Destek**: <EMAIL>
