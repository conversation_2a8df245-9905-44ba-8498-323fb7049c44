# Changes Summary

## 1. Updated New Appointment Page to Use UserContext

Modified `src/app/dashboard/appointments/new/page.tsx` to use the salon information from UserContext instead of making a new request:

```tsx
// Before
const [salonId, setSalonId] = useState<string | null>(null)
const [isLoading, setIsLoading] = useState(true)

// Get the current user's salon
useEffect(() => {
  async function getSalon() {
    // ... API call to get salon information
  }
  getSalon()
}, [])

// After
// UserContext'ten salon bilgilerini al
const { salonId, salonLoading } = useUser()
```

This change:
- Eliminates redundant API calls
- Uses the centralized salon information from UserContext
- Simplifies the component code
- Maintains consistency with other components

## 2. Fixed Customer Form to Include Salon ID

Updated `src/components/customer-form.tsx` to include the salon_id when creating a new customer:

```tsx
// Before
const customerData: CustomerInsert = {
  name: values.name,
  surname: values.surname,
  phone: values.phone,
  email: values.email || undefined,
}

// After
const customerData: CustomerInsert = {
  salon_id: salonId,
  name: values.name,
  surname: values.surname,
  phone: values.phone,
  email: values.email || undefined,
}
```

This change:
- Ensures customers are properly associated with a salon
- Fixes RLS policy issues by providing the required salon_id field
- Allows staff and salon owners to properly manage customers

## 3. Created RLS Policies for Customer Management

Created a new SQL file `customer-staff-rls-fix.sql` with updated RLS policies:

```sql
-- Salon owners can manage (view, insert, update, delete) their salon's customers
CREATE POLICY "Salon owners can manage their salon's customers" ON customers
  FOR ALL
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Staff can view their salon's customers
CREATE POLICY "Staff can view their salon's customers" ON customers
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Staff can insert customers for their salon
CREATE POLICY "Staff can insert customers for their salon" ON customers
  FOR INSERT
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Staff can update customers for their salon
CREATE POLICY "Staff can update customers for their salon" ON customers
  FOR UPDATE
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Anyone can insert customers (for booking page)
CREATE POLICY "Anyone can insert customers" ON customers
  FOR INSERT
  WITH CHECK (true);
```

These policies:
- Allow salon owners to fully manage customers for their salon
- Allow staff to view, insert, and update customers for their salon
- Allow anonymous users to insert customers (needed for the booking page)
- Ensure proper data isolation between different salons

## Next Steps

1. Apply the RLS policies in the Supabase dashboard or using the Supabase CLI
2. Test customer management functionality with both salon owner and staff roles
3. Verify that the appointment creation process correctly associates customers with salons
