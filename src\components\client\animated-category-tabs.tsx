"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

interface AnimatedCategoryTabsProps {
  categories: string[]
  activeCategory: string
  onCategoryChange: (category: string) => void
}

export function AnimatedCategoryTabs({
  categories,
  activeCategory,
  onCategoryChange
}: AnimatedCategoryTabsProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null)

  // Add "All" category if not already included
  const allCategories = ["all", ...categories].filter(
    (category, index, self) => self.indexOf(category) === index
  )

  return (
    <div className="mb-8">
      <div className="relative mx-auto flex max-w-2xl flex-wrap justify-center gap-2">
        {allCategories.map((category) => {
          const isActive = category === activeCategory
          const isHovered = category === hoveredCategory

          return (
            <motion.button
              key={category}
              className={cn(
                "relative px-4 py-2 text-sm font-medium transition-colors",
                isActive ? "text-primary" : "text-foreground hover:text-primary"
              )}
              onMouseEnter={() => setHoveredCategory(category)}
              onMouseLeave={() => setHoveredCategory(null)}
              onClick={() => onCategoryChange(category)}
              whileTap={{ scale: 0.97 }}
            >
              {/* Background highlight */}
              <AnimatePresence>
                {(isActive || isHovered) && (
                  <motion.span
                    className={cn(
                      "absolute inset-0 -z-10 rounded-full bg-primary/10",
                      isActive ? "opacity-100" : "opacity-70"
                    )}
                    layoutId="categoryBackground"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  />
                )}
              </AnimatePresence>

              {/* Category name */}
              <span>
                {category === "all"
                  ? "Tümü"
                  : category.charAt(0).toUpperCase() + category.slice(1)}
              </span>

              {/* Active indicator */}
              {isActive && (
                <motion.span
                  className="absolute bottom-0 left-1/2 h-0.5 w-1/2 -translate-x-1/2 bg-primary"
                  layoutId="activeIndicator"
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
              )}
            </motion.button>
          )
        })}
      </div>
    </div>
  )
}
