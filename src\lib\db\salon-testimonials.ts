// Salon Testimonials Database Operations
// Manages customer testimonials for salon landing pages

import { supabaseClient } from '../supabase-singleton'
import { SalonTestimonial, SalonTestimonialInsert, SalonTestimonialUpdate } from './types'

const supabase = supabaseClient

// =====================================================
// SALON TESTIMONIALS OPERATIONS
// =====================================================

/**
 * Get all testimonials for a specific salon
 */
export async function getSalonTestimonials(salonId: string): Promise<SalonTestimonial[]> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .select('*')
    .eq('salon_id', salonId)
    .order('display_order', { ascending: true })
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching salon testimonials:', error)
    throw error
  }

  return data || []
}

/**
 * Get active testimonials for a specific salon (for public display)
 */
export async function getActiveSalonTestimonials(salonId: string): Promise<SalonTestimonial[]> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .select('*')
    .eq('salon_id', salonId)
    .eq('is_active', true)
    .order('display_order', { ascending: true })
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching active salon testimonials:', error)
    throw error
  }

  return data || []
}

/**
 * Get a specific testimonial by ID
 */
export async function getSalonTestimonialById(id: string): Promise<SalonTestimonial | null> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null
    }
    console.error('Error fetching salon testimonial by ID:', error)
    throw error
  }

  return data
}

/**
 * Create new salon testimonial
 */
export async function createSalonTestimonial(
  testimonial: SalonTestimonialInsert
): Promise<SalonTestimonial> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .insert(testimonial)
    .select()
    .single()

  if (error) {
    console.error('Error creating salon testimonial:', error)
    throw error
  }

  return data
}

/**
 * Update existing salon testimonial
 */
export async function updateSalonTestimonial(
  id: string,
  updates: Partial<SalonTestimonialUpdate>
): Promise<SalonTestimonial> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating salon testimonial:', error)
    throw error
  }

  return data
}

/**
 * Delete salon testimonial
 */
export async function deleteSalonTestimonial(id: string): Promise<void> {
  const { error } = await supabase
    .from('salon_testimonials')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting salon testimonial:', error)
    throw error
  }
}

/**
 * Toggle testimonial active status
 */
export async function toggleTestimonialStatus(
  id: string,
  isActive: boolean
): Promise<SalonTestimonial> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .update({ is_active: isActive })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error toggling testimonial status:', error)
    throw error
  }

  return data
}

/**
 * Update testimonial display order
 */
export async function updateTestimonialOrder(
  id: string,
  displayOrder: number
): Promise<SalonTestimonial> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .update({ display_order: displayOrder })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating testimonial order:', error)
    throw error
  }

  return data
}

/**
 * Bulk update testimonial display orders
 */
export async function bulkUpdateTestimonialOrders(
  updates: Array<{ id: string; display_order: number }>
): Promise<SalonTestimonial[]> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .upsert(
      updates.map(update => ({
        id: update.id,
        display_order: update.display_order
      })),
      { onConflict: 'id' }
    )
    .select()

  if (error) {
    console.error('Error bulk updating testimonial orders:', error)
    throw error
  }

  return data || []
}

/**
 * Get testimonials count for a salon
 */
export async function getSalonTestimonialsCount(
  salonId: string,
  activeOnly: boolean = false
): Promise<number> {
  let query = supabase
    .from('salon_testimonials')
    .select('*', { count: 'exact', head: true })
    .eq('salon_id', salonId)

  if (activeOnly) {
    query = query.eq('is_active', true)
  }

  const { count, error } = await query

  if (error) {
    console.error('Error getting testimonials count:', error)
    throw error
  }

  return count || 0
}

/**
 * Get testimonials with pagination
 */
export async function getSalonTestimonialsPaginated(
  salonId: string,
  page: number = 1,
  limit: number = 10,
  activeOnly: boolean = false
): Promise<{
  testimonials: SalonTestimonial[]
  total: number
  totalPages: number
}> {
  const offset = (page - 1) * limit

  let query = supabase
    .from('salon_testimonials')
    .select('*')
    .eq('salon_id', salonId)

  if (activeOnly) {
    query = query.eq('is_active', true)
  }

  const { data, error } = await query
    .order('display_order', { ascending: true })
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error fetching paginated testimonials:', error)
    throw error
  }

  const total = await getSalonTestimonialsCount(salonId, activeOnly)
  const totalPages = Math.ceil(total / limit)

  return {
    testimonials: data || [],
    total,
    totalPages
  }
}

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Generate star rating display
 */
export function generateStarRating(rating: number): string {
  return '★'.repeat(rating) + '☆'.repeat(5 - rating)
}

/**
 * Validate testimonial data
 */
export function validateTestimonialData(testimonial: Partial<SalonTestimonialInsert>): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (!testimonial.customer_name?.trim()) {
    errors.push('Müşteri adı gereklidir')
  }

  if (!testimonial.comment?.trim()) {
    errors.push('Yorum gereklidir')
  }

  if (!testimonial.rating || testimonial.rating < 1 || testimonial.rating > 5) {
    errors.push('Puan 1-5 arasında olmalıdır')
  }

  if (testimonial.customer_name && testimonial.customer_name.length > 100) {
    errors.push('Müşteri adı 100 karakterden uzun olamaz')
  }

  if (testimonial.comment && testimonial.comment.length > 500) {
    errors.push('Yorum 500 karakterden uzun olamaz')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Get next display order for new testimonial
 */
export async function getNextDisplayOrder(salonId: string): Promise<number> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .select('display_order')
    .eq('salon_id', salonId)
    .order('display_order', { ascending: false })
    .limit(1)

  if (error) {
    console.error('Error getting next display order:', error)
    return 1
  }

  if (!data || data.length === 0) {
    return 1
  }

  return (data[0].display_order || 0) + 1
}
