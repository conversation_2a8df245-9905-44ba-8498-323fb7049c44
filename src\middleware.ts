import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import {
  createMiddlewareClient,
} from "@supabase/auth-helpers-nextjs";
import {
  getSalonInfoForCustomDomain,
  getSalonInfoFromSlug
} from "@/lib/utils/salon-resolver";
import { subscriptionMiddleware } from "@/middleware/subscription-check";

// Get the main domain from environment variable or use a default
// For Netlify deployments, we need to handle both the Netlify domain and custom domains
const MAIN_DOMAIN = process.env.NEXT_PUBLIC_MAIN_DOMAIN || "localhost:3000";
const NETLIFY_DOMAIN = process.env.NETLIFY_DOMAIN || "kuafor.netlify.app";

const PATHS = {
  AUTH_CALLBACK: "/auth/callback",
  AUTH_LOGIN: "/auth/login",
  DASHBOARD: "/dashboard",
  BOOKING: "/booking",
  APP: "/app",
};

// --- Middleware ---
export async function middleware(request: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req: request, res });

  const { pathname } = request.nextUrl;
  const hostname = request.headers.get("host") || "";

  // Debug logging for Netlify deployment
  console.log(`Middleware processing: hostname=${hostname}, pathname=${pathname}`);

  // 1. Handle Auth Callback immediately
  if (pathname === PATHS.AUTH_CALLBACK) {
    return NextResponse.next(); // Use a fresh NextResponse for callback to avoid cookie issues with 'res'
  }

  // 2. Get session (needed for most subsequent logic)
  const {
    data: { session },
  } = await supabase.auth.getSession();

  // 3. Handle login page access
  if (pathname.startsWith(PATHS.AUTH_LOGIN)) {
    if (session) {
      // User is logged in, redirect from login to dashboard
      return NextResponse.redirect(new URL(PATHS.DASHBOARD, request.url));
    }
    // User is not logged in, allow access to login page
    return res;
  }

  // 4. Protect dashboard routes
  if (pathname.startsWith(PATHS.DASHBOARD)) {
    if (!session) {
      // User is not logged in, redirect to login
      return NextResponse.redirect(new URL(PATHS.AUTH_LOGIN, request.url));
    }

    // Check subscription status for dashboard routes except subscription management pages
    if (!pathname.startsWith(`${PATHS.DASHBOARD}/subscription`)) {
      const subscriptionCheck = await subscriptionMiddleware(request);
      if (subscriptionCheck.status !== 200) {
        return subscriptionCheck;
      }
    }

    // User is logged in and subscription is valid, allow access to dashboard
    return res;
  }

  // 5. Handle booking pages (publicly accessible) - for backward compatibility
  if (pathname.startsWith(PATHS.BOOKING)) {
    return res;
  }

  // 6. Determine if it's the main domain or Netlify domain
  const isMainDomain =
    hostname === MAIN_DOMAIN ||
    hostname.endsWith(`.${MAIN_DOMAIN}`) ||
    hostname === NETLIFY_DOMAIN ||
    hostname.endsWith(`.${NETLIFY_DOMAIN}`);

  // 7. Handle custom domain logic
  if (!isMainDomain) {
    const salonInfo = await getSalonInfoForCustomDomain(hostname, supabase);

    if (salonInfo) {
      // Get the salon slug for this salon ID
      const { data: salonData } = await supabase
        .from("salons")
        .select("slug")
        .eq("id", salonInfo.salonId)
        .single();

      const slug = salonData?.slug;

      if (pathname === "/") {
        // Rewrite root of custom domain to its salon landing page using slug
        if (slug) {
          const url = request.nextUrl.clone();
          url.pathname = `/${slug}`;
          return NextResponse.rewrite(url);
        } else {
          // Fallback to old path if slug not found
          const url = request.nextUrl.clone();
          url.pathname = `${PATHS.BOOKING}/${salonInfo.salonId}`;
          return NextResponse.rewrite(url);
        }
      } else if (pathname === PATHS.APP) {
        // Rewrite /app on custom domain to dashboard
        const url = request.nextUrl.clone();
        url.pathname = PATHS.DASHBOARD;
        return NextResponse.rewrite(url);
      }

      // For other paths on custom domain (e.g. /dashboard/* already handled, /about, /contact etc.)
      // If they are not explicitly handled above, they are allowed by falling through.
      return res;
    } else {
      // Custom domain not found or not verified.
      console.warn(`Custom domain ${hostname} not found or not verified.`);
      return res;
    }
  }

  // 8. Handle main domain root path
  if (pathname === "/" && isMainDomain) {
    // Allow access to the main landing page
    return res;
  }

  // 9. Handle slug-based URLs on main domain
  if (isMainDomain && pathname.startsWith("/")) {
    // Check if this is a slug path (e.g., /salon-slug)
    const pathParts = pathname.split('/').filter(Boolean);

    if (pathParts.length > 0) {
      const slug = pathParts[0];
      const subPath = pathParts.length > 1 ? pathParts.slice(1).join('/') : '';

      console.log(`Checking slug: '${slug}', subPath: '${subPath}' on domain: ${hostname}`);

      // Check if this is a valid salon slug
      const salonInfo = await getSalonInfoFromSlug(slug, supabase);

      if (salonInfo) {
        console.log(`Found salon for slug '${slug}': ${salonInfo.salonId}`);

        // Handle /slug/app path - redirect to dashboard
        if (subPath === 'app') {
          // Redirect to dashboard with salon context
          const url = request.nextUrl.clone();
          url.pathname = PATHS.DASHBOARD;
          return NextResponse.rewrite(url);
        }

        // For /slug path, allow it to be handled by the [slug] page
        if (!subPath) {
          return res;
        }

        // For other subpaths, let them fall through to 404 if not handled
      } else if (pathParts.length === 1 && pathname !== PATHS.DASHBOARD) {
        // This is a potential slug URL but no salon was found
        console.log(`No salon found for slug '${slug}'`);
      }
    }
  }

  // 10. Default: Allow other requests (e.g. API routes, public pages on main domain not covered above)
  // This 'res' will have Supabase cookies if any session operations occurred.
  return res;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!_next/static|_next/image|favicon.ico).*)",
  ],
};
