"use client";

import { useMemo } from "react";
import { CustomAppSidebar } from "@/components/custom-app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { UserProvider, useUser } from "@/contexts/UserContext";
import { NotificationsProvider } from "@/contexts/NotificationsContext";
import { ReferralCodeChecker } from "@/components/referral-code-checker";
import { SubscriptionProvider, useSubscription } from "@/contexts/SubscriptionContext";

// Dashboard içerik bileşeni - yükleme durumlarını kontrol eder
function DashboardContent({ children }: { children: React.ReactNode }) {
  // UserContext'ten kullanıcı bilgilerini al
  const { user, isLoading, salonLoading } = useUser();

  // SubscriptionContext'ten abonelik bilgilerini al
  const { isLoading: subscriptionLoading } = useSubscription();

  // Tüm yükleme durumlarını kontrol et
  const showLoading = useMemo(() => {
    return (isLoading && !user) || salonLoading || subscriptionLoading;
  }, [isLoading, user, salonLoading, subscriptionLoading]);

  if (showLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center w-full h-full bg-background z-50">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <CustomAppSidebar />
      <SidebarInset>
        <ReferralCodeChecker />
        {children}
      </SidebarInset>
    </>
  );
}

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <UserProvider>
      <SubscriptionProvider>
        <SidebarProvider>
          <NotificationsProvider>
            <DashboardContent>
              {children}
            </DashboardContent>
          </NotificationsProvider>
        </SidebarProvider>
      </SubscriptionProvider>
    </UserProvider>
  );
}
