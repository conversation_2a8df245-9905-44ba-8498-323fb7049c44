# Referans Sistemi İyileştirmeleri

**Tarih:** 1 Ağustos 2024
**Saat:** 19:00

## 1. Tespit Edilen Sorunlar

Referans sistemi uygulamasında aşağıdaki sorunlar tespit edildi:

1. **RLS Politikası Hatası**: `pending_referrals` tablosuna veri eklenirken RLS politikası hatası alınıyordu.
2. **Veri Bulunamama Hatası**: `pending_referrals` tablosunda veri aranırken PGRST116 hatası alınıyordu.

## 2. Yapılan İyileştirmeler

### 2.1. RLS Politikalarını Düzeltme

`pending_referrals` tablosu için RLS politikaları güncellendi:

```sql
-- Herkes bekleyen referans kodu ekleyebilir (kayıt sırasında)
CREATE POLICY "Herkes bekleyen referans kodu ekleyebilir" ON pending_referrals
  FOR INSERT WITH CHECK (true);

-- <PERSON><PERSON> tüm bekleyen referans kodlarını görebilir (test için)
CREATE POLICY "Herkes tüm bekleyen referans kodlarını görebilir" ON pending_referrals
  FOR SELECT USING (true);
```

### 2.2. Kayıt İşlemini Güncelleme

Kayıt işlemi sırasında referans kodunu veritabanına kaydetme yaklaşımı yerine, sessionStorage kullanımına geri döndük:

```typescript
// Store the referral code in session storage to use after email verification
if (values.referralCode) {
  sessionStorage.setItem('referralCode', values.referralCode);
  console.log(`Referral code ${values.referralCode} saved in sessionStorage`);
  
  toast.success("Kayıt başarılı! Hesabınızı doğruladıktan sonra referans kodunuz uygulanacaktır.");
} else {
  toast.success("Kayıt başarılı! Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.");
}
```

### 2.3. Auth Callback Sayfasını Güncelleme

Auth callback sayfası, e-posta doğrulamasından sonra referans kodunu kontrol etmek için bir query parametresi ekleyecek şekilde güncellendi:

```typescript
// Referans kodunu kontrol et
try {
  // Referans kodunu sessionStorage'dan alamayız, çünkü bu server-side kod
  // Bunun yerine, client-side'da bir script ekleyeceğiz
  // Şimdilik, dashboard'a yönlendirirken bir query parametresi ekleyelim
  console.log("Auth callback: Dashboard'a yönlendiriliyor (referral check için)")
  return NextResponse.redirect(new URL('/dashboard?check_referral=true', request.url))
} catch (error) {
  console.error("Referans kodu işlenirken hata:", error)
  // Hata olsa bile dashboard'a yönlendir
  return NextResponse.redirect(new URL('/dashboard', request.url))
}
```

### 2.4. ReferralCodeChecker Komponenti Oluşturma

Dashboard'a giriş yapıldığında referans kodunu kontrol eden ve veritabanına kaydeden bir komponent oluşturuldu:

```typescript
"use client"

import { useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { useUser } from "@/contexts/UserContext"
import { referrals } from "@/lib/db"
import { toast } from "sonner"

export function ReferralCodeChecker() {
  const searchParams = useSearchParams()
  const { user } = useUser()
  const checkReferral = searchParams.get('check_referral')

  useEffect(() => {
    const saveReferralCode = async () => {
      if (!checkReferral || !user) return

      try {
        // sessionStorage'dan referans kodunu al
        const referralCode = sessionStorage.getItem('referralCode')
        if (!referralCode) return

        console.log(`Referral code ${referralCode} found in sessionStorage, saving to database...`)

        // Referans kodunu veritabanına kaydet
        await referrals.savePendingReferral(user.id, referralCode)
        
        // Referans kodunu sessionStorage'dan temizle
        sessionStorage.removeItem('referralCode')
        
        console.log(`Referral code ${referralCode} saved for user ${user.id}`)
        toast.success("Referans kodunuz kaydedildi. Salon oluşturduğunuzda otomatik olarak uygulanacaktır.")
      } catch (error) {
        console.error("Referans kodu kaydedilirken hata:", error)
      }
    }

    saveReferralCode()
  }, [checkReferral, user])

  // Bu komponent görünür bir şey render etmez
  return null
}
```

### 2.5. Dashboard Layout'unu Güncelleme

Dashboard layout'una ReferralCodeChecker komponenti eklendi:

```typescript
return (
  <SidebarProvider>
    <NotificationsProvider>
      <CustomAppSidebar />
      <SidebarInset>
        <ReferralCodeChecker />
        {children}
      </SidebarInset>
    </NotificationsProvider>
  </SidebarProvider>
)
```

### 2.6. Salon Oluşturma Sayfasını Güncelleme

Salon oluşturma sayfası, hem veritabanından hem de sessionStorage'dan referans kodunu kontrol edecek şekilde güncellendi:

```typescript
// Veritabanından bekleyen referans kodunu kontrol et ve uygula
let isReferred = false
try {
  // Kullanıcının bekleyen referans kodunu al
  const pendingReferral = await referrals.getPendingReferral(user.id)
  
  if (pendingReferral) {
    try {
      // Referans kodunu uygula
      await referrals.applyReferralCode(pendingReferral.referral_code, data.id)
      
      // Bekleyen referans kodunu uygulandı olarak işaretle
      await referrals.markPendingReferralAsApplied(pendingReferral.id)
      
      isReferred = true
      console.log(`Referral code ${pendingReferral.referral_code} applied for salon ${data.id}`)
      toast.success("Referans kodu başarıyla uygulandı!")
    } catch (error) {
      console.error("Referans kodu uygulanırken hata:", error)
    }
  } else {
    // sessionStorage'dan referans kodunu kontrol et (eski yöntem için geriye uyumluluk)
    if (typeof window !== 'undefined') {
      const referralCode = sessionStorage.getItem('referralCode')
      if (referralCode) {
        try {
          // Referans kodunu uygula
          await referrals.applyReferralCode(referralCode, data.id)
          
          // Referans kodunu sessionStorage'dan temizle
          sessionStorage.removeItem('referralCode')
          
          isReferred = true
          console.log(`Referral code ${referralCode} from sessionStorage applied for salon ${data.id}`)
          toast.success("Referans kodu başarıyla uygulandı!")
        } catch (error) {
          console.error("Referans kodu uygulanırken hata:", error)
        }
      }
    }
  }
} catch (error) {
  console.error("Bekleyen referans kodu kontrol edilirken hata:", error)
}
```

## 3. Sonuç

Bu değişikliklerle, referans sistemi artık daha güvenilir bir şekilde çalışmaktadır. Referans kodu ile kayıt olan kullanıcılar, e-posta doğrulamasından sonra dashboard'a yönlendirildiğinde, referans kodları veritabanına kaydedilir. Salon oluşturduklarında ise, bu referans kodu otomatik olarak uygulanır ve referans faydaları etkinleştirilir.

Hem veritabanı tabanlı hem de sessionStorage tabanlı yaklaşımlar desteklenmektedir, böylece geriye uyumluluk sağlanmıştır.
