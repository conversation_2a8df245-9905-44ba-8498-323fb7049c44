"use client"

import { useState } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { ShoppingBag, Eye } from "lucide-react"
import { cn } from "@/lib/utils"
import { Product } from "@/lib/db/types"

interface ProductSpotlightCardProps {
  product: Product
  onClick?: () => void
  index: number
}

export function ProductSpotlightCard({ product, onClick, index }: ProductSpotlightCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  
  // Staggered animation for cards appearing
  const variants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.4,
        ease: "easeOut"
      }
    })
  }

  return (
    <motion.div
      custom={index}
      initial="hidden"
      animate="visible"
      variants={variants}
      className="h-full"
    >
      <div
        className="group relative h-full overflow-hidden rounded-xl border bg-card transition-all duration-300 hover:shadow-lg"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Spotlight effect */}
        <div
          className={cn(
            "pointer-events-none absolute -inset-px opacity-0 transition-opacity duration-300",
            isHovered && "opacity-100"
          )}
          style={{
            background: `radial-gradient(600px circle at var(--x, 50%) var(--y, 50%), rgba(var(--primary-rgb), 0.15), transparent 40%)`
          }}
          onMouseMove={(e) => {
            const rect = e.currentTarget.getBoundingClientRect()
            const x = ((e.clientX - rect.left) / rect.width) * 100
            const y = ((e.clientY - rect.top) / rect.height) * 100
            e.currentTarget.style.setProperty("--x", `${x}%`)
            e.currentTarget.style.setProperty("--y", `${y}%`)
          }}
        />
        
        {/* Product image */}
        <div className="aspect-square overflow-hidden">
          {product.image_url ? (
            <div className="relative h-full w-full">
              <Image
                src={product.image_url}
                alt={product.name}
                fill
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                className={cn(
                  "h-full w-full object-cover transition-transform duration-500",
                  isHovered ? "scale-110" : "scale-100"
                )}
              />
            </div>
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-muted">
              <ShoppingBag className="h-16 w-16 text-muted-foreground" />
            </div>
          )}
          
          {/* Quick view button */}
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ 
              opacity: isHovered ? 1 : 0, 
              y: isHovered ? 0 : 20 
            }}
            transition={{ duration: 0.2 }}
            onClick={onClick}
            className="absolute bottom-4 left-1/2 flex -translate-x-1/2 items-center gap-2 rounded-full bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-md"
          >
            <Eye className="h-4 w-4" />
            Hızlı Bakış
          </motion.button>
        </div>
        
        {/* Product info */}
        <div className="p-4">
          <h3 className="font-semibold transition-colors group-hover:text-primary">
            {product.name}
          </h3>
          {product.category && (
            <p className="text-sm text-muted-foreground">{product.category}</p>
          )}
          {product.price && (
            <p className="mt-2 font-medium">{product.price} ₺</p>
          )}
        </div>
      </div>
    </motion.div>
  )
}
