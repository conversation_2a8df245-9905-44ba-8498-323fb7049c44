# Referral Used Notification Sorun<PERSON>ü<PERSON>

**Tarih:** 2 Ağustos 2024
**Saat:** 13:00

## 1. Tespit Edilen Sorun

Referans kodu kullanıldığında `referral_benefits` tablosuna veri eklerken aşağ<PERSON><PERSON>i hata tespit edildi:

```
{
    "code": "23514",
    "details": null,
    "hint": null,
    "message": "new row for relation \"notifications\" violates check constraint \"notifications_type_check\""
}
```

## 2. Sorun<PERSON>

Sorunun ana kaynağı şudur:

- `referral_benefits` tablosuna veri eklendiğinde tetiklenen `notify_referral_used` fonksiyonu, `notifications` tablosuna 'referral_used' tipinde bir bildirim ekliyor.
- Ancak, `notifications` tablosundaki `type` sütunu için check constraint, 'referral_used' değerine izin vermiyor.

## 3. Uygulanan Çözümler

### 3.1. Veritabanı Şeması Güncellemeleri

`notifications` tablosundaki `type` sütunu için check constraint'i güncelledik:

```sql
-- Mevcut kısıtlamayı kaldır
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS notifications_type_check;

-- Yeni kısıtlama ekle (referral_used değerini de kabul edecek şekilde)
ALTER TABLE notifications ADD CONSTRAINT notifications_type_check 
  CHECK (type IN ('new_booking', 'cancellation', 'update', 'subscription_reminder', 'payment_reminder', 'referral_used'));
```

### 3.2. NotificationsContext.tsx Dosyası Güncelleme

`src/contexts/NotificationsContext.tsx` dosyasında `NotificationType` tipini güncelledik:

```typescript
// Notification types
export type NotificationType = "new_booking" | "cancellation" | "update" | "subscription_reminder" | "payment_reminder" | "referral_used"
```

Bildirim tipine göre toast tipini belirleyen kodu güncelledik:

```typescript
// Bildirim tipine göre toast tipini belirle
if (notification.type === 'cancellation') {
  toastType = 'error';
} else if (notification.type === 'payment_reminder') {
  toastType = 'warning';
} else if (notification.type === 'subscription_reminder') {
  toastType = 'info';
} else if (notification.type === 'referral_used') {
  toastType = 'success';
}
```

Bildirim tıklandığında yönlendirme yapılan kodu güncelledik:

```typescript
onClick: () => {
  if (notification.type === 'subscription_reminder' || notification.type === 'payment_reminder') {
    window.location.href = `/dashboard/subscription`
  } else if (notification.type === 'referral_used') {
    window.location.href = `/dashboard/subscription`
  } else if (notification.data?.id) {
    window.location.href = `/dashboard/appointments/${notification.data.id}`
  }
},
```

## 4. Sonuç

Bu değişikliklerle, referans kodu kullanıldığında oluşturulan bildirimlerle ilgili sorunlar çözülmüştür. Artık:

1. Referans kodu kullanıldığında 'referral_used' tipinde bildirimler oluşturulabilecek
2. Bildirim tipine göre farklı toast tipleri gösterilecek (success, error, warning, info)
3. Referans kodu kullanıldığında oluşturulan bildirimler tıklandığında kullanıcı abonelik sayfasına yönlendirilecek

Bu değişiklikler, kullanıcıların referans kodu kullanıldığında bildirim almalarını ve bu bildirimlere tıkladıklarında ilgili sayfaya yönlendirilmelerini sağlayacaktır.
