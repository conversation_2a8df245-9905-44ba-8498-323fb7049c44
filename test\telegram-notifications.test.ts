// Comprehensive Test Suite for Telegram Notification System
// Tests deduplication, security, and functionality

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { 
  generateNotificationHash, 
  generateTestNotificationHash,
  shouldSendUpdateNotification,
  isValidNotificationHash,
  compareNotificationHashes,
  debugNotificationHash,
  AppointmentNotificationData,
  NotificationType
} from '../src/lib/utils/notification-hash';

// Mock data for testing
const mockAppointment: AppointmentNotificationData = {
  id: 'apt-123',
  salon_id: 'salon-456',
  salon_name: 'Test Salon',
  customer_name: '<PERSON>',
  customer_phone: '+905551234567',
  barber_name: '<PERSON>',
  service_name: '<PERSON><PERSON>',
  date: '2025-05-26',
  start_time: '10:00',
  end_time: '10:30',
  status: 'confirmed',
  notes: 'Test notes'
};

describe('Notification Hash Generation', () => {
  describe('generateNotificationHash', () => {
    it('should generate consistent hash for same data', () => {
      const hash1 = generateNotificationHash(mockAppointment, 'new_appointment');
      const hash2 = generateNotificationHash(mockAppointment, 'new_appointment');
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64); // SHA256 length
    });

    it('should generate different hashes for different notification types', () => {
      const newHash = generateNotificationHash(mockAppointment, 'new_appointment');
      const cancelHash = generateNotificationHash(mockAppointment, 'cancelled_appointment');
      
      expect(newHash).not.toBe(cancelHash);
    });

    it('should generate different hashes when critical data changes', () => {
      const originalHash = generateNotificationHash(mockAppointment, 'new_appointment');
      
      const modifiedAppointment = { ...mockAppointment, date: '2025-05-27' };
      const modifiedHash = generateNotificationHash(modifiedAppointment, 'new_appointment');
      
      expect(originalHash).not.toBe(modifiedHash);
    });

    it('should handle missing optional fields', () => {
      const appointmentWithoutOptionals = {
        ...mockAppointment,
        customer_phone: undefined,
        notes: undefined
      };
      
      const hash = generateNotificationHash(appointmentWithoutOptionals, 'new_appointment');
      expect(hash).toHaveLength(64);
    });

    it('should handle empty strings in optional fields', () => {
      const appointmentWithEmptyFields = {
        ...mockAppointment,
        customer_phone: '',
        notes: ''
      };
      
      const hash = generateNotificationHash(appointmentWithEmptyFields, 'new_appointment');
      expect(hash).toHaveLength(64);
    });
  });

  describe('generateTestNotificationHash', () => {
    it('should generate unique hashes for different timestamps', () => {
      const timestamp1 = new Date('2025-05-26T10:00:00Z');
      const timestamp2 = new Date('2025-05-26T10:01:00Z');
      
      const hash1 = generateTestNotificationHash('salon-123', 'Test Salon', timestamp1);
      const hash2 = generateTestNotificationHash('salon-123', 'Test Salon', timestamp2);
      
      expect(hash1).not.toBe(hash2);
    });

    it('should generate different hashes for different salons', () => {
      const timestamp = new Date();
      const hash1 = generateTestNotificationHash('salon-123', 'Salon A', timestamp);
      const hash2 = generateTestNotificationHash('salon-456', 'Salon B', timestamp);
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('isValidNotificationHash', () => {
    it('should validate correct SHA256 hashes', () => {
      const validHash = generateNotificationHash(mockAppointment, 'new_appointment');
      expect(isValidNotificationHash(validHash)).toBe(true);
    });

    it('should reject invalid hash formats', () => {
      expect(isValidNotificationHash('invalid')).toBe(false);
      expect(isValidNotificationHash('123')).toBe(false);
      expect(isValidNotificationHash('')).toBe(false);
      expect(isValidNotificationHash('g'.repeat(64))).toBe(false); // Invalid hex
    });
  });

  describe('compareNotificationHashes', () => {
    it('should compare hashes correctly', () => {
      const hash1 = generateNotificationHash(mockAppointment, 'new_appointment');
      const hash2 = generateNotificationHash(mockAppointment, 'new_appointment');
      const hash3 = generateNotificationHash(mockAppointment, 'cancelled_appointment');
      
      expect(compareNotificationHashes(hash1, hash2)).toBe(true);
      expect(compareNotificationHashes(hash1, hash3)).toBe(false);
    });

    it('should handle case insensitive comparison', () => {
      const hash = generateNotificationHash(mockAppointment, 'new_appointment');
      const upperHash = hash.toUpperCase();
      
      expect(compareNotificationHashes(hash, upperHash)).toBe(true);
    });

    it('should reject invalid hashes', () => {
      const validHash = generateNotificationHash(mockAppointment, 'new_appointment');
      expect(compareNotificationHashes(validHash, 'invalid')).toBe(false);
    });
  });

  describe('shouldSendUpdateNotification', () => {
    it('should detect critical field changes', () => {
      const oldAppointment = { ...mockAppointment };
      const newAppointment = { ...mockAppointment, date: '2025-05-27' };
      
      const result = shouldSendUpdateNotification(oldAppointment, newAppointment);
      
      expect(result.shouldSend).toBe(true);
      expect(result.changedFields).toContain('date');
      expect(result.hash).toHaveLength(64);
    });

    it('should ignore non-critical field changes', () => {
      const oldAppointment = { ...mockAppointment };
      const newAppointment = { ...mockAppointment, notes: 'Updated notes' };
      
      const result = shouldSendUpdateNotification(oldAppointment, newAppointment);
      
      expect(result.shouldSend).toBe(false);
      expect(result.changedFields).toHaveLength(0);
      expect(result.hash).toBe('');
    });

    it('should detect multiple field changes', () => {
      const oldAppointment = { ...mockAppointment };
      const newAppointment = { 
        ...mockAppointment, 
        date: '2025-05-27',
        start_time: '11:00',
        barber_name: 'New Barber'
      };
      
      const result = shouldSendUpdateNotification(oldAppointment, newAppointment);
      
      expect(result.shouldSend).toBe(true);
      expect(result.changedFields).toContain('date');
      expect(result.changedFields).toContain('start_time');
      expect(result.changedFields).toContain('barber_name');
    });
  });

  describe('debugNotificationHash', () => {
    it('should provide debug information', () => {
      const debug = debugNotificationHash(mockAppointment, 'new_appointment');
      
      expect(debug.hash).toHaveLength(64);
      expect(debug.components).toBeInstanceOf(Array);
      expect(debug.key).toContain(mockAppointment.id);
      expect(debug.key).toContain('new_appointment');
    });
  });
});

describe('Hash Consistency Tests', () => {
  it('should maintain hash consistency across different environments', () => {
    // Test that the same input always produces the same hash
    const testCases = [
      { ...mockAppointment },
      { ...mockAppointment, customer_phone: null },
      { ...mockAppointment, notes: null },
      { ...mockAppointment, customer_phone: '', notes: '' }
    ];

    testCases.forEach((testCase, index) => {
      const hash1 = generateNotificationHash(testCase, 'new_appointment');
      const hash2 = generateNotificationHash(testCase, 'new_appointment');
      
      expect(hash1).toBe(hash2);
      expect(hash1).toHaveLength(64);
    });
  });

  it('should generate unique hashes for edge cases', () => {
    const edgeCases = [
      { ...mockAppointment, customer_name: '' },
      { ...mockAppointment, customer_name: '   ' },
      { ...mockAppointment, notes: '|#|' }, // Delimiter in content
      { ...mockAppointment, service_name: 'Service with special chars: @#$%' }
    ];

    const hashes = edgeCases.map(testCase => 
      generateNotificationHash(testCase, 'new_appointment')
    );

    // All hashes should be unique
    const uniqueHashes = new Set(hashes);
    expect(uniqueHashes.size).toBe(hashes.length);
  });
});

describe('Performance Tests', () => {
  it('should generate hashes quickly', () => {
    const startTime = Date.now();
    
    // Generate 1000 hashes
    for (let i = 0; i < 1000; i++) {
      const testAppointment = { ...mockAppointment, id: `apt-${i}` };
      generateNotificationHash(testAppointment, 'new_appointment');
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete in less than 1 second
    expect(duration).toBeLessThan(1000);
  });

  it('should handle large data efficiently', () => {
    const largeAppointment = {
      ...mockAppointment,
      notes: 'A'.repeat(10000), // 10KB of notes
      customer_name: 'B'.repeat(1000) // 1KB name
    };

    const startTime = Date.now();
    const hash = generateNotificationHash(largeAppointment, 'new_appointment');
    const endTime = Date.now();

    expect(hash).toHaveLength(64);
    expect(endTime - startTime).toBeLessThan(100); // Should be very fast
  });
});
