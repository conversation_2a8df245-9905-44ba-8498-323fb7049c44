// Rate Limiting Monitoring API
// Provides system-wide rate limiting statistics (admin only)

import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/middleware/auth';
import rateLimitService from '@/lib/services/rate-limit';

// GET /api/telegram/monitoring/rate-limits
export async function GET(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. ADMIN CHECK
    if (!authResult.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // 3. GET RATE LIMITING STATISTICS
    const stats = rateLimitService.getStats();

    // 4. GET QUERY PARAMETERS
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');
    const includeAuditLog = searchParams.get('include_audit') === 'true';

    let response: any = {
      success: true,
      stats,
      timestamp: new Date().toISOString(),
      message: 'Rate limiting statistics retrieved successfully'
    };

    // 5. GET SALON-SPECIFIC STATUS IF REQUESTED
    if (salonId) {
      const salonStatus = rateLimitService.getSalonStatus(salonId);
      response.salonStatus = {
        salon_id: salonId,
        ...salonStatus
      };
    }

    // 6. INCLUDE AUDIT LOG IF REQUESTED
    if (includeAuditLog) {
      if (salonId) {
        // Get audit log for specific salon
        response.auditLog = rateLimitService.getAuditLog(salonId, 100);
      } else {
        // Get recent audit entries for all salons (limited for performance)
        response.auditLog = rateLimitService.getAuditLog('', 50);
      }
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in GET /api/telegram/monitoring/rate-limits:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/telegram/monitoring/rate-limits/reset (reset rate limits - admin only)
export async function POST(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. ADMIN CHECK
    if (!authResult.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // 3. PARSE REQUEST BODY
    const body = await request.json();
    const { salon_id, action = 'reset' } = body;

    if (!salon_id) {
      return NextResponse.json(
        { success: false, error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // 4. PERFORM ACTION
    let result: any = {
      success: true,
      salon_id,
      action,
      timestamp: new Date().toISOString()
    };

    switch (action) {
      case 'reset':
        // Reset rate limit for salon
        rateLimitService.resetSalonLimit(salon_id);
        result.message = `Rate limit reset for salon ${salon_id}`;
        break;

      case 'status':
        // Get current status
        const status = rateLimitService.getSalonStatus(salon_id);
        result.status = status;
        result.message = `Rate limit status retrieved for salon ${salon_id}`;
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action. Use "reset" or "status"' },
          { status: 400 }
        );
    }

    // 5. LOG ADMIN ACTION
    rateLimitService.logAttempt({
      salonId: salon_id,
      userId: authResult.user.id,
      notificationType: 'admin_action',
      success: true,
      error: `Admin ${action} action by ${authResult.user.email}`,
      timestamp: new Date(),
      ipAddress: request.ip || 'unknown'
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in POST /api/telegram/monitoring/rate-limits/reset:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/telegram/monitoring/rate-limits (cleanup rate limit data - admin only)
export async function DELETE(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. ADMIN CHECK
    if (!authResult.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // 3. PARSE REQUEST BODY
    const body = await request.json();
    const { cleanup_type = 'expired' } = body;

    let result: any = {
      success: true,
      cleanup_type,
      timestamp: new Date().toISOString()
    };

    // 4. PERFORM CLEANUP
    switch (cleanup_type) {
      case 'expired':
        // This is automatically handled by the service's cleanup method
        // We can trigger it manually here
        result.message = 'Expired rate limit entries cleaned up automatically';
        break;

      case 'all':
        // Clear all rate limit data (use with caution)
        // Note: This would require adding a method to the rate limit service
        result.message = 'All rate limit data cleared (not implemented for safety)';
        result.warning = 'Full cleanup not implemented for safety reasons';
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid cleanup type. Use "expired" or "all"' },
          { status: 400 }
        );
    }

    // 5. LOG ADMIN ACTION
    rateLimitService.logAttempt({
      salonId: 'system',
      userId: authResult.user.id,
      notificationType: 'admin_cleanup',
      success: true,
      error: `Admin cleanup ${cleanup_type} by ${authResult.user.email}`,
      timestamp: new Date(),
      ipAddress: request.ip || 'unknown'
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error in DELETE /api/telegram/monitoring/rate-limits:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/telegram/monitoring/rate-limits/config (update rate limit configuration - admin only)
export async function PUT(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. ADMIN CHECK
    if (!authResult.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // 3. PARSE REQUEST BODY
    const body = await request.json();
    const { max_requests, window_ms } = body;

    // 4. VALIDATE CONFIGURATION
    if (max_requests && (max_requests < 1 || max_requests > 1000)) {
      return NextResponse.json(
        { success: false, error: 'Max requests must be between 1 and 1000' },
        { status: 400 }
      );
    }

    if (window_ms && (window_ms < 1000 || window_ms > 3600000)) {
      return NextResponse.json(
        { success: false, error: 'Window must be between 1 second and 1 hour' },
        { status: 400 }
      );
    }

    // 5. NOTE: Configuration update would require modifying the rate limit service
    // For now, we'll return the current configuration
    const currentConfig = {
      max_requests: 10, // Current hardcoded value
      window_ms: 60000, // Current hardcoded value
      message: 'Rate limit configuration is currently hardcoded in the service'
    };

    // 6. LOG ADMIN ACTION
    rateLimitService.logAttempt({
      salonId: 'system',
      userId: authResult.user.id,
      notificationType: 'admin_config',
      success: true,
      error: `Admin config attempt by ${authResult.user.email}`,
      timestamp: new Date(),
      ipAddress: request.ip || 'unknown'
    });

    return NextResponse.json({
      success: true,
      currentConfig,
      requestedConfig: { max_requests, window_ms },
      message: 'Configuration update not implemented (hardcoded values)',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in PUT /api/telegram/monitoring/rate-limits/config:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
