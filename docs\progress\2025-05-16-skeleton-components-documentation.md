# Skeleton Loading Components Documentation

## Overview

Bu dokümantasyon, SalonFlow uygulamasında kullanılan iskelet (skeleton) yükleme bileşenlerini açıklamaktadır. Bu bile<PERSON><PERSON><PERSON>, veri yüklenirken kullanıcıya görsel geri bildirim sağlamak için tasarlanmıştır ve metin tabanlı "Yükleniyor..." mesajlarının yerini almıştır.

## Bileşen Yapısı

Tüm iskelet bileşenleri `src/components/ui/skeleton-loaders.tsx` dosyasında tanımlanmıştır ve Shadcn UI'ın temel `Skeleton` bileşenini kullanmaktadır.

### 1. Temel Skeleton Bileşeni

Shadcn UI'dan gelen temel `Skeleton` bileşeni, tüm özel iskelet bileşenlerimizin temelini oluşturur:

```tsx
// src/components/ui/skeleton.tsx
import { cn } from "@/lib/utils"

function Skeleton({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="skeleton"
      className={cn("bg-accent animate-pulse rounded-md", className)}
      {...props}
    />
  )
}

export { Skeleton }
```

### 2. Özel Skeleton Bileşenleri

Uygulamada kullanılan özel iskelet bileşenleri:

#### AppointmentHeaderSkeleton

Randevular sayfasının başlık kısmı için iskelet yükleyici.

```tsx
export function AppointmentHeaderSkeleton() {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-32" />
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <Skeleton className="h-8 w-32 rounded-md" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
        </div>
      </div>
    </div>
  )
}
```

#### WeeklyCalendarSkeleton

Haftalık takvim görünümü için iskelet yükleyici.

```tsx
export function WeeklyCalendarSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
      {Array.from({ length: 7 }).map((_, index) => (
        <Card key={index} className="h-full border">
          <CardHeader className="p-3">
            <div className="flex justify-between items-center">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
            <Skeleton className="h-4 w-24 mt-1" />
          </CardHeader>
          <CardContent className="p-3 space-y-2">
            <Skeleton className="h-10 w-full" />
            {Array.from({ length: Math.floor(Math.random() * 3) + 1 }).map((_, idx) => (
              <Skeleton key={idx} className="h-20 w-full" />
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
```

#### DailyCalendarSkeleton

Günlük takvim görünümü için iskelet yükleyici.

```tsx
export function DailyCalendarSkeleton() {
  return (
    <Card>
      <CardHeader className="py-3">
        <div className="flex justify-between items-center">
          <Skeleton className="h-6 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {Array.from({ length: 20 }).map((_, index) => (
          <div key={index} className="flex items-center justify-between p-2 border rounded-md">
            <Skeleton className="h-5 w-12" />
            <Skeleton className="h-4 w-48" />
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
```

#### MonthlyCalendarSkeleton

Aylık takvim görünümü için iskelet yükleyici.

```tsx
export function MonthlyCalendarSkeleton() {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-7 gap-1 text-center">
        {Array.from({ length: 7 }).map((_, index) => (
          <Skeleton key={index} className="h-6 w-full" />
        ))}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: 35 }).map((_, index) => (
          <Card key={index} className="min-h-24 p-1">
            <div className="flex justify-between items-start">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-8" />
            </div>
            <div className="mt-2 space-y-1">
              {Array.from({ length: Math.floor(Math.random() * 2) }).map((_, idx) => (
                <Skeleton key={idx} className="h-3 w-full" />
              ))}
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}
```

#### AppointmentsPageSkeleton

Ana randevular sayfası için iskelet yükleyici. Mevcut görünüm türüne göre uyarlanır.

```tsx
export function AppointmentsPageSkeleton({ 
  viewType = "weekly",
  className
}: { 
  viewType?: "daily" | "weekly" | "monthly" | "custom",
  className?: string
}) {
  return (
    <div className={cn("space-y-4", className)}>
      <AppointmentHeaderSkeleton />
      
      {viewType === "weekly" && <WeeklyCalendarSkeleton />}
      {viewType === "daily" && <DailyCalendarSkeleton />}
      {viewType === "monthly" && <MonthlyCalendarSkeleton />}
      {viewType === "custom" && <WeeklyCalendarSkeleton />}
    </div>
  )
}
```

## Kullanım Örnekleri

### 1. Ana Sayfa Bileşeninde Kullanım

`src/app/dashboard/appointments/page.tsx` dosyasında iskelet yükleyicinin kullanımı:

```tsx
"use client"

import Link from "next/link"
import { Plus } from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { AppointmentCalendar } from "@/components/appointment-calendar"
import { useUser } from "@/contexts/UserContext"
import { AppointmentsPageSkeleton } from "@/components/ui/skeleton-loaders"

export default function AppointmentsPage() {
  // UserContext'ten salon bilgilerini al
  const { salonId, salonLoading } = useUser()

  if (salonLoading) {
    return (
      <div className="p-4">
        <AppointmentsPageSkeleton />
      </div>
    )
  }

  if (!salonId) {
    // Salon bulunamadı durumu...
  }

  return (
    // Normal sayfa içeriği...
  )
}
```

### 2. Takvim Görünümlerinde Kullanım

`src/components/calendar/WeeklyCalendarView.tsx` dosyasında iskelet yükleyicinin kullanımı:

```tsx
import { WeeklyCalendarSkeleton } from "@/components/ui/skeleton-loaders"

export function WeeklyCalendarView({
  weekDays,
  appointmentsByDay,
  isLoading,
  onSlotClick,
  holidayDates = [],
  barberWorkingHours = [],
  onAppointmentUpdated
}: WeeklyCalendarViewProps) {
  // ...

  if (isLoading) {
    return <WeeklyCalendarSkeleton />;
  }

  // Normal bileşen içeriği...
}
```

## Avantajlar

1. **Daha İyi Kullanıcı Deneyimi**: Metin tabanlı yükleme göstergelerine kıyasla daha profesyonel bir görünüm sağlar.
2. **Düzen Kaymasını Önleme**: İskelet bileşenler, gerçek içeriğin düzenini yansıttığı için, içerik yüklendiğinde düzen kayması olmaz.
3. **Algılanan Performans İyileştirmesi**: Kullanıcılar, bir şeylerin yüklendiğini görsel olarak algıladıklarında, bekleme süresi daha kısa hissedilir.
4. **Yeniden Kullanılabilirlik**: İskelet bileşenler, uygulamanın farklı bölümlerinde kolayca yeniden kullanılabilir.
5. **Tutarlılık**: Tüm uygulama genelinde tutarlı bir yükleme deneyimi sağlar.

## Öneriler

1. **Daha Fazla Bileşen Ekleyin**: Uygulamanın diğer bölümleri için benzer iskelet bileşenleri oluşturun.
2. **Animasyonları Özelleştirin**: Daha yumuşak veya farklı animasyon stilleri ekleyerek kullanıcı deneyimini daha da iyileştirin.
3. **Tema Desteği**: İskelet bileşenlerin hem açık hem de koyu temada iyi görünmesini sağlayın.
