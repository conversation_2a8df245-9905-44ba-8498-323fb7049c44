"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { format, subDays } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  ArrowUpRight,
  ArrowDownRight,
  Loader2,
  X,
  Calendar
} from "lucide-react"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Badge } from "@/components/ui/badge"
import { useUser } from "@/contexts/UserContext"
import { FinanceTransaction, FinanceCategory } from "@/lib/db/types"
import { TransactionForm } from "@/components/transaction-form"
import { financeTransactions, financeCategories } from "@/lib/db"

export default function TransactionsPage() {
  const router = useRouter()
  const { salon } = useUser()
  const salonId = salon?.id

  const [transactions, setTransactions] = useState<FinanceTransaction[]>([])
  const [categories, setCategories] = useState<FinanceCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<FinanceTransaction | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  // Filtering state
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 30),
    to: new Date(),
  })
  const [categoryFilter, setCategoryFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Fetch transactions and categories when component mounts or filters change
  useEffect(() => {
    if (salonId) {
      fetchTransactions()
      fetchCategories()
    }
  }, [salonId, dateRange, categoryFilter, typeFilter])

  // Fetch transactions
  const fetchTransactions = async () => {
    if (!salonId) return

    setIsLoading(true)
    try {
      let data;

      // Format dates if both are selected
      if (dateRange.from && dateRange.to) {
        const startDate = format(dateRange.from, "yyyy-MM-dd");
        const endDate = format(dateRange.to, "yyyy-MM-dd");

        // Get transactions with date range filter
        data = await financeTransactions.getFinanceTransactionsInRange(salonId, startDate, endDate);
      } else {
        // Get all transactions
        data = await financeTransactions.getFinanceTransactions(salonId);
      }

      // Apply additional filters in memory
      let filteredData = [...data];

      // Filter by category if selected
      if (categoryFilter && categoryFilter !== "all") {
        filteredData = filteredData.filter(transaction => transaction.category_id === categoryFilter);
      }

      // Filter by type if selected
      if (typeFilter && typeFilter !== "all") {
        filteredData = filteredData.filter(transaction => {
          const category = transaction.finance_categories;
          return category && category.type === typeFilter;
        });
      }

      setTransactions(filteredData);
    } catch (error) {
      console.error("Error fetching transactions:", error)
      toast.error("İşlemler yüklenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch categories
  const fetchCategories = async () => {
    if (!salonId) return

    try {
      const data = await financeCategories.getFinanceCategories(salonId);
      setCategories(data);
    } catch (error) {
      console.error("Error fetching categories:", error)
      toast.error("Kategoriler yüklenirken bir hata oluştu")
    }
  }

  // Handle transaction deletion
  const handleDeleteTransaction = async () => {
    if (!selectedTransaction) return

    setIsDeleting(true)
    try {
      await financeTransactions.deleteFinanceTransaction(selectedTransaction.id);

      toast.success("İşlem silindi")
      fetchTransactions()
    } catch (error) {
      console.error("Error deleting transaction:", error)
      toast.error("İşlem silinirken bir hata oluştu")
    } finally {
      setIsDeleting(false)
      setShowDeleteDialog(false)
      setSelectedTransaction(null)
    }
  }

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    fetchTransactions()
  }

  // Reset filters
  const resetFilters = () => {
    setDateRange({
      from: subDays(new Date(), 30),
      to: new Date(),
    })
    setCategoryFilter("all")
    setTypeFilter("all")
    setSearchQuery("")
  }

  // Filter transactions by search query
  const filteredTransactions = transactions.filter(transaction => {
    if (!searchQuery) return true

    const searchLower = searchQuery.toLowerCase()
    const description = transaction.description?.toLowerCase() || ""
    const category = categories.find(c => c.id === transaction.category_id)?.name.toLowerCase() || ""

    return description.includes(searchLower) ||
           category.includes(searchLower) ||
           transaction.amount.toString().includes(searchLower)
  })

  // Pagination
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage)
  const paginatedTransactions = filteredTransactions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  // Get category details
  const getCategoryDetails = (categoryId: string) => {
    return categories.find(category => category.id === categoryId)
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/finance">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Finans İşlemleri</h1>
        </div>
      </header>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <p className="text-muted-foreground">
            Gelir ve gider işlemlerinizi yönetin
          </p>
        </div>
        <Button asChild>
          <Link href="/dashboard/finance/transactions/new">
            <Plus className="mr-2 h-4 w-4" />
            Yeni İşlem
          </Link>
        </Button>
      </div>

      {/* Filters */}
      <div className="bg-card rounded-lg border p-4">
        <div className="flex flex-col md:flex-row gap-4 items-end">
          <div className="w-full md:w-auto">
            <DatePickerWithRange
              dateRange={dateRange}
              setDateRange={setDateRange}
            />
          </div>

          <div className="w-full md:w-auto">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Tüm kategoriler" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm kategoriler</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: category.color || "#ccc" }}
                      />
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="w-full md:w-auto">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Tüm işlemler" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm işlemler</SelectItem>
                <SelectItem value="income">Gelirler</SelectItem>
                <SelectItem value="expense">Giderler</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <form onSubmit={handleSearch} className="flex w-full md:w-auto gap-2">
            <Input
              placeholder="İşlemlerde ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full md:w-[200px]"
            />
            <Button type="submit" variant="outline" size="icon">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          <Button variant="ghost" size="sm" onClick={resetFilters}>
            <X className="h-4 w-4 mr-2" />
            Filtreleri Temizle
          </Button>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-card rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Tarih</TableHead>
              <TableHead>Kategori</TableHead>
              <TableHead>Açıklama</TableHead>
              <TableHead className="text-right">Tutar</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>İşlemler yükleniyor...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : paginatedTransactions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <p>Hiç işlem bulunamadı.</p>
                  <p className="text-muted-foreground text-sm mt-2">
                    Yeni bir işlem eklemek için "Yeni İşlem" butonuna tıklayın.
                  </p>
                </TableCell>
              </TableRow>
            ) : (
              paginatedTransactions.map((transaction) => {
                const category = getCategoryDetails(transaction.category_id)
                const isIncome = category?.type === "income"

                return (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        {format(new Date(transaction.transaction_date), "dd MMM yyyy", { locale: tr })}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: category?.color || "#ccc" }}
                        />
                        <span>{category?.name || "Kategori bulunamadı"}</span>
                        <Badge variant={isIncome ? "success" : "destructive"} className="ml-2">
                          {isIncome ? "Gelir" : "Gider"}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {transaction.description || "-"}
                      {transaction.appointment_id && (
                        <Badge variant="outline" className="ml-2">
                          Randevu bağlantılı
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end">
                        {isIncome ? (
                          <ArrowUpRight className="h-4 w-4 text-emerald-500 mr-1" />
                        ) : (
                          <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
                        )}
                        <span className={isIncome ? "text-emerald-500" : "text-red-500"}>
                          {isIncome ? "+" : "-"}{transaction.amount.toLocaleString("tr-TR")} ₺
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <span className="sr-only">İşlemler</span>
                            <Filter className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedTransaction(transaction)
                              setShowEditDialog(true)
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Düzenle
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedTransaction(transaction)
                              setShowDeleteDialog(true)
                            }}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Sil
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>

        {/* Pagination */}
        {!isLoading && filteredTransactions.length > 0 && (
          <div className="p-4 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>

      {/* Edit Transaction Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>İşlemi Düzenle</DialogTitle>
            <DialogDescription>
              İşlem bilgilerini güncelleyin.
            </DialogDescription>
          </DialogHeader>

          {selectedTransaction && (
            <TransactionForm
              transaction={selectedTransaction}
              onSuccess={() => {
                setShowEditDialog(false)
                fetchTransactions()
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Transaction Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>İşlemi Silmek İstediğinize Emin Misiniz?</AlertDialogTitle>
            <AlertDialogDescription>
              Bu işlem geri alınamaz. Bu işlemi silmek istediğinize emin misiniz?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTransaction}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
