"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Edit, Trash, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import { format, parseISO, isAfter, startOfDay } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"
import { notifyCancelledAppointment, formatAppointmentForNotification } from "@/lib/utils/telegram-notifications"

import { Button } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { appointments } from "@/lib/db"
import { cn } from "@/lib/utils"

export default function AppointmentDetailsPage() {
  const params = useParams()
  const router = useRouter()

  const appointmentId = params.id as string


  const [appointment, setAppointment] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isActionLoading, setIsActionLoading] = useState(false)

  // Load appointment details
  useEffect(() => {
    async function loadAppointment() {
      setIsLoading(true)
      try {
        const data = await appointments.getAppointmentById(appointmentId)
        setAppointment(data)
      } catch (error) {
        console.error("Error loading appointment:", error)
        toast.error("Randevu bilgileri yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadAppointment()
  }, [appointmentId])

  // Handle appointment status changes
  const handleStatusChange = async (status: 'completed' | 'cancelled' | 'no-show') => {
    setIsActionLoading(true)
    try {
      switch (status) {
        case 'completed':
          await appointments.completeAppointment(appointmentId)
          toast.success("Randevu tamamlandı olarak işaretlendi.")
          break
        case 'cancelled':
          await appointments.cancelAppointment(appointmentId)
          toast.success("Randevu iptal edildi.")

          // Send Telegram notification for cancelled appointment (non-blocking)
          try {
            if (appointment) {
              const notificationData = formatAppointmentForNotification(
                appointment, // Pass the full appointment object with all data
                '', // salon name will be fetched in the API
                appointment.barbers?.name || 'Bilinmeyen Berber',
                appointment.services?.name || 'Bilinmeyen Hizmet'
              )

              notifyCancelledAppointment(notificationData)
            }
          } catch (error) {
            console.error('Failed to send Telegram notification for cancelled appointment:', error)
            // Don't show error to user as this is a background operation
          }
          break
        case 'no-show':
          await appointments.markAppointmentAsNoShow(appointmentId)
          toast.success("Randevu gelmedi olarak işaretlendi.")
          break
      }

      // Güncel veriyi al (ilişkili tablolarla birlikte)
      const updatedAppointment = await appointments.getAppointmentById(appointmentId)
      setAppointment(updatedAppointment)
    } catch (error) {
      console.error("Error updating appointment status:", error)
      toast.error("Randevu durumu güncellenirken bir hata oluştu.")
    } finally {
      setIsActionLoading(false)
    }
  }

  // Handle appointment deletion
  const handleDelete = async () => {
    if (!confirm("Bu randevuyu silmek istediğinizden emin misiniz?")) {
      return
    }

    setIsActionLoading(true)
    try {
      await appointments.deleteAppointment(appointmentId)
      toast.success("Randevu silindi.")
      router.push("/dashboard/appointments")
    } catch (error) {
      console.error("Error deleting appointment:", error)
      toast.error("Randevu silinirken bir hata oluştu.")
    } finally {
      setIsActionLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-3 sm:p-4">
        <div className="flex justify-center items-center h-64">
          <p>Yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (!appointment) {
    return (
      <div className="p-3 sm:p-4">
        <header className="flex h-12 sm:h-16 shrink-0 items-center gap-1 sm:gap-2 mb-3 sm:mb-4">
          <div className="flex items-center gap-1 sm:gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="hidden sm:block mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-xl sm:text-2xl font-bold truncate">Randevu Detayları</h1>
          </div>
        </header>

        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <p>Randevu bulunamadı.</p>
          <Button asChild className="h-10 sm:h-9">
            <Link href="/dashboard/appointments">Randevulara Dön</Link>
          </Button>
        </div>
      </div>
    )
  }

  // Format date and time
  const formattedDate = format(parseISO(appointment.date), "d MMMM yyyy, EEEE", { locale: tr })
  const startTime = appointment.start_time.substring(0, 5)
  const endTime = appointment.end_time.substring(0, 5)

  // Randevunun geçmiş olup olmadığını kontrol et
  const isAppointmentInPast = () => {
    const today = startOfDay(new Date())
    const appointmentDate = parseISO(appointment.date)
    return !isAfter(appointmentDate, today) // Bugün veya daha önceki bir tarih ise geçmiş randevudur
  }

  // Randevunun aktif olup olmadığını kontrol et
  const isActive = appointment.status === 'booked'

  // Randevunun gelecekte olup olmadığını kontrol et
  const isFuture = !isAppointmentInPast()

  return (
    <div className="p-3 sm:p-4 space-y-4 sm:space-y-6">
      <header className="flex h-12 sm:h-16 shrink-0 items-center gap-1 sm:gap-2 mb-3 sm:mb-4">
        <div className="flex items-center gap-1 sm:gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="hidden sm:block mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-1 sm:mr-2 h-9 w-9 sm:h-10 sm:w-10">
            <Link href="/dashboard/appointments">
              <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold truncate">Randevu Detayları</h1>
        </div>
      </header>

      <Card>
        <CardHeader className="pb-3 sm:pb-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
            <div>
              <CardTitle className="text-xl sm:text-2xl break-words">{appointment.fullname || (appointment.customers ? `${appointment.customers.name} ${appointment.customers.surname}` : 'İsimsiz Müşteri')}</CardTitle>
              <CardDescription>
                {formattedDate} • {startTime} - {endTime}
              </CardDescription>
            </div>
            <div className={cn(
              "px-3 py-1 rounded-full text-sm font-medium self-start sm:self-auto mt-1 sm:mt-0",
              appointment.status === 'booked' && "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
              appointment.status === 'completed' && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
              appointment.status === 'cancelled' && "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
              appointment.status === 'no-show' && "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
            )}>
              {appointment.status === 'booked' && "Aktif"}
              {appointment.status === 'completed' && "Tamamlandı"}
              {appointment.status === 'cancelled' && "İptal Edildi"}
              {appointment.status === 'no-show' && "Gelmedi"}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4 sm:space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <h3 className="text-xs sm:text-sm font-medium text-muted-foreground mb-0.5 sm:mb-1">Hizmet</h3>
              <p className="break-words">{appointment.services?.name ? `${appointment.services.name} (${appointment.services.duration} dk)` : 'Bilinmeyen Hizmet'}</p>
            </div>
            <div>
              <h3 className="text-xs sm:text-sm font-medium text-muted-foreground mb-0.5 sm:mb-1">Berber</h3>
              <p className="break-words">{appointment.barbers?.name || 'Bilinmeyen Berber'}</p>
            </div>
            <div>
              <h3 className="text-xs sm:text-sm font-medium text-muted-foreground mb-0.5 sm:mb-1">Müşteri Telefonu</h3>
              <p className="break-words">{appointment.phonenumber || (appointment.customers ? appointment.customers.phone : "-")}</p>
            </div>
            <div>
              <h3 className="text-xs sm:text-sm font-medium text-muted-foreground mb-0.5 sm:mb-1">Müşteri E-postası</h3>
              <p className="break-words">{appointment.email || (appointment.customers ? appointment.customers.email : "-") || "-"}</p>
            </div>
          </div>

          {appointment.notes && (
            <div>
              <h3 className="text-xs sm:text-sm font-medium text-muted-foreground mb-0.5 sm:mb-1">Notlar</h3>
              <p className="break-words">{appointment.notes}</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row justify-between gap-4 sm:gap-2 pt-4">
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              size="sm"
              asChild
              className="flex-1 sm:flex-initial h-10 sm:h-9"
            >
              <Link href={`/dashboard/appointments/${appointmentId}/edit`}>
                <Edit className="mr-2 h-5 w-5 sm:h-4 sm:w-4" />
                Düzenle
              </Link>
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDelete}
              disabled={isActionLoading}
              className="flex-1 sm:flex-initial h-10 sm:h-9"
            >
              <Trash className="mr-2 h-5 w-5 sm:h-4 sm:w-4" />
              Sil
            </Button>
          </div>

          {isActive && (
            <div className="flex flex-wrap gap-2 w-full sm:w-auto">
              {/* İptal Et butonu sadece gelecekteki randevular için gösterilir */}
              {isFuture && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusChange('cancelled')}
                  disabled={isActionLoading}
                  className="flex-1 sm:flex-initial h-10 sm:h-9"
                >
                  <XCircle className="mr-2 h-5 w-5 sm:h-4 sm:w-4" />
                  İptal Et
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleStatusChange('no-show')}
                disabled={isActionLoading}
                className="flex-1 sm:flex-initial h-10 sm:h-9"
              >
                <AlertTriangle className="mr-2 h-5 w-5 sm:h-4 sm:w-4" />
                Gelmedi
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() => handleStatusChange('completed')}
                disabled={isActionLoading}
                className="flex-1 sm:flex-initial h-10 sm:h-9"
              >
                <CheckCircle className="mr-2 h-5 w-5 sm:h-4 sm:w-4" />
                Tamamlandı
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
