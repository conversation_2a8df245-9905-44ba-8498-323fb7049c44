import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import { encryptChannelId, decryptChannelId, validateChannelId } from '@/lib/utils/encryption';

// Validation schemas
const updateSettingsSchema = z.object({
  telegram_channel_id: z.string().optional(),
  is_enabled: z.boolean().optional(),
});

// GET /api/telegram/settings
export async function GET(request: NextRequest) {
  try {
    // Get the query parameters
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');

    if (!salonId) {
      return NextResponse.json(
        { error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Create a Supabase client
    const supabase = createRouteHandlerClient({ cookies });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get Telegram settings for the salon
    const { data: settings, error } = await supabase
      .from('salon_telegram_settings')
      .select('*')
      .eq('salon_id', salonId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching Telegram settings:', error);
      return NextResponse.json(
        { error: 'Failed to fetch Telegram settings' },
        { status: 500 }
      );
    }

    // If no settings exist, return default values
    if (!settings) {
      return NextResponse.json({
        salon_id: salonId,
        telegram_channel_id: '',
        is_enabled: false,
        created_at: null,
        updated_at: null
      });
    }

    // Decrypt channel ID if it exists
    let decryptedChannelId = '';
    if (settings.telegram_channel_id) {
      try {
        decryptedChannelId = decryptChannelId(settings.telegram_channel_id);
      } catch (error) {
        console.error('Failed to decrypt channel ID:', error);
        // Don't expose decryption errors to client
        decryptedChannelId = '';
      }
    }

    return NextResponse.json({
      salon_id: settings.salon_id,
      telegram_channel_id: decryptedChannelId,
      is_enabled: settings.is_enabled,
      created_at: settings.created_at,
      updated_at: settings.updated_at
    });
  } catch (error) {
    console.error('Error in GET /api/telegram/settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/telegram/settings
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const validation = updateSettingsSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }

    const { telegram_channel_id, is_enabled } = validation.data;

    // Get salon_id from query params
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');

    if (!salonId) {
      return NextResponse.json(
        { error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Create a Supabase client
    const supabase = createRouteHandlerClient({ cookies });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Validate channel ID if provided
    if (telegram_channel_id && !validateChannelId(telegram_channel_id)) {
      return NextResponse.json(
        { error: 'Geçersiz Telegram kanal ID formatı' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: { telegram_channel_id?: string | null; is_enabled?: boolean } = {};

    if (telegram_channel_id !== undefined) {
      if (telegram_channel_id.trim() === '') {
        updateData.telegram_channel_id = null;
      } else {
        try {
          updateData.telegram_channel_id = encryptChannelId(telegram_channel_id);
        } catch (error) {
          console.error('Failed to encrypt channel ID:', error);
          return NextResponse.json(
            { error: 'Kanal ID şifrelenirken hata oluştu' },
            { status: 500 }
          );
        }
      }
    }

    if (is_enabled !== undefined) {
      updateData.is_enabled = is_enabled;
    }

    // If enabling Telegram but no channel ID provided, require it
    if (is_enabled && !telegram_channel_id && updateData.telegram_channel_id === undefined) {
      // Check if there's an existing channel ID
      const { data: existingSettings } = await supabase
        .from('salon_telegram_settings')
        .select('telegram_channel_id')
        .eq('salon_id', salonId)
        .single();

      if (!existingSettings?.telegram_channel_id) {
        return NextResponse.json(
          { error: 'Telegram bildirimleri etkinleştirmek için kanal ID gereklidir' },
          { status: 400 }
        );
      }
    }

    // Upsert the settings
    const { data: settings, error } = await supabase
      .from('salon_telegram_settings')
      .upsert(
        {
          salon_id: salonId,
          ...updateData,
          updated_at: new Date().toISOString()
        },
        {
          onConflict: 'salon_id'
        }
      )
      .select()
      .single();

    if (error) {
      console.error('Error updating Telegram settings:', error);
      return NextResponse.json(
        { error: 'Telegram ayarları güncellenirken hata oluştu' },
        { status: 500 }
      );
    }

    // Return success response with decrypted channel ID
    let decryptedChannelId = '';
    if (settings.telegram_channel_id) {
      try {
        decryptedChannelId = decryptChannelId(settings.telegram_channel_id);
      } catch (error) {
        console.error('Failed to decrypt channel ID for response:', error);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Telegram ayarları başarıyla güncellendi',
      settings: {
        salon_id: settings.salon_id,
        telegram_channel_id: decryptedChannelId,
        is_enabled: settings.is_enabled,
        created_at: settings.created_at,
        updated_at: settings.updated_at
      }
    });
  } catch (error) {
    console.error('Error in POST /api/telegram/settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/telegram/settings (alias for POST)
export async function PUT(request: NextRequest) {
  return POST(request);
}
