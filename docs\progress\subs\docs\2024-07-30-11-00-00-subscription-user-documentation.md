# SalonFlow Abonelik Sistemi Kullanıcı Dokümantasyonu

**Tarih:** 30 Temmuz 2024
**Saat:** 11:00

## 1. Salon Sahibi İçin Abonelik Yönetimi Kılavuzu

### 1.1. Abonelik Durumunu Görüntüleme

Salon sahibi olarak, abonelik durumunuzu görüntülemek için:

1. SalonFlow hesabınıza giriş yapın.
2. Sol menüden "Abonelik" seçeneğine tıklayın.
3. Abonelik sayfasında şu bilgileri görebilirsiniz:
   - Mevcut abonelik planınız
   - Abonelik durumunuz (deneme, aktif, ödeme bekliyor, askıda)
   - Abonelik başlangıç tarihi
   - Deneme süresi bitiş tarihi (eğer deneme sürecindeyseniz)
   - Bir sonraki ödeme tarihi
   - Ödeme yöntemi

### 1.2. Plan Yükseltme

Abonelik planınızı yükseltmek için:

1. <PERSON><PERSON><PERSON> say<PERSON> "Planı Yükselt" butonuna tıklayın.
2. Yükseltmek istediğiniz planı seçin (Solo, Small Team veya Pro Salon).
3. Ödeme döngüsünü seçin (aylık veya yıllık).
   - Yıllık ödemede %10 indirim uygulanır.
4. Ödeme özetini inceleyin.
5. "Planı Yükselt" butonuna tıklayın.
6. Ödeme bilgilerini girin ve ödemeyi tamamlayın.

**Not:** Plan yükseltme işlemi anında gerçekleşir ve yeni plan özellikleri hemen kullanılabilir hale gelir.

### 1.3. Ödeme Geçmişini Görüntüleme

Geçmiş ödemelerinizi görüntülemek için:

1. Abonelik sayfasında "Ödeme Geçmişi" sekmesine tıklayın.
2. Tüm ödemelerinizi tarih, tutar, ödeme yöntemi ve durum bilgileriyle görebilirsiniz.
3. Ödeme makbuzunu indirmek için ilgili ödemenin yanındaki "Makbuz" butonuna tıklayın.

### 1.4. Ödeme Yapma

Manuel ödeme yapmak için:

1. Abonelik sayfasında "Ödeme Yap" butonuna tıklayın.
2. Ödeme tutarını ve yöntemini seçin.
3. Banka havalesi/EFT için:
   - Görüntülenen banka hesap bilgilerini kullanarak ödemeyi gerçekleştirin.
   - Dekont bilgilerini sisteme girin.
   - "Ödemeyi Bildir" butonuna tıklayın.
4. Ödemeniz admin tarafından onaylandıktan sonra abonelik durumunuz güncellenecektir.

### 1.5. Referans Kodu Kullanımı

Referans kodunuzu kullanarak diğer salon sahiplerine indirim sağlamak için:

1. Sol menüden "Referanslar" seçeneğine tıklayın.
2. Benzersiz referans kodunuzu görüntüleyin.
3. "Kopyala" butonuna tıklayarak kodu kopyalayın.
4. Bu kodu diğer salon sahipleriyle paylaşın.
5. Kodunuzu kullanan her yeni salon için 1 aylık ücretsiz abonelik kazanırsınız.

### 1.6. Abonelik İptali

Aboneliğinizi iptal etmek için:

1. Abonelik sayfasında "Aboneliği İptal Et" butonuna tıklayın.
2. İptal nedeninizi seçin.
3. "İptal Onayı" butonuna tıklayın.
4. İptal işlemi onaylandıktan sonra, mevcut ödeme döneminin sonuna kadar hizmetleri kullanmaya devam edebilirsiniz.

**Not:** Abonelik iptali durumunda verileriniz 1 ay boyunca saklanır. Bu süre içinde aboneliğinizi yeniden aktifleştirirseniz, verilerinize erişmeye devam edebilirsiniz.

## 2. Admin İçin Abonelik Yönetimi Kılavuzu

### 2.1. Abonelikleri Görüntüleme

Admin olarak, tüm abonelikleri görüntülemek için:

1. Admin hesabınıza giriş yapın.
2. Sol menüden "Abonelikler" seçeneğine tıklayın.
3. Tüm salonların aboneliklerini içeren bir tablo görüntülenecektir.
4. Tabloyu salon adı, abonelik durumu veya plan tipine göre filtreleyebilirsiniz.
5. Arama kutusunu kullanarak belirli bir salonu arayabilirsiniz.

### 2.2. Abonelik Detaylarını Görüntüleme

Bir aboneliğin detaylarını görüntülemek için:

1. Abonelikler listesinde ilgili aboneliğin yanındaki "Detaylar" butonuna tıklayın.
2. Abonelik detay sayfasında şu bilgileri görebilirsiniz:
   - Salon bilgileri
   - Abonelik planı ve durumu
   - Başlangıç ve bitiş tarihleri
   - Ödeme geçmişi
   - Abonelik işlem geçmişi

### 2.3. Abonelik Düzenleme

Bir aboneliği düzenlemek için:

1. Abonelik detay sayfasında "Düzenle" butonuna tıklayın.
2. Aşağıdaki bilgileri düzenleyebilirsiniz:
   - Abonelik planı
   - Abonelik durumu
   - Başlangıç ve bitiş tarihleri
   - Deneme süresi bitiş tarihi
   - Ödeme döngüsü (aylık/yıllık)
3. "Kaydet" butonuna tıklayarak değişiklikleri kaydedin.

### 2.4. Ödeme Ekleme

Bir abonelik için manuel ödeme eklemek için:

1. Abonelik detay sayfasında "Ödeme Ekle" butonuna tıklayın.
2. Aşağıdaki bilgileri girin:
   - Ödeme tutarı
   - Ödeme tarihi
   - Ödeme yöntemi
   - Ödeme durumu
   - İşlem ID'si (varsa)
   - Notlar
3. "Ödeme Ekle" butonuna tıklayarak ödemeyi kaydedin.
4. Ödeme kaydedildikten sonra, abonelik durumu otomatik olarak güncellenecektir.

### 2.5. Abonelik Durumu Değiştirme

Bir aboneliğin durumunu değiştirmek için:

1. Abonelik detay sayfasında "Durum Değiştir" butonuna tıklayın.
2. Yeni durumu seçin:
   - Trial (Deneme)
   - Active (Aktif)
   - Past Due (Ödeme Bekliyor)
   - Suspended (Askıda)
   - Cancelled (İptal Edildi)
3. Durum değişikliği için bir not ekleyin.
4. "Durumu Güncelle" butonuna tıklayın.

### 2.6. Abonelik Raporları

Abonelik raporlarını görüntülemek için:

1. Admin panelinde "Raporlar" seçeneğine tıklayın.
2. "Abonelik Raporları" sekmesini seçin.
3. Aşağıdaki raporları görüntüleyebilirsiniz:
   - Aktif abonelikler
   - Deneme sürecindeki abonelikler
   - Süresi yakında dolacak abonelikler
   - Ödeme bekleyen abonelikler
   - İptal edilen abonelikler
4. Raporları CSV veya PDF formatında indirebilirsiniz.
