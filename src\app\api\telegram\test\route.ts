import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';

// Validation schema
const testNotificationSchema = z.object({
  salon_id: z.string().uuid('Geçersiz salon ID'),
});

// POST /api/telegram/test
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const validation = testNotificationSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }

    const { salon_id } = validation.data;

    // Create a Supabase client
    const supabase = createRouteHandlerClient({ cookies });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user has access to this salon
    const { data: salon, error: salonError } = await supabase
      .from('salons')
      .select('id, name, owner_id')
      .eq('id', salon_id)
      .single();

    if (salonError || !salon) {
      return NextResponse.json(
        { error: 'Salon bulunamadı' },
        { status: 404 }
      );
    }

    // Check if user is salon owner or admin
    const { data: isAdmin } = await supabase.rpc('is_admin');
    const isOwner = salon.owner_id === user.id;

    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Bu salona erişim yetkiniz yok' },
        { status: 403 }
      );
    }

    // Check if Telegram is configured and enabled
    const { data: telegramSettings, error: settingsError } = await supabase
      .from('salon_telegram_settings')
      .select('is_enabled, telegram_channel_id')
      .eq('salon_id', salon_id)
      .single();

    if (settingsError || !telegramSettings) {
      return NextResponse.json(
        {
          success: false,
          error: 'Telegram ayarları bulunamadı. Lütfen önce Telegram ayarlarını yapılandırın.'
        },
        { status: 400 }
      );
    }

    if (!telegramSettings.is_enabled) {
      return NextResponse.json(
        {
          success: false,
          error: 'Telegram bildirimleri etkin değil. Lütfen önce etkinleştirin.'
        },
        { status: 400 }
      );
    }

    if (!telegramSettings.telegram_channel_id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Telegram kanal ID tanımlanmamış. Lütfen ayarları kontrol edin.'
        },
        { status: 400 }
      );
    }

    // Call internal API route to send test notification
    const notifyResponse = await fetch(`${request.nextUrl.origin}/api/telegram/notify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'test_notification',
        salon_id: salon_id,
        salon_name: salon.name
      }),
    });

    const notifyResult = await notifyResponse.json();

    if (!notifyResponse.ok || !notifyResult.success) {
      console.error('Error calling Telegram notify API:', notifyResult.error);
      return NextResponse.json(
        {
          success: false,
          error: notifyResult.error || 'Test bildirimi gönderilirken hata oluştu. Lütfen daha sonra tekrar deneyin.'
        },
        { status: notifyResponse.status || 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Test bildirimi başarıyla gönderildi! Telegram kanalınızı kontrol edin.'
    });

  } catch (error) {
    console.error('Error in POST /api/telegram/test:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.'
      },
      { status: 500 }
    );
  }
}
