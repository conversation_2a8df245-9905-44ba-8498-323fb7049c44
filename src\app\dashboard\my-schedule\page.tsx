"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"
import { format } from "date-fns"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { barbers, barberWorkingHours } from "@/lib/db"
import type { <PERSON>, BarberWorkingHours } from "@/lib/db/types"
import { useUser } from "@/contexts/UserContext"

// Define the form schema for working hours
const workingHoursSchema = z.object({
  day_of_week: z.string(),
  is_closed: z.boolean().default(false),
  open_time: z.string().optional(),
  close_time: z.string().optional(),
})

const days = [
  { value: "0", label: "Pazar" },
  { value: "1", label: "Pazartesi" },
  { value: "2", label: "Salı" },
  { value: "3", label: "Çarşamba" },
  { value: "4", label: "Perşembe" },
  { value: "5", label: "Cuma" },
  { value: "6", label: "Cumartesi" },
]

export default function MySchedulePage() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [barber, setBarber] = useState<Barber | null>(null)
  const [workingHours, setWorkingHours] = useState<BarberWorkingHours[]>([])
  const [selectedDay, setSelectedDay] = useState("1") // Default to Monday
  const [isClosed, setIsClosed] = useState(false)

  // Initialize the form
  const form = useForm<z.infer<typeof workingHoursSchema>>({
    resolver: zodResolver(workingHoursSchema),
    defaultValues: {
      day_of_week: "1",
      is_closed: false,
      open_time: "09:00",
      close_time: "17:00",
    },
  })

  // UserContext'ten kullanıcı bilgilerini al
  const { user, isLoading: userLoading } = useUser()

  // Load staff profile and working hours
  useEffect(() => {
    async function loadData() {
      if (userLoading) return

      try {
        if (!user) {
          //router.push("/auth/login")//TODO
          return
        }

        // Get the barber profile for this user
        const barberData = await barbers.getBarberByUserId(user.id)

        if (!barberData) {
          toast.error("Staff profile not found")
          //router.push("/dashboard")//TODO
          return
        }

        setBarber(barberData)

        // Load working hours
        const hours = await barberWorkingHours.getBarberWorkingHours(barberData.id)
        setWorkingHours(hours)

        // Set initial form values based on selected day
        updateFormForSelectedDay(selectedDay, hours)
      } catch (error) {
        console.error("Error loading data:", error)
        toast.error("Failed to load schedule data")
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [user, userLoading, router])

  // Update form when selected day changes
  useEffect(() => {
    updateFormForSelectedDay(selectedDay, workingHours)
  }, [selectedDay, workingHours, form])

  // Update form values based on selected day
  function updateFormForSelectedDay(day: string, hours: BarberWorkingHours[]) {
    const dayHours = hours.find(h => h.day_of_week === parseInt(day))

    if (dayHours) {
      form.reset({
        day_of_week: day,
        is_closed: dayHours.is_closed,
        open_time: dayHours.open_time,
        close_time: dayHours.close_time,
      })
      setIsClosed(dayHours.is_closed)
    } else {
      // Default values for new day
      form.reset({
        day_of_week: day,
        is_closed: false,
        open_time: "09:00",
        close_time: "17:00",
      })
      setIsClosed(false)
    }
  }

  // Handle form submission
  async function onSubmit(values: z.infer<typeof workingHoursSchema>) {
    if (!barber) {
      toast.error("Staff profile not found")
      return
    }

    try {
      const dayOfWeek = parseInt(values.day_of_week)
      const existingHours = workingHours.find(h => h.day_of_week === dayOfWeek)

      if (existingHours) {
        // Update existing hours
        await barberWorkingHours.updateBarberWorkingHours({
          id: existingHours.id,
          is_closed: values.is_closed,
          open_time: values.is_closed ? existingHours.open_time : values.open_time,
          close_time: values.is_closed ? existingHours.close_time : values.close_time,
        })
      } else {
        // Create new hours
        await barberWorkingHours.createBarberWorkingHours({
          barber_id: barber.id,
          day_of_week: dayOfWeek,
          is_closed: values.is_closed,
          open_time: values.is_closed ? "09:00" : values.open_time!,
          close_time: values.is_closed ? "17:00" : values.close_time!,
        })
      }

      // Reload working hours
      const updatedHours = await barberWorkingHours.getBarberWorkingHours(barber.id)
      setWorkingHours(updatedHours)

      toast.success("Schedule updated successfully")
    } catch (error) {
      console.error("Error updating schedule:", error)
      toast.error("Failed to update schedule")
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Programım</h1>
        </div>
      </header>

      <Card>
        <CardHeader>
          <CardTitle>Çalışma Saatleri</CardTitle>
          <CardDescription>
            Haftanın her günü için çalışma saatlerinizi ayarlayın
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="day_of_week"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Haftanın Günü</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value)
                        setSelectedDay(value)
                      }}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Bir gün seçin" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {days.map((day) => (
                          <SelectItem key={day.value} value={day.value}>
                            {day.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_closed"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked)
                          setIsClosed(!!checked)
                        }}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        İzin Günü
                      </FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Bu günde çalışmıyorsanız işaretleyin
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              {!isClosed && (
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="open_time"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Başlangıç Saati</FormLabel>
                        <FormControl>
                          <input
                            type="time"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="close_time"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bitiş Saati</FormLabel>
                        <FormControl>
                          <input
                            type="time"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              <div className="flex justify-end">
                <Button type="submit">
                  Programı Kaydet
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
