-- Telegram Settings Schema for SalonFlow
-- Date: 2025-05-25-03-53-25
-- Description: Database schema for Telegram notification settings with encryption and RLS policies

-- Create salon_telegram_settings table
CREATE TABLE salon_telegram_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  telegram_channel_id TEXT, -- encrypted channel ID
  is_enabled BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one settings record per salon
  CONSTRAINT unique_salon_telegram_settings UNIQUE (salon_id)
);

-- Create indexes for performance
CREATE INDEX idx_salon_telegram_settings_salon_id ON salon_telegram_settings(salon_id);
CREATE INDEX idx_salon_telegram_settings_enabled ON salon_telegram_settings(salon_id, is_enabled) WHERE is_enabled = true;

-- Enable Row Level Security
ALTER TABLE salon_telegram_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Salon owners can manage their own Telegram settings
CREATE POLICY "Salon owners can manage their telegram settings" ON salon_telegram_settings
  FOR ALL USING (
    salon_id IN (
      SELECT id FROM salons WHERE owner_id = auth.uid()
    )
  );

-- RLS Policy: Admins can access all Telegram settings
CREATE POLICY "Admins can access all telegram settings" ON salon_telegram_settings
  FOR ALL USING (is_admin());

-- RLS Policy: Staff can view their salon's Telegram settings (read-only)
CREATE POLICY "Staff can view salon telegram settings" ON salon_telegram_settings
  FOR SELECT USING (
    salon_id IN (
      SELECT salon_id FROM barbers WHERE user_id = auth.uid()
    )
  );

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_telegram_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER trigger_update_telegram_settings_updated_at
  BEFORE UPDATE ON salon_telegram_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_telegram_settings_updated_at();

-- Function to get decrypted Telegram settings for a salon (for internal use)
CREATE OR REPLACE FUNCTION get_salon_telegram_settings(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  telegram_channel_id TEXT,
  is_enabled BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
)
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id,
    s.salon_id,
    s.telegram_channel_id,
    s.is_enabled,
    s.created_at,
    s.updated_at
  FROM salon_telegram_settings s
  WHERE s.salon_id = p_salon_id
    AND s.is_enabled = true;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_salon_telegram_settings(UUID) TO authenticated;

-- Function to check if Telegram notifications are enabled for a salon
CREATE OR REPLACE FUNCTION is_telegram_enabled(p_salon_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_enabled BOOLEAN := false;
BEGIN
  SELECT is_enabled INTO v_enabled
  FROM salon_telegram_settings
  WHERE salon_id = p_salon_id;
  
  RETURN COALESCE(v_enabled, false);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION is_telegram_enabled(UUID) TO authenticated;

-- Insert default settings for existing salons (disabled by default)
INSERT INTO salon_telegram_settings (salon_id, is_enabled)
SELECT id, false
FROM salons
WHERE id NOT IN (SELECT salon_id FROM salon_telegram_settings);

-- Comments for documentation
COMMENT ON TABLE salon_telegram_settings IS 'Stores Telegram notification settings for each salon with encrypted channel IDs';
COMMENT ON COLUMN salon_telegram_settings.telegram_channel_id IS 'Encrypted Telegram channel ID for receiving notifications';
COMMENT ON COLUMN salon_telegram_settings.is_enabled IS 'Whether Telegram notifications are enabled for this salon';
COMMENT ON FUNCTION get_salon_telegram_settings(UUID) IS 'Retrieves Telegram settings for a salon (internal use)';
COMMENT ON FUNCTION is_telegram_enabled(UUID) IS 'Checks if Telegram notifications are enabled for a salon';
