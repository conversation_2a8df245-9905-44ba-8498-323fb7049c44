"use client"

import { motion } from "framer-motion"
import { MapPin, Phone, Mail, Clock, ExternalLink } from "lucide-react"
import { BookingButton } from "./booking-button"
import { getDefaultSalonContent } from "@/lib/db/public"
import { usePreview, useIsPreview } from "@/contexts/PreviewContext"
import { useSalon } from "@/contexts/SalonContext"

interface ContactSectionProps {
  salon: {
    name: string
    address?: string
    phone?: string
    email?: string
  }
  salonId: string
}

export function ContactSection({ salon, salonId }: ContactSectionProps) {
  // Preview context
  const isPreview = useIsPreview()
  const previewContext = isPreview ? usePreview() : null

  // Salon context for content
  const { salonContent, contentLoading } = useSalon()

  // Get contact content based on context
  const content = isPreview && previewContext
    ? previewContext.previewContent.contact
    : salonContent?.contact || getDefaultSalonContent().contact

  const isLoading = isPreview ? false : contentLoading

  const contactItems = [
    {
      icon: MapPin,
      title: "Adres",
      content: salon.address || "Adres bilgisi güncelleniyor",
      action: salon.address ? () => {
        const encodedAddress = encodeURIComponent(salon.address!)
        window.open(`https://maps.google.com/?q=${encodedAddress}`, '_blank')
      } : undefined
    },
    {
      icon: Phone,
      title: "Telefon",
      content: salon.phone || "Telefon bilgisi güncelleniyor",
      action: salon.phone ? () => {
        window.open(`tel:${salon.phone}`, '_self')
      } : undefined
    },
    {
      icon: Mail,
      title: "E-posta",
      content: salon.email || "E-posta bilgisi güncelleniyor",
      action: salon.email ? () => {
        window.open(`mailto:${salon.email}`, '_self')
      } : undefined
    }
  ]

  const workingHours = [
    { day: "Pazartesi - Cumartesi", hours: "08:00 - 00:00" },
    { day: "Pazar", hours: "Kapalı" }
  ]

  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-6 mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
              <MapPin className="w-4 h-4" />
              <span>İletişim</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-foreground">
              {content.title || 'Bizimle İletişime Geçin'}
            </h2>

            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {content.description || 'Sorularınız için bizimle iletişime geçebilir veya hemen online randevu alabilirsiniz.'}
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-16 items-start">
            {/* Left Content - Contact Info */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              {/* Contact Items */}
              <div className="space-y-6">
                {contactItems.map((item, index) => (
                  <motion.div
                    key={item.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className={`flex items-start space-x-4 p-4 rounded-xl border border-border hover:border-primary/30 transition-colors duration-300 ${
                      item.action ? 'cursor-pointer hover:bg-primary/5' : ''
                    }`}
                    onClick={item.action}
                  >
                    <div className="p-3 bg-primary/10 rounded-lg flex-shrink-0">
                      <item.icon className="w-6 h-6 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-foreground mb-1">
                        {item.title}
                      </h3>
                      <p className="text-muted-foreground break-words">
                        {item.content}
                      </p>
                      {item.action && (
                        <div className="flex items-center space-x-1 mt-2 text-sm text-primary">
                          <span>Tıklayın</span>
                          <ExternalLink className="w-3 h-3" />
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Working Hours */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
                className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl p-6 border border-primary/10"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Clock className="w-5 h-5 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground">
                    Çalışma Saatleri
                  </h3>
                </div>
                <div className="space-y-3">
                  {workingHours.map((schedule, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-muted-foreground">{schedule.day}</span>
                      <span className="font-medium text-foreground">{schedule.hours}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t border-border/50">
                  <p className="text-sm text-muted-foreground">
                    * Özel günlerde çalışma saatleri değişebilir
                  </p>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Content - CTA */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              {/* Main CTA Card */}
              <div className="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl p-8 border border-primary/20">
                <div className="text-center space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-foreground">
                      {content.cta?.title || 'Hemen Randevu Alın'}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {content.cta?.description || 'Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz.'}
                    </p>
                  </div>

                  <BookingButton
                    buttonText="Randevu Al"
                    buttonSize="lg"
                    buttonClassName="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg hover:shadow-xl transition-all duration-300 text-lg py-6"
                  />

                  <div className="text-sm text-muted-foreground">
                    ⚡ Hızlı ve kolay • 📱 Mobil uyumlu • ✅ Anında onay
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              <div className="space-y-4">
                <div className="bg-background border border-border rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-3">
                    Neden Online Randevu?
                  </h4>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                      <span>Sıra beklemeden hizmet alın</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                      <span>Tercih ettiğiniz berberi seçin</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                      <span>Size uygun saati belirleyin</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                      <span>SMS ile hatırlatma alın</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-2">
                    💡 İpucu
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Randevunuza gelemeyecekseniz lütfen en az 2 saat önceden iptal edin.
                    Bu sayede diğer müşterilerimiz de o saati değerlendirebilir.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
