# Referans Sistemi Analizi ve İyileştirme Planı

**Tarih:** 1 Ağustos 2024
**Saat:** 14:00

## Mevcut Durum Analizi

SalonFlow referans sistemi şu anda aşağıdaki adımlarla çalışmaktadır:

1. Salon sahipleri dashboard üzerinden referans kodu oluşturabilir
2. Bu kodu başkalarıyla paylaşabilirler
3. Yeni kullanıcılar kayıt sırasında bu kodu girebilir
4. Referans kodu kayıt sırasında sessionStorage'a kaydedilir
5. E-posta doğrulamasından sonra kullanıcı dashboard'a yönlendirilir
6. Yeni kullanıcılar sistemi kullanabilmek için bir salon oluşturmalıdır
7. Salon oluşturulduktan sonra referans kodu faydası uygulanmalıdır

## Tespit Edilen Sorunlar

Yapılan incelemede aşağıdaki sorunlar tespit edilmiştir:

1. **Referans Kodu Uygulanmıyor**: Referans kodu sessionStorage'a kaydediliyor ancak salon oluşturulduktan sonra bu kod alınıp uygulanmıyor.

2. **Otomatik Deneme Aboneliği Oluşturulmuyor**: Yeni salon oluşturulduğunda otomatik olarak bir deneme aboneliği oluşturulmuyor.

3. **Referans Faydaları Uygulanmıyor**: `applyReferralCode` fonksiyonu mevcut ancak kullanıcı yolculuğunda hiçbir yerde çağrılmıyor.

4. **Abonelik Middleware Kısıtlaması**: Aktif bir abonelik olmadığında middleware erişimi kısıtlıyor, bu da deneme aboneliği oluşturulmadığında kullanıcının sidebar'ının kilitli görünmesine neden oluyor.

## Çözüm Planı

### 1. Salon Oluşturma Sürecini Güncelleme

`src/app/dashboard/settings/page.tsx` dosyasında salon oluşturma işlemi sırasında:

1. sessionStorage'dan referans kodunu kontrol et
2. Kod varsa, yeni salon ID'si ile `applyReferralCode` fonksiyonunu çağır
3. Yeni salon için otomatik olarak deneme aboneliği oluştur
4. Referans faydalarını uygula (örn. deneme süresini uzat)

```typescript
// Salon oluşturma başarılı olduktan sonra
if (data) {
  // Salon için varsayılan çalışma saatlerini ayarla
  await setupDefaultWorkingHours(data.id);

  // Referans kodunu kontrol et ve uygula
  if (typeof window !== 'undefined') {
    const referralCode = sessionStorage.getItem('referralCode');
    if (referralCode) {
      try {
        // Referans kodunu uygula
        await referrals.applyReferralCode(referralCode, data.id);
        
        // Referans kodunu sessionStorage'dan temizle
        sessionStorage.removeItem('referralCode');
        
        toast.success("Referans kodu başarıyla uygulandı!");
      } catch (error) {
        console.error("Referans kodu uygulanırken hata:", error);
      }
    }
  }

  // Deneme aboneliği oluştur
  try {
    // Solo plan ID'sini al
    const { data: soloPlans } = await supabase
      .from('subscription_plans')
      .select('id')
      .eq('name', 'Solo')
      .single();
    
    if (soloPlans) {
      await subscriptions.createTrialSubscription(data.id, soloPlans.id);
    }
  } catch (error) {
    console.error("Deneme aboneliği oluşturulurken hata:", error);
  }

  // Salon bilgilerini güncellemek için kullanıcı verilerini yenile
  await refreshUser();

  toast.success("Salon başarıyla oluşturuldu!");
}
```

### 2. Referans Faydalarını Uygulama Sürecini Güncelleme

`src/lib/db/referrals.ts` dosyasında `applyReferralCode` fonksiyonunu güncelleyerek:

1. Referans faydalarını doğru şekilde kaydet
2. Referans veren ve alan kullanıcılar için faydaları uygula
3. Deneme süresini uzat veya diğer faydaları sağla

### 3. Referans Sistemi Kullanıcı Yolculuğu

1. **Referans Veren Kullanıcı**:
   - Dashboard'da referans kodunu oluşturur
   - Kodu paylaşır
   - Kod kullanıldığında bildirim alır ve fayda kazanır

2. **Referans Alan Kullanıcı**:
   - Referans kodu ile kayıt olur
   - E-posta doğrulaması yapar
   - Giriş yapar ve salon oluşturur
   - Otomatik olarak deneme aboneliği ve referans faydaları uygulanır
   - Normal şekilde sistemi kullanabilir

## Teknik Uygulama Detayları

### 1. Referans Kodu Uygulaması

```typescript
export async function applyReferralCode(code: string, newSalonId: string) {
  // Referans kodunu bul
  const referralCode = await findReferralCode(code);
  if (!referralCode) throw new Error('Geçersiz referans kodu');

  // Referans veren salon ID'sini al
  const referrerId = referralCode.salon_id;

  // Kullanım sayısını artır
  await incrementReferralCodeUses(referralCode.id);

  // Referans faydası oluştur
  const benefit: ReferralBenefitInsert = {
    referrer_id: referrerId,
    referred_id: newSalonId,
    referral_code_id: referralCode.id,
    status: 'pending',
    benefit_amount: 750 // 1 aylık Solo plan bedeli
  };

  const { data, error } = await supabase
    .from('referral_benefits')
    .insert(benefit)
    .select()
    .single();

  if (error) throw error;
  
  // Referans faydalarını hemen uygula
  await applyReferralBenefit(data.id);
  
  return data as ReferralBenefit;
}
```

### 2. Deneme Aboneliği Oluşturma

```typescript
export async function createTrialSubscription(salonId: string, planId: string, isReferred: boolean = false) {
  const today = new Date();
  const trialEndDate = new Date();
  
  // Referans ile gelen kullanıcılar için 30 gün, diğerleri için 14 gün deneme süresi
  const trialDays = isReferred ? 30 : 14;
  trialEndDate.setDate(today.getDate() + trialDays);

  const subscription: SalonSubscriptionInsert = {
    salon_id: salonId,
    plan_id: planId,
    status: 'trial',
    start_date: today.toISOString().split('T')[0],
    trial_end_date: trialEndDate.toISOString().split('T')[0],
    payment_method: 'manual',
    is_yearly: false,
    is_active: true
  };

  return createSalonSubscription(subscription);
}
```

## Sonraki Adımlar

1. Yukarıda belirtilen değişiklikleri uygulamak
2. Referans sistemi için kapsamlı testler yapmak
3. Kullanıcı yolculuğunu belgelemek
4. Referans sistemi için kullanıcı dokümantasyonu oluşturmak
