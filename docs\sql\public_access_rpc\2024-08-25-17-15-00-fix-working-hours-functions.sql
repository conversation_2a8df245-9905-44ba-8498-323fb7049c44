-- SalonFlow Public Access Functions - Working Hours Fix
-- Oluşturulma Tarihi: 2024-08-25
-- Bu SQL dosyası, çalışma saatleri RPC fonksiyonlarını düzeltmek için oluşturulmuştur.

-- 1. Salon çalışma saatlerini getiren fonksiyonu düzelt
DROP FUNCTION IF EXISTS get_public_working_hours_by_salon_id(UUID);

CREATE OR REPLACE FUNCTION get_public_working_hours_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  day_of_week INTEGER,
  open_time TIME,
  close_time TIME,
  is_closed BOOLEAN
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT w.id, w.salon_id, w.day_of_week, w.open_time, w.close_time, w.is_closed
  FROM working_hours w
  WHERE w.salon_id = p_salon_id
  ORDER BY w.day_of_week;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_working_hours_by_salon_id(UUID) TO anon;

-- 2. Berber çalışma saatlerini getiren yeni fonksiyon ekle
CREATE OR REPLACE FUNCTION get_public_barber_working_hours_by_barber_id(p_barber_id UUID)
RETURNS TABLE (
  id UUID,
  barber_id UUID,
  day_of_week INTEGER,
  open_time TIME,
  close_time TIME,
  is_closed BOOLEAN,
  lunch_start_time TIME,
  lunch_end_time TIME,
  has_lunch_break BOOLEAN
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT bw.id, bw.barber_id, bw.day_of_week, bw.open_time, bw.close_time, bw.is_closed,
         bw.lunch_start_time, bw.lunch_end_time, bw.has_lunch_break
  FROM barber_working_hours bw
  WHERE bw.barber_id = p_barber_id
  ORDER BY bw.day_of_week;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_barber_working_hours_by_barber_id(UUID) TO anon;

-- 3. Salon ID'ye göre berber çalışma saatlerini getiren fonksiyon ekle
CREATE OR REPLACE FUNCTION get_public_barber_working_hours_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  barber_id UUID,
  salon_id UUID,
  day_of_week INTEGER,
  open_time TIME,
  close_time TIME,
  is_closed BOOLEAN,
  lunch_start_time TIME,
  lunch_end_time TIME,
  has_lunch_break BOOLEAN
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT bw.id, bw.barber_id, b.salon_id, bw.day_of_week, bw.open_time, bw.close_time, bw.is_closed,
         bw.lunch_start_time, bw.lunch_end_time, bw.has_lunch_break
  FROM barber_working_hours bw
  JOIN barbers b ON bw.barber_id = b.id
  WHERE b.salon_id = p_salon_id
  ORDER BY bw.barber_id, bw.day_of_week;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_barber_working_hours_by_salon_id(UUID) TO anon;
