"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"
import { Search, Filter, Edit, Eye, ArrowUpDown } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { useUser } from "@/contexts/UserContext"
import { getAllSalonSubscriptions } from "@/lib/db/admin"

export default function AdminSubscriptionsPage() {
  const router = useRouter()
  const [subscriptions, setSubscriptions] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [search, setSearch] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortField, setSortField] = useState("created_at")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")

  // UserContext'ten isAdminUser değişkenini al
  const { isAdminUser } = useUser()

  useEffect(() => {
    if (!isAdminUser) {
      router.push("/dashboard")
      toast.error("Bu sayfaya erişim yetkiniz yok.")
    }
  }, [isAdminUser, router])

  useEffect(() => {
    async function loadSubscriptions() {
      if (!isAdminUser) return

      try {
        setIsLoading(true)
        const { data, totalPages: pages } = await getAllSalonSubscriptions(page, 10, search, statusFilter)
        setSubscriptions(data)
        setTotalPages(pages)
      } catch (error) {
        console.error("Abonelikler yüklenirken hata:", error)
        toast.error("Abonelikler yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    // Arama için debounce uygula
    const timer = setTimeout(() => {
      loadSubscriptions()
    }, 300)

    return () => clearTimeout(timer)
  }, [isAdminUser, page, search, statusFilter])

  // Abonelik durumunu Türkçe olarak göster
  const getStatusText = (status: string) => {
    switch (status) {
      case 'trial': return 'Deneme Süresi'
      case 'active': return 'Aktif'
      case 'past_due': return 'Ödeme Gecikti'
      case 'canceled': return 'İptal Edildi'
      case 'suspended': return 'Askıya Alındı'
      default: return status
    }
  }

  // Abonelik durumuna göre renk belirle
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trial': return 'bg-blue-500'
      case 'active': return 'bg-green-500'
      case 'past_due': return 'bg-yellow-500'
      case 'canceled': return 'bg-red-500'
      case 'suspended': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  // Sıralama değiştir
  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // Sayfalama için sayfa numaralarını oluştur
  const renderPagination = () => {
    const pages = []
    const maxVisiblePages = 5
    let startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2))
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    // İlk sayfa
    if (startPage > 1) {
      pages.push(
        <Button
          key="first"
          variant="outline"
          size="icon"
          onClick={() => setPage(1)}
          className="h-8 w-8"
        >
          1
        </Button>
      )
      if (startPage > 2) {
        pages.push(<span key="ellipsis1" className="mx-1">...</span>)
      }
    }

    // Sayfa numaraları
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <Button
          key={i}
          variant={page === i ? "default" : "outline"}
          size="icon"
          onClick={() => setPage(i)}
          className="h-8 w-8"
        >
          {i}
        </Button>
      )
    }

    // Son sayfa
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(<span key="ellipsis2" className="mx-1">...</span>)
      }
      pages.push(
        <Button
          key="last"
          variant="outline"
          size="icon"
          onClick={() => setPage(totalPages)}
          className="h-8 w-8"
        >
          {totalPages}
        </Button>
      )
    }

    return (
      <div className="flex items-center justify-center space-x-2 mt-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setPage(Math.max(1, page - 1))}
          disabled={page === 1}
          className="h-8 w-8"
        >
          &lt;
        </Button>
        {pages}
        <Button
          variant="outline"
          size="icon"
          onClick={() => setPage(Math.min(totalPages, page + 1))}
          disabled={page === totalPages}
          className="h-8 w-8"
        >
          &gt;
        </Button>
      </div>
    )
  }

  if (!isAdminUser) {
    return null
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Abonelik Yönetimi</h1>
        </div>
      </header>

      {/* Arama ve filtreleme */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative max-w-xs">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Salon adı ara..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Durum filtrele" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tümü</SelectItem>
            <SelectItem value="trial">Deneme Süresi</SelectItem>
            <SelectItem value="active">Aktif</SelectItem>
            <SelectItem value="past_due">Ödeme Gecikti</SelectItem>
            <SelectItem value="canceled">İptal Edildi</SelectItem>
            <SelectItem value="suspended">Askıya Alındı</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Abonelik tablosu */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">
                <div className="flex items-center cursor-pointer" onClick={() => toggleSort("salons.name")}>
                  Salon Adı
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => toggleSort("plans.name")}>
                  Plan
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => toggleSort("status")}>
                  Durum
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => toggleSort("start_date")}>
                  Başlangıç
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center cursor-pointer" onClick={() => toggleSort("end_date")}>
                  Bitiş
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Loading state
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-6 w-full" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-full" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-6 w-20 ml-auto" /></TableCell>
                </TableRow>
              ))
            ) : subscriptions.length > 0 ? (
              subscriptions.map((subscription) => (
                <TableRow key={subscription.id}>
                  <TableCell className="font-medium">
                    {subscription.salons?.name || 'Bilinmeyen Salon'}
                  </TableCell>
                  <TableCell>
                    {subscription.plans?.name || 'Bilinmeyen Plan'}
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(subscription.status)}>
                      {getStatusText(subscription.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {subscription.start_date ?
                      format(new Date(subscription.start_date), 'd MMM yyyy', { locale: tr }) :
                      '-'}
                  </TableCell>
                  <TableCell>
                    {subscription.end_date ?
                      format(new Date(subscription.end_date), 'd MMM yyyy', { locale: tr }) :
                      subscription.trial_end_date ?
                        `Deneme: ${format(new Date(subscription.trial_end_date), 'd MMM yyyy', { locale: tr })}` :
                        '-'}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" size="icon" asChild>
                        <Link href={`/admin/subscriptions/${subscription.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" size="icon" asChild>
                        <Link href={`/admin/subscriptions/${subscription.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-4">
                  Abonelik bulunamadı.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Sayfalama */}
      {totalPages > 1 && renderPagination()}
    </div>
  )
}
