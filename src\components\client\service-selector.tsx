"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, <PERSON>, ArrowRight } from "lucide-react"
import { Service } from "@/lib/db/types"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface ServiceSelectorProps {
  services: Service[]
  selectedServiceId: string | undefined
  onSelect: (serviceId: string) => void
}

export function ServiceSelector({ services, selectedServiceId, onSelect }: ServiceSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("")

  // Filter services based on search query
  const filteredServices = services.filter(service =>
    service.name.toLowerCase().includes(searchQuery.toLowerCase())
  )



  return (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <div className="text-center space-y-3">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
        >
          <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Hizmet Seçin
          </h2>
        </motion.div>
        <p className="text-muted-foreground text-lg">
          Almak istediğiniz hizmeti seçin ve randevunuza başlayın
        </p>
      </div>

      <motion.div
        className="relative max-w-md mx-auto"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Input
          placeholder="Hizmet ara..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-12 h-12 text-base rounded-xl border-2 focus:border-primary/50 transition-all"
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 gap-4 max-h-[55vh] overflow-y-auto pr-3 pl-2 pt-1"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        {filteredServices.length > 0 ? (
          filteredServices.map((service, index) => (
            <motion.div
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div
                onClick={() => onSelect(service.id)}
                className={cn(
                  "border rounded-xl p-4 cursor-pointer transition-all",
                  selectedServiceId === service.id
                    ? "border-primary bg-primary/5 shadow-sm"
                    : "hover:border-primary/50 hover:bg-muted/50"
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={cn(
                      "p-2 rounded-lg",
                      selectedServiceId === service.id
                        ? "bg-primary/10 text-primary"
                        : "bg-muted text-muted-foreground"
                    )}>
                      <Scissors className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium">{service.name}</h3>
                      <div className="flex items-center mt-1 text-sm text-muted-foreground">
                        <Clock className="h-3.5 w-3.5 mr-1" />
                        <span>{service.duration} dakika</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    {selectedServiceId === service.id && (
                      <ArrowRight className="h-4 w-4 text-primary" />
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))
        ) : (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring" }}
            >
              <Scissors className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            </motion.div>
            <p className="text-muted-foreground text-lg">Aradığınız hizmet bulunamadı.</p>
            <p className="text-muted-foreground/70 text-sm mt-2">Farklı bir arama terimi deneyin.</p>
          </motion.div>
        )}
      </motion.div>

      {selectedServiceId && (
        <motion.div
          className="pt-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Button
            onClick={() => onSelect(selectedServiceId)}
            className="w-full h-12 text-base font-semibold bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-300"
            size="lg"
          >
            Devam Et
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </motion.div>
      )}
    </motion.div>
  )
}
