# Takvimde Çalışılmayan Günleri Devre Dışı Bırakma (İyileştirilmiş) - 2024-06-05

## Ya<PERSON><PERSON><PERSON>

`loadBarberWorkingHours` fonksiyonu gü<PERSON>erek, berberin çalışmadığı günleri belirlerken iki durumu da kontrol etmesi sağlandı:

1. `is_closed` değeri `true` olan günler
2. Veri tabanında o gün için hiç kayıt yoksa (yani o gün için çalışma saati tanımlanmamışsa)

## Teknik Detaylar

```typescript
// Load barber working hours and set disabled dates
const loadBarberWorkingHours = async (barberId: string) => {
  try {
    // Get barber working hours
    const { data, error } = await supabase
      .from('barber_working_hours')
      .select('day_of_week, is_closed')
      .eq('barber_id', barberId)
      .order('day_of_week')

    if (error) throw error

    // All days of the week (0-6)
    const allDaysOfWeek = [0, 1, 2, 3, 4, 5, 6]
    
    // Days that have records in the database
    const daysWithRecords = (data || []).map(day => day.day_of_week)
    
    // Days that don't have records in the database (considered closed)
    const daysWithoutRecords = allDaysOfWeek.filter(day => !daysWithRecords.includes(day))
    
    // Days that are explicitly marked as closed
    const explicitlyClosedDays = (data || [])
      .filter(day => day.is_closed)
      .map(day => day.day_of_week)
    
    // All closed days (either no record or explicitly marked as closed)
    const closedDays = [...new Set([...daysWithoutRecords, ...explicitlyClosedDays])]

    // Generate disabled dates for the next 3 months
    const disabledDatesArray: Date[] = []
    const today = new Date()
    const threeMonthsLater = new Date()
    threeMonthsLater.setMonth(today.getMonth() + 3)

    // Loop through each day in the next 3 months
    const currentDate = new Date(today)
    while (currentDate <= threeMonthsLater) {
      const dayOfWeek = currentDate.getDay()
      
      // If the barber doesn't work on this day of the week, add it to disabled dates
      if (closedDays.includes(dayOfWeek)) {
        // Create a new Date object to avoid reference issues
        disabledDatesArray.push(new Date(currentDate.getTime()))
      }
      
      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1)
    }

    setDisabledDates(disabledDatesArray)
  } catch (error) {
    console.error("Error loading barber working hours:", error)
    toast.error("Berber çalışma saatleri yüklenirken bir hata oluştu.")
  }
}
```

## Sonuç

Bu değişiklikle birlikte, müşteriler artık berberin çalışmadığı günleri (hem açıkça kapalı olarak işaretlenmiş günleri hem de çalışma saati tanımlanmamış günleri) takvimde seçemeyecekler. Bu, kullanıcı deneyimini iyileştirecek ve kullanıcıların müsait olmayan günleri seçmesini önleyecek.
