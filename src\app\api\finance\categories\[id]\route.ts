import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import * as db from '@/lib/db';

// Define the request schema for updating a category
const categoryUpdateSchema = z.object({
  name: z.string().min(1, 'Kategori adı gereklidir').optional(),
  type: z.enum(['income', 'expense']).optional(),
  description: z.string().optional(),
  color: z.string().optional(),
});

// GET /api/finance/categories/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the category
    const category = await db.financeCategories.getFinanceCategoryById(id);

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error fetching finance category:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/finance/categories/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Parse the request body
    const body = await request.json();
    const validation = categoryUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }

    const categoryData = validation.data;

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Update the category
    const category = await db.financeCategories.updateFinanceCategory({
      id,
      ...categoryData,
    });

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error updating finance category:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/finance/categories/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if the category is a system default
    const category = await db.financeCategories.getFinanceCategoryById(id);
    if (category.is_system_default) {
      return NextResponse.json(
        { error: 'Cannot delete a system default category' },
        { status: 400 }
      );
    }

    // Delete the category
    await db.financeCategories.deleteFinanceCategory(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting finance category:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
