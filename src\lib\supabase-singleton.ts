import { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseBrowser } from './supabase';

/**
 * Singleton class for Supabase client
 * This allows non-React files to access the same Supabase client instance
 * without having to use React hooks
 */
class SupabaseSingleton {
  private static instance: SupabaseSingleton;
  private client: SupabaseClient | null = null;

  private constructor() {
    // Private constructor to prevent direct instantiation
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): SupabaseSingleton {
    if (!SupabaseSingleton.instance) {
      SupabaseSingleton.instance = new SupabaseSingleton();
    }
    return SupabaseSingleton.instance;
  }

  /**
   * Get the Supabase client
   * This will create the client if it doesn't exist yet
   */
  public getClient(): SupabaseClient {
    if (!this.client) {
      this.client = getSupabaseBrowser();
    }
    return this.client;
  }

  /**
   * Set the Supabase client
   * This is useful for testing or when the client is created elsewhere
   */
  public setClient(client: SupabaseClient): void {
    this.client = client;
  }
}

// Export a singleton instance
const supabaseInstance = SupabaseSingleton.getInstance();

// Export a function to get the client
export function getSupabaseClient(): SupabaseClient {
  return supabaseInstance.getClient();
}

// Export the client directly for convenience
export const supabaseClient = getSupabaseClient();
