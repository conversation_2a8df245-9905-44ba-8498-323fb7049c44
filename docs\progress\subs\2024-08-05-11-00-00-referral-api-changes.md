# Referans Sistemi API Değişiklikleri

Bu belge, referans sistemi değişikliği için yapılması gereken API değişikliklerini içermektedir.

## 1. `src/lib/db/types.ts` Dosyası Değişiklikleri

```typescript
// Referral benefit type
export interface ReferralBenefit {
  id: string;
  referrer_salon_id: string;
  referred_salon_id: string;
  referral_code_id: string;
  benefit_type: string; // 'referred_discount' olarak güncellenecek
  benefit_value: string;
  discount_amount?: number; // Yeni alan
  discount_applied?: boolean; // Yeni alan
  discount_applied_payment_id?: string; // Yeni alan
  is_applied: boolean;
  applied_date?: string;
  created_at: string;
  updated_at: string;
}

export type ReferralBenefitInsert = Omit<ReferralBenefit, 'id' | 'created_at' | 'updated_at'>;
export type ReferralBenefitUpdate = Partial<Omit<ReferralBenefit, 'id' | 'created_at' | 'updated_at'>> & { id: string };
```

## 2. `src/lib/db/referrals.ts` Dosyası Değişiklikleri

### 2.1. `applyReferralCode` Fonksiyonu Güncelleme

```typescript
/**
 * Apply a referral code during registration
 */
export async function applyReferralCode(code: string, newSalonId: string) {
  // Referans kodunu bul
  const referralCode = await findReferralCode(code);
  if (!referralCode) throw new Error('Geçersiz referans kodu');

  // Referans veren salon ID'sini al
  const referrerSalonId = referralCode.salon_id;

  // Kullanım sayısını artır
  await incrementReferralCodeUses(referralCode.id);

  // En düşük plan ücretini al
  const { data: plans, error: plansError } = await supabase
    .from('subscription_plans')
    .select('price_monthly')
    .order('price_monthly', { ascending: true })
    .limit(1);

  if (plansError) throw plansError;

  const lowestPlanPrice = plans[0]?.price_monthly || 750; // Varsayılan olarak 750 TL

  // Referans faydası oluştur - Artık referans veren değil, alan kişi fayda kazanıyor
  const benefit: ReferralBenefitInsert = {
    referrer_salon_id: referrerSalonId,
    referred_salon_id: newSalonId,
    referral_code_id: referralCode.id,
    benefit_type: 'referred_discount', // Yeni fayda tipi
    benefit_value: lowestPlanPrice.toString(), // Güncel en düşük plan ücreti
    discount_amount: lowestPlanPrice, // İndirim tutarı
    discount_applied: false,
    is_applied: false
  };

  const { data, error } = await supabase
    .from('referral_benefits')
    .insert(benefit)
    .select()
    .single();

  if (error) throw error;

  console.log(`Referral code ${code} applied for salon ${newSalonId}, benefit ID: ${data.id}, discount amount: ${lowestPlanPrice}`);

  return data as ReferralBenefit;
}
```

### 2.2. Yeni Fonksiyon: `checkReferralDiscount`

```typescript
/**
 * Check if a salon has an unused referral discount
 */
export async function checkReferralDiscount(salonId: string) {
  const { data, error } = await supabase
    .from('referral_benefits')
    .select('*')
    .eq('referred_salon_id', salonId)
    .eq('benefit_type', 'referred_discount')
    .eq('discount_applied', false)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as ReferralBenefit | null;
}
```

### 2.3. Yeni Fonksiyon: `applyReferralDiscount`

```typescript
/**
 * Apply a referral discount to a payment
 */
export async function applyReferralDiscount(benefitId: string, paymentId: string) {
  const { data, error } = await supabase
    .from('referral_benefits')
    .update({
      discount_applied: true,
      discount_applied_payment_id: paymentId,
      is_applied: true,
      applied_date: new Date().toISOString().split('T')[0]
    })
    .eq('id', benefitId)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralBenefit;
}
```

## 3. Referans Sistemi Frontend Değişiklikleri

### 3.1. `src/app/dashboard/referrals/page.tsx` Dosyası Değişiklikleri

```tsx
<Card>
  <CardHeader>
    <CardTitle>Referans Sistemi Nasıl Çalışır?</CardTitle>
  </CardHeader>
  <CardContent>
    <div className="space-y-4">
      <div className="flex items-start gap-4">
        <div className="bg-primary/10 p-2 rounded-full">
          <Share2 className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="font-medium">1. Referans Kodunuzu Paylaşın</h3>
          <p className="text-muted-foreground text-sm">
            Referans kodunuzu veya özel bağlantınızı arkadaşlarınızla paylaşın.
          </p>
        </div>
      </div>

      <div className="flex items-start gap-4">
        <div className="bg-primary/10 p-2 rounded-full">
          <UserPlus className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="font-medium">2. Arkadaşınız Kaydolsun</h3>
          <p className="text-muted-foreground text-sm">
            Arkadaşınız referans kodunuzu kullanarak SalonFlow'a kaydolsun.
          </p>
        </div>
      </div>

      <div className="flex items-start gap-4">
        <div className="bg-primary/10 p-2 rounded-full">
          <Gift className="h-5 w-5 text-primary" />
        </div>
        <div>
          <h3 className="font-medium">3. Arkadaşınız İndirim Kazansın</h3>
          <p className="text-muted-foreground text-sm">
            Referans kodunuzu kullanan arkadaşınız, ilk abonelik ödemesinde en düşük paket ücreti kadar indirim kazanır.
          </p>
        </div>
      </div>
    </div>
  </CardContent>
</Card>
```
