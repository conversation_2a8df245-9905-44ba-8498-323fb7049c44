import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Header } from "@/components/header"

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <Header />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-r from-primary/10 to-secondary/10">
          <div className="container mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Salon Randevularınızı Kolaylaştırın
            </h1>
            <p className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto">
              Kuaförler ve salonlar için hepsi bir arada randevu yönetim sistemi.
              <PERSON><PERSON>u kaçırma oranını azaltın, personeli yönetin ve müşterilerinizi memnun edin.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth/register">
                <Button size="lg" className="text-lg px-8">He<PERSON></Button>
              </Link>
              <Link href="#demo">
                <Button size="lg" variant="outline" className="text-lg px-8">Demo Görün</Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20">
          <div className="container mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-16">
              Salon Başarısı İçin Tasarlanmış Özellikler
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
              <div className="flex flex-col items-center text-center">
                <div className="bg-primary/10 p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-8 w-8"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Online Randevu</h3>
                <p className="text-muted-foreground">
                  Müşterilerin 7/24 herhangi bir cihazdan randevu almalarını sağlayarak, telefon görüşmelerini ve kaçırılan fırsatları azaltın.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-primary/10 p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-8 w-8"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Personel Yönetimi</h3>
                <p className="text-muted-foreground">
                  Birden fazla berberi, programlarını ve hizmet uzmanlıklarını tek bir yerden kolayca yönetin.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-primary/10 p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-8 w-8"><path d="M22 10v6M2 10l10-5 10 5-10 5z"></path><path d="M6 12v5c3 3 9 3 12 0v-5"></path></svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Otomatik Hatırlatmalar</h3>
                <p className="text-muted-foreground">
                  Randevulardan önce müşterilere gönderilen otomatik e-posta ve SMS hatırlatmaları ile randevu kaçırma oranını azaltın.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-primary/10 p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-8 w-8"><path d="M3 3v18h18"></path><path d="m19 9-5 5-4-4-3 3"></path></svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Analitik ve Raporlama</h3>
                <p className="text-muted-foreground">
                  Randevular, hizmetler ve personel performansı hakkında detaylı raporlarla işletmeniz hakkında içgörüler elde edin.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-primary/10 p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-8 w-8"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Özelleştirilebilir Ayarlar</h3>
                <p className="text-muted-foreground">
                  Özelleştirilebilir çalışma saatleri, hizmetler ve daha fazlasıyla sistemi salonunuzun ihtiyaçlarına göre uyarlayın.
                </p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="bg-primary/10 p-4 rounded-full mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-8 w-8"><rect width="20" height="14" x="2" y="5" rx="2"></rect><line x1="2" x2="22" y1="10" y2="10"></line></svg>
                </div>
                <h3 className="text-xl font-semibold mb-2">Mobil Uyumlu</h3>
                <p className="text-muted-foreground">
                  İster salonda olun ister hareket halinde, salon panelinize herhangi bir cihazdan erişin.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20 bg-muted/50">
          <div className="container mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-16">
              Basit, Şeffaf Fiyatlandırma
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {/* Basic Plan */}
              <div className="bg-card rounded-lg shadow-lg overflow-hidden">
                <div className="p-6 border-b">
                  <h3 className="text-2xl font-bold mb-2">Temel</h3>
                  <p className="text-muted-foreground mb-4">Tek berberler için mükemmel</p>
                  <div className="text-3xl font-bold">₺499<span className="text-lg font-normal text-muted-foreground">/ay</span></div>
                </div>
                <div className="p-6 space-y-4">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      1 Personel
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Sınırsız Randevu
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      E-posta Hatırlatmaları
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Temel Analitik
                    </li>
                  </ul>
                  <Button className="w-full">Hemen Başlayın</Button>
                </div>
              </div>

              {/* Standard Plan */}
              <div className="bg-card rounded-lg shadow-lg overflow-hidden border-2 border-primary transform scale-105">
                <div className="p-6 border-b bg-primary text-primary-foreground">
                  <h3 className="text-2xl font-bold mb-2">Standart</h3>
                  <p className="text-primary-foreground/80 mb-4">Büyüyen salonlar için</p>
                  <div className="text-3xl font-bold">₺999<span className="text-lg font-normal text-primary-foreground/80">/ay</span></div>
                </div>
                <div className="p-6 space-y-4">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      5 Personele Kadar
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Sınırsız Randevu
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      E-posta ve SMS Hatırlatmaları
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Gelişmiş Analitik
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Müşteri Yönetimi
                    </li>
                  </ul>
                  <Button className="w-full">Hemen Başlayın</Button>
                </div>
              </div>

              {/* Premium Plan */}
              <div className="bg-card rounded-lg shadow-lg overflow-hidden">
                <div className="p-6 border-b">
                  <h3 className="text-2xl font-bold mb-2">Premium</h3>
                  <p className="text-muted-foreground mb-4">Kurumsal salonlar için</p>
                  <div className="text-3xl font-bold">₺1999<span className="text-lg font-normal text-muted-foreground">/ay</span></div>
                </div>
                <div className="p-6 space-y-4">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Sınırsız Personel
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Sınırsız Randevu
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      E-posta, SMS ve WhatsApp
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Premium Analitik
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary h-5 w-5 mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg>
                      Öncelikli Destek
                    </li>
                  </ul>
                  <Button className="w-full">Hemen Başlayın</Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-primary text-primary-foreground">
          <div className="container mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Salonunuzu Dönüştürmeye Hazır mısınız?
            </h2>
            <p className="text-xl mb-10 max-w-2xl mx-auto">
              Randevu süreçlerini kolaylaştıran ve işlerini SalonFlow ile büyüten binlerce salon sahibine katılın.
            </p>
            <Button size="lg" variant="secondary" className="text-lg px-8">
              Ücretsiz Deneme Başlatın
            </Button>
          </div>
        </section>
      </main>

      <footer className="bg-muted py-12">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-lg font-bold mb-4">SalonFlow</h3>
              <p className="text-muted-foreground">
                Kuaförler ve salonlar için hepsi bir arada randevu yönetim sistemi.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">Ürün</h3>
              <ul className="space-y-2">
                <li><Link href="#features" className="text-muted-foreground hover:text-foreground">Özellikler</Link></li>
                <li><Link href="#pricing" className="text-muted-foreground hover:text-foreground">Fiyatlandırma</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">Müşteri Yorumları</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">SSS</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">Şirket</h3>
              <ul className="space-y-2">
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">Hakkımızda</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">Blog</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">Kariyer</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">İletişim</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold mb-4">Yasal</h3>
              <ul className="space-y-2">
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">Gizlilik Politikası</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">Kullanım Koşulları</Link></li>
                <li><Link href="#" className="text-muted-foreground hover:text-foreground">Çerez Politikası</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 text-center text-muted-foreground">
            <p>&copy; {new Date().getFullYear()} SalonFlow. Tüm hakları saklıdır.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
