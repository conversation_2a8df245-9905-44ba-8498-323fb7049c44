# SalonFlow - Development Tasks from PRD v1.0

This document outlines the development tasks for the SalonFlow (Kuafor Randevu System) project, derived from `PRD.md v1.0`. Tasks are organized by implementation phase. Each task includes references to relevant User Stories (US-Xxx), Functional Requirements (FRxx.x), Technical Requirements (TRxx), and Design Requirements (DRxx) from the PRD.

## Phase 0: Setup & Core (Estimate: 4-6 Weeks)

### 0.1. Project Initialization & Configuration
-   **Task:** Setup Next.js 15 project. (TR01)
-   **Task:** Integrate Tailwind CSS for styling. (TR01)
-   **Task:** Setup Shadcn UI component library. (TR01)
-   **Task:** Initialize Supabase project (Database, Auth, Realtime, Storage). (TR02)
-   **Task:** Configure version control using Git and set up a repository (e.g., GitHub, GitLab). (PRD 4.3)
-   **Task:** Choose and set up a project management tool (e.g., <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Asana). (PRD 4.3)

### 0.2. Database Schema & Initial Setup
-   **Task:** Design the initial database schema in Supabase. (TR06)
    -   **Sub-task:** Create `salons` table (store salon-specific information, owner ID).
    -   **Sub-task:** Create `users` table (Supabase Auth users, link to salon owners/staff).
    -   **Sub-task:** Create `services` table (name, duration, salon_id).
    -   **Sub-task:** Create `appointments` table (customer_id, service_id, staff_id (nullable initially), salon_id, appointment_time, status).
    -   **Sub-task:** Create `customers` table (name, surname, phone, email, salon_id).
    -   **Sub-task:** Create `salon_settings` table (salon_id, working_hours, holidays, reminder_config).
    -   **Sub-task:** Create `staff_invitations` table (email, salon_id, token, status, role).
-   **Task:** Implement the designed schema in Supabase, including relationships and constraints. (TR06)

### 0.3. Core Authentication & Authorization
-   **Task:** Implement user authentication for Salon Owners using Supabase Auth (email/password login, logout). (FR02.1, US-B02, TR05)
-   **Task:** Implement user registration for Salon Owners. (FR02.1, US-B02)
-   **Task:** Set up basic Role-Based Access Control (RBAC) structure for "Salon Owner" role. (FR02.2, TR09)

### 0.4. Salon & Service Management (Core)
-   **Task:** Implement CRUD operations for Services (name, duration) manageable by authenticated Salon Owner. (FR02.5, US-B04)
-   **Task:** Implement UI for Salon Owners to manage services.
-   **Task:** Implement functionality for Salon Owners to define basic salon settings: general salon working hours and barber working hours, and holidays. (FR02.7, US-B03)
-   **Task:** Implement UI for Salon Owners to manage salon settings.

### 0.5. Initial UI/UX & Design System
-   **Task:** Set up basic layout structure for the Admin Dashboard using Next.js and Shadcn UI. (DR01)
-   **Task:** Define initial color palette and typography based on Shadcn UI defaults, keeping DR03 (Branding) in mind for future updates.

## Phase 1: MVP - Booking & Core Barber/Salon Features (Estimate: 8-12 Weeks)

### 1.1. Customer Booking Portal
-   **Task:** Develop the public-facing dynamic landing page for each salon (e.g., `salonflow.app/[salonId]` or `[salonId].salonflow.app`). (FR01.1, US-B01)
-   **Task:** Display salon information (name, fetched from DB) on the booking portal. (FR01.2)
-   **Task:** Display list of services (name, duration) offered by the salon. (FR01.2, US-C06)
-   **Task:** Implement logic to display salon availability based on salon working hours and existing appointments. (FR01.2, US-C01)
-   **Task:** Develop UI for customers to select service(s). (FR01.3, US-C01)
-   **Task:** Develop UI for customers to select an available date and time slot. (FR01.3, US-C01)
-   **Task:** Implement a simple booking form: Name, Surname, Phone, Email. (FR01.4, US-C02)
-   **Task:** Implement booking submission logic:
    -   **Sub-task:** Create/update customer record in `customers` table.
    -   **Sub-task:** Create appointment record in `appointments` table.
-   **Task:** Implement automated booking confirmation email to the customer. (FR01.5, US-C03, TR12)
    -   **Sub-task:** Integrate an email service provider (e.g., Resend, SendGrid, Supabase SMTP). (TR12)
-   **Task:** Include a unique cancellation link in the booking confirmation email. (FR01.6)

### 1.2. Salon Admin Dashboard: Calendar & Appointments
-   **Task:** Develop Calendar View:
    -   **Sub-task:** Implement daily, weekly, and monthly calendar views for appointments. (FR02.3, US-B05, US-S03)
    -   **Sub-task:** Display appointment details on the calendar (customer name, service, time). (FR02.3)
    -   **Sub-task:** Implement filtering for the calendar view by barber. (US-B15)
-   **Task:** Implement real-time calendar updates using Supabase Realtime when new appointments are booked or existing ones are cancelled. (FR02.3, TR04, US-B09, US-S08)
-   **Task:** Manual Appointment Creation by Salon User:
    -   **Sub-task:** Allow salon users to click on calendar slots to initiate new appointment creation modal. (FR02.3)
    -   **Sub-task:** Implement pre-selection logic in the appointment creation modal based on the clicked slot and current calendar filters:
        -   If a barber is selected in the calendar filter, pre-select that barber in the modal. Otherwise, pre-select the first available barber.
        -   Pre-select the first available service type.
        -   Pre-select the date of the clicked slot.
        -   Pre-select the time of the clicked slot.
    -   **Sub-task:** Develop appointment creation form (select service, date, time). (FR02.4)
    -   **Sub-task:** Implement customer search (by name/phone) for existing customers or add new customer details (name, surname, phone, email). (FR02.4, US-B06, US-B07)
    -   **Sub-task:** Logic to save manually created appointments.

### 1.3. Salon Admin Dashboard: Notifications
-   **Task:** Implement real-time on-screen (dashboard) notifications for new bookings. (FR02.9, US-B08, US-S07)
-   **Task:** Implement real-time on-screen (dashboard) notifications for cancellations. (FR02.9, US-B10)
-   **Task:** Ensure real-time notifications and screen updates work correctly across all devices and browsers. (FR02.9, TR04)
    -   **Sub-task:** Configure Supabase real-time features for the appointments table.
    -   **Sub-task:** Implement proper channel management for real-time subscriptions.
    -   **Sub-task:** Test real-time updates with multiple concurrent users.

### 1.4. Staff Dashboard (Initial Features for Staff Role)
-   **Task:** Allow users with "Staff" role to log in and access a dashboard. (FR03.1, US-S02)
-   **Task:** Staff can view all salon appointments in the calendar (daily, weekly, monthly). (FR03.2, US-S03)
-   **Task:** Staff can create new appointments (similar to FR02.4). (FR03.2, US-S04)
-   **Task:** Staff can update and cancel their *own* appointments (requires linking appointments to staff). (FR03.2, US-S04) - *If not feasible for MVP, staff might manage all appointments initially.*
-   **Task:** Staff can manage their own working hours and days off (distinct from general salon hours). (FR03.3, US-S05)
    -   **Sub-task:** Create `staff_working_hours` table if not covered by a flexible `salon_settings` or `user_settings`.
-   **Task:** Staff can view salon customers and their contact information. (FR03.4, US-S06)

### 1.5. General System Features (MVP)
-   **Task:** Implement Customer Cancellation Workflow:
    -   **Sub-task:** Develop endpoint/page for the cancellation link from email. (FR04.2)
    -   **Sub-task:** Logic to validate cancellation and update appointment status to 'cancelled'. (FR04.2, US-C05)
    -   **Sub-task:** Ensure appointment is removed/marked on the calendar. (FR04.2)
    -   **Sub-task:** Notify salon (and staff if applicable) of the cancellation. (FR04.2)
-   **Task:** Implement Mobile-Responsive Design for customer portal and admin/staff dashboards. (FR04.4, US-B12, US-S09, DR02)
-   **Task:** Implement Dark/Light Mode toggle for admin/staff dashboards. (FR04.4, US-B14, US-S10, DR07)
-   **Task:** Implement basic Role-Based UI (show/hide options based on Salon Owner vs Staff roles). (FR04.4, FR02.2)
-   **Task:** Implement Row-Level Security (RLS) policies in Supabase to ensure users can only access data relevant to their salon and role. (TR09)
-   **Task:** Ensure basic protection against common web vulnerabilities (XSS, CSRF) by leveraging Next.js and Supabase features. (TR09)
-   **Task:** Ensure all critical data (bookings, customer details, salon configurations) is persistently saved. (FR04.6)

## Phase 2: Enhancements & Tiered Features (Estimate: 6-10 Weeks)

### 2.1. Staff Management (for Multi-Staff Salons)
-   **Task:** Salon Owner can add/edit/deactivate staff profiles. (FR02.6, US-B15)
-   **Task:** Allow assigning services to specific staff members (if services can be staff-specific). (FR02.6)
-   **Task:** Salon Owner can set individual working hours/days off for each staff member (overriding general salon hours or staff's own settings). (FR02.6)
-   **Task:** Implement Staff Invitation Workflow:
    -   **Sub-task:** Salon Owner enters staff email and triggers an invitation. (FR04.3, US-B17)
    -   **Sub-task:** System generates a unique token and sends an invitation email with a registration link (using Supabase Edge Function). (FR04.3)
    -   **Sub-task:** Develop staff registration page from invitation link (set password, complete profile). (FR04.3, US-S01)
    -   **Sub-task:** Link new staff user account to the salon and the staff profile. (FR04.3)
    -   **Sub-task:** Track invitation status (pending, accepted, expired) in `staff_invitations` table. (FR02.6)
-   **Task:** Refine RBAC: Salon Owner manages staff access and permissions within their salon. (FR02.2, US-B18)

### 2.2. Customer Management (Enhanced)
-   **Task:** Salon Owner/Staff can view a list of all salon customers. (FR02.8)
-   **Task:** Salon Owner/Staff can view individual customer appointment history. (FR02.8, US-B16)
-   **Task:** UI for adding notes to customer profiles. (FR02.8)
-   **Task:** Implement customer statistics and insights. (FR02.8, FR02.10)
    -   **Sub-task:** Display total number of appointments for each customer.
    -   **Sub-task:** Calculate and display customer loyalty metrics (frequency of visits).
    -   **Sub-task:** Show preferred services and barbers for each customer.
    -   **Sub-task:** Implement visual charts for customer appointment patterns over time.

### 2.3. Reminders System
-   **Task:** Implement automated appointment reminder emails for customers. (FR04.1, US-C04)
-   **Task:** Salon Owner can configure reminder settings (e.g., timing: 24 hours before, 2 hours before). (FR02.7, US-B11)
-   **Task:** Design database schema and UI placeholders for future SMS/WhatsApp reminder channel selection. (FR02.7, TR13)

### 2.3.1. Telegram Notification System
-   **Task:** Implement Telegram notification system for appointment events. (FR02.9, US-B08, US-B10)
    -   **Sub-task:** Create `salon_telegram_settings` database table with encrypted channel IDs and RLS policies.
    -   **Sub-task:** Implement encryption utilities for secure storage of Telegram channel IDs.
    -   **Sub-task:** Create Supabase Edge Function for Telegram Bot API integration.
    -   **Sub-task:** Implement message formatting for Turkish language notifications.
    -   **Sub-task:** Create Next.js API routes for Telegram settings management (CRUD operations).
    -   **Sub-task:** Integrate with existing appointment triggers for real-time notifications.
    -   **Sub-task:** Develop frontend dashboard components for Telegram configuration.
    -   **Sub-task:** Add Telegram settings page to admin dashboard with Turkish UI.
    -   **Sub-task:** Implement test notification functionality.
    -   **Sub-task:** Add proper error handling, retry logic, and rate limiting.
    -   **Sub-task:** Ensure multi-tenant security with salon_id isolation and is_admin() function usage.

### 2.4. Analytics & Reporting (Basic)
-   **Task:** Develop dashboard section for basic analytics. (FR02.10, US-B13)
-   **Task:** Report: Number of services rendered (count, popularity by service type). (FR02.10)
-   **Task:** Report: Placeholder for revenue reports (structure for future price implementation). (FR02.10)
-   **Task:** Report: Performance per staff member (e.g., number of appointments handled). (FR02.10)

### 2.5. Pricing Tiers & Subscription Logic
-   **Task:** Define logic for three pricing tiers based on the number of staff members (e.g., Solo, Small Team, Pro Salon). (FR04.5)
-   **Task:** Implement feature flagging/access control based on a (mock/manually set) subscription tier. (FR04.5)
    -   **Example:** Staff management features (Section 2.1) might only be available for "Small Team" tier and above.
    -   **Example:** Limit number of staff members based on tier.

### 2.6. UI/UX & Accessibility
-   **Task:** Refine UI/UX across the application based on internal reviews and potential early user feedback. (DR01)
-   **Task:** Ensure clear visual hierarchy and consistent user feedback mechanisms (loading states, success/error messages). (DR05, DR06)
-   **Task:** Conduct an accessibility review and make improvements to align with WCAG 2.1 Level AA where feasible. (DR04)

## Phase 2.7: Financial Management Module (Estimate: TBD)

-   **Task:** Design and implement `finance_categories` database table. (TRxx)
    -   **Sub-task:** Create SQL script for `finance_categories` table.
    -   **Sub-task:** Implement RLS for `finance_categories`.
-   **Task:** Design and implement `finance_transactions` database table. (TRxx)
    -   **Sub-task:** Create SQL script for `finance_transactions` table.
    -   **Sub-task:** Implement RLS for `finance_transactions`.
-   **Task:** Implement API endpoints for `finance_categories` (CRUD). (TRxx)
    -   **Sub-task:** Create API endpoint for `GET /api/finance/categories`.
    -   **Sub-task:** Create API endpoint for `POST /api/finance/categories`.
    -   **Sub-task:** Create API endpoint for `PUT /api/finance/categories/[id]`.
    -   **Sub-task:** Create API endpoint for `DELETE /api/finance/categories/[id]`.
-   **Task:** Implement API endpoints for `finance_transactions` (CRUD and filtering). (TRxx)
    -   **Sub-task:** Create API endpoint for `GET /api/finance/transactions`.
    -   **Sub-task:** Create API endpoint for `POST /api/finance/transactions`.
    -   **Sub-task:** Create API endpoint for `PUT /api/finance/transactions/[id]`.
    -   **Sub-task:** Create API endpoint for `DELETE /api/finance/transactions/[id]`.
-   **Task:** Implement API endpoints for financial reports. (TRxx)
    -   **Sub-task:** Create API endpoint for `GET /api/finance/reports/summary`.
    -   **Sub-task:** Create API endpoint for `GET /api/finance/reports/category-breakdown`.
-   **Task:** Implement automatic income recording from completed appointments (Edge Function or Trigger). (TRxx)
-   **Task:** Create Finance Dashboard page (`/dashboard/finance`). (FRxx.x, US-Bxx)
    -   **Sub-task:** Display summary financial data.
    -   **Sub-task:** Integrate charting library and display charts.
-   **Task:** Create Categories Management page/components (`/dashboard/finance/categories`). (FRxx.x, US-Bxx)
    -   **Sub-task:** Implement table for categories.
    -   **Sub-task:** Implement form for adding/editing categories.
-   **Task:** Create Transactions Management page/components (`/dashboard/finance/transactions`). (FRxx.x, US-Bxx)
    -   **Sub-task:** Implement table for transactions with filtering.
    -   **Sub-task:** Implement form for adding/editing transactions.
    -   **Sub-task:** Add option to link transactions to appointments.
-   **Task:** Ensure RLS policies are correctly applied and tested for all financial data access. (TRxx)

## Phase 3: Polish & Go-to-Market Prep (Estimate: 4-6 Weeks)

### 3.1. Optional Integrations (Prioritize based on feedback/strategy)
-   **Task:** If prioritized, integrate SMS/WhatsApp for reminders using a third-party provider (e.g., Twilio, Vonage). (FR04.1, US-C03, US-C04, TR13)
    -   **Sub-task:** Develop Supabase Edge Function for sending SMS/WhatsApp.
    -   **Sub-task:** Update salon settings UI to configure and enable SMS/WhatsApp.
    -   **Sub-task:** Manage costs and API keys securely.

### 3.2. SaaS Product Marketing & Sales Infrastructure
-   **Task:** Develop a public-facing marketing website for the SalonFlow SaaS product itself (features, pricing, sign-up). (PRD 4.2)

### 3.3. Subscription Management & Payments
-   **Task:** Integrate a payment gateway (e.g., Stripe) for subscription billing. (FR04.5, PRD 4.1)
    -   **Sub-task:** Handle subscription creation, upgrades, downgrades, cancellations.
    -   **Sub-task:** Securely manage payment details (via provider).
-   **Task:** Develop UI for Salon Owners to manage their subscription. (FR04.5)

### 3.4. Advanced Features (Placeholders for future development)
-   **Task:** (Future) Analytics: No-show rates, peak booking times. (FR02.10)
-   **Task:** (Future) Customer Management: Add notes to customer profiles. (FR02.8)
-   **Task:** (Future) Service Management: Define service price and link to revenue reports. (FR02.5)

### 3.5. Final Testing, Optimization & Deployment
-   **Task:** Conduct thorough end-to-end testing across all user flows and roles.
-   **Task:** Address all identified bugs and issues.
-   **Task:** Performance optimization:
    -   **Sub-task:** Optimize database queries (review expensive queries). (TR10)
    -   **Sub-task:** Optimize frontend load times (code splitting, image optimization). (TR10)
-   **Task:** Security Hardening:
    -   **Sub-task:** Final review of all security measures: HTTPS, XSS, CSRF, SQLi prevention, RLS, RBAC. (TR09)
    -   **Sub-task:** Implement rate limiting for API endpoints, especially for anonymous access (booking form). (TR09)
    -   **Sub-task:** Add CAPTCHA or similar protection to the public booking form to prevent abuse. (TR09)
    -   **Sub-task:** Configure monitoring and logging for suspicious activities, especially for anonymous endpoints. (TR09)
    -   **Sub-task:** Review and optimize real-time subscription configurations for security and performance. (TR04, TR09)
    -   **Sub-task:** Implement fallback mechanisms for critical features in case real-time updates fail. (TR04)
    -   **Sub-task:** Conduct security audit/penetration testing (if budget allows).
-   **Task:** Finalize deployment configuration on Vercel. (TR08)
-   **Task:** Set up production monitoring, logging, and alerting.
-   **Task:** Prepare user documentation / knowledge base / FAQ for salon owners and staff.
-   **Task:** Define branding elements (Logo, Color Scheme, Typography) if not already done. (DR03)

This list should serve as a good starting point for managing the development of SalonFlow. Remember that these tasks can be further broken down into smaller sub-tasks as development progresses.
