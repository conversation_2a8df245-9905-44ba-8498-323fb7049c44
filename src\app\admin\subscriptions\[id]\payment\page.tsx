"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { format, addMonths, differenceInDays } from "date-fns";
import { tr } from "date-fns/locale";
import { toast } from "sonner";
import {
  ArrowLeft,
  Save,
  Calculator,
  Calendar,
  CreditCard,
  Info,
  CheckCircle,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { getSalonSubscriptionById } from "@/lib/db/admin";
import { subscriptionPayments } from "@/lib/db";
import { formatCurrency } from "@/lib/utils";
import { useUser } from "@/contexts/UserContext";

export default function AddPaymentPage({ params }: { params: { id: string } }) {
  const id = params.id as string

  const router = useRouter();
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [periodStartDate, setPeriodStartDate] = useState("");
  const [periodEndDate, setPeriodEndDate] = useState("");
  const [paymentDate, setPaymentDate] = useState(
    format(new Date(), "yyyy-MM-dd")
  );
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [originalAmount, setOriginalAmount] = useState(0);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [calculatedAmount, setCalculatedAmount] = useState(0);
  const [isCalculating, setIsCalculating] = useState(false);
  const [discountInfo, setDiscountInfo] = useState<any>(null);
  const [isLoadingNextPeriod, setIsLoadingNextPeriod] = useState(false);

  const { isAdminUser } = useUser();

  useEffect(() => {
    if (!isAdminUser) {
      router.push("/dashboard");
      toast.error("Bu sayfaya erişim yetkiniz yok.");
    }
  }, [router, isAdminUser]);

  // Ödeme tutarını hesapla
  const calculateAmount = async () => {
    if (!subscription || !periodStartDate || !periodEndDate) return;

    try {
      setIsCalculating(true);

      // Ödeme tutarını hesapla
      const result = await subscriptionPayments.calculatePaymentAmount(
        id,
        periodStartDate,
        periodEndDate
      );

      setOriginalAmount(result.originalAmount);

      // İndirimleri kontrol et
      const discounts = await subscriptionPayments.checkAvailableDiscounts(
        subscription.salon_id
      );
      setDiscountInfo(discounts);

      if (discounts.hasDiscount) {
        setDiscountAmount(discounts.discountAmount);
        // İndirim tutarı, orijinal tutardan büyükse, ödeme tutarı 0 olur
        const finalAmount = Math.max(
          0,
          result.originalAmount - discounts.discountAmount
        );
        setCalculatedAmount(finalAmount);
      } else {
        setDiscountAmount(0);
        setCalculatedAmount(result.originalAmount);
      }
    } catch (error) {
      console.error("Ödeme tutarı hesaplanırken hata:", error);
      toast.error("Ödeme tutarı hesaplanırken bir hata oluştu.");
    } finally {
      setIsCalculating(false);
    }
  };

  // Tarih değişikliklerinde tutarı otomatik hesapla
  useEffect(() => {
    if (subscription) {
      calculateAmount();
    }
  }, [periodStartDate, periodEndDate, subscription?.id]);

  // Bir sonraki ödeme dönemini yükle
  const loadNextPaymentPeriod = async () => {
    if (!id) return;

    try {
      setIsLoadingNextPeriod(true);

      // Bir sonraki ödeme dönemini hesapla
      const nextPeriod = await subscriptionPayments.getNextPaymentPeriod(id);

      if (nextPeriod.nextPeriodStartDate && nextPeriod.nextPeriodEndDate) {
        setPeriodStartDate(nextPeriod.nextPeriodStartDate);
        setPeriodEndDate(nextPeriod.nextPeriodEndDate);
      } else {
        // Eğer hesaplanamazsa, varsayılan değerleri kullan
        const today = new Date();
        const nextPeriodEnd = subscription?.is_yearly
          ? addMonths(today, 12) // Yıllık abonelik
          : addMonths(today, 1); // Aylık abonelik

        setPeriodStartDate(format(today, "yyyy-MM-dd"));
        setPeriodEndDate(format(nextPeriodEnd, "yyyy-MM-dd"));
      }
    } catch (error) {
      console.error("Bir sonraki ödeme dönemi hesaplanırken hata:", error);
      toast.error("Ödeme dönemi hesaplanamadı.");

      // Hata durumunda varsayılan değerleri kullan
      const today = new Date();
      const nextPeriodEnd = subscription?.is_yearly
        ? addMonths(today, 12) // Yıllık abonelik
        : addMonths(today, 1); // Aylık abonelik

      setPeriodStartDate(format(today, "yyyy-MM-dd"));
      setPeriodEndDate(format(nextPeriodEnd, "yyyy-MM-dd"));
    } finally {
      setIsLoadingNextPeriod(false);
    }
  };

  useEffect(() => {
    async function loadSubscription() {
      if (!isAdminUser) return;

      try {
        setIsLoading(true);

        // Abonelik detaylarını yükle
        const subscriptionData = await getSalonSubscriptionById(id);
        setSubscription(subscriptionData);
      } catch (error) {
        console.error("Abonelik detayları yüklenirken hata:", error);
        toast.error("Abonelik detayları yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    }

    if (id) {
      loadSubscription();
    }
  }, [isAdminUser, id]);

  // Abonelik yüklendiğinde bir sonraki ödeme dönemini hesapla
  useEffect(() => {
    if (subscription) {
      loadNextPaymentPeriod();
    }
  }, [subscription]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!subscription) return;

    // Validasyon
    if (calculatedAmount < 0) {
      toast.error("Ödeme tutarı geçerli değil.");
      return;
    }

    if (!periodStartDate || !periodEndDate) {
      toast.error("Lütfen ödeme dönemini seçin.");
      return;
    }

    if (!paymentDate) {
      toast.error("Lütfen ödeme tarihini girin.");
      return;
    }

    try {
      setIsSubmitting(true);

      // Ödeme kaydı oluştur
      const paymentData = {
        subscription_id: id,
        amount: calculatedAmount,
        original_amount: originalAmount,
        discount_amount: discountAmount,
        period_start_date: periodStartDate,
        period_end_date: periodEndDate,
        payment_date: paymentDate,
        payment_method: "manual",
        status: "completed" as const,
        invoice_number: invoiceNumber || undefined,
        notes: discountInfo?.hasDiscount
          ? `İndirim uygulandı: ${discountInfo.discountType}`
          : undefined,
      };

      // Eğer indirim varsa, indirim bilgilerini ekle
      if (discountInfo?.hasDiscount) {
        paymentData.discount_type = discountInfo.discountType;
        paymentData.discount_reference_id = discountInfo.discountId;
      }

      await subscriptionPayments.createSubscriptionPayment(paymentData);

      toast.success("Ödeme kaydı başarıyla oluşturuldu.");
      router.push(`/admin/subscriptions/${id}`);
    } catch (error) {
      console.error("Ödeme kaydı oluşturulurken hata:", error);
      toast.error("Ödeme kaydı oluşturulurken bir hata oluştu.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isAdminUser) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href={`/admin/subscriptions/${id}`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Ödeme Ekle</h1>
        </header>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-32 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <Button variant="outline" size="icon" asChild className="mr-2">
          <Link href={`/admin/subscriptions/${id}`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">Ödeme Ekle</h1>
      </header>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>
              {subscription?.salons?.name || "Bilinmeyen Salon"}
            </CardTitle>
            <CardDescription>
              Plan: {subscription?.plans?.name || "Bilinmeyen Plan"} -
              {subscription?.is_yearly ? " Yıllık" : " Aylık"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Ödeme Dönemi */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-muted-foreground" />
                    <h3 className="font-medium">Ödeme Dönemi</h3>
                  </div>

                  <Badge variant="outline" className="font-normal">
                    {subscription?.is_yearly
                      ? "Yıllık Abonelik"
                      : "Aylık Abonelik"}
                  </Badge>
                </div>

                {isLoadingNextPeriod ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin h-5 w-5 border-2 border-primary border-r-transparent rounded-full mr-2"></div>
                    <span>Ödeme dönemi hesaplanıyor...</span>
                  </div>
                ) : (
                  <>
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Otomatik Hesaplanan Dönem</AlertTitle>
                      <AlertDescription>
                        Ödeme dönemi, abonelik tipine ve son ödeme tarihine göre
                        otomatik olarak hesaplanmıştır.
                        {subscription?.is_yearly
                          ? " Yıllık abonelik için bir sonraki 12 aylık dönem."
                          : " Aylık abonelik için bir sonraki 30 günlük dönem."}
                      </AlertDescription>
                    </Alert>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="periodStartDate">
                          Başlangıç Tarihi
                        </Label>
                        <Input
                          id="periodStartDate"
                          type="date"
                          value={periodStartDate}
                          readOnly
                          className="bg-muted"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="periodEndDate">Bitiş Tarihi</Label>
                        <Input
                          id="periodEndDate"
                          type="date"
                          value={periodEndDate}
                          readOnly
                          className="bg-muted"
                        />
                      </div>
                    </div>

                    {periodStartDate && periodEndDate && (
                      <div className="text-sm text-muted-foreground">
                        Ödeme dönemi:{" "}
                        <strong>
                          {differenceInDays(
                            new Date(periodEndDate),
                            new Date(periodStartDate)
                          )}
                        </strong>{" "}
                        gün
                      </div>
                    )}
                  </>
                )}
              </div>

              <Separator />

              {/* Ödeme Bilgileri */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <h3 className="font-medium">Ödeme Bilgileri</h3>
                </div>

                {/* Ödeme Özeti */}
                <div className="bg-muted p-4 rounded-md space-y-2">
                  {isCalculating ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin h-5 w-5 border-2 border-primary border-r-transparent rounded-full mr-2"></div>
                      <span>Hesaplanıyor...</span>
                    </div>
                  ) : (
                    <>
                      <div className="flex justify-between">
                        <span>Orijinal Tutar:</span>
                        <span>{formatCurrency(originalAmount)}</span>
                      </div>

                      {discountAmount > 0 && (
                        <div className="flex justify-between text-green-500">
                          <span>
                            İndirim (
                            {discountInfo?.discountType === "referral"
                              ? "Referans"
                              : "Diğer"}
                            ):
                          </span>
                          <span>-{formatCurrency(discountAmount)}</span>
                        </div>
                      )}

                      <Separator />

                      <div className="flex justify-between font-bold">
                        <span>Ödenecek Tutar:</span>
                        <span>{formatCurrency(calculatedAmount)}</span>
                      </div>
                    </>
                  )}
                </div>

                {discountInfo?.hasDiscount && (
                  <Alert className="bg-green-500/10 border-green-500/50">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <AlertTitle>İndirim Uygulandı</AlertTitle>
                    <AlertDescription>
                      {discountInfo.discountType === "referral"
                        ? "Referans indirimi uygulandı. Bu kullanıcı referans kodu ile kaydolduğu için ilk ödemesinde indirim kazandı."
                        : "İndirim uygulandı."}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Abonelik bitiş tarihi güncelleme bilgisi */}
                <Alert className="mt-4">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Abonelik Bitiş Tarihi Güncellemesi</AlertTitle>
                  <AlertDescription>
                    Bu ödeme kaydedildiğinde, abonelik bitiş tarihi otomatik
                    olarak ödeme döneminin bitiş tarihine ({periodEndDate})
                    güncellenecektir.
                    {subscription?.status === "trial" &&
                      " Ayrıca abonelik durumu 'deneme' durumundan 'aktif' durumuna geçecektir."}
                    {subscription?.status === "past_due" &&
                      " Ayrıca abonelik durumu 'ödeme gecikmiş' durumundan 'aktif' durumuna geçecektir."}
                    {subscription?.status === "suspended" &&
                      " Ayrıca abonelik durumu 'askıya alınmış' durumundan 'aktif' durumuna geçecektir."}
                  </AlertDescription>
                </Alert>

                {/* Ödeme Tarihi */}
                <div className="space-y-2">
                  <Label htmlFor="paymentDate">Ödeme Tarihi</Label>
                  <Input
                    id="paymentDate"
                    type="date"
                    value={paymentDate}
                    onChange={(e) => setPaymentDate(e.target.value)}
                    required
                  />
                </div>

                {/* Fatura Numarası */}
                <div className="space-y-2">
                  <Label htmlFor="invoiceNumber">
                    Fatura Numarası (Opsiyonel)
                  </Label>
                  <Input
                    id="invoiceNumber"
                    type="text"
                    value={invoiceNumber}
                    onChange={(e) => setInvoiceNumber(e.target.value)}
                    placeholder="Örn: INV-2024-001"
                  />
                </div>
              </div>

              <Separator />

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Info className="h-4 w-4" />
                <p>
                  Bu ödeme kaydı manuel olarak oluşturulacak ve "tamamlandı"
                  durumunda kaydedilecektir. Ödeme yöntemi "Manuel (Havale/EFT)"
                  olarak ayarlanacaktır.
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" asChild>
              <Link href={`/admin/subscriptions/${id}`}>İptal</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-0 border-r-0 rounded-full"></div>
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Ödeme Ekle
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
