"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Menu } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"
import { useState } from "react"

export function Header() {
  const pathname = usePathname()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const isLandingPage = pathname === "/"
  const isAuthPage = pathname.startsWith("/auth")
  const isDashboardPage = pathname.startsWith("/dashboard")

  return (
    <header className="border-b">
      <div className="container mx-auto flex justify-between items-center py-4">
        <div className="text-2xl font-bold">
          <Link href="/">SalonFlow</Link>
        </div>

        {isLandingPage && (
          <>
            <nav className="hidden md:flex gap-6">
              <Link href="#features" className="hover:text-primary"><PERSON><PERSON><PERSON><PERSON></Link>
              <Link href="#pricing" className="hover:text-primary">Fiyatlandırma</Link>
              <Link href="#testimonials" className="hover:text-primary">Müşteri Yorumları</Link>
              <Link href="#contact" className="hover:text-primary">İletişim</Link>
            </nav>
            <div className="flex items-center gap-4">
              <div className="hidden md:flex gap-4">
                <Link href="/auth/login">
                  <Button variant="outline">Giriş Yap</Button>
                </Link>
                <Link href="/auth/register">
                  <Button>Kayıt Ol</Button>
                </Link>
              </div>
              <ThemeToggle />
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Menüyü aç/kapat</span>
              </Button>
            </div>
          </>
        )}

        {isAuthPage && (
          <div className="flex items-center gap-4">
            <ThemeToggle />
          </div>
        )}

        {isDashboardPage && (
          <div className="flex items-center gap-4">
            <ThemeToggle />
          </div>
        )}
      </div>

      {/* Mobile menu */}
      {isLandingPage && mobileMenuOpen && (
        <div className="md:hidden border-t">
          <div className="container mx-auto py-4 flex flex-col gap-4">
            <nav className="flex flex-col gap-4">
              <Link
                href="#features"
                className="hover:text-primary"
                onClick={() => setMobileMenuOpen(false)}
              >
                Özellikler
              </Link>
              <Link
                href="#pricing"
                className="hover:text-primary"
                onClick={() => setMobileMenuOpen(false)}
              >
                Fiyatlandırma
              </Link>
              <Link
                href="#testimonials"
                className="hover:text-primary"
                onClick={() => setMobileMenuOpen(false)}
              >
                Müşteri Yorumları
              </Link>
              <Link
                href="#contact"
                className="hover:text-primary"
                onClick={() => setMobileMenuOpen(false)}
              >
                İletişim
              </Link>
            </nav>
            <div className="flex gap-4">
              <Link href="/auth/login" className="w-full">
                <Button variant="outline" className="w-full">Giriş Yap</Button>
              </Link>
              <Link href="/auth/register" className="w-full">
                <Button className="w-full">Kayıt Ol</Button>
              </Link>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
