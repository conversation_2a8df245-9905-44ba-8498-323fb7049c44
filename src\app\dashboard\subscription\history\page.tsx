"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"

import { Button } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { useUser } from "@/contexts/UserContext"
import { getSalonPayments } from "@/lib/db/subscription-payments"
import { SubscriptionPayment } from "@/lib/db/types"

export default function PaymentHistoryPage() {
  const { salonId } = useUser()
  const [payments, setPayments] = useState<SubscriptionPayment[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadPayments() {
      if (!salonId) return

      try {
        setIsLoading(true)

        // Ödeme geçmişini yükle
        const paymentsData = await getSalonPayments(salonId)
        setPayments(paymentsData)
      } catch (error) {
        console.error("Ödeme geçmişi yüklenirken hata:", error)
        toast.error("Ödeme geçmişi yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadPayments()
  }, [salonId])

  // Ödeme durumunu Türkçe olarak göster
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Bekliyor'
      case 'completed': return 'Tamamlandı'
      case 'failed': return 'Başarısız'
      case 'refunded': return 'İade Edildi'
      default: return status
    }
  }

  // Ödeme durumuna göre renk belirle
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500 hover:bg-yellow-600'
      case 'completed': return 'bg-green-500 hover:bg-green-600'
      case 'failed': return 'bg-red-500 hover:bg-red-600'
      case 'refunded': return 'bg-blue-500 hover:bg-blue-600'
      default: return 'bg-gray-500 hover:bg-gray-600'
    }
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/subscription">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Ödeme Geçmişi</h1>
        </div>
      </header>

      <div className="rounded-md border">
        <Table>
          <TableCaption>Abonelik ödeme geçmişiniz</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Ödeme Tarihi</TableHead>
              <TableHead>Ödeme Dönemi</TableHead>
              <TableHead>Tutar</TableHead>
              <TableHead>İndirim</TableHead>
              <TableHead>Ödeme Yöntemi</TableHead>
              <TableHead>Fatura No</TableHead>
              <TableHead>Durum</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                </TableRow>
              ))
            ) : payments.length > 0 ? (
              payments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell>
                    {payment.payment_date ?
                      format(new Date(payment.payment_date), 'd MMM yyyy', { locale: tr }) :
                      format(new Date(payment.created_at), 'd MMM yyyy', { locale: tr })}
                  </TableCell>
                  <TableCell>
                    {payment.period_start_date && payment.period_end_date ? (
                      <>
                        {format(new Date(payment.period_start_date), 'd MMM yyyy', { locale: tr })} - {format(new Date(payment.period_end_date), 'd MMM yyyy', { locale: tr })}
                      </>
                    ) : '-'}
                  </TableCell>
                  <TableCell>
                    {payment.amount} TL
                    {payment.original_amount && payment.original_amount > payment.amount && (
                      <div className="text-xs text-muted-foreground">
                        Orijinal: {payment.original_amount} TL
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {payment.discount_amount && payment.discount_amount > 0 ? (
                      <div className="text-green-500">
                        {payment.discount_amount} TL
                        {payment.discount_type && (
                          <div className="text-xs text-muted-foreground">
                            {payment.discount_type === 'referral' ? 'Referans' : payment.discount_type}
                          </div>
                        )}
                      </div>
                    ) : '-'}
                  </TableCell>
                  <TableCell>
                    {payment.payment_method === 'manual' ? 'Havale/EFT' :
                     payment.payment_method === 'iyzico' ? 'iyzico' :
                     payment.payment_method === 'paytr' ? 'PayTR' :
                     payment.payment_method}
                  </TableCell>
                  <TableCell>{payment.invoice_number || '-'}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(payment.status)}>
                      {getStatusText(payment.status)}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  Henüz ödeme kaydı bulunmuyor.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
