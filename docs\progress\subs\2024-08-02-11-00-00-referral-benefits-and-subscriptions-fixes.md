# Referral Benefits ve Salon Subscriptions Sorunlarının Çözümü

**Tarih:** 2 Ağustos 2024
**Saat:** 11:00

## 1. Tespit Edilen Sorunlar

Referans kodu ve deneme aboneliği oluşturma sürecinde aşağıdaki yeni sorunlar tespit edildi:

1. **Referral Benefits Hatası**: `referral_benefits` tablosundaki `benefit_type` sütunu için check constraint ihlali:
   ```
   "new row for relation \"referral_benefits\" violates check constraint \"referral_benefits_benefit_type_check\""
   ```

2. **Salon Subscriptions Hatası**: `salon_subscriptions` tablosundaki `plan` sütunu için not-null constraint ihlali:
   ```
   "null value in column \"plan\" of relation \"salon_subscriptions\" violates not-null constraint"
   ```

## 2. Sorun<PERSON><PERSON>n Kaynağı

Sorunların ana kaynakları şunlardır:

1. **Referral Benefits Constraint Sorunu**: 
   - `referral_benefits` tablosundaki `benefit_type` sütunu için check constraint, sadece 'discount', 'free_month' ve 'feature_unlock' değerlerine izin veriyor.
   - Ancak kodumuzda 'subscription_extension' değeri kullanılıyor.

2. **Salon Subscriptions Not-Null Constraint Sorunu**:
   - `salon_subscriptions` tablosundaki `plan` sütunu not-null olarak tanımlanmış.
   - Ancak yeni kayıtlarda bu sütuna değer atanmıyor, bunun yerine `plan_id` kullanılıyor.

## 3. Uygulanan Çözümler

### 3.1. Veritabanı Şeması Güncellemeleri

#### 3.1.1. Referral Benefits Constraint Güncelleme

`referral_benefits` tablosundaki `benefit_type` sütunu için check constraint'i güncelledik:

```sql
-- Mevcut kısıtlamayı kaldır
ALTER TABLE referral_benefits DROP CONSTRAINT IF EXISTS referral_benefits_benefit_type_check;

-- Yeni kısıtlama ekle (subscription_extension değerini de kabul edecek şekilde)
ALTER TABLE referral_benefits ADD CONSTRAINT referral_benefits_benefit_type_check 
  CHECK (benefit_type = ANY (ARRAY['discount', 'free_month', 'feature_unlock', 'subscription_extension']));
```

#### 3.1.2. Salon Subscriptions Plan Sütunu Güncelleme

`salon_subscriptions` tablosundaki `plan` sütununu nullable yaptık ve varsayılan değer ekledik:

```sql
-- plan sütununu nullable yap
ALTER TABLE salon_subscriptions ALTER COLUMN plan DROP NOT NULL;

-- Eski kayıtlar için plan sütununa varsayılan değer ekle
UPDATE salon_subscriptions SET plan = 'basic' WHERE plan IS NULL;
```

### 3.2. Kod Güncellemeleri

#### 3.2.1. Referrals.ts Dosyası Güncelleme

`src/lib/db/referrals.ts` dosyasında `applyReferralCode` fonksiyonunu güncelledik:

```typescript
// Referans faydası oluştur
const benefit: ReferralBenefitInsert = {
  referrer_salon_id: referrerSalonId,
  referred_salon_id: newSalonId,
  referral_code_id: referralCode.id,
  benefit_type: 'discount', // Geçici olarak 'discount' kullanıyoruz, 'subscription_extension' yerine
  benefit_value: '30', // 30 gün deneme süresi
  is_applied: false
};
```

#### 3.2.2. Subscriptions.ts Dosyası Güncelleme

`src/lib/db/subscriptions.ts` dosyasında `createTrialSubscription` fonksiyonunu güncelledik:

```typescript
const subscription: SalonSubscriptionInsert = {
  salon_id: salonId,
  plan_id: planId,
  plan: 'basic', // Eski plan sütunu için varsayılan değer
  status: 'trial',
  start_date: today.toISOString().split('T')[0],
  trial_end_date: trialEndDate.toISOString().split('T')[0],
  payment_method: 'manual',
  is_yearly: false,
  is_active: true
};
```

## 4. Test Sonuçları

Yapılan değişiklikler sonrasında test kayıtları başarıyla oluşturuldu:

1. **Referral Benefits Test Kaydı**: 'discount' benefit_type değeri ile başarıyla oluşturuldu.
2. **Salon Subscriptions Test Kaydı**: 'basic' plan değeri ile başarıyla oluşturuldu.

## 5. Sonuç

Bu değişikliklerle, referans kodu ve deneme aboneliği oluşturma süreçlerindeki yeni sorunlar çözülmüştür. Artık:

1. Referans kodu uygulandığında 'discount' benefit_type değeri kullanılarak referral_benefits tablosuna kayıt eklenebilecek
2. Deneme aboneliği oluşturulduğunda 'basic' plan değeri ile salon_subscriptions tablosuna kayıt eklenebilecek

Bu değişiklikler, kullanıcıların referans kodu ile kaydolup salon oluşturduklarında sorunsuz bir deneyim yaşamalarını sağlayacaktır.
