-- Drop all existing policies for barbers table
DROP POLICY IF EXISTS "Salon owners can manage their barbers" ON barbers;
DROP POLICY IF EXISTS "Staff can view salon barbers" ON barbers;
DROP POLICY IF EXISTS "Staff can update their own profile" ON barbers;
DROP POLICY IF EXISTS "Anyone can view barbers" ON barbers;
DROP POLICY IF EXISTS "Service role bypass" ON barbers;
DROP POLICY IF EXISTS "Salon owners can manage barbers" ON barbers;

-- Make sure RLS is enabled
ALTER TABLE barbers ENABLE ROW LEVEL SECURITY;

-- Create a policy for salon owners to manage their barbers
CREATE POLICY "Salon owners can manage barbers" ON barbers
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM salons
    WHERE salons.id = barbers.salon_id
    AND salons.owner_id = auth.uid()
  ))
  WITH CHECK (EXISTS (
    SELECT 1 FROM salons
    WHERE salons.id = barbers.salon_id
    AND salons.owner_id = auth.uid()
  ));

-- Create a policy for anyone to view barbers
CREATE POLICY "Anyone can view barbers" ON barbers
  FOR SELECT
  USING (true);

-- Create a policy for staff to update their own profile
CREATE POLICY "Staff can update their own profile" ON barbers
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Create a service role bypass policy
CREATE POLICY "Service role bypass" ON barbers
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);
