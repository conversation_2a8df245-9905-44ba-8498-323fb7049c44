-- Referans sistemi <PERSON>ğ<PERSON>şiklikleri için SQL script
-- Tarih: 2024-08-05

-- 1. referral_benefits tablosuna yeni alanlar ekleme
ALTER TABLE referral_benefits
ADD COLUMN discount_amount NUMERIC DEFAULT 0,
ADD COLUMN discount_applied BOOLEAN DEFAULT FALSE,
ADD COLUMN discount_applied_payment_id UUID REFERENCES subscription_payments(id);

-- 2. Mevcut kayıtları güncelleme
-- Referans veren kişi 1 ay ücretsiz abonelik yerine, referans alan kişi güncel en düşük plan ücreti kadar indirim kazanacak
UPDATE referral_benefits rb
SET benefit_type = 'referred_discount',
    benefit_value = (SELECT CAST(price_monthly AS TEXT) FROM subscription_plans ORDER BY price_monthly ASC LIMIT 1),
    discount_amount = (SELECT price_monthly FROM subscription_plans ORDER BY price_monthly ASC LIMIT 1)
WHERE benefit_type = 'free_month';

-- 3. Yeni R<PERSON> politikaları oluşturma
-- Mevcut politikaları kaldır
DROP POLICY IF EXISTS "Salon sahipleri kendi referans faydalarını görebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin tüm referans faydalarını görebilir" ON referral_benefits;

-- Yeni politikaları ekle
CREATE POLICY "Salon sahipleri kendi referans faydalarını görebilir"
ON referral_benefits
FOR SELECT
TO authenticated
USING (
  (referrer_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())) OR
  (referred_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
);

CREATE POLICY "Admin tüm referans faydalarını görebilir"
ON referral_benefits
FOR ALL
TO authenticated
USING (
  (SELECT is_admin())
);

-- 4. Referans indirimi kontrolü için fonksiyon oluşturma
CREATE OR REPLACE FUNCTION check_referral_discount(salon_id UUID)
RETURNS TABLE (
  id UUID,
  discount_amount NUMERIC,
  referrer_salon_id UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    rb.id,
    rb.discount_amount,
    rb.referrer_salon_id
  FROM
    referral_benefits rb
  WHERE
    rb.referred_salon_id = salon_id
    AND rb.benefit_type = 'referred_discount'
    AND rb.discount_applied = FALSE
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Referans indirimi uygulama için fonksiyon oluşturma
CREATE OR REPLACE FUNCTION apply_referral_discount(benefit_id UUID, payment_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE referral_benefits
  SET
    discount_applied = TRUE,
    discount_applied_payment_id = payment_id,
    is_applied = TRUE,
    applied_date = CURRENT_DATE
  WHERE
    id = benefit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
