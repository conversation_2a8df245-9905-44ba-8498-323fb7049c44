import { supabaseClient } from '../supabase-singleton';

/**
 * Types for analytics data
 */
export interface BarberDistribution {
  barber_id: string;
  barber_name: string;
  appointment_count: number; // PostgreSQL'de BIGINT, JavaScript'te number
  percentage: number;
}

export interface ServicePopularity {
  service_id: string;
  service_name: string;
  appointment_count: number; // PostgreSQL'de BIGINT, JavaScript'te number
  percentage: number;
}

export interface AppointmentTrend {
  month: string;
  year: number;
  month_number: number;
  appointment_count: number; // PostgreSQL'de BIGINT, JavaScript'te number
}

export interface BarberPerformance {
  barber_id: string;
  barber_name: string;
  total_appointments: number; // PostgreSQL'de BIGINT, JavaScript'te number
  completed_appointments: number; // PostgreSQL'de BIGINT, JavaScript'te number
  cancelled_appointments: number; // PostgreSQL'de BIGINT, JavaScript'te number
  no_show_appointments: number; // PostgreSQL'de BIGINT, JavaScript'te number
  completion_rate: number;
  unique_customers: number; // PostgreSQL'de BIGINT, JavaScript'te number
}

/**
 * Get appointment distribution by barber
 */
export async function getAppointmentDistributionByBarber(
  salonId: string,
  startDate: string,
  endDate: string
): Promise<BarberDistribution[]> {
  const supabase = supabaseClient;

  const { data, error } = await supabase.rpc('get_appointment_distribution_by_barber', {
    p_salon_id: salonId,
    p_start_date: startDate,
    p_end_date: endDate,
  });

  if (error) {
    console.error('Error fetching barber distribution:', error);
    throw error;
  }

  return data || [];
}

/**
 * Get service popularity
 */
export async function getServicePopularity(
  salonId: string,
  startDate: string,
  endDate: string,
  limit: number = 10
): Promise<ServicePopularity[]> {
  const supabase = supabaseClient;

  const { data, error } = await supabase.rpc('get_service_popularity', {
    p_salon_id: salonId,
    p_start_date: startDate,
    p_end_date: endDate,
    p_limit: limit,
  });

  if (error) {
    console.error('Error fetching service popularity:', error);
    throw error;
  }

  return data || [];
}

/**
 * Get appointment trends
 */
export async function getAppointmentTrends(
  salonId: string,
  startDate: string,
  endDate: string
): Promise<AppointmentTrend[]> {
  const supabase = supabaseClient;

  const { data, error } = await supabase.rpc('get_appointment_trends', {
    p_salon_id: salonId,
    p_start_date: startDate,
    p_end_date: endDate,
  });

  if (error) {
    console.error('Error fetching appointment trends:', error);
    throw error;
  }

  return data || [];
}

/**
 * Get barber performance
 */
export async function getBarberPerformance(
  salonId: string,
  startDate: string,
  endDate: string
): Promise<BarberPerformance[]> {
  const supabase = supabaseClient;

  const { data, error } = await supabase.rpc('get_barber_performance', {
    p_salon_id: salonId,
    p_start_date: startDate,
    p_end_date: endDate,
  });

  if (error) {
    console.error('Error fetching barber performance:', error);
    throw error;
  }

  return data || [];
}
