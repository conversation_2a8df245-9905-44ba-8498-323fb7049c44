-- Add lunch break columns to barber_working_hours table
ALTER TABLE barber_working_hours 
ADD COLUMN lunch_start_time TIME,
ADD COLUMN lunch_end_time TIME,
ADD COLUMN has_lunch_break BOOLEAN DEFAULT FALSE;

-- Update the barber_working_hours table with default lunch break values
UPDATE barber_working_hours
SET 
  lunch_start_time = '12:00',
  lunch_end_time = '13:00',
  has_lunch_break = FALSE;

-- Comment explaining the migration
COMMENT ON COLUMN barber_working_hours.lunch_start_time IS 'Start time of the lunch break';
COMMENT ON COLUMN barber_working_hours.lunch_end_time IS 'End time of the lunch break';
COMMENT ON COLUMN barber_working_hours.has_lunch_break IS 'Whether the barber has a lunch break on this day';
