import { createClient } from '@supabase/supabase-js';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { SupabaseClient } from '@supabase/supabase-js';

// Use placeholder values for development if environment variables are not set
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder-url.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// Create a mock client if we're using placeholder values
const isMockClient = !process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create the Supabase client for server-side operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Singleton instance for browser client
let browserClient: SupabaseClient | null = null;

// Function to get client-side Supabase client with cookie-based auth (singleton pattern)
export function getSupabaseBrowser() {
  // If we already have an instance, return it
  if (browserClient !== null) {
    return browserClient;
  }

  // Only log once when creating the client
  console.log('Creating Supabase browser client with enhanced options');

  // Create a new instance
  browserClient = createClientComponentClient();

  // Log the session for debugging (only once)
  browserClient.auth.getSession().then(({ data, error }) => {
    if (error) {
      console.error('Error getting session:', error);
    } else if (data.session) {
      console.log('Session found. User authenticated with ID:', data.session.user.id);
      if (data.session.expires_at) {
        console.log('Token expires at:', new Date(data.session.expires_at * 1000).toISOString());
      }
    } else {
      console.warn('No session found. User is not authenticated.');
    }
  });

  return browserClient;
}

// If we're using a mock client, override methods to prevent API calls
if (isMockClient && typeof window !== 'undefined') {
  console.warn(
    'Using mock Supabase client. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables.'
  );
}
