// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/examples/supabase-functions

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from "../_shared/cors.ts"
import { EmailClient } from "https://esm.sh/@azure/communication-email"

const connectionString = Deno.env.get("AZURE_COMMUNICATION_CONNECTION_STRING") || "";
const emailClient = new EmailClient(connectionString);
const senderEmail = Deno.env.get("SENDER_EMAIL") || "<EMAIL>";

interface EmailRequestBody {
  to: string;
  salonName: string;
  staffName: string;
  invitationUrl: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { to, salonName, staffName, invitationUrl } = await req.json() as EmailRequestBody;

    if (!to || !salonName || !staffName || !invitationUrl) {
      return new Response(
        JSON.stringify({ error: "Missing required fields" }),
        { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 400 }
      );
    }

    // Create the email content
    const subject = `${salonName} has invited you to join SalonFlow`;
    const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>You've been invited to join SalonFlow</h2>
        <p>Hello ${staffName},</p>
        <p>${salonName} has invited you to join their team on SalonFlow, a salon appointment management system.</p>
        <p>As a staff member, you'll be able to:</p>
        <ul>
          <li>View and manage your appointments</li>
          <li>Set your working hours</li>
          <li>View salon customers</li>
        </ul>
        <p>To accept this invitation and create your account, please click the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${invitationUrl}" style="background-color: #0070f3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Accept Invitation
          </a>
        </div>
        <p>Or copy and paste this URL into your browser:</p>
        <p style="word-break: break-all;">${invitationUrl}</p>
        <p>This invitation link will expire in 7 days.</p>
        <p>If you have any questions, please contact the salon directly.</p>
        <p>Thank you,<br>The SalonFlow Team</p>
      </div>
    `;

    // Send the email
    const emailMessage = {
      senderAddress: senderEmail,
      content: {
        subject,
        html: htmlContent,
      },
      recipients: {
        to: [
          {
            address: to,
            displayName: staffName,
          },
        ],
      },
    };

    const poller = await emailClient.beginSend(emailMessage);
    const response = await poller.pollUntilDone();

    return new Response(
      JSON.stringify({ success: true, messageId: response.messageId }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 200 }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" }, status: 500 }
    );
  }
});
