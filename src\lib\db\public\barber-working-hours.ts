import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * <PERSON>lik doğrulaması olmayan kullanıcılar için berber çalışma saatlerini berber ID ile getir
 */
export async function getPublicBarberWorkingHoursByBarberId(barberId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_barber_working_hours_by_barber_id', { p_barber_id: barberId });

  if (error) throw error;
  return data;
}

/**
 * Kimlik doğrulaması olmayan kullanıcılar için berber çalışma saatlerini salon ID ile getir
 */
export async function getPublicBarberWorkingHoursBySalonId(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_barber_working_hours_by_salon_id', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
