import { supabaseClient } from '../supabase-singleton';
import { FinanceTransaction, FinanceTransactionInsert, FinanceTransactionUpdate } from './types';

/**
 * Get all finance transactions for a salon
 */
export async function getFinanceTransactions(salonId: string) {
  const supabase = supabaseClient;
  console.log('Fetching all finance transactions for salon:', salonId);

  const { data, error } = await supabase
    .from('finance_transactions')
    .select(`
      *,
      finance_categories:category_id(name, type, color)
    `)
    .eq('salon_id', salonId)
    .order('transaction_date', { ascending: false });

  if (error) {
    console.error('Error fetching finance transactions:', error);
    throw error;
  }

  console.log(`Retrieved ${data ? data.length : 0} finance transactions`);
  return data;
}

/**
 * Get finance transactions for a salon within a date range
 */
export async function getFinanceTransactionsInRange(salonId: string, startDate: string, endDate: string) {
  const supabase = supabaseClient;
  const { data, error } = await supabase
    .from('finance_transactions')
    .select(`
      *,
      finance_categories:category_id(name, type, color)
    `)
    .eq('salon_id', salonId)
    .gte('transaction_date', startDate)
    .lte('transaction_date', endDate)
    .order('transaction_date', { ascending: false });

  if (error) {
    console.error('Error fetching finance transactions in range:', error);
    throw error;
  }

  console.log(`Retrieved ${data ? data.length : 0} finance transactions in date range`);
  return data;
}

/**
 * Get finance transactions by category
 */
export async function getFinanceTransactionsByCategory(salonId: string, categoryId: string) {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_transactions')
    .select(`
      *,
      finance_categories:category_id(name, type, color)
    `)
    .eq('salon_id', salonId)
    .eq('category_id', categoryId)
    .order('transaction_date', { ascending: false });

  if (error) {
    console.error('Error fetching finance transactions by category:', error);
    throw error;
  }

  console.log(`Retrieved ${data ? data.length : 0} finance transactions for category`);
  return data;
}

/**
 * Get finance transactions by type (income/expense)
 */
export async function getFinanceTransactionsByType(salonId: string, type: 'income' | 'expense') {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_transactions')
    .select(`
      *,
      finance_categories:category_id(name, type, color)
    `)
    .eq('salon_id', salonId)
    .eq('finance_categories.type', type)
    .order('transaction_date', { ascending: false });

  if (error) {
    console.error('Error fetching finance transactions by type:', error);
    throw error;
  }

  console.log(`Retrieved ${data ? data.length : 0} finance transactions for type`);
  return data;
}

/**
 * Get a finance transaction by ID
 */
export async function getFinanceTransactionById(id: string) {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_transactions')
    .select(`
      *,
      finance_categories:category_id(name, type, color),
      appointments:appointment_id(date, start_time, service_id, barber_id, fullname, phonenumber)
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching finance transaction by ID:', error);
    throw error;
  }

  return data;
}

/**
 * Create a new finance transaction
 */
export async function createFinanceTransaction(transaction: FinanceTransactionInsert) {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_transactions')
    .insert(transaction)
    .select()
    .single();

  if (error) {
    console.error('Error creating finance transaction:', error);
    throw error;
  }

  return data as FinanceTransaction;
}

/**
 * Update a finance transaction
 */
export async function updateFinanceTransaction({ id, ...transaction }: FinanceTransactionUpdate) {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .from('finance_transactions')
    .update(transaction)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating finance transaction:', error);
    throw error;
  }

  return data as FinanceTransaction;
}

/**
 * Delete a finance transaction
 */
export async function deleteFinanceTransaction(id: string) {
  const supabase = supabaseClient;

  const { error } = await supabase
    .from('finance_transactions')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting finance transaction:', error);
    throw error;
  }

  return true;
}

/**
 * Get financial summary for a salon within a date range
 */
export async function getFinancialSummary(salonId: string, startDate: string, endDate: string) {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .rpc('get_financial_summary', {
      p_salon_id: salonId,
      p_start_date: startDate,
      p_end_date: endDate
    });

  if (error) {
    console.error('Error fetching financial summary:', error);
    throw error;
  }

  console.log('Financial summary data from RPC:', data);

  // Check if data is an array and extract the first item if it is
  if (Array.isArray(data) && data.length > 0) {
    return data[0];
  }

  return data;
}

/**
 * Get category breakdown for a salon within a date range
 */
export async function getCategoryBreakdown(salonId: string, startDate: string, endDate: string) {
  const supabase = supabaseClient;

  const { data, error } = await supabase
    .rpc('get_category_breakdown', {
      p_salon_id: salonId,
      p_start_date: startDate,
      p_end_date: endDate
    });

  if (error) {
    console.error('Error fetching category breakdown:', error);
    throw error;
  }

  console.log('Category breakdown data from RPC:', data);

  // Ensure data is an array
  if (!Array.isArray(data)) {
    return data ? [data] : [];
  }

  return data;
}
