-- SalonFlow RLS Politikaları Yedeği
-- Oluşturulma Tarihi: 2024-08-25
-- <PERSON>u SQL dosyası, SECURITY DEFINER fonksiyonları uygulanmadan önce mevcut RLS politikalarının yedeğini içerir.
-- <PERSON><PERSON> dosya, herhangi bir sorun olması durumunda politikaları geri yüklemek için kullanılabilir.

-- NOT: Bu dosya sadece yedekleme amaçlıdır. Politikaları geri yüklemek için,
-- önce mevcut politikaları kaldırmanız ve ardından bu dosyadaki politikaları uygulamanız gerekir.

-- Politikaları geri yüklemek için:
-- 1. Önce mevcut politikaları kaldırın:
-- DROP POLICY IF EXISTS "Authenticated users can view salons" ON salons;
-- DROP POLICY IF EXISTS "Authenticated users can view barbers" ON barbers;
-- ... (diğer politikalar için benzer komutlar)

-- 2. Ardından bu dosyadaki politikaları uygulayın.

-- Appointments tablosu politikaları
-- 1. Anyone can insert appointments
CREATE POLICY "Anyone can insert appointments" ON appointments
  FOR INSERT
  TO public
  WITH CHECK (true);

-- 2. Anyone can view appointments
CREATE POLICY "Anyone can view appointments" ON appointments
  FOR SELECT
  TO public
  USING (true);

-- 3. Salon owners can manage appointments at their salon
CREATE POLICY "Salon owners can manage appointments at their salon" ON appointments
  FOR ALL
  TO public
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- 4. Staff can delete their own appointments
CREATE POLICY "Staff can delete their own appointments" ON appointments
  FOR DELETE
  TO public
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

-- 5. Staff can manage their own appointments
CREATE POLICY "Staff can manage their own appointments" ON appointments
  FOR INSERT
  TO public
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

-- 6. Staff can update their own appointments
CREATE POLICY "Staff can update their own appointments" ON appointments
  FOR UPDATE
  TO public
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

-- 7. Staff can view all salon appointments
CREATE POLICY "Staff can view all salon appointments" ON appointments
  FOR SELECT
  TO public
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Barbers tablosu politikaları
-- 1. Anyone can view barbers
CREATE POLICY "Anyone can view barbers" ON barbers
  FOR SELECT
  TO public
  USING (true);

-- 2. Salon owners can manage barbers
CREATE POLICY "Salon owners can manage barbers" ON barbers
  FOR ALL
  TO authenticated
  USING (EXISTS (SELECT 1 FROM salons WHERE salons.id = barbers.salon_id AND salons.owner_id = auth.uid()))
  WITH CHECK (EXISTS (SELECT 1 FROM salons WHERE salons.id = barbers.salon_id AND salons.owner_id = auth.uid()));

-- 3. Service role bypass
CREATE POLICY "Service role bypass" ON barbers
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 4. Staff can update their own profile
CREATE POLICY "Staff can update their own profile" ON barbers
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Services tablosu politikaları
-- 1. Anyone can view services
CREATE POLICY "Anyone can view services" ON services
  FOR SELECT
  TO public
  USING (true);

-- 2. Salon owners can manage their salon's services
CREATE POLICY "Salon owners can manage their salon's services" ON services
  FOR ALL
  TO public
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- 3. Staff can view salon services
CREATE POLICY "Staff can view salon services" ON services
  FOR SELECT
  TO public
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Working Hours tablosu politikaları
-- 1. Anyone can view working hours
CREATE POLICY "Anyone can view working hours" ON working_hours
  FOR SELECT
  TO public
  USING (true);

-- 2. Salon owners can manage their salon's working hours
CREATE POLICY "Salon owners can manage their salon's working hours" ON working_hours
  FOR ALL
  TO public
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- 3. Staff can view salon working hours
CREATE POLICY "Staff can view salon working hours" ON working_hours
  FOR SELECT
  TO public
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Holidays tablosu politikaları
-- 1. Anyone can view holidays
CREATE POLICY "Anyone can view holidays" ON holidays
  FOR SELECT
  TO public
  USING (true);

-- 2. Salon owners can manage their salon's holidays
CREATE POLICY "Salon owners can manage their salon's holidays" ON holidays
  FOR ALL
  TO public
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- 3. Staff can view salon holidays
CREATE POLICY "Staff can view salon holidays" ON holidays
  FOR SELECT
  TO public
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Products tablosu politikaları
-- 1. Anyone can view active products
CREATE POLICY "Anyone can view active products" ON products
  FOR SELECT
  TO public
  USING (is_active = true);

-- 2. Salon owners can manage their salon's products
CREATE POLICY "Salon owners can manage their salon's products" ON products
  FOR ALL
  TO public
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- 3. Staff can view their salon's products
CREATE POLICY "Staff can view their salon's products" ON products
  FOR SELECT
  TO public
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Salons tablosu politikaları
-- 1. Anyone can view salons
CREATE POLICY "Anyone can view salons" ON salons
  FOR SELECT
  TO public
  USING (true);

-- 2. Salon owners can manage their own salons
CREATE POLICY "Salon owners can manage their own salons" ON salons
  FOR ALL
  TO public
  USING (owner_id = auth.uid())
  WITH CHECK (owner_id = auth.uid());
