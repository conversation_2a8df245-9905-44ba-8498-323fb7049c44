-- Finance Management Module Tables
-- Date: 2024-07-10

-- Finance Categories table
CREATE TABLE finance_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
  description TEXT,
  color TEXT,
  is_system_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Finance Transactions table
CREATE TABLE finance_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  category_id UUID REFERENCES finance_categories(id) ON DELETE RESTRICT,
  amount DECIMAL(10, 2) NOT NULL,
  transaction_date DATE NOT NULL,
  description TEXT,
  appointment_id UUID REFERENCES appointments(id) ON DELETE SET NULL,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_finance_categories_salon_id ON finance_categories(salon_id);
CREATE INDEX idx_finance_transactions_salon_id ON finance_transactions(salon_id);
CREATE INDEX idx_finance_transactions_category_id ON finance_transactions(category_id);
CREATE INDEX idx_finance_transactions_appointment_id ON finance_transactions(appointment_id);
CREATE INDEX idx_finance_transactions_transaction_date ON finance_transactions(transaction_date);

-- Row Level Security Policies for finance_categories

-- Enable RLS on finance_categories
ALTER TABLE finance_categories ENABLE ROW LEVEL SECURITY;

-- Salon owners can manage their own finance categories
CREATE POLICY "Salon owners can manage their own finance categories" ON finance_categories
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Staff can view finance categories of their salon
CREATE POLICY "Staff can view finance categories of their salon" ON finance_categories
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Row Level Security Policies for finance_transactions

-- Enable RLS on finance_transactions
ALTER TABLE finance_transactions ENABLE ROW LEVEL SECURITY;

-- Salon owners can manage their own finance transactions
CREATE POLICY "Salon owners can manage their own finance transactions" ON finance_transactions
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Staff can view finance transactions of their salon
CREATE POLICY "Staff can view finance transactions of their salon" ON finance_transactions
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Staff can create finance transactions for their salon
CREATE POLICY "Staff can create finance transactions for their salon" ON finance_transactions
  FOR INSERT
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Staff can update finance transactions they created
CREATE POLICY "Staff can update finance transactions they created" ON finance_transactions
  FOR UPDATE
  USING (created_by = auth.uid())
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Create stored procedures for financial reports

-- Function to get financial summary
CREATE OR REPLACE FUNCTION get_financial_summary(
  p_salon_id UUID,
  p_start_date DATE,
  p_end_date DATE
) RETURNS TABLE (
  total_income DECIMAL(10, 2),
  total_expense DECIMAL(10, 2),
  net_profit DECIMAL(10, 2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COALESCE(SUM(CASE WHEN fc.type = 'income' THEN ft.amount ELSE 0 END), 0) AS total_income,
    COALESCE(SUM(CASE WHEN fc.type = 'expense' THEN ft.amount ELSE 0 END), 0) AS total_expense,
    COALESCE(SUM(CASE WHEN fc.type = 'income' THEN ft.amount ELSE -ft.amount END), 0) AS net_profit
  FROM
    finance_transactions ft
    JOIN finance_categories fc ON ft.category_id = fc.id
  WHERE
    ft.salon_id = p_salon_id
    AND ft.transaction_date BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql;

-- Function to get category breakdown
CREATE OR REPLACE FUNCTION get_category_breakdown(
  p_salon_id UUID,
  p_start_date DATE,
  p_end_date DATE
) RETURNS TABLE (
  category_id UUID,
  category_name TEXT,
  category_type TEXT,
  category_color TEXT,
  total_amount DECIMAL(10, 2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    fc.id AS category_id,
    fc.name AS category_name,
    fc.type AS category_type,
    fc.color AS category_color,
    COALESCE(SUM(ft.amount), 0) AS total_amount
  FROM
    finance_categories fc
    LEFT JOIN finance_transactions ft ON fc.id = ft.category_id
      AND ft.transaction_date BETWEEN p_start_date AND p_end_date
  WHERE
    fc.salon_id = p_salon_id
  GROUP BY
    fc.id, fc.name, fc.type, fc.color
  ORDER BY
    fc.type, total_amount DESC;
END;
$$ LANGUAGE plpgsql;

-- Insert default categories for each salon
CREATE OR REPLACE FUNCTION create_default_finance_categories() RETURNS TRIGGER AS $$
BEGIN
  -- Income categories
  INSERT INTO finance_categories (salon_id, name, type, color, is_system_default)
  VALUES 
    (NEW.id, 'Hizmet Geliri', 'income', '#4CAF50', TRUE),
    (NEW.id, 'Ürün Satışı', 'income', '#8BC34A', TRUE),
    (NEW.id, 'Diğer Gelir', 'income', '#CDDC39', TRUE);
  
  -- Expense categories
  INSERT INTO finance_categories (salon_id, name, type, color, is_system_default)
  VALUES 
    (NEW.id, 'Kira', 'expense', '#F44336', TRUE),
    (NEW.id, 'Faturalar', 'expense', '#FF5722', TRUE),
    (NEW.id, 'Malzeme Alımı', 'expense', '#FF9800', TRUE),
    (NEW.id, 'Personel Maaşları', 'expense', '#FFC107', TRUE),
    (NEW.id, 'Diğer Gider', 'expense', '#FFE082', TRUE);
    
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to add default categories when a new salon is created
CREATE TRIGGER create_default_finance_categories_trigger
AFTER INSERT ON salons
FOR EACH ROW
EXECUTE FUNCTION create_default_finance_categories();

-- Create trigger to automatically record income when an appointment is completed
CREATE OR REPLACE FUNCTION record_appointment_income() RETURNS TRIGGER AS $$
DECLARE
  v_service_name TEXT;
  v_service_id UUID;
  v_salon_id UUID;
  v_category_id UUID;
  v_amount DECIMAL(10, 2);
BEGIN
  -- Only proceed if status is changing to 'completed'
  IF (TG_OP = 'UPDATE' AND NEW.status = 'completed' AND OLD.status != 'completed') THEN
    -- Get service details
    SELECT s.name, s.id, a.salon_id
    INTO v_service_name, v_service_id, v_salon_id
    FROM appointments a
    JOIN services s ON a.service_id = s.id
    WHERE a.id = NEW.id;
    
    -- Find the default service income category
    SELECT id INTO v_category_id
    FROM finance_categories
    WHERE salon_id = v_salon_id AND name = 'Hizmet Geliri' AND is_system_default = TRUE;
    
    -- If no default category exists, use the first income category
    IF v_category_id IS NULL THEN
      SELECT id INTO v_category_id
      FROM finance_categories
      WHERE salon_id = v_salon_id AND type = 'income'
      LIMIT 1;
    END IF;
    
    -- For now, we'll use a placeholder amount since we don't have pricing in the system yet
    -- In a real implementation, this would come from the service price
    v_amount := 100.00;
    
    -- Record the transaction
    INSERT INTO finance_transactions (
      salon_id,
      category_id,
      amount,
      transaction_date,
      description,
      appointment_id,
      created_by
    ) VALUES (
      v_salon_id,
      v_category_id,
      v_amount,
      NEW.date,
      'Otomatik kaydedildi: ' || v_service_name,
      NEW.id,
      auth.uid()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on appointments table
CREATE TRIGGER record_appointment_income_trigger
AFTER UPDATE ON appointments
FOR EACH ROW
EXECUTE FUNCTION record_appointment_income();
