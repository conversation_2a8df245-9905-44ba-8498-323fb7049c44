# Berber Çalışma Saatleri Hatası Düzeltmesi

**Tarih:** 25 Ağustos 2024
**Saat:** 18:00

## Sorun

`get_public_barber_working_hours_by_barber_id` RPC fonksiyonu Supabase'de bulunamadı. Hata mesajı:

```json
{
    "code": "PGRST202",
    "details": "Searched for the function public.get_public_barber_working_hours_by_barber_id with parameter p_barber_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
    "hint": "Perhaps you meant to call the function public.get_public_working_hours_by_salon_id",
    "message": "Could not find the function public.get_public_barber_working_hours_by_barber_id(p_barber_id) in the schema cache"
}
```

## Geçici Çözüm

Client-side kodu geçici olarak düzelttik:

```typescript
// Geçici çözüm: Sadece salon çalışma saatlerini kullan
// Not: SQL fonksiyonları Supabase'de çalıştırıldıktan sonra bu kodu tekrar düzeltebilirsiniz
const salonWorkingHours = await publicAccess.getPublicWorkingHoursBySalonId(salonId)

// Salon çalışma saatlerini kullan
const hoursToUse = salonWorkingHours.map(wh => ({
  ...wh,
  barber_id: barberId, // Geçici olarak barber_id ekle
  lunch_start_time: null,
  lunch_end_time: null,
  has_lunch_break: false
}))
```

## Kalıcı Çözüm

SQL fonksiyonlarını Supabase'de çalıştırmak için aşağıdaki adımları izleyin:

1. Supabase Dashboard'a giriş yapın
2. Projenizi seçin
3. Sol menüden "SQL Editor" seçeneğine tıklayın
4. "New Query" butonuna tıklayın
5. `docs/sql/public_access_rpc/2024-08-25-17-15-00-fix-working-hours-functions.sql` dosyasındaki SQL kodunu kopyalayıp yapıştırın
6. "Run" butonuna tıklayın
7. SQL fonksiyonları başarıyla çalıştırıldıktan sonra, uygulamayı yeniden başlatın

SQL fonksiyonları başarıyla çalıştırıldıktan sonra, client-side kodu tekrar düzeltebilirsiniz:

```typescript
// Get both salon working hours and barber working hours
const salonWorkingHours = await publicAccess.getPublicWorkingHoursBySalonId(salonId)
const barberWorkingHours = await publicAccess.getPublicBarberWorkingHoursByBarberId(barberId)

// Use barber working hours if available, otherwise use salon working hours
const hoursToUse = barberWorkingHours.length > 0 
  ? barberWorkingHours.map(bwh => ({
      ...bwh,
      salon_id: salonId // Ensure salon_id is set for consistency
    }))
  : salonWorkingHours
```

## Sonuç

Bu değişiklikler sayesinde, uygulama geçici olarak çalışmaya devam edebilir. SQL fonksiyonları Supabase'de çalıştırıldıktan sonra, client-side kodu tekrar düzelterek tam işlevselliği sağlayabilirsiniz.
