import { supabaseClient } from '../supabase-singleton';
import {
  ReferralCode,
  ReferralCodeInsert,
  ReferralCodeUpdate,
  ReferralBenefit,
  ReferralBenefitInsert,
  ReferralBenefitUpdate,
  PendingReferral,
  PendingReferralInsert
} from './types';
import { nanoid } from 'nanoid';
import { isTrialExpired } from './subscriptions';

const supabase = supabaseClient;

/**
 * Get referral code for a salon
 */
export async function getSalonReferralCode(salonId: string) {
  const { data, error } = await supabase
    .from('referral_codes')
    .select('*')
    .eq('salon_id', salonId)
    .eq('is_active', true)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as ReferralCode | null;
}

/**
 * Get all referral codes for a salon (including inactive)
 */
export async function getAllSalonReferralCodes(salonId: string) {
  const { data, error } = await supabase
    .from('referral_codes')
    .select('*')
    .eq('salon_id', salonId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as ReferralCode[];
}

/**
 * Create a new referral code for a salon
 * @throws {Error} Deneme sürecindeki salonlar referans kodu oluşturamaz
 */
export async function createReferralCode(salonId: string) {
  // Önce mevcut aktif kodu kontrol et
  const existingCode = await getSalonReferralCode(salonId);
  if (existingCode) return existingCode;

  // Deneme sürecinde olup olmadığını kontrol et
  const isTrialOver = await isTrialExpired(salonId);
  if (!isTrialOver) {
    throw new Error('Deneme sürecindeki salonlar referans kodu oluşturamaz. Lütfen abonelik planınızı aktifleştirin.');
  }

  // Yeni kod oluştur
  const code = nanoid(8).toUpperCase();

  const referralCode: ReferralCodeInsert = {
    salon_id: salonId,
    code,
    uses: 0,
    is_active: true
  };

  const { data, error } = await supabase
    .from('referral_codes')
    .insert(referralCode)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralCode;
}

/**
 * Update a referral code
 */
export async function updateReferralCode({ id, ...code }: ReferralCodeUpdate) {
  const { data, error } = await supabase
    .from('referral_codes')
    .update(code)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralCode;
}

/**
 * Deactivate a referral code
 */
export async function deactivateReferralCode(id: string) {
  const { data, error } = await supabase
    .from('referral_codes')
    .update({ is_active: false })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralCode;
}

/**
 * Find a referral code by code string
 */
export async function findReferralCode(code: string) {
  const { data, error } = await supabase
    .from('referral_codes')
    .select('*')
    .eq('code', code)
    .eq('is_active', true)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as ReferralCode | null;
}

/**
 * Increment the uses count for a referral code
 */
export async function incrementReferralCodeUses(id: string) {
  const { data: currentCode, error: fetchError } = await supabase
    .from('referral_codes')
    .select('uses')
    .eq('id', id)
    .single();

  if (fetchError) throw fetchError;

  const { data, error } = await supabase
    .from('referral_codes')
    .update({ uses: (currentCode.uses || 0) + 1 })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralCode;
}

/**
 * Get referral benefits for a salon (as referrer)
 */
export async function getReferrerBenefits(salonId: string) {
  const { data, error } = await supabase
    .from('referral_benefits')
    .select('*')
    .eq('referrer_salon_id', salonId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as ReferralBenefit[];
}

/**
 * Get referral benefits for a salon (as referred)
 */
export async function getReferredBenefits(salonId: string) {
  const { data, error } = await supabase
    .from('referral_benefits')
    .select('*')
    .eq('referred_salon_id', salonId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as ReferralBenefit[];
}

/**
 * Apply a referral code during registration
 * @returns İndirim referans veren kişiye (referrer) uygulanır, referans alan kişi (referred) 1 aylık deneme süresi kazanır
 */
export async function applyReferralCode(code: string, newSalonId: string) {
  // Referans kodunu bul
  const referralCode = await findReferralCode(code);
  if (!referralCode) throw new Error('Geçersiz referans kodu');

  // Referans veren salon ID'sini al
  const referrerSalonId = referralCode.salon_id;

  // Kullanım sayısını artır
  await incrementReferralCodeUses(referralCode.id);

  // En düşük plan ücretini al
  const { data: plans, error: plansError } = await supabase
    .from('subscription_plans')
    .select('price_monthly')
    .order('price_monthly', { ascending: true })
    .limit(1);

  if (plansError) throw plansError;

  const lowestPlanPrice = plans[0]?.price_monthly || 750; // Varsayılan olarak 750 TL

  // Referans faydası oluştur - Referans veren kişi (referrer) indirim kazanır
  const benefit: ReferralBenefitInsert = {
    referrer_salon_id: referrerSalonId,
    referred_salon_id: newSalonId,
    referral_code_id: referralCode.id,
    benefit_type: 'referrer_discount', // Referans veren kişi için indirim
    benefit_value: lowestPlanPrice.toString(), // Güncel en düşük plan ücreti
    discount_amount: lowestPlanPrice, // İndirim tutarı
    discount_applied: false,
    is_applied: false
  };

  const { data, error } = await supabase
    .from('referral_benefits')
    .insert(benefit)
    .select()
    .single();

  if (error) throw error;

  console.log(`Referral code ${code} applied for salon ${newSalonId}, benefit ID: ${data.id}`);
  console.log(`Referrer salon ${referrerSalonId} will receive a discount of ${lowestPlanPrice} TL`);
  console.log(`Referred salon ${newSalonId} will receive a 30-day trial period`);

  return data as ReferralBenefit;
}

/**
 * Apply a referral benefit
 */
export async function applyReferralBenefit(id: string) {
  const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD

  const { data, error } = await supabase
    .from('referral_benefits')
    .update({
      is_applied: true,
      applied_date: today
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralBenefit;
}

/**
 * Save a pending referral code for a user
 */
export async function savePendingReferral(userId: string, referralCode: string) {
  // Önce mevcut bekleyen referans kodunu kontrol et
  const { data: existingReferral, error: checkError } = await supabase
    .from('pending_referrals')
    .select('*')
    .eq('user_id', userId)
    .eq('is_applied', false)
    .single();

  if (checkError && checkError.code !== 'PGRST116') throw checkError;

  // Eğer zaten bekleyen bir referans kodu varsa, güncelle
  if (existingReferral) {
    const { data, error } = await supabase
      .from('pending_referrals')
      .update({ referral_code: referralCode })
      .eq('id', existingReferral.id)
      .select()
      .single();

    if (error) throw error;
    return data as PendingReferral;
  }

  // Yeni bir bekleyen referans kodu oluştur
  const pendingReferral: PendingReferralInsert = {
    user_id: userId,
    referral_code: referralCode,
    is_applied: false
  };

  const { data, error } = await supabase
    .from('pending_referrals')
    .insert(pendingReferral)
    .select()
    .single();

  if (error) throw error;
  return data as PendingReferral;
}

/**
 * Get pending referral for a user
 */
export async function getPendingReferral(userId: string) {
  const { data, error } = await supabase
    .from('pending_referrals')
    .select('*')
    .eq('user_id', userId)
    .eq('is_applied', false)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as PendingReferral | null;
}

/**
 * Mark a pending referral as applied
 */
export async function markPendingReferralAsApplied(id: string) {
  const { data, error } = await supabase
    .from('pending_referrals')
    .update({ is_applied: true })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as PendingReferral;
}

/**
 * Check if a salon has an available referral discount
 * @description Referans veren kişi (referrer) için indirim kontrolü yapar
 */
export async function checkReferralDiscount(salonId: string) {
  const { data, error } = await supabase.rpc('check_referral_discount', {
    salon_id: salonId
  });

  if (error) throw error;

  if (data && data.length > 0) {
    console.log(`Found referral discount for salon ${salonId}: ${JSON.stringify(data[0])}`);
    return data[0];
  }

  return null;
}

/**
 * Apply a referral discount to a payment
 */
export async function applyReferralDiscount(benefitId: string, paymentId: string) {
  const { data, error } = await supabase
    .from('referral_benefits')
    .update({
      discount_applied: true,
      discount_applied_payment_id: paymentId,
      is_applied: true,
      applied_date: new Date().toISOString().split('T')[0]
    })
    .eq('id', benefitId)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralBenefit;
}
