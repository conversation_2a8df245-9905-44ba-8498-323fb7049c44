"use client"

import { useState, useEffect } from "react"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"
import { RefreshCw, Users } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { TimeInput } from "@/components/ui/time-input"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Input } from "@/components/ui/input"
import { CopyToAllDaysDialog } from "@/components/ui/copy-to-all-days-dialog"
import { WorkingHoursCalendar } from "@/components/ui/working-hours-calendar"
import { useIsMobile } from "@/hooks/use-mobile"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import * as db from "@/lib/db"

const DAYS_OF_WEEK = [
  "Pazartesi",
  "Salı",
  "Çarşamba",
  "Perşembe",
  "Cuma",
  "Cumartesi",
  "Pazar",
]

// Define the form schema
const formSchema = z.object({
  barber_id: z.string({
    required_error: "Lütfen bir berber seçin",
  }),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_open`,
      z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
        message: "Lütfen geçerli bir saat girin (HH:MM)",
      }),
    ])
  ),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_close`,
      z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
        message: "Lütfen geçerli bir saat girin (HH:MM)",
      }),
    ])
  ),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_closed`,
      z.boolean().default(false),
    ])
  ),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_has_lunch_break`,
      z.boolean().default(false),
    ])
  ),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_lunch_start`,
      z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
        message: "Lütfen geçerli bir saat girin (HH:MM)",
      }).optional(),
    ])
  ),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_lunch_end`,
      z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
        message: "Lütfen geçerli bir saat girin (HH:MM)",
      }).optional(),
    ])
  ),
})

type FormValues = z.infer<typeof formSchema>

interface BarberWorkingHoursFormProps {
  salonId: string;
  salonWorkingHours?: db.WorkingHours[];
}

export function BarberWorkingHoursForm({ salonId, salonWorkingHours }: BarberWorkingHoursFormProps) {
  const [loading, setLoading] = useState(false)
  const [barbers, setBarbers] = useState<db.Barber[]>([])
  const [selectedBarber, setSelectedBarber] = useState<string | null>(null)
  const [isApplyAllDialogOpen, setIsApplyAllDialogOpen] = useState(false)
  const [workingHours, setWorkingHours] = useState<db.BarberWorkingHours[]>([])
  const isMobile = useIsMobile()

  // Initialize the form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      barber_id: "",
      monday_open: "09:00",
      monday_close: "18:00",
      monday_closed: false,
      monday_has_lunch_break: false,
      monday_lunch_start: "12:00",
      monday_lunch_end: "13:00",
      tuesday_open: "09:00",
      tuesday_close: "18:00",
      tuesday_closed: false,
      tuesday_has_lunch_break: false,
      tuesday_lunch_start: "12:00",
      tuesday_lunch_end: "13:00",
      wednesday_open: "09:00",
      wednesday_close: "18:00",
      wednesday_closed: false,
      wednesday_has_lunch_break: false,
      wednesday_lunch_start: "12:00",
      wednesday_lunch_end: "13:00",
      thursday_open: "09:00",
      thursday_close: "18:00",
      thursday_closed: false,
      thursday_has_lunch_break: false,
      thursday_lunch_start: "12:00",
      thursday_lunch_end: "13:00",
      friday_open: "09:00",
      friday_close: "18:00",
      friday_closed: false,
      friday_has_lunch_break: false,
      friday_lunch_start: "12:00",
      friday_lunch_end: "13:00",
      saturday_open: "10:00",
      saturday_close: "16:00",
      saturday_closed: false,
      saturday_has_lunch_break: false,
      saturday_lunch_start: "12:00",
      saturday_lunch_end: "13:00",
      sunday_open: "10:00",
      sunday_close: "16:00",
      sunday_closed: true,
      sunday_has_lunch_break: false,
      sunday_lunch_start: "12:00",
      sunday_lunch_end: "13:00",
    },
  })

  // Load barbers when the component mounts
  useEffect(() => {
    if (salonId) {
      loadBarbers()
    }
  }, [salonId])

  // Load barber working hours when a barber is selected
  useEffect(() => {
    if (selectedBarber) {
      loadBarberWorkingHours(selectedBarber)
    }
  }, [selectedBarber])

  // Load barbers from the database
  const loadBarbers = async () => {
    try {
      setLoading(true)
      const data = await db.barbers.getBarbers(salonId)
      setBarbers(data)
    } catch (error) {
      console.error("Error loading barbers:", error)
      toast.error("Berberler yüklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Load barber working hours from the database
  const loadBarberWorkingHours = async (barberId: string) => {
    try {
      setLoading(true)
      const data = await db.barberWorkingHours.getBarberWorkingHours(barberId)

      // Set the working hours state for the calendar view
      setWorkingHours(data)

      // Convert the data to form values
      const formValues: any = {
        barber_id: barberId,
      }

      // Initialize with default values
      DAYS_OF_WEEK.forEach((day, index) => {
        const dayOfWeek = (index + 1) % 7 // Convert to 0-6 format (0 = Sunday)
        const dayData = data.find(d => d.day_of_week === dayOfWeek)

        formValues[`${day.toLowerCase()}_open`] = dayData ? dayData.open_time.substring(0, 5) : "09:00"
        formValues[`${day.toLowerCase()}_close`] = dayData ? dayData.close_time.substring(0, 5) : "18:00"
        formValues[`${day.toLowerCase()}_closed`] = dayData ? dayData.is_closed : (dayOfWeek === 0) // Sunday is closed by default

        // Set lunch break values
        formValues[`${day.toLowerCase()}_has_lunch_break`] = dayData ? !!dayData.has_lunch_break : false
        formValues[`${day.toLowerCase()}_lunch_start`] = dayData && dayData.lunch_start_time ? dayData.lunch_start_time.substring(0, 5) : "12:00"
        formValues[`${day.toLowerCase()}_lunch_end`] = dayData && dayData.lunch_end_time ? dayData.lunch_end_time.substring(0, 5) : "13:00"
      })

      // Update the form
      form.reset(formValues)
    } catch (error) {
      console.error("Error loading barber working hours:", error)
      toast.error("Berber çalışma saatleri yüklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Handle copying working hours from one day to all days
  const handleCopyToAllDays = (sourceDay: string) => {
    const sourceDayLower = sourceDay.toLowerCase()
    const isClosed = form.getValues(`${sourceDayLower}_closed`)
    const openTime = form.getValues(`${sourceDayLower}_open`)
    const closeTime = form.getValues(`${sourceDayLower}_close`)
    const hasLunchBreak = form.getValues(`${sourceDayLower}_has_lunch_break`)
    const lunchStart = form.getValues(`${sourceDayLower}_lunch_start`)
    const lunchEnd = form.getValues(`${sourceDayLower}_lunch_end`)

    // Update the form with the new values for all days
    DAYS_OF_WEEK.forEach(day => {
      const dayLower = day.toLowerCase()
      if (dayLower !== sourceDayLower) {
        form.setValue(`${dayLower}_closed`, isClosed as boolean)
        form.setValue(`${dayLower}_open`, openTime as string)
        form.setValue(`${dayLower}_close`, closeTime as string)
        form.setValue(`${dayLower}_has_lunch_break`, hasLunchBreak as boolean)
        form.setValue(`${dayLower}_lunch_start`, lunchStart as string)
        form.setValue(`${dayLower}_lunch_end`, lunchEnd as string)
      }
    })

    toast.success(`${sourceDay} çalışma saatleri tüm günlere kopyalandı`)
  }

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    if (!values.barber_id) {
      toast.error("Lütfen bir berber seçin")
      return
    }

    try {
      setLoading(true)

      // Convert form values to barber working hours
      const workingHoursPromises = DAYS_OF_WEEK.map(async (day, index) => {
        const dayOfWeek = (index + 1) % 7 // Convert to 0-6 format (0 = Sunday)

        // Get existing working hours for this day
        const existingHours = await db.barberWorkingHours.getBarberWorkingHoursByDay(values.barber_id, dayOfWeek)

        if (existingHours) {
          // Update existing hours
          return db.barberWorkingHours.updateBarberWorkingHours({
            id: existingHours.id,
            is_closed: values[`${day.toLowerCase()}_closed` as keyof FormValues] as boolean,
            open_time: values[`${day.toLowerCase()}_open` as keyof FormValues] as string,
            close_time: values[`${day.toLowerCase()}_close` as keyof FormValues] as string,
            has_lunch_break: values[`${day.toLowerCase()}_has_lunch_break` as keyof FormValues] as boolean,
            lunch_start_time: values[`${day.toLowerCase()}_lunch_start` as keyof FormValues] as string,
            lunch_end_time: values[`${day.toLowerCase()}_lunch_end` as keyof FormValues] as string,
          })
        } else {
          // Create new hours
          return db.barberWorkingHours.createBarberWorkingHours({
            barber_id: values.barber_id,
            day_of_week: dayOfWeek,
            is_closed: values[`${day.toLowerCase()}_closed` as keyof FormValues] as boolean,
            open_time: values[`${day.toLowerCase()}_open` as keyof FormValues] as string,
            close_time: values[`${day.toLowerCase()}_close` as keyof FormValues] as string,
            has_lunch_break: values[`${day.toLowerCase()}_has_lunch_break` as keyof FormValues] as boolean,
            lunch_start_time: values[`${day.toLowerCase()}_lunch_start` as keyof FormValues] as string,
            lunch_end_time: values[`${day.toLowerCase()}_lunch_end` as keyof FormValues] as string,
          })
        }
      })

      await Promise.all(workingHoursPromises)
      toast.success("Berber çalışma saatleri başarıyla güncellendi")
    } catch (error) {
      console.error("Error updating barber working hours:", error)
      toast.error("Berber çalışma saatleri güncellenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Sync barber working hours with salon working hours
  const syncWithSalonHours = async () => {
    if (!selectedBarber || !salonWorkingHours || salonWorkingHours.length === 0) {
      toast.error("Salon çalışma saatleri bulunamadı")
      return
    }

    try {
      setLoading(true)
      await db.barberWorkingHours.setupDefaultBarberWorkingHours(selectedBarber, salonId)
      toast.success("Berber çalışma saatleri salon saatleri ile senkronize edildi")

      // Reload barber working hours
      await loadBarberWorkingHours(selectedBarber)
    } catch (error) {
      console.error("Error syncing barber working hours:", error)
      toast.error("Berber çalışma saatleri senkronize edilirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Apply salon working hours to all barbers
  const applyToAllBarbers = async () => {
    if (!salonWorkingHours || salonWorkingHours.length === 0) {
      toast.error("Salon çalışma saatleri bulunamadı")
      return
    }

    try {
      setLoading(true)
      const result = await db.barberWorkingHours.applyWorkingHoursToAllBarbers(salonId)
      toast.success(result.message)

      // If a barber is selected, reload their working hours
      if (selectedBarber) {
        await loadBarberWorkingHours(selectedBarber)
      }

      // Close the dialog
      setIsApplyAllDialogOpen(false)
    } catch (error) {
      console.error("Error applying working hours to all barbers:", error)
      toast.error("Çalışma saatleri tüm berberlere uygulanırken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }



  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <Card className="w-full md:w-2/3">
          <CardHeader>
            <CardTitle>Berber Çalışma Saatleri</CardTitle>
            <CardDescription>
              Seçilen berberin çalışma saatlerini düzenleyin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <div className="flex flex-col md:flex-row gap-4 items-start md:items-end">
                <div className="w-full md:w-1/2">
                  <FormField
                    control={form.control}
                    name="barber_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Berber</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value)
                            setSelectedBarber(value)
                          }}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Berber seçin" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {barbers.map((barber) => (
                              <SelectItem key={barber.id} value={barber.id}>
                                {barber.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                {selectedBarber && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={syncWithSalonHours}
                    disabled={loading || !selectedBarber}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Salon Saatleri ile Senkronize Et
                  </Button>
                )}
              </div>

              {selectedBarber && workingHours.length > 0 && (
                <div className="mt-4">
                  <WorkingHoursCalendar workingHours={workingHours} />
                </div>
              )}

              {selectedBarber && (
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-sm font-medium">Günlük Çalışma Saatleri</h3>
                    <CopyToAllDaysDialog days={DAYS_OF_WEEK} onCopy={(day) => handleCopyToAllDays(day)} />
                  </div>
                  {DAYS_OF_WEEK.map((day) => (
                    <div key={day} className="grid grid-cols-1 md:grid-cols-[200px_1fr] gap-4 items-center p-3 rounded-lg hover:bg-muted/30 transition-colors">
                      <div className="font-medium flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className={`w-3 h-3 rounded-full mr-2 ${form.watch(`${day.toLowerCase()}_closed` as any) ? 'bg-red-500' : 'bg-green-500'}`} />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{form.watch(`${day.toLowerCase()}_closed` as any) ? 'Kapalı' : 'Açık'}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        {day}
                      </div>
                      <div className="space-y-4">
                        <div className={`grid gap-4 items-start ${isMobile ? 'grid-cols-1' : 'sm:grid-cols-[auto_1fr_1fr] sm:items-center'}`}>
                          <FormField
                            control={form.control}
                            name={`${day.toLowerCase()}_closed` as any}
                            render={({ field }) => (
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal cursor-pointer">
                                  Kapalı
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name={`${day.toLowerCase()}_open` as any}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-xs">Açılış</FormLabel>
                                <FormControl>
                                  <TimeInput
                                    value={field.value as string}
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    disabled={form.watch(`${day.toLowerCase()}_closed` as any)}
                                    placeholder="09:00"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name={`${day.toLowerCase()}_close` as any}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-xs">Kapanış</FormLabel>
                                <FormControl>
                                  <TimeInput
                                    value={field.value as string}
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    disabled={form.watch(`${day.toLowerCase()}_closed` as any)}
                                    placeholder="18:00"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="border-t pt-4">
                          <FormField
                            control={form.control}
                            name={`${day.toLowerCase()}_has_lunch_break` as any}
                            render={({ field }) => (
                              <FormItem className="flex items-center space-x-2 space-y-0 mb-4">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                    disabled={form.watch(`${day.toLowerCase()}_closed` as any)}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal cursor-pointer">
                                  Öğle Arası Var
                                </FormLabel>
                              </FormItem>
                            )}
                          />

                          {form.watch(`${day.toLowerCase()}_has_lunch_break` as any) && (
                            <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'sm:grid-cols-2'}`}>
                              <FormField
                                control={form.control}
                                name={`${day.toLowerCase()}_lunch_start` as any}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-xs">Öğle Arası Başlangıç</FormLabel>
                                    <FormControl>
                                      <TimeInput
                                        value={field.value as string}
                                        onChange={field.onChange}
                                        onBlur={field.onBlur}
                                        name={field.name}
                                        disabled={form.watch(`${day.toLowerCase()}_closed` as any)}
                                        placeholder="12:00"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`${day.toLowerCase()}_lunch_end` as any}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel className="text-xs">Öğle Arası Bitiş</FormLabel>
                                    <FormControl>
                                      <TimeInput
                                        value={field.value as string}
                                        onChange={field.onChange}
                                        onBlur={field.onBlur}
                                        name={field.name}
                                        disabled={form.watch(`${day.toLowerCase()}_closed` as any)}
                                        placeholder="13:00"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="flex justify-end">
                    <Button type="submit" disabled={loading}>
                      {loading ? "Kaydediliyor..." : "Kaydet"}
                    </Button>
                  </div>
                </form>
              )}
            </Form>
          </CardContent>
        </Card>

        <Card className="w-full md:w-1/3">
          <CardHeader>
            <CardTitle>Toplu İşlemler</CardTitle>
            <CardDescription>
              Çalışma saatlerini toplu olarak yönetin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">


            <div>
              <h3 className="text-sm font-medium mb-2">Tüm Berberlere Uygula</h3>
              <p className="text-sm text-muted-foreground mb-2">
                Salon çalışma saatlerini tüm berberlere uygulayın
              </p>
              <AlertDialog open={isApplyAllDialogOpen} onOpenChange={setIsApplyAllDialogOpen}>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="w-full">
                    <Users className="mr-2 h-4 w-4" />
                    Tüm Berberlere Uygula
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Tüm Berberlere Uygula</AlertDialogTitle>
                    <AlertDialogDescription>
                      Bu işlem, salon çalışma saatlerini tüm berberlere uygulayacaktır. Mevcut berber çalışma saatleri üzerine yazılacaktır. Bu işlemi yapmak istediğinizden emin misiniz?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction onClick={applyToAllBarbers} disabled={loading}>
                      {loading ? "Uygulanıyor..." : "Uygula"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
