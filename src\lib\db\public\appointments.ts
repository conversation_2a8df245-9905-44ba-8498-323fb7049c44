import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * <PERSON><PERSON> doğrulaması olmayan kullanıcılar için randevu bilgilerini salon ID ve tarih aralığı ile getir
 */
export async function getPublicAppointmentsBySalonId(salonId: string, startDate: string, endDate: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_appointments_by_salon_id', { 
      p_salon_id: salonId,
      p_start_date: startDate,
      p_end_date: endDate
    });

  if (error) throw error;
  return data;
}

/**
 * <PERSON><PERSON> doğrulaması olmayan kullanıcılar için randevu oluştur
 */
export async function createPublicAppointment(
  salonId: string,
  barberId: string,
  serviceId: string,
  date: string,
  startTime: string,
  endTime: string,
  fullname: string,
  phonenumber: string,
  email: string
) {
  const { data, error } = await supabaseClient
    .rpc('create_public_appointment', {
      p_salon_id: salonId,
      p_barber_id: barberId,
      p_service_id: serviceId,
      p_date: date,
      p_start_time: startTime,
      p_end_time: endTime,
      p_fullname: fullname,
      p_phonenumber: phonenumber,
      p_email: email
    });

  if (error) throw error;
  return data;
}
