// <PERSON>ript to run all subscription-related tests
require('dotenv').config({ path: '.env.local' });
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Define test files to run
const testFiles = [
  'hooks/useSubscriptionFeatures.test.js',
  'api/subscription-api.test.js',
  'middleware/subscription-middleware.test.js'
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

// Function to run a test file
async function runTest(testFile) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.bright}${colors.fg.cyan}Running test: ${testFile}${colors.reset}`);
    
    const testPath = path.join(__dirname, testFile);
    
    // Check if file exists
    if (!fs.existsSync(testPath)) {
      console.error(`${colors.fg.red}Error: Test file not found: ${testPath}${colors.reset}`);
      reject(new Error(`Test file not found: ${testPath}`));
      return;
    }
    
    // Run the test using Node.js
    const child = spawn('node', [testPath], {
      stdio: 'inherit'
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`${colors.fg.green}✓ Test passed: ${testFile}${colors.reset}`);
        resolve();
      } else {
        console.error(`${colors.fg.red}✗ Test failed: ${testFile} (exit code: ${code})${colors.reset}`);
        reject(new Error(`Test failed with exit code ${code}`));
      }
    });
    
    child.on('error', (err) => {
      console.error(`${colors.fg.red}Error running test: ${err.message}${colors.reset}`);
      reject(err);
    });
  });
}

// Run all tests
async function runAllTests() {
  console.log(`${colors.bright}${colors.fg.yellow}=== Running Subscription Tests ====${colors.reset}`);
  console.log(`${colors.dim}Test files: ${testFiles.length}${colors.reset}`);
  
  let passed = 0;
  let failed = 0;
  const failedTests = [];
  
  for (const testFile of testFiles) {
    try {
      await runTest(testFile);
      passed++;
    } catch (error) {
      failed++;
      failedTests.push({ file: testFile, error });
    }
    
    // Add a separator between tests
    console.log(`${colors.dim}----------------------------------------${colors.reset}`);
  }
  
  // Print summary
  console.log(`${colors.bright}${colors.fg.yellow}=== Test Summary ====${colors.reset}`);
  console.log(`${colors.fg.green}Passed: ${passed}${colors.reset}`);
  console.log(`${colors.fg.red}Failed: ${failed}${colors.reset}`);
  
  if (failed > 0) {
    console.log(`${colors.bright}${colors.fg.red}=== Failed Tests ====${colors.reset}`);
    failedTests.forEach((test, index) => {
      console.log(`${colors.fg.red}${index + 1}. ${test.file}: ${test.error.message}${colors.reset}`);
    });
    
    process.exit(1);
  } else {
    console.log(`${colors.bright}${colors.fg.green}All tests passed!${colors.reset}`);
    process.exit(0);
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error(`${colors.fg.red}Unexpected error: ${error.message}${colors.reset}`);
  process.exit(1);
});
