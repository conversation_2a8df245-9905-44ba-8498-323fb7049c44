"use client"

import React, { createContext, useContext, useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, Calendar, Clock, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ClientBookingForm } from "@/components/client/client-booking-form"

interface BookingModalContextType {
  isOpen: boolean
  openModal: () => void
  closeModal: () => void
}

const BookingModalContext = createContext<BookingModalContextType | undefined>(undefined)

export function useBookingModal() {
  const context = useContext(BookingModalContext)
  if (context === undefined) {
    throw new Error('useBookingModal must be used within a BookingModalProvider')
  }
  return context
}

// Safe version that returns null if not available
export function useBookingModalSafe() {
  const context = useContext(BookingModalContext)
  return context || null
}

interface BookingModalProviderProps {
  children: React.ReactNode
  salonId: string
}

export function BookingModalProvider({ children, salonId }: BookingModalProviderProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)
  const [formKey, setFormKey] = useState(0)

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  const openModal = () => {
    setIsOpen(true)
    setBookingSuccess(false)
    setFormKey(prev => prev + 1)
  }

  const closeModal = () => {
    setIsOpen(false)
    // Reset states when closing
    setTimeout(() => {
      setBookingSuccess(false)
      setFormKey(prev => prev + 1)
    }, 300)
  }

  const handleBookingSuccess = () => {
    setBookingSuccess(true)
  }

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        closeModal()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen])

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Sadece backdrop'a tıklandığında kapat
    if (e.target === e.currentTarget) {
      closeModal()
    }
  }

  return (
    <BookingModalContext.Provider value={{ isOpen, openModal, closeModal }}>
      {children}

      {/* Global Modal */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop Layer - Separate from content container */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 z-[9998] bg-black/60 backdrop-blur-sm"
              onClick={closeModal}
            />

            {/* Modal Container - Pure centering layer */}
            <div
              className="fixed inset-0 z-[9999] flex items-center justify-center p-0 pointer-events-none"
              onClick={handleBackdropClick}
            >
              {/* Modal Content */}
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="relative w-full max-w-2xl max-h-[90vh] sm:max-h-[85vh] bg-background rounded-2xl shadow-2xl overflow-hidden pointer-events-auto mx-4 sm:mx-6"
                style={{
                  maxWidth: 'min(640px, calc(100vw - 2rem))',
                  maxHeight: 'min(90vh, calc(100vh - 2rem))'
                }}
                onClick={(e) => e.stopPropagation()}
              >
              {/* Header */}
              <div className="relative bg-gradient-to-r from-primary/10 to-primary/5 px-6 py-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Calendar className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-foreground">
                        {bookingSuccess ? "Randevu Onaylandı!" : "Randevu Oluştur"}
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        {bookingSuccess
                          ? "Randevunuz başarıyla oluşturuldu"
                          : "Hızlı ve kolay randevu sistemi"
                        }
                      </p>
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={closeModal}
                    className="rounded-full hover:bg-background/80"
                  >
                    <X className="w-5 h-5" />
                  </Button>
                </div>
              </div>

              {/* Content */}
              <div className="overflow-y-auto max-h-[calc(90vh-80px)] sm:max-h-[calc(85vh-80px)]">
                <AnimatePresence mode="wait">
                  {bookingSuccess ? (
                    <motion.div
                      key="success"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.4 }}
                      className="p-8 text-center space-y-6"
                    >
                      <div className="flex justify-center">
                        <div className="p-4 bg-green-100 dark:bg-green-900/20 rounded-full">
                          <CheckCircle className="w-12 h-12 text-green-600 dark:text-green-400" />
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h3 className="text-2xl font-bold text-foreground">
                          Harika! Randevunuz Alındı
                        </h3>
                        <p className="text-muted-foreground max-w-md mx-auto">
                          Randevunuz başarıyla oluşturuldu. Size SMS ve e-posta ile onay gönderilecektir.
                        </p>
                      </div>

                      <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                        <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                          <Clock className="w-4 h-4" />
                          <span>Randevunuza 15 dakika önce hatırlatma yapılacaktır</span>
                        </div>
                        <p className="text-xs text-muted-foreground/70 italic">
                          Randevunuza gelemeyecekseniz lütfen önceden haber veriniz.
                        </p>
                      </div>

                      <Button
                        onClick={closeModal}
                        size="lg"
                        className="w-full sm:w-auto px-8 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                      >
                        Tamam
                      </Button>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="form"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ClientBookingForm
                        key={formKey}
                        salonId={salonId}
                        onSuccess={handleBookingSuccess}
                      />
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
            </div>
          </>
        )}
      </AnimatePresence>
    </BookingModalContext.Provider>
  )
}
