-- Notifications Type Check Constraint Güncelleme
-- Tarih: 2024-08-02

-- 1. Mevcut notifications_type_check kıs<PERSON>tlamasını kaldır
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS notifications_type_check;

-- 2. <PERSON><PERSON> k<PERSON>kle (subscription_reminder ve payment_reminder değerlerini de kabul edecek şekilde)
ALTER TABLE notifications ADD CONSTRAINT notifications_type_check 
  CHECK (type IN ('new_booking', 'cancellation', 'update', 'subscription_reminder', 'payment_reminder'));

-- 3. Notifications tablosunun yapısını kontrol et
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'notifications';

-- 4. Notifications tablosundaki kısıtlamaları kontrol et
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'notifications'::regclass;

-- 5. Test bildirim ekleme
DO $$
DECLARE
  test_salon_id UUID := '00000000-0000-0000-0000-000000000001';
  test_user_id UUID := '00000000-0000-0000-0000-000000000002';
BEGIN
  -- Test bildirimini ekle
  INSERT INTO notifications (
    salon_id, 
    user_id, 
    type, 
    title, 
    message, 
    read, 
    data
  ) VALUES (
    test_salon_id, 
    test_user_id, 
    'subscription_reminder', 
    'Test Bildirim', 
    'Bu bir test bildirimidir.', 
    false, 
    jsonb_build_object('test', true)
  );
  
  -- Test bildirimini sil
  DELETE FROM notifications 
  WHERE salon_id = test_salon_id AND user_id = test_user_id AND title = 'Test Bildirim';
  
  RAISE NOTICE 'Test başarılı: Notifications tablosuna subscription_reminder tipinde bildirim eklenebiliyor.';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Test başarısız: %', SQLERRM;
END;
$$;

-- 6. Subscription notification trigger fonksiyonunu güncelle
CREATE OR REPLACE FUNCTION create_subscription_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  notification_title TEXT;
  notification_message TEXT;
  notification_type TEXT;
BEGIN
  -- Salon sahibini bul
  SELECT owner_id INTO salon_owner_id
  FROM salons
  WHERE id = NEW.salon_id;
  
  -- Bildirim içeriğini belirle
  IF TG_OP = 'INSERT' THEN
    -- Yeni abonelik oluşturulduğunda
    IF NEW.status = 'trial' THEN
      notification_title := 'Deneme Aboneliği Başladı';
      notification_message := 'Deneme aboneliğiniz başarıyla oluşturuldu. Deneme süreniz ' || NEW.trial_end_date || ' tarihinde sona erecek.';
      notification_type := 'subscription_reminder';
    ELSIF NEW.status = 'active' THEN
      notification_title := 'Aboneliğiniz Aktifleştirildi';
      notification_message := 'Aboneliğiniz başarıyla aktifleştirildi. Teşekkür ederiz!';
      notification_type := 'subscription_reminder';
    END IF;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Abonelik durumu değiştiğinde
    IF OLD.status != NEW.status THEN
      IF NEW.status = 'active' THEN
        notification_title := 'Aboneliğiniz Aktifleştirildi';
        notification_message := 'Aboneliğiniz başarıyla aktifleştirildi. Teşekkür ederiz!';
        notification_type := 'subscription_reminder';
      ELSIF NEW.status = 'past_due' THEN
        notification_title := 'Ödeme Hatırlatması';
        notification_message := 'Abonelik ödemeniz gecikti. Lütfen en kısa sürede ödemenizi yapın.';
        notification_type := 'payment_reminder';
      ELSIF NEW.status = 'suspended' THEN
        notification_title := 'Aboneliğiniz Askıya Alındı';
        notification_message := 'Ödeme yapılmadığı için aboneliğiniz askıya alındı. Hizmetlerinize tekrar erişmek için lütfen ödemenizi yapın.';
        notification_type := 'payment_reminder';
      ELSIF NEW.status = 'cancelled' THEN
        notification_title := 'Aboneliğiniz İptal Edildi';
        notification_message := 'Aboneliğiniz iptal edildi. Tekrar abone olmak için lütfen bizimle iletişime geçin.';
        notification_type := 'subscription_reminder';
      END IF;
    -- Plan değiştiğinde
    ELSIF OLD.plan_id != NEW.plan_id THEN
      notification_title := 'Abonelik Planınız Güncellendi';
      notification_message := 'Abonelik planınız başarıyla güncellendi.';
      notification_type := 'subscription_reminder';
    END IF;
  END IF;
  
  -- Bildirim oluştur
  IF notification_title IS NOT NULL THEN
    INSERT INTO notifications (
      salon_id,
      user_id,
      type,
      title,
      message,
      read,
      data
    ) VALUES (
      NEW.salon_id,
      salon_owner_id,
      notification_type,
      notification_title,
      notification_message,
      FALSE,
      jsonb_build_object('subscription_id', NEW.id, 'status', NEW.status)
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
