"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Co<PERSON>, Share, Users, Gift, CheckCircle, Clock, AlertCircle, AlertTriangle } from "lucide-react"
import { toast } from "sonner"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

import { useUser } from "@/contexts/UserContext"
import { useSubscription } from "@/contexts/SubscriptionContext"
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"
import * as db from "@/lib/db"
import { ReferralBenefit, ReferralCode } from "@/lib/db/types"

export default function ReferralsPage() {
  const router = useRouter()
  const { salon } = useUser()
  const { subscription } = useSubscription()
  const salonId = salon?.id
  const [referralCode, setReferralCode] = useState<ReferralCode | null>(null)
  const [referrerBenefits, setReferrerBenefits] = useState<ReferralBenefit[]>([])
  const [referredBenefits, setReferredBenefits] = useState<ReferralBenefit[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)

  // Deneme sürecinde olup olmadığını kontrol et
  const isInTrialPeriod = subscription?.status === 'trial'

  // Referral URL
  const referralUrl = typeof window !== 'undefined'
    ? `${window.location.origin}/auth/register?ref=${referralCode?.code}`
    : '';

  // Load referral data
  useEffect(() => {
    async function loadReferralData() {
      if (!salonId) return;

      try {
        setIsLoading(true);

        // Get referral code
        const code = await db.referrals.getSalonReferralCode(salonId);
        setReferralCode(code);

        // Get benefits
        if (code) {
          const [referrerData, referredData] = await Promise.all([
            db.referrals.getReferrerBenefits(salonId),
            db.referrals.getReferredBenefits(salonId)
          ]);

          setReferrerBenefits(referrerData);
          setReferredBenefits(referredData);
        }
      } catch (error) {
        console.error("Referans verileri yüklenirken hata:", error);
        toast.error("Referans verileri yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    }

    loadReferralData();
  }, [salonId]);

  // Create referral code
  const handleCreateReferralCode = async () => {
    if (!salonId) return;

    // Deneme sürecindeki kullanıcılar referans kodu oluşturamaz
    if (isInTrialPeriod) {
      toast.error("Deneme sürecindeki salonlar referans kodu oluşturamaz. Lütfen abonelik planınızı aktifleştirin.");
      return;
    }

    try {
      setIsCreating(true);
      const newCode = await db.referrals.createReferralCode(salonId);
      setReferralCode(newCode);
      toast.success("Referans kodu başarıyla oluşturuldu!");
    } catch (error) {
      console.error("Referans kodu oluşturulurken hata:", error);
      toast.error(error instanceof Error ? error.message : "Referans kodu oluşturulurken bir hata oluştu.");
    } finally {
      setIsCreating(false);
    }
  };

  // Copy referral code to clipboard
  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast.success(message);
      },
      () => {
        toast.error("Kopyalama işlemi başarısız oldu.");
      }
    );
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Beklemede</Badge>;
      case 'applied':
        return <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Uygulandı</Badge>;
      case 'expired':
        return <Badge variant="destructive" className="flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Süresi Doldu</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Referans Programı</h1>
        </div>
      </header>

      {isLoading ? (
        <div className="space-y-6">
          <Skeleton className="h-[200px] w-full" />
          <Skeleton className="h-[300px] w-full" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Referral Code Card */}
          <Card>
            <CardHeader>
              <CardTitle>Referans Kodunuz</CardTitle>
              <CardDescription>
                Arkadaşlarınızı davet edin, siz indirim kazanın, onlar da uzun deneme süresi kazansın
              </CardDescription>
            </CardHeader>
            <CardContent>
              {referralCode ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                    <div className="font-mono text-lg font-bold">{referralCode.code}</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(referralCode.code, "Referans kodu kopyalandı!")}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Kopyala
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                    <div className="text-sm truncate flex-1 mr-2">{referralUrl}</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(referralUrl, "Referans bağlantısı kopyalandı!")}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Kopyala
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-muted-foreground" />
                    <span>Toplam Kullanım: <strong>{referralCode.uses}</strong></span>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-6 space-y-4">
                  <p className="text-muted-foreground">Henüz bir referans kodunuz bulunmuyor.</p>

                  {isInTrialPeriod ? (
                    <div className="space-y-4 w-full">
                      <Alert variant="warning" className="bg-amber-500/10 border-amber-500/50">
                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                        <AlertTitle>Deneme Süreci Kısıtlaması</AlertTitle>
                        <AlertDescription>
                          Deneme sürecindeki salonlar referans kodu oluşturamaz. Referans kodu oluşturmak için abonelik planınızı aktifleştirmeniz gerekmektedir.
                        </AlertDescription>
                      </Alert>

                      <Button
                        asChild
                        className="w-full"
                      >
                        <Link href="/dashboard/subscription">
                          Abonelik Planını Aktifleştir
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <Button
                      onClick={handleCreateReferralCode}
                      disabled={isCreating}
                    >
                      {isCreating ? "Oluşturuluyor..." : "Referans Kodu Oluştur"}
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col items-start space-y-2">
              <p className="text-sm text-muted-foreground">
                Referans kodunuzu kullanarak kaydolan arkadaşlarınız 30 günlük uzun deneme süresi kazanır, siz ise ilk ödemenizde güncel en düşük paket ücreti kadar indirim kazanırsınız. Referans kodunuzu paylaşarak arkadaşlarınızın SalonFlow'a kaydolmasını sağlayın.
              </p>
            </CardFooter>
          </Card>

          {/* Referral Benefits */}
          <Card>
            <CardHeader>
              <CardTitle>Referans İndirimleri</CardTitle>
              <CardDescription>
                Referans kodunuzla kaydolan kullanıcılardan kazandığınız indirimler
              </CardDescription>
            </CardHeader>
            <CardContent>
              {referrerBenefits.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tarih</TableHead>
                      <TableHead>İndirim Tutarı</TableHead>
                      <TableHead>Kullanıldı mı?</TableHead>
                      <TableHead>Kullanım Tarihi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {referrerBenefits.map((benefit) => (
                      <TableRow key={benefit.id}>
                        <TableCell>{formatDate(benefit.created_at)}</TableCell>
                        <TableCell>{benefit.discount_amount || benefit.benefit_value} ₺</TableCell>
                        <TableCell>
                          {benefit.discount_applied ?
                            <Badge variant="success" className="flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Evet</Badge> :
                            <Badge variant="outline" className="flex items-center gap-1"><Clock className="h-3 w-3" /> Hayır</Badge>
                          }
                        </TableCell>
                        <TableCell>
                          {benefit.applied_date ? formatDate(benefit.applied_date) : '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center py-6">
                  <p className="text-muted-foreground">Henüz referans kodunuzla kaydolan ve size indirim kazandıran kullanıcı bulunmuyor.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
