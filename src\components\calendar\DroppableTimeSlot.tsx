"use client"

import { useDroppable } from "@dnd-kit/core"
import { cn } from "@/lib/utils"

interface DroppableTimeSlotProps {
  id: string
  date: Date
  time?: string
  isAvailable: boolean
  unavailableReason?: string
  children?: React.ReactNode
  className?: string
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void
}

export function DroppableTimeSlot({
  id,
  date,
  time,
  isAvailable,
  unavailableReason,
  children,
  className,
  onClick
}: DroppableTimeSlotProps) {
  const {
    isOver,
    setNodeRef
  } = useDroppable({
    id,
    data: {
      date,
      time,
      isAvailable,
      type: 'timeSlot'
    },
    disabled: !isAvailable
  })

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "p-3 rounded-md border transition-colors min-h-[80px]",
        isAvailable ? "hover:bg-muted/50" : "bg-muted/30 opacity-60",
        isOver && isAvailable && "bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700",
        className
      )}
      title={!isAvailable ? unavailableReason : undefined}
      onClick={onClick}
    >
      {children}
    </div>
  )
}
