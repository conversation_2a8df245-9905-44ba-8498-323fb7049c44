-- Content Management System (CMS) Database Schema
-- Date: 2025-05-23-22-14-25
-- Description: Tables for managing salon landing page content

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- SALON CONTENT TABLE
-- =====================================================
-- Stores all customizable content for salon landing pages
-- Uses flexible key-value structure for different content types
CREATE TABLE public.salon_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES public.salons(id) ON DELETE CASCADE,
    section TEXT NOT NULL, -- 'hero', 'about', 'services', 'contact'
    content_key TEXT NOT NULL,
    content_value TEXT,
    content_type TEXT DEFAULT 'text' CHECK (content_type IN ('text', 'number', 'boolean', 'json')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_salon_content UNIQUE(salon_id, section, content_key)
);

-- Add indexes for better performance
CREATE INDEX idx_salon_content_salon_id ON public.salon_content(salon_id);
CREATE INDEX idx_salon_content_section ON public.salon_content(salon_id, section);

-- =====================================================
-- SALON TESTIMONIALS TABLE
-- =====================================================
-- Stores customer testimonials for each salon
CREATE TABLE public.salon_testimonials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES public.salons(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    service_name TEXT,
    date_text TEXT, -- e.g., "2 hafta önce", "1 ay önce"
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX idx_salon_testimonials_salon_id ON public.salon_testimonials(salon_id);
CREATE INDEX idx_salon_testimonials_active ON public.salon_testimonials(salon_id, is_active, display_order);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on salon_content table
ALTER TABLE public.salon_content ENABLE ROW LEVEL SECURITY;

-- Policy for salon_content: Users can only access content for their own salon
CREATE POLICY "Users can view their salon content" ON public.salon_content
    FOR SELECT USING (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
        OR 
        salon_id IN (
            SELECT b.salon_id FROM public.barbers b 
            WHERE b.user_id = auth.uid()
        )
    );

CREATE POLICY "Salon owners can insert their salon content" ON public.salon_content
    FOR INSERT WITH CHECK (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
    );

CREATE POLICY "Salon owners can update their salon content" ON public.salon_content
    FOR UPDATE USING (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
    );

CREATE POLICY "Salon owners can delete their salon content" ON public.salon_content
    FOR DELETE USING (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
    );

-- Enable RLS on salon_testimonials table
ALTER TABLE public.salon_testimonials ENABLE ROW LEVEL SECURITY;

-- Policy for salon_testimonials: Users can only access testimonials for their own salon
CREATE POLICY "Users can view their salon testimonials" ON public.salon_testimonials
    FOR SELECT USING (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
        OR 
        salon_id IN (
            SELECT b.salon_id FROM public.barbers b 
            WHERE b.user_id = auth.uid()
        )
    );

CREATE POLICY "Salon owners can insert their salon testimonials" ON public.salon_testimonials
    FOR INSERT WITH CHECK (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
    );

CREATE POLICY "Salon owners can update their salon testimonials" ON public.salon_testimonials
    FOR UPDATE USING (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
    );

CREATE POLICY "Salon owners can delete their salon testimonials" ON public.salon_testimonials
    FOR DELETE USING (
        salon_id IN (
            SELECT s.id FROM public.salons s 
            WHERE s.owner_id = auth.uid()
        )
    );

-- =====================================================
-- PUBLIC ACCESS POLICIES (for customer-facing pages)
-- =====================================================

-- Allow public read access to active testimonials
CREATE POLICY "Public can view active testimonials" ON public.salon_testimonials
    FOR SELECT USING (is_active = true);

-- Allow public read access to salon content
CREATE POLICY "Public can view salon content" ON public.salon_content
    FOR SELECT USING (true);

-- =====================================================
-- ADMIN POLICIES
-- =====================================================

-- Admin can access all content (using is_admin function)
CREATE POLICY "Admin can access all salon content" ON public.salon_content
    FOR ALL USING (is_admin());

CREATE POLICY "Admin can access all salon testimonials" ON public.salon_testimonials
    FOR ALL USING (is_admin());

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for salon_content
CREATE TRIGGER update_salon_content_updated_at 
    BEFORE UPDATE ON public.salon_content 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger for salon_testimonials
CREATE TRIGGER update_salon_testimonials_updated_at 
    BEFORE UPDATE ON public.salon_testimonials 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- DEFAULT CONTENT INSERTION FUNCTION
-- =====================================================

-- Function to insert default content for a new salon
CREATE OR REPLACE FUNCTION insert_default_salon_content(p_salon_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Insert default hero section content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (p_salon_id, 'hero', 'badge_text', 'Profesyonel Berber Hizmetleri', 'text'),
    (p_salon_id, 'hero', 'tagline', 'ile tarzını yansıt.', 'text'),
    (p_salon_id, 'hero', 'description', 'Uzman ekibimizle kaliteli hizmet garantisi. Modern teknikler ve kişisel yaklaşımla tarzınızı yenileyin.', 'text'),
    (p_salon_id, 'hero', 'cta_primary', 'Randevu Al', 'text'),
    (p_salon_id, 'hero', 'cta_secondary', 'Hizmetlerimizi Keşfet', 'text'),
    (p_salon_id, 'hero', 'stats_customers', '500+', 'text'),
    (p_salon_id, 'hero', 'stats_customers_label', 'Mutlu Müşteri', 'text'),
    (p_salon_id, 'hero', 'stats_experience', '5+', 'text'),
    (p_salon_id, 'hero', 'stats_experience_label', 'Yıl Deneyim', 'text'),
    (p_salon_id, 'hero', 'stats_rating', '4.9', 'text'),
    (p_salon_id, 'hero', 'stats_rating_label', 'Müşteri Puanı', 'text'),
    (p_salon_id, 'hero', 'stats_support', '24/7', 'text'),
    (p_salon_id, 'hero', 'stats_support_label', 'Online Randevu', 'text');

    -- Insert default about section content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (p_salon_id, 'about', 'badge_text', 'Hakkımızda', 'text'),
    (p_salon_id, 'about', 'title', 'Neden Bizi Tercih Etmelisiniz?', 'text'),
    (p_salon_id, 'about', 'description', 'Yılların deneyimi ve modern yaklaşımımızla, her müşterimize özel hizmet sunuyoruz. Kaliteli ürünler ve uzman ekibimizle tarzınızı en iyi şekilde yansıtmanızı sağlıyoruz.', 'text'),
    (p_salon_id, 'about', 'features', '[{"title":"Uzman Ekip","description":"Alanında deneyimli ve sertifikalı berberlerimiz"},{"title":"Esnek Saatler","description":"Size uygun saatlerde randevu imkanı"},{"title":"Müşteri Memnuniyeti","description":"Her müşterimize özel ilgi ve kaliteli hizmet"},{"title":"Modern Yaklaşım","description":"Güncel trendler ve tekniklerle hizmet"}]', 'json'),
    (p_salon_id, 'about', 'stats', '[{"number":"500+","label":"Mutlu Müşteri"},{"number":"5+","label":"Yıl Deneyim"},{"number":"4.9","label":"Müşteri Puanı"},{"number":"24/7","label":"Online Destek"}]', 'json');

    -- Insert default services section content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (p_salon_id, 'services', 'badge_text', 'Hizmetlerimiz', 'text'),
    (p_salon_id, 'services', 'title', 'Profesyonel Hizmetlerimiz', 'text'),
    (p_salon_id, 'services', 'description', 'Uzman ekibimizle sunduğumuz kaliteli hizmetler', 'text'),
    (p_salon_id, 'services', 'cta_title', 'Özel İhtiyaçlarınız mı Var?', 'text'),
    (p_salon_id, 'services', 'cta_description', 'Listelenen hizmetler dışında özel talepleriniz için bizimle iletişime geçin. Size özel çözümler sunmaktan mutluluk duyarız.', 'text'),
    (p_salon_id, 'services', 'cta_button', 'İletişime Geç', 'text');

    -- Insert default contact section content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (p_salon_id, 'contact', 'badge_text', 'İletişim', 'text'),
    (p_salon_id, 'contact', 'title', 'Bizimle İletişime Geçin', 'text'),
    (p_salon_id, 'contact', 'description', 'Sorularınız için bizimle iletişime geçebilirsiniz', 'text'),
    (p_salon_id, 'contact', 'cta_title', 'Hemen Randevu Alın', 'text'),
    (p_salon_id, 'contact', 'cta_description', 'Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz.', 'text'),
    (p_salon_id, 'contact', 'cta_button', 'Randevu Al', 'text');

    -- Insert default testimonials
    INSERT INTO public.salon_testimonials (salon_id, customer_name, rating, comment, service_name, date_text, display_order) VALUES
    (p_salon_id, 'Ahmet Yılmaz', 5, 'Harika bir deneyim yaşadım. Berberim çok profesyoneldi ve istediğim tarzı mükemmel bir şekilde yaptı. Kesinlikle tekrar geleceğim.', 'Saç Kesimi & Sakal Düzenleme', '2 hafta önce', 1),
    (p_salon_id, 'Mehmet Kaya', 5, 'Randevu sistemi çok pratik, salon temiz ve hijyenik. Ekip çok güler yüzlü ve işini bilen. Herkese tavsiye ederim.', 'Saç Kesimi', '1 ay önce', 2),
    (p_salon_id, 'Can Demir', 5, 'Yıllardır gittiğim en iyi kuaför. Hem kaliteli hizmet hem de uygun fiyatlar. Personel çok ilgili ve deneyimli.', 'Saç Kesimi & Yıkama', '3 hafta önce', 3);

END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE public.salon_content IS 'Stores customizable content for salon landing pages using flexible key-value structure';
COMMENT ON TABLE public.salon_testimonials IS 'Stores customer testimonials for each salon with rating and display order';
COMMENT ON FUNCTION insert_default_salon_content(UUID) IS 'Inserts default content for a new salon including hero, about, services, contact sections and sample testimonials';
