"use client"

import { useState } from "react"
import { format, isSameDay, parse, addMinutes } from "date-fns"
import { tr } from "date-fns/locale"
import Link from "next/link"
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, useSensor, useSensors, PointerSensor } from "@dnd-kit/core"
import { toast } from "sonner"

import { cn, checkAppointmentOverlap } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Appointment, BarberWorkingHours } from "@/lib/db/types"
import { HolidayDate } from "@/components/ui/holiday-calendar"
import { DraggableAppointment } from "@/components/calendar/DraggableAppointment"
import { DroppableTimeSlot } from "@/components/calendar/DroppableTimeSlot"
import { appointments as appointmentsApi } from "@/lib/db"
import { WeeklyCalendarSkeleton } from "@/components/ui/skeleton-loaders"

// Extended Appointment type to include joined data from the API
interface AppointmentWithJoins extends Appointment {
  customers?: {
    name: string;
    surname: string;
    phone: string;
  };
  barbers?: {
    name: string;
  };
  services?: {
    name: string;
    duration: number;
  };
}

interface WeeklyCalendarViewProps {
  weekDays: Date[];
  appointmentsByDay: { date: Date; appointments: AppointmentWithJoins[] }[];
  isLoading: boolean;
  onSlotClick?: (date: Date, time?: string) => void;
  holidayDates?: HolidayDate[];
  barberWorkingHours?: BarberWorkingHours[];
  onAppointmentUpdated?: () => void;
}

export function WeeklyCalendarView({
  weekDays,
  appointmentsByDay,
  isLoading,
  onSlotClick,
  holidayDates = [],
  barberWorkingHours = [],
  onAppointmentUpdated
}: WeeklyCalendarViewProps) {
  const [activeAppointment, setActiveAppointment] = useState<AppointmentWithJoins | null>(null)
  const [isDragging, setIsDragging] = useState(false)

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required before drag starts
      },
    })
  )



  if (isLoading) {
    return <WeeklyCalendarSkeleton />;
  }

  // Function to check if a date is a holiday
  const isHoliday = (date: Date) => {
    return holidayDates.some(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
    );
  };

  // Function to get holiday description
  const getHolidayDescription = (date: Date) => {
    const holiday = holidayDates.find(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
    );
    return holiday?.description || "Tatil Günü";
  };

  // Function to check if a barber works on a specific day
  const isBarberWorkingDay = (date: Date) => {
    if (!barberWorkingHours.length) return true;

    const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday
    const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek);

    // If no working hours found for this day or is_closed is true, barber doesn't work
    return workingHoursForDay && !workingHoursForDay.is_closed;
  };

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const { appointment } = active.data.current as { appointment: AppointmentWithJoins }
    setActiveAppointment(appointment)
    setIsDragging(true)
  }

  // Handle drag end
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    setIsDragging(false)
    setActiveAppointment(null)

    // If no drop target, do nothing
    if (!over) return

    const appointmentId = active.id as string

    // Find the appointment in all days
    let appointment: AppointmentWithJoins | undefined
    for (const day of appointmentsByDay) {
      const found = day.appointments.find(apt => apt.id === appointmentId)
      if (found) {
        appointment = found
        break
      }
    }

    if (!appointment) return

    // Sadece aktif randevular taşınabilir
    if (appointment.status !== 'booked') {
      let statusMessage = "";
      switch (appointment.status) {
        case 'cancelled':
          statusMessage = "İptal edilmiş";
          break;
        case 'completed':
          statusMessage = "Tamamlanmış";
          break;
        case 'no-show':
          statusMessage = "Gelmemiş";
          break;
        default:
          statusMessage = "Bu durumdaki";
      }
      toast.error(`${statusMessage} randevular taşınamaz`)
      return
    }

    // Get the source date from the appointment
    const sourceDate = format(new Date(appointment.date), "yyyy-MM-dd")

    const { date, isAvailable } = over.data.current as {
      date: Date,
      isAvailable: boolean
    }

    // If the time slot is not available, do nothing
    if (!isAvailable) {
      toast.error("Bu güne randevu taşınamaz")
      return
    }

    try {
      // Format date for database
      const formattedDate = format(date, "yyyy-MM-dd")

      // If the appointment is dropped on the same date, do nothing
      if (sourceDate === formattedDate) {
        return
      }

      // Get all appointments from all days
      const allAppointments: AppointmentWithJoins[] = []
      appointmentsByDay.forEach(day => {
        allAppointments.push(...day.appointments)
      })

      // Check for appointment overlaps
      // Since we're only changing the date and keeping the same time,
      // we need to check if there are any appointments for the same barber on the target date
      // with the same time slot
      const hasOverlap = allAppointments.some(apt =>
        apt.id !== appointmentId && // Not the same appointment
        apt.barber_id === appointment.barber_id && // Same barber
        apt.date === formattedDate && // Same date
        apt.start_time === appointment.start_time // Same start time
      )

      if (hasOverlap) {
        toast.error("Bu berberin seçilen günde aynı saat aralığında başka bir randevusu var")
        return
      }

      // Update appointment in database - just change the date
      await appointmentsApi.updateAppointment({
        id: appointmentId,
        date: formattedDate
      })

      toast.success("Randevu başarıyla taşındı")

      // Notify parent component to refresh appointments
      if (onAppointmentUpdated) {
        onAppointmentUpdated()
      }
    } catch (error) {
      console.error("Error updating appointment:", error)
      toast.error("Randevu taşınırken bir hata oluştu")
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        {appointmentsByDay.map(({ date, appointments }) => {
          const isHolidayDay = isHoliday(date);
          const isBarberWorking = isBarberWorkingDay(date);
          const isAvailable = !isHolidayDay && isBarberWorking;
          const dayId = `day-${format(date, "yyyy-MM-dd")}`;

          return (
            <DroppableTimeSlot
              key={dayId}
              id={dayId}
              date={date}
              isAvailable={isAvailable || false}
              unavailableReason={isHolidayDay ? "Tatil günü" : "Berber çalışmıyor"}
              className="p-0 min-h-[200px]"
            >
              <Card
                className={cn(
                  "h-full border-0",
                  isSameDay(date, new Date()) && "border-primary",
                  isHolidayDay && "border-red-500 dark:border-red-700",
                  !isBarberWorking && barberWorkingHours.length > 0 && "border-yellow-500 dark:border-yellow-700"
                )}
              >
                <CardHeader className="p-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-sm font-medium">
                      {format(date, "EEEE", { locale: tr })}
                    </CardTitle>
                    {isHolidayDay && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="destructive" className="ml-2">Tatil</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{getHolidayDescription(date)}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    {!isBarberWorking && barberWorkingHours.length > 0 && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="outline" className="ml-2 bg-yellow-100 dark:bg-yellow-900/30">Çalışma Dışı</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Berber bu gün çalışmıyor</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {format(date, "d MMMM", { locale: tr })}
                  </p>
                </CardHeader>
                <CardContent className="p-3 space-y-2">
                  {!isHolidayDay && isBarberWorking ? (
                    <div
                      className="text-xs text-muted-foreground p-2 rounded-md hover:bg-muted cursor-pointer"
                      onClick={() => onSlotClick && onSlotClick(date)}
                    >
                      Yeni randevu eklemek için tıklayın
                    </div>
                  ) : (
                    <div className="text-xs text-muted-foreground p-2 rounded-md bg-muted/30">
                      {isHolidayDay ? "Tatil günü - Randevu alınamaz" : "Berber çalışmıyor - Randevu alınamaz"}
                    </div>
                  )}
                  {appointments.map((apt) => (
                    <DraggableAppointment
                      key={apt.id}
                      appointment={apt}
                      onAppointmentUpdated={onAppointmentUpdated}
                    />
                  ))}
                </CardContent>
              </Card>
            </DroppableTimeSlot>
          );
        })}
      </div>

      {/* Drag overlay for visual feedback during dragging */}
      <DragOverlay>
        {activeAppointment && (
          <DraggableAppointment
            appointment={activeAppointment}
            isDraggingEnabled={false}
            onAppointmentUpdated={onAppointmentUpdated}
          />
        )}
      </DragOverlay>
    </DndContext>
  );
}
