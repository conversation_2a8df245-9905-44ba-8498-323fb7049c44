# Müşteri Tablosu RLS Sorunu ve Kalıcı Çözümü

## Sorun

Müşteri tablosunda RLS (Row Level Security) politikaları doğru şekilde yapılandırılmış olmasına rağ<PERSON>, müş<PERSON>i ekleme ve görüntüleme işlemleri çalışmıyordu ve şu hata alınıyordu:

```
{
    "code": "42501",
    "details": null,
    "hint": null,
    "message": "new row violates row-level security policy for table \"customers\""
}
```

## Sorunun Kökeni

Sorunun temel kaynağı, Supabase istemcisinin kullanım şekli olarak tespit edildi. `src/lib/db/customers.ts` dosyasında doğrudan `supabase` istemcisi kullanılıyordu, ancak bu istemci JWT token'ı otomatik olarak yönetmiyor ve bu nedenle RLS politikaları doğru şekilde uygulanmıyordu.

Next.js uygulamalarında, özellikle client-side bileşenlerde, `createClientComponentClient` ile oluşturulan istemci kullanılmalıdır. Bu istemci, Next.js'in cookie tabanlı oturum yönetimini kullanarak JWT token'ı otomatik olarak yönetir.

## Uygulanan Çözüm

### 1. RLS Politikalarını Doğru Şekilde Yapılandırma

```sql
-- Salon sahipleri için politika
CREATE POLICY "Salon owners can manage their salon's customers" ON customers
  FOR ALL
  TO authenticated
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Berberler için görüntüleme politikası
CREATE POLICY "Staff can view their salon's customers" ON customers
  FOR SELECT
  TO authenticated
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Berberler için ekleme politikası
CREATE POLICY "Staff can insert customers for their salon" ON customers
  FOR INSERT
  TO authenticated
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Berberler için güncelleme politikası
CREATE POLICY "Staff can update customers for their salon" ON customers
  FOR UPDATE
  TO authenticated
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Anonim kullanıcılar için ekleme politikası (booking sayfası için)
CREATE POLICY "Anyone can insert customers" ON customers
  FOR INSERT
  TO anon
  WITH CHECK (true);
```

### 2. Supabase İstemcisini Doğru Şekilde Kullanma

`src/lib/db/customers.ts` dosyasındaki tüm fonksiyonları güncelleyerek, `getSupabaseBrowser()` fonksiyonu ile oluşturulan istemciyi kullanmak:

```typescript
import { Customer, CustomerInsert, CustomerUpdate } from './types';
import { getSupabaseBrowser } from '../supabase';

/**
 * Get all customers for a salon
 */
export async function getCustomers(salonId: string) {
  const supabase = getSupabaseBrowser();
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('salon_id', salonId)
    .order('surname', { ascending: true })
    .order('name', { ascending: true });

  if (error) throw error;
  return data as Customer[];
}

// Diğer fonksiyonlar da benzer şekilde güncellendi
```

## Güvenlik Değerlendirmesi

Bu çözüm, SaaS uygulamasında RLS'nin doğru şekilde çalışmasını sağlar ve salon müşterilerinin yalnızca ilgili salon sahipleri ve çalışanları tarafından görülebilmesini garanti eder.

Önemli noktalar:
1. RLS politikaları, salon_id'ye göre veri izolasyonu sağlar
2. Salon sahipleri yalnızca kendi salonlarının müşterilerini yönetebilir
3. Berberler yalnızca kendi salonlarının müşterilerini görüntüleyebilir, ekleyebilir ve güncelleyebilir
4. Anonim kullanıcılar müşteri ekleyebilir (booking sayfası için)
5. Supabase istemcisi, JWT token'ı otomatik olarak yönetir ve RLS politikalarının doğru şekilde uygulanmasını sağlar

## Sonuç

Bu çözüm, RLS'den taviz vermeden, SaaS uygulamasında veri izolasyonunu sağlayan kalıcı bir çözümdür. Salon müşterilerinin yalnızca ilgili salon sahipleri ve çalışanları tarafından görülebilmesini garanti eder ve güvenlik açısından ideal bir çözüm sunar.
