-- <PERSON>kleyen Referans Kodları Tablosu
-- Tarih: 2024-08-01

-- <PERSON>llanıcı ve referans kodu arasındaki ilişkiyi saklamak için yeni bir tablo oluştur
CREATE TABLE IF NOT EXISTS pending_referrals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  referral_code TEXT NOT NULL,
  is_applied BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> politikalarını ekle
ALTER TABLE pending_referrals ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON><PERSON><PERSON>lar kendi bekleyen referans kodlarını görebilir
CREATE POLICY "Kullanıcılar kendi bekleyen referans kodlarını görebilir" ON pending_referrals
  FOR SELECT USING (auth.uid() = user_id);

-- Admin tüm bekleyen referans kodlarını görebilir
CREATE POLICY "Admin tüm bekleyen referans kodlarını görebilir" ON pending_referrals
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Kullanıcılar kendi bekleyen referans kodlarını güncelleyebilir
CREATE POLICY "Kullanıcılar kendi bekleyen referans kodlarını güncelleyebilir" ON pending_referrals
  FOR UPDATE USING (auth.uid() = user_id);

-- Admin tüm bekleyen referans kodlarını güncelleyebilir
CREATE POLICY "Admin tüm bekleyen referans kodlarını güncelleyebilir" ON pending_referrals
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Kullanıcılar kendi bekleyen referans kodlarını silebilir
CREATE POLICY "Kullanıcılar kendi bekleyen referans kodlarını silebilir" ON pending_referrals
  FOR DELETE USING (auth.uid() = user_id);

-- Admin tüm bekleyen referans kodlarını silebilir
CREATE POLICY "Admin tüm bekleyen referans kodlarını silebilir" ON pending_referrals
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Herkes bekleyen referans kodu ekleyebilir (kayıt sırasında)
CREATE POLICY "Herkes bekleyen referans kodu ekleyebilir" ON pending_referrals
  FOR INSERT WITH CHECK (true);

-- Indeks ekle
CREATE INDEX IF NOT EXISTS pending_referrals_user_id_idx ON pending_referrals(user_id);
CREATE INDEX IF NOT EXISTS pending_referrals_referral_code_idx ON pending_referrals(referral_code);

-- Açıklama
COMMENT ON TABLE pending_referrals IS 'Kullanıcıların kayıt sırasında girdiği ve henüz uygulanmamış referans kodlarını saklar';
COMMENT ON COLUMN pending_referrals.user_id IS 'Referans kodunu giren kullanıcının ID''si';
COMMENT ON COLUMN pending_referrals.referral_code IS 'Girilen referans kodu';
COMMENT ON COLUMN pending_referrals.is_applied IS 'Referans kodunun uygulanıp uygulanmadığı';
