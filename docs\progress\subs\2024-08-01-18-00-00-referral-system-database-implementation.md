# Referans Sistemi Veritabanı Tabanlı Uygulama

**Tarih:** 1 Ağustos 2024
**Saat:** 18:00

## 1. Mevcut Durum ve Sorunlar

Mevcut referans sistemi uygulaması, referans kodlarını sessionStorage'da saklıyor. Bu yaklaşımın aşağıdaki sorunları vardır:

1. **Kalıcı Değil**: sessionStorage tarayıcı oturumu ile sınırlıdır. Kullanıcı tarayıcıyı kapatıp açtığında veya farklı bir tarayıcıdan giriş yaptığında referans kodu bilgisi kaybolur.

2. **Zaman Kısıtlaması**: Kullanıcı kayıt olduktan sonra uzun bir süre (örn. 10 gün) geçerse ve daha sonra giriş yaparsa, referans kodu bilgisi kaybolmuş olur.

3. **Cihaz Bağımlılığı**: Kullanıcı farklı bir cihazdan giriş yaptığında referans kodu bilgisine erişemez.

## 2. Çözüm: Veritabanı Tabanlı Referans Kodu Saklama

Bu sorunları çözmek için, referans kodlarını veritabanında saklayan bir yaklaşım uyguladık:

1. **Yeni Tablo**: `pending_referrals` adında yeni bir tablo oluşturuldu. Bu tablo, kullanıcı ve referans kodu arasındaki ilişkiyi saklar.

2. **Kayıt Süreci**: Kullanıcı kayıt olduğunda, referans kodu sessionStorage yerine veritabanında saklanır.

3. **Salon Oluşturma**: Kullanıcı salon oluşturduğunda, veritabanından bekleyen referans kodu alınır ve uygulanır.

## 3. Veritabanı Şeması

```sql
CREATE TABLE IF NOT EXISTS pending_referrals (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  referral_code TEXT NOT NULL,
  is_applied BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 4. Uygulama Detayları

### 4.1. Referans Kodu API'si Güncelleme

`src/lib/db/referrals.ts` dosyasına aşağıdaki yeni fonksiyonlar eklendi:

```typescript
/**
 * Save a pending referral code for a user
 */
export async function savePendingReferral(userId: string, referralCode: string) {
  // Önce mevcut bekleyen referans kodunu kontrol et
  const { data: existingReferral, error: checkError } = await supabase
    .from('pending_referrals')
    .select('*')
    .eq('user_id', userId)
    .eq('is_applied', false)
    .single();

  if (checkError && checkError.code !== 'PGRST116') throw checkError;
  
  // Eğer zaten bekleyen bir referans kodu varsa, güncelle
  if (existingReferral) {
    const { data, error } = await supabase
      .from('pending_referrals')
      .update({ referral_code: referralCode })
      .eq('id', existingReferral.id)
      .select()
      .single();

    if (error) throw error;
    return data as PendingReferral;
  }

  // Yeni bir bekleyen referans kodu oluştur
  const pendingReferral: PendingReferralInsert = {
    user_id: userId,
    referral_code: referralCode,
    is_applied: false
  };

  const { data, error } = await supabase
    .from('pending_referrals')
    .insert(pendingReferral)
    .select()
    .single();

  if (error) throw error;
  return data as PendingReferral;
}

/**
 * Get pending referral for a user
 */
export async function getPendingReferral(userId: string) {
  const { data, error } = await supabase
    .from('pending_referrals')
    .select('*')
    .eq('user_id', userId)
    .eq('is_applied', false)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as PendingReferral | null;
}

/**
 * Mark a pending referral as applied
 */
export async function markPendingReferralAsApplied(id: string) {
  const { data, error } = await supabase
    .from('pending_referrals')
    .update({ is_applied: true })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as PendingReferral;
}
```

### 4.2. Kayıt Sayfası Güncelleme

`src/app/auth/register/page.tsx` dosyasında, referans kodunu sessionStorage yerine veritabanında saklamak için değişiklikler yapıldı:

```typescript
// Store the referral code in the database to use after email verification
if (values.referralCode && data.user) {
  try {
    // Referans kodunu veritabanında sakla
    await referrals.savePendingReferral(data.user.id, values.referralCode);
    console.log(`Referral code ${values.referralCode} saved for user ${data.user.id}`);
    
    toast.success("Kayıt başarılı! Hesabınızı doğruladıktan sonra referans kodunuz uygulanacaktır.");
  } catch (error) {
    console.error("Referans kodu kaydedilirken hata:", error);
    toast.success("Kayıt başarılı! Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.");
  }
} else {
  toast.success("Kayıt başarılı! Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.");
}
```

### 4.3. Salon Oluşturma Sayfası Güncelleme

`src/app/dashboard/settings/page.tsx` dosyasında, referans kodunu sessionStorage yerine veritabanından almak için değişiklikler yapıldı:

```typescript
// Veritabanından bekleyen referans kodunu kontrol et ve uygula
let isReferred = false
try {
  // Kullanıcının bekleyen referans kodunu al
  const pendingReferral = await referrals.getPendingReferral(user.id)
  
  if (pendingReferral) {
    try {
      // Referans kodunu uygula
      await referrals.applyReferralCode(pendingReferral.referral_code, data.id)
      
      // Bekleyen referans kodunu uygulandı olarak işaretle
      await referrals.markPendingReferralAsApplied(pendingReferral.id)
      
      isReferred = true
      console.log(`Referral code ${pendingReferral.referral_code} applied for salon ${data.id}`)
      toast.success("Referans kodu başarıyla uygulandı!")
    } catch (error) {
      console.error("Referans kodu uygulanırken hata:", error)
    }
  }
} catch (error) {
  console.error("Bekleyen referans kodu kontrol edilirken hata:", error)
}
```

## 5. Avantajlar

Bu veritabanı tabanlı yaklaşımın avantajları:

1. **Kalıcılık**: Referans kodu bilgisi veritabanında saklandığı için, kullanıcı ne zaman veya hangi cihazdan giriş yaparsa yapsın, referans kodu bilgisi korunur.

2. **Zaman Bağımsızlığı**: Kullanıcı kayıt olduktan sonra uzun bir süre geçse bile, salon oluşturduğunda referans kodu uygulanabilir.

3. **Cihaz Bağımsızlığı**: Kullanıcı farklı bir cihazdan giriş yapsa bile, referans kodu bilgisine erişilebilir.

4. **Güvenilirlik**: Tarayıcı önbelleği veya oturum sorunlarından etkilenmez.

## 6. Sonuç

Bu değişikliklerle, referans sistemi daha güvenilir ve kullanıcı dostu hale getirildi. Kullanıcılar artık kayıt olduktan sonra istedikleri zaman salon oluşturabilir ve referans kodları otomatik olarak uygulanır.
