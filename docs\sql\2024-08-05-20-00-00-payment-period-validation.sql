-- Ödeme dönemi validasyonu için SQL script
-- Tarih: 2024-08-05

-- 1. <PERSON><PERSON><PERSON> dönem için birden fazla ödeme yapılmasını engellemek için fonksiyon oluşturma
CREATE OR REPLACE FUNCTION check_payment_period_overlap(
  p_subscription_id UUID,
  p_period_start_date DATE,
  p_period_end_date DATE
)
RETURNS BOOLEAN AS $$
DECLARE
  overlap_exists BOOLEAN;
BEGIN
  -- Aynı abonelik için aynı dönemi kapsayan başka bir ödeme var mı kontrol et
  SELECT EXISTS (
    SELECT 1
    FROM subscription_payments
    WHERE subscription_id = p_subscription_id
      AND status = 'completed'
      AND (
        -- Başlangıç tarihi mevcut bir ödeme döneminin içinde
        (p_period_start_date >= period_start_date AND p_period_start_date < period_end_date)
        OR
        -- Bitiş tarihi mevcut bir ödeme döneminin içinde
        (p_period_end_date > period_start_date AND p_period_end_date <= period_end_date)
        OR
        -- Yeni dönem mevcut bir ödeme dönemini tamamen kapsıyor
        (p_period_start_date <= period_start_date AND p_period_end_date >= period_end_date)
      )
  ) INTO overlap_exists;

  RETURN overlap_exists;
END;
$$ LANGUAGE plpgsql;

-- 2. Bir sonraki ödeme dönemini hesaplamak için fonksiyon oluşturma
CREATE OR REPLACE FUNCTION calculate_next_payment_period(
  p_subscription_id UUID
)
RETURNS TABLE (
  next_period_start_date DATE,
  next_period_end_date DATE
) AS $$
DECLARE
  last_period_end_date DATE;
  is_yearly BOOLEAN;
  period_length INTEGER;
BEGIN
  -- Aboneliğin yıllık mı aylık mı olduğunu kontrol et
  SELECT ss.is_yearly
  INTO is_yearly
  FROM salon_subscriptions ss
  WHERE ss.id = p_subscription_id;

  -- Dönem uzunluğunu belirle (yıllık: 365 gün, aylık: 30 gün)
  period_length := CASE WHEN is_yearly THEN 365 ELSE 30 END;

  -- Son ödeme döneminin bitiş tarihini bul
  SELECT MAX(period_end_date)
  INTO last_period_end_date
  FROM subscription_payments
  WHERE subscription_id = p_subscription_id
    AND status = 'completed';

  -- Eğer hiç ödeme yoksa, aboneliğin başlangıç tarihini kullan
  IF last_period_end_date IS NULL THEN
    SELECT
      CASE
        WHEN ss.status = 'trial' THEN ss.trial_end_date
        ELSE ss.start_date
      END
    INTO last_period_end_date
    FROM salon_subscriptions ss
    WHERE ss.id = p_subscription_id;
    
    -- Eğer hala NULL ise, bugünü kullan
    IF last_period_end_date IS NULL THEN
      last_period_end_date := CURRENT_DATE;
    END IF;
  END IF;

  -- Bir sonraki dönemin başlangıç ve bitiş tarihlerini hesapla
  RETURN QUERY
  SELECT
    last_period_end_date AS next_period_start_date,
    (last_period_end_date + period_length) AS next_period_end_date;
END;
$$ LANGUAGE plpgsql;
