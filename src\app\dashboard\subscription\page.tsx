"use client"

import Link from "next/link"
import { ArrowR<PERSON>, Check, AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useSubscription } from "@/contexts/SubscriptionContext"
import { Skeleton } from "@/components/ui/skeleton"

export default function SubscriptionPage() {
  const { subscription, plans, isLoading } = useSubscription()

  // Abonelik durumunu Türkçe olarak göster
  const getStatusText = (status: string) => {
    switch (status) {
      case 'trial': return '<PERSON>em<PERSON>üresi'
      case 'active': return 'Aktif'
      case 'past_due': return '<PERSON><PERSON><PERSON>'
      case 'cancelled': return 'İptal Edildi'
      case 'suspended': return 'Askıya Alındı'
      default: return status
    }
  }

  // Abonelik durumuna göre renk belirle
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trial': return 'bg-blue-500 hover:bg-blue-600'
      case 'active': return 'bg-green-500 hover:bg-green-600'
      case 'past_due': return 'bg-yellow-500 hover:bg-yellow-600'
      case 'cancelled': return 'bg-red-500 hover:bg-red-600'
      case 'suspended': return 'bg-gray-500 hover:bg-gray-600'
      default: return 'bg-gray-500 hover:bg-gray-600'
    }
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Abonelik</h1>
        </div>
      </header>

      <div className="space-y-6">
        {/* Mevcut Abonelik Bilgileri */}
        {isLoading ? (
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-6 w-32" />
                </div>
                <div>
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-6 w-32" />
                </div>
                <div>
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-6 w-32" />
                </div>
                <div>
                  <Skeleton className="h-4 w-20 mb-2" />
                  <Skeleton className="h-6 w-32" />
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Skeleton className="h-10 w-32" />
            </CardFooter>
          </Card>
        ) : subscription ? (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Mevcut Abonelik</CardTitle>
                  <CardDescription>Abonelik durumunuz ve detayları</CardDescription>
                </div>
                <Badge className={getStatusColor(subscription.status)}>
                  {getStatusText(subscription.status)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Plan</p>
                  <p className="text-lg font-semibold">{subscription.plans?.name || 'Bilinmiyor'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Ödeme Döngüsü</p>
                  <p className="text-lg font-semibold">{subscription.is_yearly ? 'Yıllık' : 'Aylık'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Başlangıç Tarihi</p>
                  <p className="text-lg font-semibold">
                    {format(new Date(subscription.start_date), 'd MMMM yyyy', { locale: tr })}
                  </p>
                </div>
                {subscription.status === 'trial' && subscription.trial_end_date && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Deneme Süresi Bitiş</p>
                    <p className="text-lg font-semibold">
                      {format(new Date(subscription.trial_end_date), 'd MMMM yyyy', { locale: tr })}
                    </p>
                  </div>
                )}
                {subscription.end_date && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Bitiş Tarihi</p>
                    <p className="text-lg font-semibold">
                      {format(new Date(subscription.end_date), 'd MMMM yyyy', { locale: tr })}
                    </p>
                  </div>
                )}
              </div>

              {/* Uyarı mesajları */}
              {subscription.status === 'trial' && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Deneme Süresi</AlertTitle>
                  <AlertDescription>
                    Deneme süreniz {subscription.trial_end_date ?
                      format(new Date(subscription.trial_end_date), 'd MMMM yyyy', { locale: tr }) :
                      '14 gün'} tarihinde sona erecek. Kesintisiz hizmet için aboneliğinizi aktifleştirin.
                  </AlertDescription>
                </Alert>
              )}

              {subscription.status === 'past_due' && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Ödeme Gecikti</AlertTitle>
                  <AlertDescription>
                    Abonelik ödemesi gecikti. Lütfen en kısa sürede ödemenizi yapın, aksi takdirde hizmetiniz askıya alınacak.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" asChild>
                <Link href="/dashboard/subscription/history">Ödeme Geçmişi</Link>
              </Button>
            </CardFooter>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Abonelik Bulunamadı</CardTitle>
              <CardDescription>Henüz aktif bir aboneliğiniz bulunmuyor</CardDescription>
            </CardHeader>
            <CardContent>
              <p>SalonFlow&apos;u kullanmak için bir abonelik planı seçmelisiniz.</p>
            </CardContent>
            <CardFooter>
              <Button asChild>
                <Link href="/dashboard/subscription/plans">
                  Planları Görüntüle
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Plan Karşılaştırma */}
        {isLoading ? (
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[1, 2, 3].map((i) => (
                  <Card key={i}>
                    <CardHeader>
                      <Skeleton className="h-6 w-24" />
                      <Skeleton className="h-8 w-32" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {[1, 2, 3, 4].map((j) => (
                          <div key={j} className="flex items-center">
                            <Skeleton className="h-4 w-4 mr-2 rounded-full" />
                            <Skeleton className="h-4 w-full" />
                          </div>
                        ))}
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Skeleton className="h-10 w-full" />
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Abonelik Planları</CardTitle>
              <CardDescription>Tüm planları karşılaştırın ve size uygun olanı seçin</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4" style={{ display: 'grid', gridTemplateRows: 'auto 1fr' }}>
                {plans.map((plan) => (
                  <Card key={plan.id} className={`flex flex-col h-full ${
                    subscription?.plans?.id === plan.id ? 'border-primary' : ''
                  }`}>
                    <CardHeader>
                      <CardTitle>{plan.name}</CardTitle>
                      <CardDescription>
                        <div className="flex flex-col">
                          <span className="text-xl font-bold">{plan.price_monthly} TL/ay</span>
                          <span className="text-sm">veya {plan.price_yearly} TL/yıl</span>
                        </div>
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-grow">
                      <ul className="space-y-2">
                        <li className="flex items-center">
                          <Check className="mr-2 h-4 w-4 text-green-500" />
                          <span>Maksimum {plan.max_staff} personel</span>
                        </li>
                        <li className="flex items-center">
                          <Check className="mr-2 h-4 w-4 text-green-500" />
                          <span>Sınırsız randevu</span>
                        </li>
                        {plan.features.analytics && (
                          <li className="flex items-center">
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            <span>Gelişmiş analitik</span>
                          </li>
                        )}
                        {plan.features.finance && (
                          <li className="flex items-center">
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            <span>Finans yönetimi</span>
                          </li>
                        )}
                        {plan.features.custom_domain && (
                          <li className="flex items-center">
                            <Check className="mr-2 h-4 w-4 text-green-500" />
                            <span>Özel alan adı</span>
                          </li>
                        )}
                      </ul>
                    </CardContent>
                    <CardFooter className="mt-auto">
                      {subscription?.plans?.id === plan.id ? (
                        <Button variant="outline" className="w-full" disabled>
                          Mevcut Plan
                        </Button>
                      ) : (
                        subscription?.plans &&
                        plans.findIndex(p => p.id === subscription.plans?.id) >
                        plans.findIndex(p => p.id === plan.id) ? (
                          <div className="w-full h-10"></div> // Boş alan bırak ama yüksekliği koru
                        ) : (
                          <Button
                            className="w-full"
                            asChild
                          >
                            <Link href={`/dashboard/subscription/upgrade?plan=${plan.id}`}>
                              {subscription ? 'Yükselt' : 'Seç'}
                            </Link>
                          </Button>
                        )
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
