# Berber Çalışma Saatleri Doğrudan E<PERSON>ş<PERSON>

**Tarih:** 25 Ağustos 2024
**Saat:** 18:30

## Sorun

`get_public_barber_working_hours_by_barber_id` RPC fonksiyonu Supabase'de bulunamadı. Hata mesajı:

```json
{
    "code": "PGRST202",
    "details": "Searched for the function public.get_public_barber_working_hours_by_barber_id with parameter p_barber_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
    "hint": "Perhaps you meant to call the function public.get_public_working_hours_by_salon_id",
    "message": "Could not find the function public.get_public_barber_working_hours_by_barber_id(p_barber_id) in the schema cache"
}
```

## Çözüm

Berber çalışma saatlerini çekme mantığını korumak için, doğrudan veritabanı erişimi kull<PERSON>rak geçici bir çözüm uyguladık:

```typescript
// Berber çalışma saatlerini getir
let barberWorkingHours = []
try {
  const { data, error } = await supabaseClient
    .from('barber_working_hours')
    .select('*')
    .eq('barber_id', barberId)
    .order('day_of_week')
  
  if (!error) {
    barberWorkingHours = data || []
  }
} catch (err) {
  console.error("Error fetching barber working hours:", err)
}

// Salon çalışma saatlerini getir
const salonWorkingHours = await publicAccess.getPublicWorkingHoursBySalonId(salonId)

// Berber çalışma saatleri varsa onları, yoksa salon çalışma saatlerini kullan
const hoursToUse = barberWorkingHours.length > 0
  ? barberWorkingHours.map(bwh => ({
      id: bwh.id,
      barber_id: bwh.barber_id,
      salon_id: salonId,
      day_of_week: bwh.day_of_week,
      open_time: bwh.open_time,
      close_time: bwh.close_time,
      is_closed: bwh.is_closed,
      lunch_start_time: bwh.lunch_start_time,
      lunch_end_time: bwh.lunch_end_time,
      has_lunch_break: bwh.has_lunch_break
    }))
  : salonWorkingHours.map(wh => ({
      id: wh.id,
      barber_id: barberId,
      salon_id: wh.salon_id,
      day_of_week: wh.day_of_week,
      open_time: wh.open_time,
      close_time: wh.close_time,
      is_closed: wh.is_closed,
      lunch_start_time: null,
      lunch_end_time: null,
      has_lunch_break: false
    }))
```

Bu çözüm, berber çalışma saatlerini doğrudan veritabanından çekerek, önceki mantığı korumaktadır. Berber çalışma saatleri varsa onları, yoksa salon çalışma saatlerini kullanır.

## Kalıcı Çözüm İçin Talimatlar

SQL fonksiyonlarını Supabase'de çalıştırmak için aşağıdaki adımları izleyin:

1. Supabase Dashboard'a giriş yapın
2. Projenizi seçin
3. Sol menüden "SQL Editor" seçeneğine tıklayın
4. "New Query" butonuna tıklayın
5. `docs/sql/public_access_rpc/2024-08-25-17-15-00-fix-working-hours-functions.sql` dosyasındaki SQL kodunu kopyalayıp yapıştırın
6. "Run" butonuna tıklayın
7. SQL fonksiyonları başarıyla çalıştırıldıktan sonra, uygulamayı yeniden başlatın

SQL fonksiyonları başarıyla çalıştırıldıktan sonra, doğrudan veritabanı erişimi yerine RPC fonksiyonlarını kullanacak şekilde kodu güncelleyebilirsiniz.

## Sonuç

Bu geçici çözüm, berber çalışma saatlerini çekme mantığını koruyarak uygulamanın çalışmaya devam etmesini sağlar. SQL fonksiyonları Supabase'de çalıştırıldıktan sonra, doğrudan veritabanı erişimi yerine RPC fonksiyonlarını kullanacak şekilde kodu güncelleyebilirsiniz.
