-- Fix for ambiguous salon_id column in create_payment_notification trigger
-- Oluşturulma Tarihi: 2024-07-28

-- <PERSON><PERSON><PERSON> bild<PERSON> için trigger fonksiyonunu güncelle
CREATE OR REPLACE FUNCTION create_payment_notification()
RETURNS TRIGGER AS $$
DECLARE
  subscription_salon_id UUID;
  salon_owner_id UUID;
  notification_title TEXT;
  notification_message TEXT;
  notification_type TEXT := 'update';
BEGIN
  -- Salon ID'sini al - salon_id sütun adını açıkça belirterek
  SELECT ss.salon_id INTO subscription_salon_id
  FROM salon_subscriptions ss
  WHERE ss.id = NEW.subscription_id;

  -- Salon sahibi ID'sini al
  SELECT owner_id INTO salon_owner_id
  FROM salons
  WHERE id = subscription_salon_id;

  -- <PERSON><PERSON><PERSON>im içeriğini belirle
  IF NEW.status = 'completed' AND (OLD IS NULL OR OLD.status != 'completed') THEN
    notification_title := 'Ödeme Tamamlandı';
    notification_message := format('%s TL tutarındaki ödemeniz başar<PERSON><PERSON> ta<PERSON>.', NEW.amount);
  ELSIF NEW.status = 'pending' AND (OLD IS NULL OR OLD.status != 'pending') THEN
    notification_title := 'Ödeme Bekleniyor';
    notification_message := format('%s TL tutarındaki ödemeniz bekleniyor.', NEW.amount);
  ELSIF NEW.status = 'failed' AND (OLD IS NULL OR OLD.status != 'failed') THEN
    notification_title := 'Ödeme Başarısız';
    notification_message := format('%s TL tutarındaki ödemeniz başarısız oldu.', NEW.amount);
  END IF;

  -- Bildirim oluştur
  IF notification_title IS NOT NULL THEN
    INSERT INTO notifications (
      salon_id,
      user_id,
      type,
      title,
      message,
      read,
      data
    ) VALUES (
      subscription_salon_id,
      salon_owner_id,
      notification_type,
      notification_title,
      notification_message,
      FALSE,
      jsonb_build_object('payment_id', NEW.id, 'status', NEW.status, 'amount', NEW.amount)
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ı subscription_payments tablosuna bağla
DROP TRIGGER IF EXISTS payment_notification_trigger ON subscription_payments;

CREATE TRIGGER payment_notification_trigger
AFTER INSERT OR UPDATE OF status
ON subscription_payments
FOR EACH ROW
EXECUTE FUNCTION create_payment_notification();
