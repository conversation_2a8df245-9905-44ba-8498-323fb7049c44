"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, CreditCard, Calendar } from "lucide-react"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Skeleton } from "@/components/ui/skeleton"
import { useUser } from "@/contexts/UserContext"
import { useSubscription } from "@/contexts/SubscriptionContext"
import { upgradeSubscription } from "@/lib/db/subscriptions"
import { getSubscriptionPlanById } from "@/lib/db/subscription-plans"
import { SubscriptionPlan } from "@/lib/db/types"

export default function UpgradeSubscriptionPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const planId = searchParams.get('plan')
  const { salonId } = useUser()
  const { subscription, isLoading: subscriptionLoading, refreshSubscription } = useSubscription()

  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [isYearly, setIsYearly] = useState(false)
  const [isPlanLoading, setIsPlanLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Combine loading states
  const isLoading = subscriptionLoading || isPlanLoading

  useEffect(() => {
    async function loadData() {
      if (!salonId) return

      try {
        setIsPlanLoading(true)

        // Seçilen planı yükle (URL'den)
        if (planId) {
          const plan = await getSubscriptionPlanById(planId)
          setSelectedPlan(plan)
        }

        // Mevcut ödeme döngüsünü ayarla
        if (subscription) {
          setIsYearly(subscription.is_yearly)
        }
      } catch (error) {
        console.error("Plan bilgileri yüklenirken hata:", error)
        toast.error("Plan bilgileri yüklenirken bir hata oluştu.")
      } finally {
        setIsPlanLoading(false)
      }
    }

    loadData()
  }, [salonId, planId, subscription])

  const handleUpgrade = async () => {
    if (!subscription || !selectedPlan || !salonId) {
      toast.error("Gerekli bilgiler eksik. Lütfen tekrar deneyin.")
      return
    }

    try {
      setIsSubmitting(true)

      // Aboneliği yükselt
      await upgradeSubscription(subscription.id, selectedPlan.id, isYearly)

      // Abonelik bilgilerini yenile
      await refreshSubscription()

      toast.success("Aboneliğiniz başarıyla yükseltildi!")
      router.push('/dashboard/subscription')
    } catch (error) {
      console.error("Abonelik yükseltme hatası:", error)
      toast.error("Abonelik yükseltilirken bir hata oluştu.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/subscription">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Plan Yükseltme</h1>
        </div>
      </header>

      <div className="max-w-2xl mx-auto space-y-6">
        {isLoading ? (
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Skeleton className="h-6 w-40 mb-3" />
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-32 w-full" />
                  <Skeleton className="h-32 w-full" />
                </div>
              </div>
              <Skeleton className="h-40 w-full" />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-32" />
            </CardFooter>
          </Card>
        ) : !selectedPlan ? (
          <Card>
            <CardHeader>
              <CardTitle>Plan Seçilmedi</CardTitle>
              <CardDescription>Lütfen yükseltmek istediğiniz planı seçin</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Lütfen abonelik sayfasına dönüp yükseltmek istediğiniz planı seçiniz.</p>
            </CardContent>
            <CardFooter>
              <Button asChild>
                <Link href="/dashboard/subscription">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Abonelik Sayfasına Dön
                </Link>
              </Button>
            </CardFooter>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Plan Yükseltme: {selectedPlan.name}</CardTitle>
              <CardDescription>Aboneliğinizi yükseltmek için aşağıdaki bilgileri onaylayın</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Ödeme Döngüsü Seçimi */}
              <div>
                <h3 className="text-lg font-medium mb-3">Ödeme Döngüsü</h3>
                <RadioGroup
                  value={isYearly ? "yearly" : "monthly"}
                  onValueChange={(value) => setIsYearly(value === "yearly")}
                  className="grid grid-cols-2 gap-4"
                >
                  <div>
                    <RadioGroupItem
                      value="monthly"
                      id="monthly"
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor="monthly"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <CreditCard className="mb-3 h-6 w-6" />
                      <span className="text-lg font-medium">Aylık</span>
                      <span className="text-sm text-muted-foreground">{selectedPlan.price_monthly} TL/ay</span>
                    </Label>
                  </div>
                  <div>
                    <RadioGroupItem
                      value="yearly"
                      id="yearly"
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor="yearly"
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                    >
                      <Calendar className="mb-3 h-6 w-6" />
                      <span className="text-lg font-medium">Yıllık</span>
                      <span className="text-sm text-muted-foreground">{selectedPlan.price_yearly} TL/yıl</span>
                      <span className="text-xs text-green-500 mt-1">%10 Tasarruf</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Özet */}
              <div className="border rounded-lg p-4">
                <h3 className="text-lg font-medium mb-3">Ödeme Özeti</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Plan</span>
                    <span>{selectedPlan.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Döngü</span>
                    <span>{isYearly ? 'Yıllık' : 'Aylık'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tutar</span>
                    <span>{isYearly ? selectedPlan.price_yearly : selectedPlan.price_monthly} TL</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between font-bold">
                    <span>Toplam</span>
                    <span>{isYearly ? selectedPlan.price_yearly : selectedPlan.price_monthly} TL</span>
                  </div>
                </div>
              </div>

              <div className="text-sm text-muted-foreground">
                <p>Ödeme işlemi, manuel havale/EFT ile gerçekleştirilecektir. Yükseltme işleminden sonra ödeme talimatları görüntülenecektir.</p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" asChild>
                <Link href="/dashboard/subscription">İptal</Link>
              </Button>
              <Button onClick={handleUpgrade} disabled={isSubmitting}>
                {isSubmitting ? "İşleniyor..." : "Planı Yükselt"}
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  )
}
