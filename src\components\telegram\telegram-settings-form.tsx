"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Loader2, Send, Settings, AlertCircle, CheckCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"

// Form schema
const telegramSettingsSchema = z.object({
  telegram_channel_id: z.string().optional(),
  is_enabled: z.boolean(),
})

type TelegramSettingsForm = z.infer<typeof telegramSettingsSchema>

interface TelegramSettings {
  salon_id: string
  telegram_channel_id: string
  is_enabled: boolean
  created_at: string | null
  updated_at: string | null
}

interface TelegramSettingsFormProps {
  salonId: string
  salonName: string
}

export function TelegramSettingsForm({ salonId, salonName }: TelegramSettingsFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [settings, setSettings] = useState<TelegramSettings | null>(null)
  const [isLoadingSettings, setIsLoadingSettings] = useState(true)

  const form = useForm<TelegramSettingsForm>({
    resolver: zodResolver(telegramSettingsSchema),
    defaultValues: {
      telegram_channel_id: "",
      is_enabled: false,
    },
  })

  // Load existing settings
  useEffect(() => {
    loadSettings()
  }, [salonId])

  const loadSettings = async () => {
    try {
      setIsLoadingSettings(true)
      const response = await fetch(`/api/telegram/settings?salon_id=${salonId}`)
      
      if (!response.ok) {
        throw new Error('Ayarlar yüklenemedi')
      }

      const data = await response.json()
      setSettings(data)
      
      form.reset({
        telegram_channel_id: data.telegram_channel_id || "",
        is_enabled: data.is_enabled || false,
      })
    } catch (error) {
      console.error('Error loading Telegram settings:', error)
      toast.error('Telegram ayarları yüklenirken hata oluştu')
    } finally {
      setIsLoadingSettings(false)
    }
  }

  const onSubmit = async (data: TelegramSettingsForm) => {
    try {
      setIsLoading(true)

      const response = await fetch(`/api/telegram/settings?salon_id=${salonId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Ayarlar kaydedilemedi')
      }

      setSettings(result.settings)
      toast.success('Telegram ayarları başarıyla kaydedildi')
    } catch (error) {
      console.error('Error saving Telegram settings:', error)
      toast.error(error instanceof Error ? error.message : 'Ayarlar kaydedilirken hata oluştu')
    } finally {
      setIsLoading(false)
    }
  }

  const sendTestNotification = async () => {
    try {
      setIsTesting(true)

      const response = await fetch('/api/telegram/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          salon_id: salonId,
        }),
      })

      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Test bildirimi gönderilemedi')
      }

      toast.success(result.message || 'Test bildirimi başarıyla gönderildi!')
    } catch (error) {
      console.error('Error sending test notification:', error)
      toast.error(error instanceof Error ? error.message : 'Test bildirimi gönderilirken hata oluştu')
    } finally {
      setIsTesting(false)
    }
  }

  const isEnabled = form.watch("is_enabled")
  const channelId = form.watch("telegram_channel_id")

  if (isLoadingSettings) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Telegram Bildirimleri
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Ayarlar yükleniyor...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Telegram Bildirimleri
        </CardTitle>
        <CardDescription>
          Randevu oluşturulduğunda ve iptal edildiğinde Telegram kanalınıza otomatik bildirim gönderin.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Telegram bildirimleri için önce bir Telegram botu oluşturmanız ve bot tokenini sistem yöneticisine vermeniz gerekir.
            Ardından botunuzu kanalınıza ekleyin ve kanal ID'sini aşağıya girin.
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="is_enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Telegram Bildirimleri
                    </FormLabel>
                    <FormDescription>
                      Randevu olayları için Telegram bildirimleri gönder
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="telegram_channel_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telegram Kanal ID</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="@kanaladi veya -1001234567890"
                      {...field}
                      disabled={!isEnabled}
                    />
                  </FormControl>
                  <FormDescription>
                    Telegram kanal kullanıcı adı (@kanaladi) veya kanal ID'si (-1001234567890 formatında)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-3">
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Ayarları Kaydet
              </Button>

              {isEnabled && channelId && (
                <>
                  <Separator orientation="vertical" className="h-10" />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={sendTestNotification}
                    disabled={isTesting || isLoading}
                  >
                    {isTesting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {!isTesting && <Send className="mr-2 h-4 w-4" />}
                    Test Bildirimi Gönder
                  </Button>
                </>
              )}
            </div>
          </form>
        </Form>

        {settings?.updated_at && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <CheckCircle className="h-4 w-4" />
            Son güncelleme: {new Date(settings.updated_at).toLocaleString('tr-TR')}
          </div>
        )}

        <Separator />

        <div className="space-y-3">
          <h4 className="text-sm font-medium">Telegram Bot Kurulum Rehberi</h4>
          <div className="text-sm text-muted-foreground space-y-2">
            <p>1. Telegram'da @BotFather'a mesaj gönderin</p>
            <p>2. /newbot komutunu kullanarak yeni bir bot oluşturun</p>
            <p>3. Bot tokenini sistem yöneticisine verin</p>
            <p>4. Botunuzu kanalınıza ekleyin ve yönetici yapın</p>
            <p>5. Kanal ID'sini öğrenmek için @userinfobot'u kullanabilirsiniz</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
