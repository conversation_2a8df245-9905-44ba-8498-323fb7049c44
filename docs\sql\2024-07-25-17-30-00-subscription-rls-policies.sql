-- SalonFlow Abonelik Sistemi RLS Politikaları
-- Oluşturulma Tarihi: 2024-07-25

-- 1. subscription_plans tablosu için RLS
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;

-- Herkes subscription_plans görebilir
CREATE POLICY "Herkes subscription_plans görebilir" ON subscription_plans
  FOR SELECT USING (true);

-- <PERSON><PERSON>e admin subscription_plans ekleyebilir, güncelleyebilir veya silebilir
CREATE POLICY "Admin subscription_plans ekleyebilir" ON subscription_plans
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin subscription_plans güncelleyebilir" ON subscription_plans
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin subscription_plans silebilir" ON subscription_plans
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- 2. salon_subscriptions tablosu için RLS
ALTER TABLE salon_subscriptions ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi aboneliklerini görebilir
CREATE POLICY "Salon sahipleri kendi aboneliklerini görebilir" ON salon_subscriptions
  FOR SELECT USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Admin tüm abonelikleri görebilir
CREATE POLICY "Admin tüm abonelikleri görebilir" ON salon_subscriptions
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Admin tüm abonelikleri ekleyebilir, güncelleyebilir veya silebilir
CREATE POLICY "Admin abonelikleri ekleyebilir" ON salon_subscriptions
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin abonelikleri güncelleyebilir" ON salon_subscriptions
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin abonelikleri silebilir" ON salon_subscriptions
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- 3. subscription_payments tablosu için RLS
ALTER TABLE subscription_payments ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi ödemelerini görebilir
CREATE POLICY "Salon sahipleri kendi ödemelerini görebilir" ON subscription_payments
  FOR SELECT USING (subscription_id IN (
    SELECT id FROM salon_subscriptions WHERE salon_id IN (
      SELECT id FROM salons WHERE owner_id = auth.uid()
    )
  ));

-- Admin tüm ödemeleri görebilir
CREATE POLICY "Admin tüm ödemeleri görebilir" ON subscription_payments
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Admin tüm ödemeleri ekleyebilir, güncelleyebilir veya silebilir
CREATE POLICY "Admin ödemeleri ekleyebilir" ON subscription_payments
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin ödemeleri güncelleyebilir" ON subscription_payments
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin ödemeleri silebilir" ON subscription_payments
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- 4. referral_codes tablosu için RLS
ALTER TABLE referral_codes ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi referans kodlarını görebilir
CREATE POLICY "Salon sahipleri kendi referans kodlarını görebilir" ON referral_codes
  FOR SELECT USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Admin tüm referans kodlarını görebilir
CREATE POLICY "Admin tüm referans kodlarını görebilir" ON referral_codes
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Salon sahipleri kendi referans kodlarını oluşturabilir
CREATE POLICY "Salon sahipleri referans kodu oluşturabilir" ON referral_codes
  FOR INSERT WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Admin tüm referans kodlarını ekleyebilir, güncelleyebilir veya silebilir
CREATE POLICY "Admin referans kodlarını ekleyebilir" ON referral_codes
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans kodlarını güncelleyebilir" ON referral_codes
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans kodlarını silebilir" ON referral_codes
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- 5. referral_benefits tablosu için RLS
ALTER TABLE referral_benefits ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi referans faydalarını görebilir
CREATE POLICY "Salon sahipleri kendi referans faydalarını görebilir" ON referral_benefits
  FOR SELECT USING (
    referrer_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()) OR 
    referred_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())
  );

-- Admin tüm referans faydalarını görebilir
CREATE POLICY "Admin tüm referans faydalarını görebilir" ON referral_benefits
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Admin tüm referans faydalarını ekleyebilir, güncelleyebilir veya silebilir
CREATE POLICY "Admin referans faydalarını ekleyebilir" ON referral_benefits
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans faydalarını güncelleyebilir" ON referral_benefits
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans faydalarını silebilir" ON referral_benefits
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));
