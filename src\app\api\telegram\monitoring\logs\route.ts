// Telegram Notification Logs API
// Provides access to notification history and audit logs

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { authenticateUser, checkSalonAccess } from '@/lib/middleware/auth';

// GET /api/telegram/monitoring/logs
export async function GET(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. EXTRACT QUERY PARAMETERS
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const notificationType = searchParams.get('type');
    const appointmentId = searchParams.get('appointment_id');

    if (!salonId) {
      return NextResponse.json(
        { success: false, error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Validate limit parameter
    if (limit < 1 || limit > 200) {
      return NextResponse.json(
        { success: false, error: 'Limit must be between 1 and 200' },
        { status: 400 }
      );
    }

    // 3. SALON ACCESS CONTROL
    const accessResult = await checkSalonAccess(authResult.user, salonId);
    if (!accessResult.success) {
      return NextResponse.json(
        { success: false, error: accessResult.error || 'Failed to verify salon access' },
        { status: accessResult.statusCode || 500 }
      );
    }

    if (!accessResult.hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Access denied to salon' },
        { status: 403 }
      );
    }

    // 4. BUILD QUERY
    const supabase = createRouteHandlerClient({ cookies });

    let query = supabase
      .from('telegram_notification_log')
      .select('*')
      .eq('salon_id', salonId)
      .order('sent_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (notificationType) {
      query = query.eq('notification_type', notificationType);
    }

    if (appointmentId) {
      query = query.eq('appointment_id', appointmentId);
    }

    // 5. FETCH LOGS
    const { data: logs, error: logsError, count } = await query;

    if (logsError) {
      console.error('Error fetching notification logs:', logsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch notification logs' },
        { status: 500 }
      );
    }

    // 6. GET TOTAL COUNT FOR PAGINATION
    let totalQuery = supabase
      .from('telegram_notification_log')
      .select('id', { count: 'exact', head: true })
      .eq('salon_id', salonId);

    if (notificationType) {
      totalQuery = totalQuery.eq('notification_type', notificationType);
    }

    if (appointmentId) {
      totalQuery = totalQuery.eq('appointment_id', appointmentId);
    }

    const { count: totalCount, error: countError } = await totalQuery;

    if (countError) {
      console.error('Error fetching total count:', countError);
    }

    // 7. PREPARE RESPONSE
    const pagination = {
      limit,
      offset,
      total: totalCount || 0,
      hasMore: (offset + limit) < (totalCount || 0)
    };

    return NextResponse.json({
      success: true,
      logs: logs || [],
      pagination,
      filters: {
        salon_id: salonId,
        notification_type: notificationType,
        appointment_id: appointmentId
      },
      message: 'Notification logs retrieved successfully'
    });

  } catch (error) {
    console.error('Error in GET /api/telegram/monitoring/logs:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/telegram/monitoring/logs (cleanup old logs - admin only)
export async function DELETE(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. ADMIN CHECK
    if (!authResult.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // 3. PARSE REQUEST BODY
    const body = await request.json();
    const { days_older_than = 30, salon_id } = body;

    // Validate days parameter
    if (days_older_than < 1 || days_older_than > 365) {
      return NextResponse.json(
        { success: false, error: 'Days must be between 1 and 365' },
        { status: 400 }
      );
    }

    // 4. PERFORM CLEANUP
    const supabase = createRouteHandlerClient({ cookies });

    if (salon_id) {
      // Cleanup for specific salon
      const cutoffDate = new Date(Date.now() - days_older_than * 24 * 60 * 60 * 1000);
      
      const { count, error } = await supabase
        .from('telegram_notification_log')
        .delete()
        .eq('salon_id', salon_id)
        .lt('sent_at', cutoffDate.toISOString());

      if (error) {
        console.error('Error cleaning up salon logs:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to cleanup logs' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        deletedCount: count || 0,
        salon_id,
        cutoffDate: cutoffDate.toISOString(),
        message: `Cleaned up ${count || 0} log entries for salon`
      });
    } else {
      // Global cleanup using database function
      const { data: deletedCount, error } = await supabase.rpc('cleanup_old_notification_logs');

      if (error) {
        console.error('Error running global cleanup:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to run global cleanup' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        deletedCount: deletedCount || 0,
        message: `Global cleanup completed, removed ${deletedCount || 0} old log entries`
      });
    }

  } catch (error) {
    console.error('Error in DELETE /api/telegram/monitoring/logs:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/telegram/monitoring/logs/search (advanced search)
export async function POST(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. PARSE REQUEST BODY
    const body = await request.json();
    const { 
      salon_id, 
      start_date, 
      end_date, 
      notification_types = [], 
      appointment_ids = [],
      hash_prefix,
      limit = 50,
      offset = 0
    } = body;

    if (!salon_id) {
      return NextResponse.json(
        { success: false, error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // 3. SALON ACCESS CONTROL
    const accessResult = await checkSalonAccess(authResult.user, salon_id);
    if (!accessResult.success || !accessResult.hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Access denied to salon' },
        { status: 403 }
      );
    }

    // 4. BUILD ADVANCED QUERY
    const supabase = createRouteHandlerClient({ cookies });

    let query = supabase
      .from('telegram_notification_log')
      .select('*')
      .eq('salon_id', salon_id)
      .order('sent_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply date filters
    if (start_date) {
      query = query.gte('sent_at', new Date(start_date).toISOString());
    }

    if (end_date) {
      query = query.lte('sent_at', new Date(end_date).toISOString());
    }

    // Apply notification type filter
    if (notification_types.length > 0) {
      query = query.in('notification_type', notification_types);
    }

    // Apply appointment ID filter
    if (appointment_ids.length > 0) {
      query = query.in('appointment_id', appointment_ids);
    }

    // Apply hash prefix filter
    if (hash_prefix) {
      query = query.like('notification_hash', `${hash_prefix}%`);
    }

    // 5. EXECUTE SEARCH
    const { data: logs, error: searchError } = await query;

    if (searchError) {
      console.error('Error executing advanced search:', searchError);
      return NextResponse.json(
        { success: false, error: 'Failed to execute search' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      logs: logs || [],
      searchCriteria: {
        salon_id,
        start_date,
        end_date,
        notification_types,
        appointment_ids,
        hash_prefix
      },
      resultCount: logs?.length || 0,
      message: 'Advanced search completed successfully'
    });

  } catch (error) {
    console.error('Error in POST /api/telegram/monitoring/logs/search:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
