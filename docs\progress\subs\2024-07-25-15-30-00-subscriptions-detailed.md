# SalonFlow Abonelik Sistemi Detaylı Geliştirme Planı

## 1. Veritabanı Yapısı

### 1.1. Abonelik Tabloları Oluşturma
- **Zorluk:** Orta
- **<PERSON><PERSON><PERSON>:** 1 gün
- **Bağ<PERSON>mlılıklar:** Yok

#### 1.1.1. subscription_plans Tablosu
```sql
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  price_monthly DECIMAL(10, 2) NOT NULL,
  price_yearly DECIMAL(10, 2) NOT NULL,
  max_staff INTEGER NOT NULL,
  features JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> planları ekle
INSERT INTO subscription_plans (name, price_monthly, price_yearly, max_staff, features) VALUES
('Solo', 750.00, 8100.00, 1, '{"analytics": false, "finance": false, "custom_domain": false}'),
('Small Team', 1500.00, 16200.00, 5, '{"analytics": true, "finance": true, "custom_domain": false}'),
('Pro Salon', 5000.00, 54000.00, 20, '{"analytics": true, "finance": true, "custom_domain": true}');
```

#### 1.1.2. salon_subscriptions Tablosu Güncelleme
```sql
-- Mevcut salon_subscriptions tablosunu güncelle
ALTER TABLE salon_subscriptions
DROP COLUMN plan,
ADD COLUMN plan_id UUID REFERENCES subscription_plans(id),
ADD COLUMN status TEXT NOT NULL CHECK (status IN ('trial', 'active', 'past_due', 'canceled', 'suspended')),
ADD COLUMN trial_end_date DATE,
ADD COLUMN payment_method TEXT NOT NULL CHECK (payment_method IN ('manual', 'iyzico', 'paytr')),
ADD COLUMN is_yearly BOOLEAN DEFAULT FALSE;
```

#### 1.1.3. subscription_payments Tablosu
```sql
CREATE TABLE subscription_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subscription_id UUID REFERENCES salon_subscriptions(id) ON DELETE CASCADE,
  amount DECIMAL(10, 2) NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')),
  payment_date DATE,
  payment_method TEXT NOT NULL CHECK (payment_method IN ('manual', 'iyzico', 'paytr')),
  invoice_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 1.1.4. referral_codes Tablosu
```sql
CREATE TABLE referral_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  code TEXT NOT NULL UNIQUE,
  uses INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 1.1.5. referral_benefits Tablosu
```sql
CREATE TABLE referral_benefits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  referrer_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  referred_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('pending', 'applied', 'expired')),
  benefit_amount DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 1.2. RLS Politikaları Oluşturma
- **Zorluk:** Orta
- **Tahmini Süre:** 4 saat
- **Bağımlılıklar:** Abonelik tabloları oluşturma (1.1)

```sql
-- subscription_plans tablosu için RLS
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Herkes subscription_plans görebilir" ON subscription_plans
  FOR SELECT USING (true);

-- salon_subscriptions tablosu için RLS
ALTER TABLE salon_subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon sahipleri kendi aboneliklerini görebilir" ON salon_subscriptions
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Admin tüm abonelikleri görebilir" ON salon_subscriptions
  USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- subscription_payments tablosu için RLS
ALTER TABLE subscription_payments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon sahipleri kendi ödemelerini görebilir" ON subscription_payments
  USING (subscription_id IN (SELECT id FROM salon_subscriptions WHERE salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())));
CREATE POLICY "Admin tüm ödemeleri görebilir" ON subscription_payments
  USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- referral_codes tablosu için RLS
ALTER TABLE referral_codes ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon sahipleri kendi referans kodlarını görebilir" ON referral_codes
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Admin tüm referans kodlarını görebilir" ON referral_codes
  USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- referral_benefits tablosu için RLS
ALTER TABLE referral_benefits ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon sahipleri kendi referans faydalarını görebilir" ON referral_benefits
  USING (referrer_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()) OR referred_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Admin tüm referans faydalarını görebilir" ON referral_benefits
  USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));
```

### 1.3. TypeScript Tip Tanımlamaları Güncelleme
- **Zorluk:** Kolay
- **Tahmini Süre:** 2 saat
- **Bağımlılıklar:** Abonelik tabloları oluşturma (1.1)

```typescript
// src/lib/db/types.ts dosyasına eklenecek

// Subscription plan type
export interface SubscriptionPlan {
  id: string;
  name: string;
  price_monthly: number;
  price_yearly: number;
  max_staff: number;
  features: {
    analytics: boolean;
    finance: boolean;
    custom_domain: boolean;
    [key: string]: any;
  };
  created_at: string;
}

// Updated salon subscription type
export interface SalonSubscription {
  id: string;
  salon_id: string;
  plan_id: string;
  status: 'trial' | 'active' | 'past_due' | 'canceled' | 'suspended';
  start_date: string;
  end_date?: string;
  trial_end_date?: string;
  payment_method: 'manual' | 'iyzico' | 'paytr';
  is_yearly: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  // Join fields
  plans?: SubscriptionPlan;
}

// Subscription payment type
export interface SubscriptionPayment {
  id: string;
  subscription_id: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  payment_date?: string;
  payment_method: 'manual' | 'iyzico' | 'paytr';
  invoice_number?: string;
  created_at: string;
}

// Referral code type
export interface ReferralCode {
  id: string;
  salon_id: string;
  code: string;
  uses: number;
  created_at: string;
}

// Referral benefit type
export interface ReferralBenefit {
  id: string;
  referrer_id: string;
  referred_id: string;
  status: 'pending' | 'applied' | 'expired';
  benefit_amount: number;
  created_at: string;
}

// Insert ve Update tipleri
export type SubscriptionPlanInsert = Omit<SubscriptionPlan, 'id' | 'created_at'>;
export type SalonSubscriptionInsert = Omit<SalonSubscription, 'id' | 'created_at' | 'updated_at' | 'plans'>;
export type SubscriptionPaymentInsert = Omit<SubscriptionPayment, 'id' | 'created_at'>;
export type ReferralCodeInsert = Omit<ReferralCode, 'id' | 'created_at'>;
export type ReferralBenefitInsert = Omit<ReferralBenefit, 'id' | 'created_at'>;

export type SubscriptionPlanUpdate = Partial<Omit<SubscriptionPlan, 'id' | 'created_at'>> & { id: string };
export type SalonSubscriptionUpdate = Partial<Omit<SalonSubscription, 'id' | 'created_at' | 'updated_at' | 'plans'>> & { id: string };
export type SubscriptionPaymentUpdate = Partial<Omit<SubscriptionPayment, 'id' | 'created_at'>> & { id: string };
export type ReferralCodeUpdate = Partial<Omit<ReferralCode, 'id' | 'created_at'>> & { id: string };
export type ReferralBenefitUpdate = Partial<Omit<ReferralBenefit, 'id' | 'created_at'>> & { id: string };
```

## 2. Backend İşlevselliği

### 2.1. Abonelik Yönetimi API'leri
- **Zorluk:** Orta
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** TypeScript tip tanımlamaları (1.3)

#### 2.1.1. Abonelik Planları API'si
```typescript
// src/lib/db/subscription-plans.ts
import { getSupabaseBrowser } from '../supabase';
import { SubscriptionPlan, SubscriptionPlanInsert, SubscriptionPlanUpdate } from './types';

const supabase = getSupabaseBrowser();

/**
 * Get all subscription plans
 */
export async function getSubscriptionPlans() {
  const { data, error } = await supabase
    .from('subscription_plans')
    .select('*')
    .order('price_monthly');

  if (error) throw error;
  return data as SubscriptionPlan[];
}

/**
 * Get a subscription plan by ID
 */
export async function getSubscriptionPlanById(id: string) {
  const { data, error } = await supabase
    .from('subscription_plans')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as SubscriptionPlan;
}
```

#### 2.1.2. Salon Abonelikleri API'si Güncelleme
```typescript
// src/lib/db/subscriptions.ts güncelleme
import { getSupabaseBrowser } from '../supabase';
import {
  SalonSubscription,
  SalonSubscriptionInsert,
  SalonSubscriptionUpdate,
  SubscriptionPayment,
  SubscriptionPaymentInsert
} from './types';

const supabase = getSupabaseBrowser();

/**
 * Get the active subscription for a salon with plan details
 */
export async function getActiveSalonSubscription(salonId: string) {
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .select('*, plans:plan_id(*)')
    .eq('salon_id', salonId)
    .eq('is_active', true)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as SalonSubscription | null;
}

/**
 * Create a trial subscription for a salon
 */
export async function createTrialSubscription(salonId: string, planId: string) {
  const today = new Date();
  const trialEndDate = new Date();
  trialEndDate.setDate(today.getDate() + 14); // 14 gün deneme süresi

  const subscription: SalonSubscriptionInsert = {
    salon_id: salonId,
    plan_id: planId,
    status: 'trial',
    start_date: today.toISOString().split('T')[0],
    trial_end_date: trialEndDate.toISOString().split('T')[0],
    payment_method: 'manual',
    is_yearly: false,
    is_active: true
  };

  return createSalonSubscription(subscription);
}

/**
 * Upgrade a subscription to a new plan
 */
export async function upgradeSubscription(subscriptionId: string, newPlanId: string, isYearly: boolean) {
  const { data: currentSub, error: fetchError } = await supabase
    .from('salon_subscriptions')
    .select('*')
    .eq('id', subscriptionId)
    .single();

  if (fetchError) throw fetchError;

  // Aboneliği güncelle
  const updateData: SalonSubscriptionUpdate = {
    id: subscriptionId,
    plan_id: newPlanId,
    is_yearly: isYearly,
    status: 'active'
  };

  return updateSalonSubscription(updateData);
}

/**
 * Check if a salon's trial is expired
 */
export async function isTrialExpired(salonId: string) {
  const subscription = await getActiveSalonSubscription(salonId);

  if (!subscription) return true;
  if (subscription.status !== 'trial') return true;

  const today = new Date();
  const trialEndDate = new Date(subscription.trial_end_date || '');

  return today > trialEndDate;
}
```

#### 2.1.3. Ödeme Yönetimi API'si
```typescript
// src/lib/db/subscription-payments.ts
import { getSupabaseBrowser } from '../supabase';
import { SubscriptionPayment, SubscriptionPaymentInsert, SubscriptionPaymentUpdate } from './types';

const supabase = getSupabaseBrowser();

/**
 * Get all payments for a subscription
 */
export async function getSubscriptionPayments(subscriptionId: string) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .select('*')
    .eq('subscription_id', subscriptionId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as SubscriptionPayment[];
}

/**
 * Create a new payment record
 */
export async function createSubscriptionPayment(payment: SubscriptionPaymentInsert) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .insert(payment)
    .select()
    .single();

  if (error) throw error;
  return data as SubscriptionPayment;
}

/**
 * Update a payment record
 */
export async function updateSubscriptionPayment({ id, ...payment }: SubscriptionPaymentUpdate) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .update(payment)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SubscriptionPayment;
}
```

#### 2.1.4. Referans Sistemi API'si
```typescript
// src/lib/db/referrals.ts
import { getSupabaseBrowser } from '../supabase';
import { ReferralCode, ReferralCodeInsert, ReferralBenefit, ReferralBenefitInsert } from './types';
import { nanoid } from 'nanoid';

const supabase = getSupabaseBrowser();

/**
 * Get referral code for a salon
 */
export async function getSalonReferralCode(salonId: string) {
  const { data, error } = await supabase
    .from('referral_codes')
    .select('*')
    .eq('salon_id', salonId)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as ReferralCode | null;
}

/**
 * Create a new referral code for a salon
 */
export async function createReferralCode(salonId: string) {
  // Önce mevcut kodu kontrol et
  const existingCode = await getSalonReferralCode(salonId);
  if (existingCode) return existingCode;

  // Yeni kod oluştur
  const code = nanoid(8).toUpperCase();

  const referralCode: ReferralCodeInsert = {
    salon_id: salonId,
    code,
    uses: 0
  };

  const { data, error } = await supabase
    .from('referral_codes')
    .insert(referralCode)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralCode;
}

/**
 * Apply a referral code during registration
 */
export async function applyReferralCode(code: string, newSalonId: string) {
  // Referans kodunu bul
  const { data: referralCode, error: codeError } = await supabase
    .from('referral_codes')
    .select('*')
    .eq('code', code)
    .single();

  if (codeError) throw codeError;

  // Referans veren salon ID'sini al
  const referrerId = referralCode.salon_id;

  // Kullanım sayısını artır
  await supabase
    .from('referral_codes')
    .update({ uses: referralCode.uses + 1 })
    .eq('id', referralCode.id);

  // Referans faydası oluştur
  const benefit: ReferralBenefitInsert = {
    referrer_id: referrerId,
    referred_id: newSalonId,
    status: 'pending',
    benefit_amount: 750 // 1 aylık Solo plan bedeli
  };

  const { data, error } = await supabase
    .from('referral_benefits')
    .insert(benefit)
    .select()
    .single();

  if (error) throw error;
  return data as ReferralBenefit;
}
```

### 2.2. Abonelik Durumu Kontrolü ve Kısıtlama Mekanizması
- **Zorluk:** Zor
- **Tahmini Süre:** 3 gün
- **Bağımlılıklar:** Abonelik Yönetimi API'leri (2.1)

#### 2.2.1. Abonelik Durumu Kontrolü Middleware
```typescript
// src/middleware/subscription-check.ts
import { NextRequest, NextResponse } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { getActiveSalonSubscription, isTrialExpired } from '@/lib/db/subscriptions';

export async function subscriptionMiddleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  // Kullanıcı oturumunu kontrol et
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) return res;

  // Salon ID'sini al
  const { data: salon } = await supabase
    .from('salons')
    .select('id')
    .eq('owner_id', session.user.id)
    .single();

  if (!salon) return res;

  // Abonelik durumunu kontrol et
  const subscription = await getActiveSalonSubscription(salon.id);

  // Abonelik yoksa veya askıya alınmışsa
  if (!subscription || subscription.status === 'suspended') {
    // Ödeme sayfasına yönlendir
    return NextResponse.redirect(new URL('/dashboard/subscription/payment', req.url));
  }

  // Deneme süresi bitmişse
  if (subscription.status === 'trial' && isTrialExpired(salon.id)) {
    // Ödeme sayfasına yönlendir
    return NextResponse.redirect(new URL('/dashboard/subscription/payment', req.url));
  }

  // Ödeme gecikmiş durumda ise
  if (subscription.status === 'past_due') {
    // Ödeme sayfasına yönlendir, ancak 7 gün içinde
    const pastDueDate = new Date(subscription.updated_at);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - pastDueDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 7) {
      return NextResponse.redirect(new URL('/dashboard/subscription/payment', req.url));
    }
  }

  return res;
}
```

#### 2.2.2. Özellik Erişim Kontrolü Hook
```typescript
// src/hooks/useSubscriptionFeatures.ts
import { useEffect, useState } from 'react';
import { useUser } from '@/contexts/UserContext';
import { getActiveSalonSubscription } from '@/lib/db/subscriptions';
import { getSubscriptionPlanById } from '@/lib/db/subscription-plans';

export function useSubscriptionFeatures() {
  const { salonId } = useUser();
  const [features, setFeatures] = useState<{
    maxStaff: number;
    hasAnalytics: boolean;
    hasFinance: boolean;
    hasCustomDomain: boolean;
    [key: string]: any;
  }>({
    maxStaff: 1,
    hasAnalytics: false,
    hasFinance: false,
    hasCustomDomain: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    async function loadFeatures() {
      if (!salonId) return;

      try {
        setIsLoading(true);
        const subscription = await getActiveSalonSubscription(salonId);

        if (!subscription || !subscription.plan_id) {
          setFeatures({
            maxStaff: 1,
            hasAnalytics: false,
            hasFinance: false,
            hasCustomDomain: false
          });
          return;
        }

        const plan = await getSubscriptionPlanById(subscription.plan_id);

        setFeatures({
          maxStaff: plan.max_staff,
          hasAnalytics: plan.features.analytics,
          hasFinance: plan.features.finance,
          hasCustomDomain: plan.features.custom_domain,
          ...plan.features
        });
      } catch (err) {
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    }

    loadFeatures();
  }, [salonId]);

  return { features, isLoading, error };
}
```

### 2.3. Bildirim Sistemi Entegrasyonu
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** Abonelik Yönetimi API'leri (2.1)

#### 2.3.1. Abonelik Bildirimleri Trigger'ı
```sql
-- Abonelik bildirimleri için trigger
CREATE OR REPLACE FUNCTION create_subscription_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  notification_title TEXT;
  notification_message TEXT;
  notification_type TEXT := 'subscription_update';
BEGIN
  -- Salon sahibi ID'sini al
  SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;

  -- Bildirim içeriğini belirle
  IF NEW.status = 'trial' THEN
    notification_title := 'Deneme Süresi Başladı';
    notification_message := 'Abonelik deneme süreniz başladı. 14 gün boyunca tüm özellikleri ücretsiz kullanabilirsiniz.';
  ELSIF NEW.status = 'active' AND OLD.status = 'trial' THEN
    notification_title := 'Aboneliğiniz Aktifleştirildi';
    notification_message := 'Deneme süreniz sona erdi ve aboneliğiniz aktifleştirildi.';
  ELSIF NEW.status = 'past_due' THEN
    notification_title := 'Ödeme Hatırlatması';
    notification_message := 'Abonelik ödemesi gecikti. Lütfen en kısa sürede ödemenizi yapın.';
  ELSIF NEW.status = 'suspended' THEN
    notification_title := 'Abonelik Askıya Alındı';
    notification_message := 'Ödeme yapılmadığı için aboneliğiniz askıya alındı.';
  ELSIF NEW.status = 'canceled' THEN
    notification_title := 'Abonelik İptal Edildi';
    notification_message := 'Aboneliğiniz iptal edildi.';
  END IF;

  -- Bildirim oluştur
  IF notification_title IS NOT NULL THEN
    INSERT INTO notifications (
      salon_id,
      user_id,
      type,
      title,
      message,
      read,
      data
    ) VALUES (
      NEW.salon_id,
      salon_owner_id,
      notification_type,
      notification_title,
      notification_message,
      FALSE,
      jsonb_build_object('subscription_id', NEW.id, 'status', NEW.status)
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ı salon_subscriptions tablosuna bağla
CREATE TRIGGER subscription_notification_trigger
AFTER INSERT OR UPDATE OF status
ON salon_subscriptions
FOR EACH ROW
EXECUTE FUNCTION create_subscription_notification();
```

#### 2.3.2. Deneme Süresi Bitimine Yaklaşma Bildirimi
```typescript
// src/lib/cron/subscription-reminders.ts
import { createClient } from '@supabase/supabase-js';
import { addDays, format } from 'date-fns';

// Bu kod sunucu tarafında çalışacak bir cron job için
export async function sendTrialEndingReminders() {
  const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_KEY!);

  const today = new Date();
  const threeDaysLater = addDays(today, 3);
  const formattedDate = format(threeDaysLater, 'yyyy-MM-dd');

  // Deneme süresi 3 gün içinde bitecek abonelikler
  const { data: subscriptions, error } = await supabase
    .from('salon_subscriptions')
    .select('*, salons(id, owner_id)')
    .eq('status', 'trial')
    .eq('trial_end_date', formattedDate)
    .eq('is_active', true);

  if (error) {
    console.error('Error fetching trial subscriptions:', error);
    return;
  }

  // Her abonelik için bildirim oluştur
  for (const subscription of subscriptions) {
    await supabase
      .from('notifications')
      .insert({
        salon_id: subscription.salon_id,
        user_id: subscription.salons.owner_id,
        type: 'subscription_reminder',
        title: 'Deneme Süreniz Bitiyor',
        message: 'Deneme sürenizin bitmesine 3 gün kaldı. Kesintisiz hizmet için aboneliğinizi aktifleştirin.',
        read: false,
        data: { subscription_id: subscription.id, trial_end_date: subscription.trial_end_date }
      });
  }
}
```

## 3. Frontend Bileşenleri

### 3.1. Salon Sahibi Abonelik Paneli
- **Zorluk:** Orta
- **Tahmini Süre:** 3 gün
- **Bağımlılıklar:** Abonelik Yönetimi API'leri (2.1)

#### 3.1.1. Abonelik Durumu Sayfası
```typescript
// src/app/dashboard/subscription/page.tsx
"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowRight, Check, AlertCircle } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useUser } from "@/contexts/UserContext"
import { getActiveSalonSubscription } from "@/lib/db/subscriptions"
import { getSubscriptionPlans } from "@/lib/db/subscription-plans"
import { getSubscriptionPayments } from "@/lib/db/subscription-payments"
import { SalonSubscription, SubscriptionPlan, SubscriptionPayment } from "@/lib/db/types"

export default function SubscriptionPage() {
  const { salonId } = useUser()
  const [subscription, setSubscription] = useState<SalonSubscription | null>(null)
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [payments, setPayments] = useState<SubscriptionPayment[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadData() {
      if (!salonId) return

      try {
        setIsLoading(true)

        // Abonelik bilgilerini yükle
        const subscriptionData = await getActiveSalonSubscription(salonId)
        setSubscription(subscriptionData)

        // Plan bilgilerini yükle
        const plansData = await getSubscriptionPlans()
        setPlans(plansData)

        // Ödeme geçmişini yükle (eğer abonelik varsa)
        if (subscriptionData) {
          const paymentsData = await getSubscriptionPayments(subscriptionData.id)
          setPayments(paymentsData)
        }
      } catch (error) {
        console.error("Abonelik bilgileri yüklenirken hata:", error)
        toast.error("Abonelik bilgileri yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [salonId])

  // Abonelik durumunu Türkçe olarak göster
  const getStatusText = (status: string) => {
    switch (status) {
      case 'trial': return 'Deneme Süresi'
      case 'active': return 'Aktif'
      case 'past_due': return 'Ödeme Gecikti'
      case 'canceled': return 'İptal Edildi'
      case 'suspended': return 'Askıya Alındı'
      default: return status
    }
  }

  // Abonelik durumuna göre renk belirle
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trial': return 'bg-blue-500'
      case 'active': return 'bg-green-500'
      case 'past_due': return 'bg-yellow-500'
      case 'canceled': return 'bg-red-500'
      case 'suspended': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  if (isLoading) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Abonelik</h1>
          </div>
        </header>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Abonelik</h1>
        </div>
      </header>

      <div className="space-y-6">
        {/* Mevcut Abonelik Bilgileri */}
        {subscription ? (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Mevcut Abonelik</CardTitle>
                  <CardDescription>Abonelik durumunuz ve detayları</CardDescription>
                </div>
                <Badge className={getStatusColor(subscription.status)}>
                  {getStatusText(subscription.status)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Plan</p>
                  <p className="text-lg font-semibold">{subscription.plans?.name || 'Bilinmiyor'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Ödeme Döngüsü</p>
                  <p className="text-lg font-semibold">{subscription.is_yearly ? 'Yıllık' : 'Aylık'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Başlangıç Tarihi</p>
                  <p className="text-lg font-semibold">
                    {format(new Date(subscription.start_date), 'd MMMM yyyy', { locale: tr })}
                  </p>
                </div>
                {subscription.status === 'trial' && subscription.trial_end_date && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Deneme Süresi Bitiş</p>
                    <p className="text-lg font-semibold">
                      {format(new Date(subscription.trial_end_date), 'd MMMM yyyy', { locale: tr })}
                    </p>
                  </div>
                )}
                {subscription.end_date && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Bitiş Tarihi</p>
                    <p className="text-lg font-semibold">
                      {format(new Date(subscription.end_date), 'd MMMM yyyy', { locale: tr })}
                    </p>
                  </div>
                )}
              </div>

              {/* Uyarı mesajları */}
              {subscription.status === 'trial' && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Deneme Süresi</AlertTitle>
                  <AlertDescription>
                    Deneme süreniz {subscription.trial_end_date ?
                      format(new Date(subscription.trial_end_date), 'd MMMM yyyy', { locale: tr }) :
                      '14 gün'} tarihinde sona erecek. Kesintisiz hizmet için aboneliğinizi aktifleştirin.
                  </AlertDescription>
                </Alert>
              )}

              {subscription.status === 'past_due' && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Ödeme Gecikti</AlertTitle>
                  <AlertDescription>
                    Abonelik ödemesi gecikti. Lütfen en kısa sürede ödemenizi yapın, aksi takdirde hizmetiniz askıya alınacak.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" asChild>
                <Link href="/dashboard/subscription/history">Ödeme Geçmişi</Link>
              </Button>
              {subscription.status !== 'canceled' && (
                <Button asChild>
                  <Link href="/dashboard/subscription/upgrade">
                    Plan Yükselt
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              )}
            </CardFooter>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Abonelik Bulunamadı</CardTitle>
              <CardDescription>Henüz aktif bir aboneliğiniz bulunmuyor</CardDescription>
            </CardHeader>
            <CardContent>
              <p>SalonFlow'u kullanmak için bir abonelik planı seçmelisiniz.</p>
            </CardContent>
            <CardFooter>
              <Button asChild>
                <Link href="/dashboard/subscription/plans">
                  Planları Görüntüle
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Plan Karşılaştırma */}
        <Card>
          <CardHeader>
            <CardTitle>Abonelik Planları</CardTitle>
            <CardDescription>Tüm planları karşılaştırın ve size uygun olanı seçin</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {plans.map((plan) => (
                <Card key={plan.id} className={
                  subscription?.plans?.id === plan.id ? 'border-primary' : ''
                }>
                  <CardHeader>
                    <CardTitle>{plan.name}</CardTitle>
                    <CardDescription>
                      <div className="flex flex-col">
                        <span className="text-xl font-bold">{plan.price_monthly} TL/ay</span>
                        <span className="text-sm">veya {plan.price_yearly} TL/yıl</span>
                      </div>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      <li className="flex items-center">
                        <Check className="mr-2 h-4 w-4 text-green-500" />
                        <span>Maksimum {plan.max_staff} personel</span>
                      </li>
                      <li className="flex items-center">
                        <Check className="mr-2 h-4 w-4 text-green-500" />
                        <span>Sınırsız randevu</span>
                      </li>
                      {plan.features.analytics && (
                        <li className="flex items-center">
                          <Check className="mr-2 h-4 w-4 text-green-500" />
                          <span>Gelişmiş analitik</span>
                        </li>
                      )}
                      {plan.features.finance && (
                        <li className="flex items-center">
                          <Check className="mr-2 h-4 w-4 text-green-500" />
                          <span>Finans yönetimi</span>
                        </li>
                      )}
                      {plan.features.custom_domain && (
                        <li className="flex items-center">
                          <Check className="mr-2 h-4 w-4 text-green-500" />
                          <span>Özel alan adı</span>
                        </li>
                      )}
                    </ul>
                  </CardContent>
                  <CardFooter>
                    {subscription?.plans?.id === plan.id ? (
                      <Button variant="outline" className="w-full" disabled>
                        Mevcut Plan
                      </Button>
                    ) : (
                      <Button
                        className="w-full"
                        asChild
                        disabled={subscription?.plans &&
                          plans.findIndex(p => p.id === subscription.plans?.id) >
                          plans.findIndex(p => p.id === plan.id)}
                      >
                        <Link href={`/dashboard/subscription/upgrade?plan=${plan.id}`}>
                          {subscription ? 'Yükselt' : 'Seç'}
                        </Link>
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
```

#### 3.1.2. Plan Yükseltme Sayfası
```typescript
// src/app/dashboard/subscription/upgrade/page.tsx
"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { ArrowLeft, CreditCard, Calendar } from "lucide-react"
import { toast } from "sonner"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useUser } from "@/contexts/UserContext"
import { getActiveSalonSubscription, upgradeSubscription } from "@/lib/db/subscriptions"
import { getSubscriptionPlanById } from "@/lib/db/subscription-plans"
import { SubscriptionPlan } from "@/lib/db/types"

export default function UpgradeSubscriptionPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const planId = searchParams.get('plan')
  const { salonId } = useUser()

  const [currentSubscription, setCurrentSubscription] = useState<any>(null)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [isYearly, setIsYearly] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    async function loadData() {
      if (!salonId) return

      try {
        setIsLoading(true)

        // Mevcut aboneliği yükle
        const subscription = await getActiveSalonSubscription(salonId)
        setCurrentSubscription(subscription)

        // Seçilen planı yükle (URL'den)
        if (planId) {
          const plan = await getSubscriptionPlanById(planId)
          setSelectedPlan(plan)
        }

        // Mevcut ödeme döngüsünü ayarla
        if (subscription) {
          setIsYearly(subscription.is_yearly)
        }
      } catch (error) {
        console.error("Abonelik bilgileri yüklenirken hata:", error)
        toast.error("Abonelik bilgileri yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [salonId, planId])

  const handleUpgrade = async () => {
    if (!currentSubscription || !selectedPlan || !salonId) {
      toast.error("Gerekli bilgiler eksik. Lütfen tekrar deneyin.")
      return
    }

    try {
      setIsSubmitting(true)

      // Aboneliği yükselt
      await upgradeSubscription(currentSubscription.id, selectedPlan.id, isYearly)

      toast.success("Aboneliğiniz başarıyla yükseltildi!")
      router.push('/dashboard/subscription')
    } catch (error) {
      console.error("Abonelik yükseltme hatası:", error)
      toast.error("Abonelik yükseltilirken bir hata oluştu.")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Plan Yükseltme</h1>
          </div>
        </header>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!selectedPlan) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Plan Yükseltme</h1>
          </div>
        </header>
        <Card>
          <CardHeader>
            <CardTitle>Plan Seçilmedi</CardTitle>
            <CardDescription>Lütfen yükseltmek istediğiniz planı seçin</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Abonelik planları sayfasından yükseltmek istediğiniz planı seçebilirsiniz.</p>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href="/dashboard/subscription">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Abonelik Sayfasına Dön
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/subscription">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Plan Yükseltme</h1>
        </div>
      </header>

      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Plan Yükseltme: {selectedPlan.name}</CardTitle>
            <CardDescription>Aboneliğinizi yükseltmek için aşağıdaki bilgileri onaylayın</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Ödeme Döngüsü Seçimi */}
            <div>
              <h3 className="text-lg font-medium mb-3">Ödeme Döngüsü</h3>
              <RadioGroup
                value={isYearly ? "yearly" : "monthly"}
                onValueChange={(value) => setIsYearly(value === "yearly")}
                className="grid grid-cols-2 gap-4"
              >
                <div>
                  <RadioGroupItem
                    value="monthly"
                    id="monthly"
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="monthly"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <CreditCard className="mb-3 h-6 w-6" />
                    <span className="text-lg font-medium">Aylık</span>
                    <span className="text-sm text-muted-foreground">{selectedPlan.price_monthly} TL/ay</span>
                  </Label>
                </div>
                <div>
                  <RadioGroupItem
                    value="yearly"
                    id="yearly"
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor="yearly"
                    className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                  >
                    <Calendar className="mb-3 h-6 w-6" />
                    <span className="text-lg font-medium">Yıllık</span>
                    <span className="text-sm text-muted-foreground">{selectedPlan.price_yearly} TL/yıl</span>
                    <span className="text-xs text-green-500 mt-1">%10 Tasarruf</span>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* Özet */}
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-3">Ödeme Özeti</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Plan</span>
                  <span>{selectedPlan.name}</span>
                </div>
                <div className="flex justify-between">
                  <span>Döngü</span>
                  <span>{isYearly ? 'Yıllık' : 'Aylık'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tutar</span>
                  <span>{isYearly ? selectedPlan.price_yearly : selectedPlan.price_monthly} TL</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-bold">
                  <span>Toplam</span>
                  <span>{isYearly ? selectedPlan.price_yearly : selectedPlan.price_monthly} TL</span>
                </div>
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              <p>Ödeme işlemi, manuel havale/EFT ile gerçekleştirilecektir. Yükseltme işleminden sonra ödeme talimatları görüntülenecektir.</p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" asChild>
              <Link href="/dashboard/subscription">İptal</Link>
            </Button>
            <Button onClick={handleUpgrade} disabled={isSubmitting}>
              {isSubmitting ? "İşleniyor..." : "Planı Yükselt"}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
```

#### 3.1.3. Ödeme Geçmişi Sayfası
```typescript
// src/app/dashboard/subscription/history/page.tsx
"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"

import { Button } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { useUser } from "@/contexts/UserContext"
import { getActiveSalonSubscription } from "@/lib/db/subscriptions"
import { getSubscriptionPayments } from "@/lib/db/subscription-payments"
import { SubscriptionPayment } from "@/lib/db/types"

export default function PaymentHistoryPage() {
  const { salonId } = useUser()
  const [payments, setPayments] = useState<SubscriptionPayment[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadPayments() {
      if (!salonId) return

      try {
        setIsLoading(true)

        // Aktif aboneliği al
        const subscription = await getActiveSalonSubscription(salonId)

        if (!subscription) {
          setPayments([])
          return
        }

        // Ödeme geçmişini yükle
        const paymentsData = await getSubscriptionPayments(subscription.id)
        setPayments(paymentsData)
      } catch (error) {
        console.error("Ödeme geçmişi yüklenirken hata:", error)
        toast.error("Ödeme geçmişi yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadPayments()
  }, [salonId])

  // Ödeme durumunu Türkçe olarak göster
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Bekliyor'
      case 'completed': return 'Tamamlandı'
      case 'failed': return 'Başarısız'
      default: return status
    }
  }

  // Ödeme durumuna göre renk belirle
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500'
      case 'completed': return 'bg-green-500'
      case 'failed': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  if (isLoading) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Button variant="outline" size="icon" asChild className="mr-2">
              <Link href="/dashboard/subscription">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold">Ödeme Geçmişi</h1>
          </div>
        </header>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/subscription">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Ödeme Geçmişi</h1>
        </div>
      </header>

      <div className="rounded-md border">
        <Table>
          <TableCaption>Abonelik ödeme geçmişiniz</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Tarih</TableHead>
              <TableHead>Tutar</TableHead>
              <TableHead>Ödeme Yöntemi</TableHead>
              <TableHead>Fatura No</TableHead>
              <TableHead>Durum</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {payments.length > 0 ? (
              payments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell>
                    {payment.payment_date ?
                      format(new Date(payment.payment_date), 'd MMMM yyyy', { locale: tr }) :
                      format(new Date(payment.created_at), 'd MMMM yyyy', { locale: tr })}
                  </TableCell>
                  <TableCell>{payment.amount} TL</TableCell>
                  <TableCell>
                    {payment.payment_method === 'manual' ? 'Havale/EFT' :
                     payment.payment_method === 'iyzico' ? 'iyzico' :
                     payment.payment_method === 'paytr' ? 'PayTR' :
                     payment.payment_method}
                  </TableCell>
                  <TableCell>{payment.invoice_number || '-'}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(payment.status)}>
                      {getStatusText(payment.status)}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  Henüz ödeme kaydı bulunmuyor.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
```

### 3.2. Admin Paneli
- **Zorluk:** Zor
- **Tahmini Süre:** 4 gün
- **Bağımlılıklar:** Abonelik Yönetimi API'leri (2.1)

#### 3.2.1. Admin Abonelik Yönetimi Sayfası
```typescript
// src/app/admin/subscriptions/page.tsx
"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Edit, Eye, Search } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { getSupabaseBrowser } from "@/lib/supabase"

export default function AdminSubscriptionsPage() {
  const [subscriptions, setSubscriptions] = useState<any[]>([])
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<any[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  useEffect(() => {
    async function loadSubscriptions() {
      try {
        setIsLoading(true)

        const supabase = getSupabaseBrowser()

        // Admin yetkisi kontrolü
        const { data: { user } } = await supabase.auth.getUser()

        if (!user || user.email !== '<EMAIL>') {
          toast.error("Bu sayfaya erişim yetkiniz bulunmuyor.")
          return
        }

        // Tüm abonelikleri yükle
        const { data, error } = await supabase
          .from('salon_subscriptions')
          .select(`
            *,
            salons:salon_id(id, name, email, owner_id),
            plans:plan_id(id, name, price_monthly, price_yearly)
          `)
          .order('created_at', { ascending: false })

        if (error) throw error

        setSubscriptions(data || [])
        setFilteredSubscriptions(data || [])
      } catch (error) {
        console.error("Abonelikler yüklenirken hata:", error)
        toast.error("Abonelikler yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadSubscriptions()
  }, [])

  // Arama işlevi
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredSubscriptions(subscriptions)
      return
    }

    const query = searchQuery.toLowerCase().trim()
    const filtered = subscriptions.filter(sub =>
      sub.salons?.name?.toLowerCase().includes(query) ||
      sub.salons?.email?.toLowerCase().includes(query) ||
      sub.status.toLowerCase().includes(query) ||
      sub.plans?.name?.toLowerCase().includes(query)
    )

    setFilteredSubscriptions(filtered)
    setCurrentPage(1) // Arama yapıldığında ilk sayfaya dön
  }, [searchQuery, subscriptions])

  // Sayfalama için
  const totalPages = Math.ceil(filteredSubscriptions.length / itemsPerPage)
  const currentItems = filteredSubscriptions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  // Abonelik durumunu Türkçe olarak göster
  const getStatusText = (status: string) => {
    switch (status) {
      case 'trial': return 'Deneme Süresi'
      case 'active': return 'Aktif'
      case 'past_due': return 'Ödeme Gecikti'
      case 'canceled': return 'İptal Edildi'
      case 'suspended': return 'Askıya Alındı'
      default: return status
    }
  }

  // Abonelik durumuna göre renk belirle
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trial': return 'bg-blue-500'
      case 'active': return 'bg-green-500'
      case 'past_due': return 'bg-yellow-500'
      case 'canceled': return 'bg-red-500'
      case 'suspended': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  if (isLoading) {
    return (
      <div className="p-4">
        <header className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Abonelik Yönetimi</h1>
        </header>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Abonelik Yönetimi</h1>
      </header>

      <div className="flex items-center mb-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Salon adı, e-posta veya durum ara..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableCaption>Toplam {filteredSubscriptions.length} abonelik</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Salon</TableHead>
              <TableHead>Plan</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Başlangıç</TableHead>
              <TableHead>Bitiş/Deneme Sonu</TableHead>
              <TableHead>Döngü</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentItems.length > 0 ? (
              currentItems.map((subscription) => (
                <TableRow key={subscription.id}>
                  <TableCell className="font-medium">{subscription.salons?.name || 'Bilinmiyor'}</TableCell>
                  <TableCell>{subscription.plans?.name || 'Bilinmiyor'}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(subscription.status)}>
                      {getStatusText(subscription.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {format(new Date(subscription.start_date), 'd MMM yyyy', { locale: tr })}
                  </TableCell>
                  <TableCell>
                    {subscription.status === 'trial' && subscription.trial_end_date ?
                      format(new Date(subscription.trial_end_date), 'd MMM yyyy', { locale: tr }) :
                      subscription.end_date ?
                      format(new Date(subscription.end_date), 'd MMM yyyy', { locale: tr }) :
                      '-'}
                  </TableCell>
                  <TableCell>{subscription.is_yearly ? 'Yıllık' : 'Aylık'}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" size="icon" asChild>
                        <Link href={`/admin/subscriptions/${subscription.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" size="icon" asChild>
                        <Link href={`/admin/subscriptions/${subscription.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  {searchQuery ? 'Arama kriterlerine uygun abonelik bulunamadı.' : 'Henüz abonelik kaydı bulunmuyor.'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <Pagination className="mt-4">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault()
                  if (currentPage > 1) setCurrentPage(currentPage - 1)
                }}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>

            {Array.from({ length: totalPages }, (_, i) => i + 1)
              .filter(page =>
                page === 1 ||
                page === totalPages ||
                (page >= currentPage - 1 && page <= currentPage + 1)
              )
              .map((page, i, array) => {
                // Sayfa numaraları arasında boşluk varsa ellipsis ekle
                const showEllipsisBefore = i > 0 && array[i - 1] !== page - 1
                const showEllipsisAfter = i < array.length - 1 && array[i + 1] !== page + 1

                return (
                  <React.Fragment key={page}>
                    {showEllipsisBefore && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}
                    <PaginationItem>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault()
                          setCurrentPage(page)
                        }}
                        isActive={page === currentPage}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                    {showEllipsisAfter && (
                      <PaginationItem>
                        <PaginationEllipsis />
                      </PaginationItem>
                    )}
                  </React.Fragment>
                )
              })
            }

            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault()
                  if (currentPage < totalPages) setCurrentPage(currentPage + 1)
                }}
                className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  )
}
```

## 4. Özellik Erişim Kontrolü

### 4.1. Personel Sayısı Kısıtlaması
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** Özellik Erişim Kontrolü Hook (2.2.2)

```typescript
// src/app/dashboard/staff/page.tsx dosyasında güncelleme
// ...mevcut importlar...
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"

export default function StaffPage() {
  // ...mevcut state ve hooks...
  const { features, isLoading: featuresLoading } = useSubscriptionFeatures()

  // ...mevcut kodlar...

  const handleAddBarber = async () => {
    try {
      // Personel sayısı kontrolü
      const currentStaffCount = barbers.length

      if (currentStaffCount >= features.maxStaff) {
        toast.error(`Planınız maksimum ${features.maxStaff} personel eklemenize izin veriyor. Daha fazla personel eklemek için planınızı yükseltin.`)
        return
      }

      // ...mevcut personel ekleme kodu...
    } catch (error) {
      // ...hata işleme...
    }
  }

  // ...mevcut render kodu...
}
```

### 4.2. Özellik Kısıtlamaları
- **Zorluk:** Orta
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** Özellik Erişim Kontrolü Hook (2.2.2)

```typescript
// src/components/custom-app-sidebar.tsx dosyasında güncelleme
// ...mevcut importlar...
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"

export function CustomAppSidebar({ ...props }) {
  // ...mevcut kodlar...
  const { features, isLoading: featuresLoading } = useSubscriptionFeatures()

  // Kullanıcı rolüne göre navigasyon gruplarını oluştur
  const navigationGroups = React.useMemo(() => {
    // ...mevcut kodlar...

    if (userRole === 'owner') {
      // ...mevcut kodlar...

      // İş operasyonları grubu - Finans özelliği kontrolü
      const businessOperationsGroup: NavigationGroup = {
        label: "Finans",
        items: features.hasFinance ? [
          {
            title: "Finans",
            href: "/dashboard/finance",
            icon: DollarSign,
            isActive: pathname.startsWith("/dashboard/finance"),
          },
        ] : []
      };

      // ...mevcut kodlar...

      // Sadece özellik varsa grubu ekle
      const groups = [coreGroup, salonManagementGroup];

      if (businessOperationsGroup.items.length > 0) {
        groups.push(businessOperationsGroup);
      }

      groups.push(scheduleManagementGroup, systemGroup);

      return groups;
    }

    // ...mevcut kodlar...
  }, [pathname, userRole, features]);

  // ...mevcut render kodu...
}
```

## 5. Uygulama Aşamaları ve Öncelikler

### 5.1. Aşama 1: Temel Abonelik Altyapısı
- **Zorluk:** Zor
- **Tahmini Süre:** 1 hafta
- **Öncelik:** Yüksek

1. Veritabanı tablolarının oluşturulması (1.1)
2. RLS politikalarının oluşturulması (1.2)
3. TypeScript tip tanımlamalarının güncellenmesi (1.3)
4. Temel abonelik yönetimi API'lerinin geliştirilmesi (2.1.1, 2.1.2)
5. Salon sahibi abonelik durumu sayfasının oluşturulması (3.1.1)

### 5.2. Aşama 2: Deneme Süreci ve Plan Yükseltme
- **Zorluk:** Orta
- **Tahmini Süre:** 1 hafta
- **Öncelik:** Yüksek

1. Ödeme yönetimi API'sinin geliştirilmesi (2.1.3)
2. Abonelik durumu kontrolü middleware'inin geliştirilmesi (2.2.1)
3. Plan yükseltme sayfasının oluşturulması (3.1.2)
4. Ödeme geçmişi sayfasının oluşturulması (3.1.3)
5. Özellik erişim kontrolü hook'unun geliştirilmesi (2.2.2)
6. Personel sayısı kısıtlamasının uygulanması (4.1)

### 5.3. Aşama 3: Admin Paneli ve Özellik Kısıtlamaları
- **Zorluk:** Zor
- **Tahmini Süre:** 1 hafta
- **Öncelik:** Orta

1. Admin abonelik yönetimi sayfasının oluşturulması (3.2.1)
2. Özellik kısıtlamalarının uygulanması (4.2)
3. Bildirim sistemi entegrasyonunun geliştirilmesi (2.3)

### 5.4. Aşama 4: Referans Sistemi
- **Zorluk:** Orta
- **Tahmini Süre:** 5 gün
- **Öncelik:** Düşük

1. Referans sistemi API'sinin geliştirilmesi (2.1.4)
2. Referans kodu yönetimi sayfasının oluşturulması
3. Kayıt sürecine referans kodu entegrasyonu

## 6. Sonuç ve Değerlendirme

Bu detaylı geliştirme planı, SalonFlow için kapsamlı bir abonelik sistemi oluşturmak için gereken tüm bileşenleri içermektedir. Plan, veritabanı yapısından başlayarak, backend API'leri, frontend bileşenleri ve özellik erişim kontrolüne kadar tüm aşamaları kapsamaktadır.

Geliştirme süreci, öncelikle temel abonelik altyapısının oluşturulması ile başlayacak, ardından deneme süreci ve plan yükseltme özellikleri eklenecek, daha sonra admin paneli ve özellik kısıtlamaları uygulanacak ve son olarak referans sistemi entegre edilecektir.

Bu plan, SalonFlow'un gelir modelini destekleyecek ve kullanıcılara farklı abonelik seçenekleri sunarak uygulamanın sürdürülebilirliğini sağlayacaktır.