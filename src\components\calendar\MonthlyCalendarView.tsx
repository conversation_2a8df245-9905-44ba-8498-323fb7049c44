"use client"

import { useState, useEffect } from "react"
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, getDay } from "date-fns"
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, useSensor, useSensors, PointerSensor, TouchSensor } from "@dnd-kit/core"
import { toast } from "sonner"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Appointment, BarberWorkingHours } from "@/lib/db/types"
import { HolidayDate } from "@/components/ui/holiday-calendar"
import { DraggableAppointment } from "@/components/calendar/DraggableAppointment"
import { DroppableTimeSlot } from "@/components/calendar/DroppableTimeSlot"
import { appointments as appointmentsApi } from "@/lib/db"
import { MonthlyCalendarSkeleton } from "@/components/ui/skeleton-loaders"
import { useIsMobile } from "@/hooks/use-mobile"

// Extended Appointment type to include joined data from the API
interface AppointmentWithJoins extends Appointment {
  customers?: {
    name: string;
    surname: string;
    phone: string;
  };
  barbers?: {
    name: string;
  };
  services?: {
    name: string;
    duration: number;
  };
}

interface MonthlyCalendarViewProps {
  selectedMonth: Date
  appointments: AppointmentWithJoins[]
  isLoading: boolean
  onDateClick?: (date: Date) => void
  holidayDates?: HolidayDate[]
  barberWorkingHours?: BarberWorkingHours[]
  onAppointmentUpdated?: () => void
}

export function MonthlyCalendarView({
  selectedMonth,
  appointments,
  isLoading,
  onDateClick,
  holidayDates = [],
  barberWorkingHours = [],
  onAppointmentUpdated
}: MonthlyCalendarViewProps) {
  const [calendarDays, setCalendarDays] = useState<Date[]>([])
  const [appointmentsByDate, setAppointmentsByDate] = useState<Record<string, AppointmentWithJoins[]>>({})
  const [activeAppointment, setActiveAppointment] = useState<AppointmentWithJoins | null>(null)
  const isMobile = useIsMobile()

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // Increase distance for mobile to prevent accidental drags
        distance: isMobile ? 12 : 8,
        // Add delay for mobile to prevent accidental drags during scroll
        delay: isMobile ? 150 : 0,
        // Tolerance helps with diagonal movements
        tolerance: 5,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 150,
        tolerance: 8,
      },
    })
  )

  // Generate days for the month
  useEffect(() => {
    const monthStart = startOfMonth(selectedMonth)
    const monthEnd = endOfMonth(selectedMonth)
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd })

    // Add days from previous month to start from Monday (1)
    const firstDayOfMonth = getDay(monthStart)
    const daysToAdd = (firstDayOfMonth === 0 ? 6 : firstDayOfMonth - 1) // Convert Sunday (0) to 6, others to day - 1

    const prevMonthDays = []
    for (let i = daysToAdd; i > 0; i--) {
      const date = new Date(monthStart)
      date.setDate(date.getDate() - i)
      prevMonthDays.push(date)
    }

    // Add days from next month to complete the grid (6 rows x 7 days = 42 cells)
    const totalDays = prevMonthDays.length + daysInMonth.length
    const daysToAddAtEnd = 42 - totalDays

    const nextMonthDays = []
    for (let i = 1; i <= daysToAddAtEnd; i++) {
      const date = new Date(monthEnd)
      date.setDate(date.getDate() + i)
      nextMonthDays.push(date)
    }

    setCalendarDays([...prevMonthDays, ...daysInMonth, ...nextMonthDays])
  }, [selectedMonth])

  // Group appointments by date
  useEffect(() => {
    const groupedAppointments: Record<string, AppointmentWithJoins[]> = {}

    appointments.forEach(appointment => {
      const dateKey = appointment.date
      if (!groupedAppointments[dateKey]) {
        groupedAppointments[dateKey] = []
      }
      groupedAppointments[dateKey].push(appointment)
    })

    setAppointmentsByDate(groupedAppointments)
  }, [appointments])



  if (isLoading) {
    return <MonthlyCalendarSkeleton />;
  }

  // Function to check if a date is a holiday
  const isHoliday = (date: Date) => {
    return holidayDates.some(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
    )
  }

  // Function to get holiday description
  const getHolidayDescription = (date: Date) => {
    const holiday = holidayDates.find(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
    )
    return holiday?.description || "Tatil Günü"
  }

  // Function to check if a barber works on a specific day
  const isBarberWorkingDay = (date: Date) => {
    if (!barberWorkingHours.length) return true

    const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday
    const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

    // If no working hours found for this day or is_closed is true, barber doesn't work
    return workingHoursForDay && !workingHoursForDay.is_closed
  }

  // Get appointments for a specific date
  const getAppointmentsForDate = (date: Date) => {
    const dateKey = format(date, "yyyy-MM-dd")
    return appointmentsByDate[dateKey] || []
  }

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const { appointment } = active.data.current as { appointment: AppointmentWithJoins }
    setActiveAppointment(appointment)
  }

  // Handle drag end
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    setActiveAppointment(null)

    // If no drop target, do nothing
    if (!over) return

    const appointmentId = active.id as string
    const appointment = appointments.find(apt => apt.id === appointmentId)

    if (!appointment) return

    // Sadece aktif randevular taşınabilir
    if (appointment.status !== 'booked') {
      let statusMessage = "";
      switch (appointment.status) {
        case 'cancelled':
          statusMessage = "İptal edilmiş";
          break;
        case 'completed':
          statusMessage = "Tamamlanmış";
          break;
        case 'no-show':
          statusMessage = "Gelmemiş";
          break;
        default:
          statusMessage = "Bu durumdaki";
      }
      toast.error(`${statusMessage} randevular taşınamaz`)
      return
    }

    // Get the source date from the appointment
    const sourceDate = format(new Date(appointment.date), "yyyy-MM-dd")

    const { date, isAvailable } = over.data.current as {
      date: Date,
      isAvailable: boolean
    }

    // If the time slot is not available, do nothing
    if (!isAvailable) {
      toast.error("Bu güne randevu taşınamaz")
      return
    }

    try {
      // Format date for database
      const formattedDate = format(date, "yyyy-MM-dd")

      // If the appointment is dropped on the same date, do nothing
      if (sourceDate === formattedDate) {
        return
      }

      // Check for appointment overlaps
      // Since we're only changing the date and keeping the same time,
      // we need to check if there are any appointments for the same barber on the target date
      // with the same time slot
      const hasOverlap = appointments.some(apt =>
        apt.id !== appointmentId && // Not the same appointment
        apt.barber_id === appointment.barber_id && // Same barber
        apt.date === formattedDate && // Same date
        apt.start_time === appointment.start_time // Same start time
      )

      if (hasOverlap) {
        toast.error("Bu berberin seçilen günde aynı saat aralığında başka bir randevusu var")
        return
      }

      // Update appointment in database - just change the date
      await appointmentsApi.updateAppointment({
        id: appointmentId,
        date: formattedDate
      })

      toast.success("Randevu başarıyla taşındı")

      // Notify parent component to refresh appointments
      if (onAppointmentUpdated) {
        onAppointmentUpdated()
      }
    } catch (error) {
      console.error("Error updating appointment:", error)
      toast.error("Randevu taşınırken bir hata oluştu")
    }
  }

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-4">
        <div className="grid grid-cols-7 gap-1 text-center mb-2">
          {["Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi", "Pazar"].map((day) => (
            <div key={day} className="font-medium text-sm py-2">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((date) => {
            const isCurrentMonth = isSameMonth(date, selectedMonth)
            const isToday = isSameDay(date, new Date())
            const dateAppointments = getAppointmentsForDate(date)
            const isHolidayDate = isHoliday(date)
            const isBarberWorking = isBarberWorkingDay(date)
            const isAvailable = isCurrentMonth && !isHolidayDate && isBarberWorking ? true : false
            const dayId = `month-day-${format(date, "yyyy-MM-dd")}`

            return (
              <DroppableTimeSlot
                key={dayId}
                id={dayId}
                date={date}
                isAvailable={isAvailable}
                unavailableReason={
                  !isCurrentMonth
                    ? "Farklı ay"
                    : isHolidayDate
                      ? "Tatil günü"
                      : "Berber çalışmıyor"
                }
                className={cn(
                  "min-h-24 p-2 border rounded-md",
                  !isCurrentMonth && "opacity-40",
                  isToday && "border-primary",
                  isHolidayDate && isCurrentMonth && "border-red-500 dark:border-red-700",
                  !isBarberWorking && barberWorkingHours.length > 0 && isCurrentMonth && "border-yellow-500 dark:border-yellow-700",
                  "hover:bg-muted/50 cursor-pointer"
                )}
                onClick={(e) => {
                  // Sadece doğrudan DroppableTimeSlot'a tıklandığında çalışsın
                  // ve sürükleme işlemi yapılmıyorsa
                  if (e.currentTarget === e.target && onDateClick) {
                    // Eğer tıklanan element bir randevu değilse
                    const isAppointmentClick = (e.target as HTMLElement).closest('.draggable-appointment');
                    if (!isAppointmentClick) {
                      onDateClick(date);
                    }
                  }
                }}
              >
                <div className="flex justify-between items-center mb-1">
                  <div className="flex gap-1">
                    {isHolidayDate && isCurrentMonth && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="destructive" className="h-5 text-[10px]">Tatil</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{getHolidayDescription(date)}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    {!isBarberWorking && barberWorkingHours.length > 0 && isCurrentMonth && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Badge variant="outline" className="h-5 text-[10px] bg-yellow-100 dark:bg-yellow-900/30">Çalışma Dışı</Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Berber bu gün çalışmıyor</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                  <span className={cn(
                    "inline-block rounded-full w-7 h-7 text-center leading-7",
                    isToday && "bg-primary text-primary-foreground"
                  )}>
                    {format(date, "d")}
                  </span>
                </div>

                {dateAppointments.length > 0 ? (
                  <div className="space-y-1">
                    {dateAppointments.length <= 3 ? (
                      dateAppointments.map((apt) => (
                        <DraggableAppointment
                          key={apt.id}
                          appointment={apt}
                          onAppointmentUpdated={onAppointmentUpdated}
                        />
                      ))
                    ) : (
                      <>
                        <DraggableAppointment
                          appointment={dateAppointments[0]}
                          onAppointmentUpdated={onAppointmentUpdated}
                        />
                        <DraggableAppointment
                          appointment={dateAppointments[1]}
                          onAppointmentUpdated={onAppointmentUpdated}
                        />
                        <div className="text-xs text-center bg-muted p-1 rounded">
                          +{dateAppointments.length - 2} daha
                        </div>
                      </>
                    )}
                  </div>
                ) : (
                  isCurrentMonth && (
                    <div className="text-xs text-center mt-4">
                      {isHolidayDate ? (
                        <span className="text-red-500 dark:text-red-400">Tatil günü</span>
                      ) : !isBarberWorking && barberWorkingHours.length > 0 ? (
                        <span className="text-yellow-500 dark:text-yellow-400">Çalışma dışı</span>
                      ) : (
                        <span className="text-muted-foreground">Randevu yok</span>
                      )}
                    </div>
                  )
                )}
              </DroppableTimeSlot>
            )
          })}
        </div>
      </div>

      {/* Drag overlay for visual feedback during dragging */}
      <DragOverlay>
        {activeAppointment && (
          <DraggableAppointment
            appointment={activeAppointment}
            isDraggingEnabled={false}
            onAppointmentUpdated={onAppointmentUpdated}
          />
        )}
      </DragOverlay>
    </DndContext>
  )
}
