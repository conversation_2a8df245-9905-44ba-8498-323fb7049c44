"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { format, addMinutes, parse } from "date-fns"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DatePicker } from "@/components/ui/date-picker"
import { TimeSlotGrid } from "@/components/ui/time-slot-grid"
import { HolidayDate } from "@/components/ui/holiday-calendar"
import {
  appointments,
  barbers,
  services,
  Barber,
  Service
} from "@/lib/db"
import { supabase } from '@/lib/supabase'

// Form schema
const formSchema = z.object({
  date: z.date({
    required_error: "<PERSON><PERSON>h seçmelisiniz.",
  }),
  start_time: z.string({
    required_error: "Saat seçmelisiniz.",
  }),
  barber_id: z.string({
    required_error: "Berber seçmelisiniz.",
  }),
  service_id: z.string({
    required_error: "Hizmet seçmelisiniz.",
  }),
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }),
  surname: z.string().min(2, {
    message: "Soyisim en az 2 karakter olmalıdır.",
  }),
  phone: z.string().min(10, {
    message: "Geçerli bir telefon numarası giriniz.",
  }),
  email: z.string().email({
    message: "Geçerli bir e-posta adresi giriniz.",
  }).optional().or(z.literal("")),
})

interface BookingFormProps {
  salonId: string
  onSuccess?: () => void
}

export function BookingForm({ salonId, onSuccess }: BookingFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [barbersList, setBarbersList] = useState<Barber[]>([])
  const [servicesList, setServicesList] = useState<Service[]>([])
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [availableTimes, setAvailableTimes] = useState<string[]>([])
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [selectedBarber, setSelectedBarber] = useState<string | null>(null)
  const [disabledDates, setDisabledDates] = useState<Date[]>([])
  const [holidayDates, setHolidayDates] = useState<HolidayDate[]>([])
  const [barberWorkingHoursList, setBarberWorkingHoursList] = useState<any[]>([])
  const [existingAppointmentTimes, setExistingAppointmentTimes] = useState<string[]>([])

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      date: new Date(),
      name: "",
      surname: "",
      phone: "",
      email: "",
    },
  })

  // Load services and barbers
  useEffect(() => {
    async function loadData() {
      try {
        // Load services
        const servicesData = await services.getServices(salonId)
        setServicesList(servicesData)

        // Load barbers
        const barbersData = await barbers.getBarbers(salonId)
        setBarbersList(barbersData)
      } catch (error) {
        console.error("Error loading data:", error)
        toast.error("Veriler yüklenirken bir hata oluştu.")
      }
    }

    loadData()
  }, [salonId])

  // Handle service selection
  const handleServiceChange = (serviceId: string) => {
    const service = servicesList.find(s => s.id === serviceId)
    setSelectedService(service || null)
    form.setValue("service_id", serviceId)

    // Reset barber and time when service changes
    form.setValue("barber_id", "")
    form.setValue("start_time", "")
    setSelectedBarber(null)
    setAvailableTimes([])
  }

  // Load barber working hours and set disabled dates
  const loadBarberWorkingHours = async (barberId: string) => {
    try {
      // Get barber working hours
      const { data, error } = await supabase
        .from('barber_working_hours')
        .select('*')
        .eq('barber_id', barberId)
        .order('day_of_week')

      if (error) throw error

      // Set barber working hours list for TimeSlotGrid
      setBarberWorkingHoursList(data || [])

      // All days of the week (0-6)
      const allDaysOfWeek = [0, 1, 2, 3, 4, 5, 6]

      // Days that have records in the database
      const daysWithRecords = (data || []).map(day => day.day_of_week)

      // Days that don't have records in the database (considered closed)
      const daysWithoutRecords = allDaysOfWeek.filter(day => !daysWithRecords.includes(day))

      // Days that are explicitly marked as closed
      const explicitlyClosedDays = (data || [])
        .filter(day => day.is_closed)
        .map(day => day.day_of_week)

      // All closed days (either no record or explicitly marked as closed)
      const closedDays = [...new Set([...daysWithoutRecords, ...explicitlyClosedDays])]

      // Generate disabled dates for the next 3 months
      const disabledDatesArray: Date[] = []
      const today = new Date()
      const threeMonthsLater = new Date()
      threeMonthsLater.setMonth(today.getMonth() + 3)

      // Get salon holidays
      const { data: holidaysData, error: holidaysError } = await supabase
        .from('holidays')
        .select('*')
        .eq('salon_id', salonId)
        .gte('date', format(today, "yyyy-MM-dd"))
        .lte('date', format(threeMonthsLater, "yyyy-MM-dd"))

      if (holidaysError) throw holidaysError

      // Convert holiday dates to Date objects for disabled dates
      const holidayDateObjects = (holidaysData || []).map(holiday => new Date(holiday.date))

      // Convert to HolidayDate format for the TimeSlotGrid component
      const holidayDateFormatted = (holidaysData || []).map(holiday => ({
        date: new Date(holiday.date),
        description: holiday.description || "Tatil Günü"
      }))

      setHolidayDates(holidayDateFormatted)

      // Loop through each day in the next 3 months
      const currentDate = new Date(today)
      while (currentDate <= threeMonthsLater) {
        const dayOfWeek = currentDate.getDay()
        const currentDateStr = format(currentDate, "yyyy-MM-dd")

        // If the barber doesn't work on this day of the week, add it to disabled dates
        if (closedDays.includes(dayOfWeek)) {
          // Create a new Date object to avoid reference issues
          disabledDatesArray.push(new Date(currentDate.getTime()))
        }
        // Check if the date is a holiday
        else if (holidayDateObjects.some(date => format(date, "yyyy-MM-dd") === currentDateStr)) {
          disabledDatesArray.push(new Date(currentDate.getTime()))
        }

        // Move to the next day
        currentDate.setDate(currentDate.getDate() + 1)
      }

      setDisabledDates(disabledDatesArray)
    } catch (error) {
      console.error("Error loading barber working hours:", error)
      toast.error("Berber çalışma saatleri yüklenirken bir hata oluştu.")
    }
  }

  // Handle barber selection
  const handleBarberChange = (barberId: string) => {
    setSelectedBarber(barberId)
    form.setValue("barber_id", barberId)
    form.setValue("start_time", "")

    // Load barber working hours
    loadBarberWorkingHours(barberId)

    // Load available times when both date and barber are selected
    if (selectedDate && barberId) {
      loadAvailableTimes(selectedDate, barberId)
    }
  }

  // Handle date selection
  const handleDateChange = (date: Date | undefined) => {
    if (!date) return

    setSelectedDate(date)
    form.setValue("date", date)
    form.setValue("start_time", "")

    // Load available times when both date and barber are selected
    if (date && selectedBarber) {
      loadAvailableTimes(date, selectedBarber)
    }
  }

  // Load available times for a specific date and barber
  const loadAvailableTimes = async (date: Date, barberId: string) => {
    try {
      const formattedDate = format(date, "yyyy-MM-dd")
      const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday

      // Check if the selected date is a holiday
      const { data: holidayData, error: holidayError } = await supabase
        .from('holidays')
        .select('description')
        .eq('salon_id', salonId)
        .eq('date', formattedDate)
        .single()

      // If the selected date is a holiday, show a message and return
      if (!holidayError && holidayData) {
        setAvailableTimes([])
        const holidayDescription = holidayData.description ? ` (${holidayData.description})` : ''
        toast.error(`Seçilen tarih tatil günüdür${holidayDescription}. Lütfen başka bir tarih seçin.`)
        return
      }

      // Get barber working hours for the selected day
      const { data: workingHoursData, error: workingHoursError } = await supabase
        .from('barber_working_hours')
        .select('*')
        .eq('barber_id', barberId)
        .eq('day_of_week', dayOfWeek)
        .single()

      // Check if barber is working on the selected day
      if (workingHoursError || !workingHoursData || workingHoursData.is_closed) {
        setAvailableTimes([])
        if (!workingHoursError || workingHoursError.code === 'PGRST116') { // PGRST116 is "No rows returned" error
          toast.error("Seçilen berber bu gün çalışmıyor. Lütfen başka bir gün veya berber seçin.")
        } else {
          console.error("Error loading barber working hours:", workingHoursError)
          toast.error("Berber çalışma saatleri yüklenirken bir hata oluştu.")
        }
        return
      }

      // Get all appointments for the barber on the selected date
      const { data: existingAppointments, error } = await supabase
        .from('appointments')
        .select('start_time, end_time')
        .eq('barber_id', barberId)
        .eq('date', formattedDate)
        .not('status', 'eq', 'cancelled')

      if (error) throw error

      // Set existing appointment times for the TimeSlotGrid component
      const existingTimes = (existingAppointments || []).map(apt => apt.start_time.substring(0, 5))
      setExistingAppointmentTimes(existingTimes)

      // Generate available time slots based on barber's working hours
      const timeSlots = []

      // Parse working hours
      const openTime = workingHoursData.open_time.substring(0, 5)
      const closeTime = workingHoursData.close_time.substring(0, 5)

      const [openHour, openMinute] = openTime.split(':').map(Number)
      const [closeHour, closeMinute] = closeTime.split(':').map(Number)

      // Generate time slots at 30-minute intervals within working hours
      let currentHour = openHour
      let currentMinute = openMinute

      while (
        currentHour < closeHour ||
        (currentHour === closeHour && currentMinute < closeMinute)
      ) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`
        timeSlots.push(timeString)

        // Increment by 30 minutes
        currentMinute += 30
        if (currentMinute >= 60) {
          currentHour += 1
          currentMinute = 0
        }
      }

      // Filter out times that conflict with existing appointments
      const availableSlots = timeSlots.filter(timeSlot => {
        // Skip if we don't have a selected service
        if (!selectedService) return false

        const startTime = parse(timeSlot, "HH:mm", new Date())
        const endTime = addMinutes(startTime, selectedService.duration)
        const endTimeString = format(endTime, "HH:mm")

        // Skip if the appointment would end after closing time
        if (endTimeString > closeTime) return false

        // Check if this time slot conflicts with any existing appointment
        return !existingAppointments.some(apt => {
          const aptStartTime = apt.start_time.substring(0, 5)
          const aptEndTime = apt.end_time.substring(0, 5)

          // Check if the new appointment would overlap with an existing one
          return (
            (timeSlot >= aptStartTime && timeSlot < aptEndTime) || // Start time is during an existing appointment
            (endTimeString > aptStartTime && endTimeString <= aptEndTime) || // End time is during an existing appointment
            (timeSlot <= aptStartTime && endTimeString >= aptEndTime) // New appointment completely contains an existing one
          )
        })
      })

      setAvailableTimes(availableSlots)

      // If no available slots, show a message
      if (availableSlots.length === 0) {
        toast.error("Seçilen tarih ve berber için müsait saat bulunmamaktadır. Lütfen başka bir tarih veya berber seçin.")
      }
    } catch (error) {
      console.error("Error loading available times:", error)
      toast.error("Müsait saatler yüklenirken bir hata oluştu.")
    }
  }

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true)

    try {
      const formattedDate = format(values.date, "yyyy-MM-dd")

      if (!selectedService) {
        toast.error("Lütfen bir hizmet seçin.")
        setIsLoading(false)
        return
      }

      // Calculate end time
      const startTime = parse(values.start_time, "HH:mm", new Date())
      const endTime = addMinutes(startTime, selectedService.duration)
      const endTimeString = format(endTime, "HH:mm")

      // Appointment data
      const appointmentData = {
        salon_id: salonId,
        date: formattedDate,
        start_time: values.start_time,
        end_time: endTimeString,
        barber_id: values.barber_id,
        service_id: values.service_id,
        fullname: values.name + ' ' + values.surname,
        phonenumber: values.phone,
        email: values.email || undefined,
        status: "booked" as const
      }

      // Create appointment
      await appointments.createAppointment(appointmentData)
      // Müşteri tarafından oluşturulan randevular için toast mesajı gösteriyoruz
      // Çünkü müşteriler NotificationsContext'e erişemezler
      toast.success("Randevu başarıyla oluşturuldu!")

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }

      // Reset form and all states (for when user wants to create another appointment)
      form.reset({
        date: new Date(),
        start_time: "",
        barber_id: "",
        service_id: "",
        name: "",
        surname: "",
        phone: "",
        email: "",
      })

      // Reset all states
      setSelectedService(null)
      setSelectedBarber(null)
      setSelectedDate(new Date())
      setAvailableTimes([])
    } catch (error) {
      console.error("Randevu oluşturulurken bir hata oluştu:", error)
      toast.error("Randevu oluşturulurken bir hata oluştu.")
    } finally {
      setIsLoading(false)
    }
  }

  // Artık başarı ekranı booking-modal.tsx'de yönetiliyor

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="service_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Hizmet</FormLabel>
                <Select
                  onValueChange={handleServiceChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Hizmet seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {servicesList.map((service) => (
                      <SelectItem key={service.id} value={service.id}>
                        {service.name} ({service.duration} dk)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="barber_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Berber</FormLabel>
                <Select
                  onValueChange={handleBarberChange}
                  defaultValue={field.value}
                  disabled={!selectedService}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Berber seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {barbersList.map((barber) => (
                      <SelectItem key={barber.id} value={barber.id}>
                        {barber.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Tarih</FormLabel>
                <DatePicker
                  date={field.value}
                  setDate={handleDateChange}
                  disabled={!selectedBarber}
                  disabledDates={disabledDates}
                  fromDate={new Date()}
                  compact={true}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="start_time"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Saat</FormLabel>
                <FormControl>
                  {selectedBarber && selectedDate ? (
                    <TimeSlotGrid
                      date={selectedDate}
                      availableTimes={availableTimes}
                      selectedTime={field.value}
                      onTimeSelect={field.onChange}
                      holidayDates={holidayDates}
                      barberWorkingHours={barberWorkingHoursList}
                      existingAppointmentTimes={existingAppointmentTimes}
                    />
                  ) : (
                    <div className="text-sm text-muted-foreground p-4 border rounded-md bg-muted/30">
                      Önce tarih ve berber seçin
                    </div>
                  )}
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>İsim</FormLabel>
                <FormControl>
                  <Input placeholder="İsim" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="surname"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Soyisim</FormLabel>
                <FormControl>
                  <Input placeholder="Soyisim" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Telefon</FormLabel>
                <FormControl>
                  <Input placeholder="Telefon" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>E-posta (İsteğe bağlı)</FormLabel>
                <FormControl>
                  <Input placeholder="E-posta" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? "Randevu Oluşturuluyor..." : "Randevu Oluştur"}
        </Button>
      </form>
    </Form>
  )
}
