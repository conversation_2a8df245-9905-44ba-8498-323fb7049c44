-- Ödeme yönetimi değişiklikleri için SQL script
-- Tarih: 2024-08-05

-- 1. subscription_payments tablosuna yeni alanlar ekleme
ALTER TABLE subscription_payments
ADD COLUMN period_start_date DATE,
ADD COLUMN period_end_date DATE,
ADD COLUMN original_amount NUMERIC DEFAULT 0,
ADD COLUMN discount_amount NUMERIC DEFAULT 0,
ADD COLUMN discount_type VARCHAR(50),
ADD COLUMN discount_reference_id UUID;

-- 2. Mevcut kayıtları güncelleme
-- Mevcut ödemelerin period_start_date ve period_end_date alanlarını payment_date değerine göre ayarla
UPDATE subscription_payments
SET
  period_start_date = payment_date::DATE,
  period_end_date = (payment_date::DATE + INTERVAL '1 month')::DATE,
  original_amount = amount
WHERE
  period_start_date IS NULL;

-- 3. <PERSON>de<PERSON> tutarı hesaplama fonksiyonu oluşturma
CREATE OR REPLACE FUNCTION calculate_payment_amount(
  subscription_id UUID,
  start_date DATE,
  end_date DATE
)
RETURNS TABLE (
  original_amount NUMERIC,
  plan_name TEXT,
  plan_price NUMERIC,
  day_count INTEGER
) AS $$
DECLARE
  monthly_price NUMERIC;
  daily_price NUMERIC;
  total_amount NUMERIC;
  plan_name TEXT;
  days_diff INTEGER;
BEGIN
  -- Abonelik bilgilerini al
  SELECT
    sp.price_monthly,
    sp.name
  INTO
    monthly_price,
    plan_name
  FROM
    salon_subscriptions ss
    JOIN subscription_plans sp ON ss.plan_id = sp.id
  WHERE
    ss.id = subscription_id;

  -- Tarih aralığını hesapla
  days_diff := (end_date - start_date);

  -- Gün başına fiyat
  daily_price := monthly_price / 30;

  -- Toplam fiyat
  total_amount := ROUND(daily_price * days_diff);

  RETURN QUERY
  SELECT
    total_amount,
    plan_name,
    monthly_price,
    days_diff;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Kullanılabilir indirimleri kontrol etme fonksiyonu oluşturma
CREATE OR REPLACE FUNCTION check_available_discounts(salon_id UUID, subscription_id UUID)
RETURNS TABLE (
  has_discount BOOLEAN,
  discount_type TEXT,
  discount_amount NUMERIC,
  discount_id UUID
) AS $$
DECLARE
  referral_discount RECORD;
  has_payments BOOLEAN;
BEGIN
  -- Daha önce ödeme yapılmış mı kontrol et
  SELECT EXISTS (
    SELECT 1 FROM subscription_payments
    WHERE subscription_id = subscription_id
    AND status = 'completed'
  ) INTO has_payments;

  -- Eğer daha önce ödeme yapılmamışsa ve referans indirimi varsa
  IF NOT has_payments THEN
    -- Referans indirimi kontrolü
    SELECT
      rb.id,
      rb.discount_amount
    INTO
      referral_discount
    FROM
      referral_benefits rb
    WHERE
      rb.referred_salon_id = salon_id
      AND rb.benefit_type = 'referred_discount'
      AND rb.discount_applied = FALSE
    LIMIT 1;

    IF FOUND THEN
      RETURN QUERY
      SELECT
        TRUE,
        'referral'::TEXT,
        referral_discount.discount_amount,
        referral_discount.id;
      RETURN;
    END IF;
  END IF;

  -- Eğer indirim yoksa veya ödeme yapılmışsa
  RETURN QUERY
  SELECT
    FALSE,
    NULL::TEXT,
    NULL::NUMERIC,
    NULL::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Yeni RLS politikaları oluşturma
-- Mevcut politikaları kaldır
DROP POLICY IF EXISTS "Salon sahipleri kendi ödemelerini görebilir" ON subscription_payments;
DROP POLICY IF EXISTS "Admin tüm ödemeleri görebilir" ON subscription_payments;

-- Yeni politikaları ekle
CREATE POLICY "Salon sahipleri kendi ödemelerini görebilir"
ON subscription_payments
FOR SELECT
TO authenticated
USING (
  subscription_id IN (
    SELECT id FROM salon_subscriptions
    WHERE salon_id IN (
      SELECT id FROM salons WHERE owner_id = auth.uid()
    )
  )
);

CREATE POLICY "Admin tüm ödemeleri görebilir"
ON subscription_payments
FOR ALL
TO authenticated
USING (
  (SELECT is_admin())
);
