-- Referans <PERSON><PERSON><PERSON>
-- Tarih: 2024-08-01

-- 1. referral_benefits tablosunda eksik sütunları kontrol et ve ekle
DO $$
BEGIN
    -- applied_date sütunu yoksa ekle
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'referral_benefits' AND column_name = 'applied_date') THEN
        ALTER TABLE referral_benefits ADD COLUMN applied_date DATE;
    END IF;

    -- referral_code_id sütunu yoksa ekle
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'referral_benefits' AND column_name = 'referral_code_id') THEN
        ALTER TABLE referral_benefits ADD COLUMN referral_code_id UUID REFERENCES referral_codes(id) ON DELETE CASCADE;
    END IF;

    -- status sütunu için check constraint ekle veya güncelle
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'referral_benefits' AND column_name = 'status') THEN
        ALTER TABLE referral_benefits DROP CONSTRAINT IF EXISTS referral_benefits_status_check;
        ALTER TABLE referral_benefits ADD CONSTRAINT referral_benefits_status_check 
            CHECK (status IN ('pending', 'applied', 'expired'));
    END IF;
END
$$;

-- 2. referral_codes tablosunda is_active sütunu yoksa ekle
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'referral_codes' AND column_name = 'is_active') THEN
        ALTER TABLE referral_codes ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
END
$$;

-- 3. RLS politikalarını güncelle

-- referral_codes tablosu için RLS politikalarını güncelle
DROP POLICY IF EXISTS "Salon sahipleri kendi referans kodlarını görebilir" ON referral_codes;
DROP POLICY IF EXISTS "Admin tüm referans kodlarını görebilir" ON referral_codes;
DROP POLICY IF EXISTS "Salon sahipleri referans kodu oluşturabilir" ON referral_codes;
DROP POLICY IF EXISTS "Admin referans kodlarını ekleyebilir" ON referral_codes;
DROP POLICY IF EXISTS "Admin referans kodlarını güncelleyebilir" ON referral_codes;
DROP POLICY IF EXISTS "Admin referans kodlarını silebilir" ON referral_codes;

-- Salon sahipleri kendi referans kodlarını görebilir
CREATE POLICY "Salon sahipleri kendi referans kodlarını görebilir" ON referral_codes
  FOR SELECT USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Admin tüm referans kodlarını görebilir
CREATE POLICY "Admin tüm referans kodlarını görebilir" ON referral_codes
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Salon sahipleri kendi referans kodlarını oluşturabilir
CREATE POLICY "Salon sahipleri referans kodu oluşturabilir" ON referral_codes
  FOR INSERT WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Admin tüm referans kodlarını ekleyebilir, güncelleyebilir veya silebilir
CREATE POLICY "Admin referans kodlarını ekleyebilir" ON referral_codes
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans kodlarını güncelleyebilir" ON referral_codes
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans kodlarını silebilir" ON referral_codes
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Salon sahipleri kendi referans kodlarını güncelleyebilir
CREATE POLICY "Salon sahipleri kendi referans kodlarını güncelleyebilir" ON referral_codes
  FOR UPDATE USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())));

-- referral_benefits tablosu için RLS politikalarını güncelle
DROP POLICY IF EXISTS "Salon sahipleri kendi referans faydalarını görebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin tüm referans faydalarını görebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin referans faydalarını ekleyebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin referans faydalarını güncelleyebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin referans faydalarını silebilir" ON referral_benefits;

-- Salon sahipleri kendi referans faydalarını görebilir
CREATE POLICY "Salon sahipleri kendi referans faydalarını görebilir" ON referral_benefits
  FOR SELECT USING (
    referrer_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()) OR 
    referrer_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())
  );

-- Admin tüm referans faydalarını görebilir
CREATE POLICY "Admin tüm referans faydalarını görebilir" ON referral_benefits
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Admin tüm referans faydalarını ekleyebilir, güncelleyebilir veya silebilir
CREATE POLICY "Admin referans faydalarını ekleyebilir" ON referral_benefits
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans faydalarını güncelleyebilir" ON referral_benefits
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin referans faydalarını silebilir" ON referral_benefits
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- Salon sahipleri kendi referans faydalarını ekleyebilir
CREATE POLICY "Salon sahipleri referans faydası ekleyebilir" ON referral_benefits
  FOR INSERT WITH CHECK (
    referrer_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()) OR 
    referrer_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())
  );

-- 4. Bildirim tetikleyicisi oluştur
-- Referans kodu kullanıldığında referans veren kullanıcıya bildirim gönder

CREATE OR REPLACE FUNCTION notify_referral_used()
RETURNS TRIGGER AS $$
BEGIN
  -- Referans veren salon sahibinin ID'sini al
  INSERT INTO notifications (
    salon_id,
    user_id,
    type,
    title,
    message,
    read,
    data
  )
  SELECT 
    NEW.referrer_id,
    salons.owner_id,
    'referral_used',
    'Referans Kodunuz Kullanıldı',
    'Referans kodunuz yeni bir kullanıcı tarafından kullanıldı. Aboneliğinize 1 ay eklenecek.',
    false,
    jsonb_build_object(
      'referral_benefit_id', NEW.id,
      'referred_salon_id', NEW.referred_salon_id
    )
  FROM salons
  WHERE salons.id = NEW.referrer_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Tetikleyici varsa kaldır ve yeniden oluştur
DROP TRIGGER IF EXISTS referral_used_notification ON referral_benefits;

CREATE TRIGGER referral_used_notification
AFTER INSERT ON referral_benefits
FOR EACH ROW
EXECUTE FUNCTION notify_referral_used();
