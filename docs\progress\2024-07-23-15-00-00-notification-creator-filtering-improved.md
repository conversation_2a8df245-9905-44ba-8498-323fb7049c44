# Bildirim Oluşturucu Filtreleme (İyileştirilmiş) - 2024-07-23

## Gene<PERSON> Bakış

Bu belge, berber/salon sahiplerinin kendi oluşturdukları randevuların bildirimlerini görmemelerini sağlamak için yapılan iyileştirilmiş değişiklikleri belgelemektedir.

## Sorun

Mevcut sistemde, bir randevu oluşturulduğunda veya iptal edildiğinde, salon sahibine ve ilgili berbere bildirim gönderilmektedir. <PERSON><PERSON><PERSON>, eğer randevuyu oluşturan veya iptal eden kişi salon sahibi veya berberin kendisi ise, bu kişilerin kendi yaptıkları işlemler için bildirim almamaları gerekmektedir.

Önceki çözüm (appointments tablosuna created_by eklemek) işe yaramadı. <PERSON><PERSON><PERSON> neden<PERSON>, trigger<PERSON><PERSON>n çalışma sırası olabilir - created_by alanı dolmadan notification trigger'ı çalışıyor olabilir.

## Çözüm

Daha genel ve güvenilir bir çözüm olarak:

1. Notifications tablosuna `created_by` sütunu eklendi. Bu sütun, bildirimi oluşturan kullanıcının ID'sini saklamaktadır.
2. Bildirim trigger'ları güncellendi:
   - Bildirim oluşturulurken `created_by` alanı `auth.uid()` ile dolduruluyor.
3. Frontend tarafında bildirimler filtreleniyor:
   - Bildirimleri getirirken, `created_by != user.id` koşulu eklendi.
   - Realtime subscription'da da benzer bir kontrol eklendi.

Bu yaklaşımın avantajları:
- Triggerların çalışma sırası artık sorun olmayacak
- Daha genel bir çözüm olacak, sadece randevular için değil, tüm bildirimler için geçerli olacak
- Frontend tarafında da filtreleme yapıldığı için, backend'de bir sorun olsa bile frontend'de filtreleme yapılabilecek

## Teknik Detaylar

### 1. Notifications Tablosuna created_by Sütunu Ekleme

```sql
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
```

### 2. Bildirim Trigger'larını Güncelleme

Yeni randevu bildirimleri için:
- `created_by` alanını `auth.uid()` ile dolduruyoruz

İptal edilen randevu bildirimleri için:
- `created_by` alanını `auth.uid()` ile dolduruyoruz

### 3. Frontend Filtreleme

NotificationsContext.tsx dosyasında:
- Bildirimleri getirirken, `created_by != user.id` koşulunu ekledik:
```typescript
const { data, error } = await supabase
  .from('notifications')
  .select('*')
  .eq('user_id', user.id)
  .or(`created_by.is.null,created_by.neq.${user.id}`) // Kendi oluşturduğumuz bildirimleri filtreleme
  .order('created_at', { ascending: false })
  .limit(50)
```

- Realtime subscription'da da benzer bir kontrol ekledik:
```typescript
// Kendi oluşturduğumuz bildirimleri gösterme
if (newNotification.created_by === user.id) {
  return;
}
```

## Uygulama

Bu değişiklikler, `docs/sql/2024-07-23-15-00-00-notification-creator-tracking.sql` dosyasında bulunmaktadır ve Supabase veritabanına uygulanmalıdır.

Ayrıca, `src/contexts/NotificationsContext.tsx` dosyası da güncellenmiştir.

## Sonuç

Bu değişikliklerle birlikte, berber/salon sahipleri kendi oluşturdukları veya iptal ettikleri randevular için bildirim almayacaklardır. Bu, kullanıcı deneyimini iyileştirecek ve gereksiz bildirimleri önleyecektir.
