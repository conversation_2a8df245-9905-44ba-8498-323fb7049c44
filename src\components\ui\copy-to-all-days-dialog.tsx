"use client"

import { useState } from "react"
import { Check, Copy } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface CopyToAllDaysDialogProps {
  days: string[]
  onCopy: (sourceDay: string) => void
}

export function CopyToAllDaysDialog({ days, onCopy }: CopyToAllDaysDialogProps) {
  const [selectedDay, setSelectedDay] = useState<string>(days[0])
  const [open, setOpen] = useState(false)

  const handleCopy = () => {
    onCopy(selectedDay)
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-1">
          <Copy className="h-4 w-4" />
          <span>Tüm Günlere Kopyala</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Çalışma Saatlerini Kopyala</DialogTitle>
          <DialogDescription>
            Seçilen günün çalışma saatlerini tüm günlere kopyalayın.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="source-day" className="text-sm font-medium">
              Kaynak Gün
            </label>
            <Select
              value={selectedDay}
              onValueChange={setSelectedDay}
            >
              <SelectTrigger id="source-day">
                <SelectValue placeholder="Bir gün seçin" />
              </SelectTrigger>
              <SelectContent>
                {days.map((day) => (
                  <SelectItem key={day} value={day}>
                    {day}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            İptal
          </Button>
          <Button onClick={handleCopy} className="gap-1">
            <Check className="h-4 w-4" />
            <span>Kopyala</span>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
