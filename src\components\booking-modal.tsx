"use client"

import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { BookingForm } from "@/components/booking-form"

interface BookingModalProps {
  salonId: string
  buttonText?: string
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  buttonSize?: "default" | "sm" | "lg" | "icon"
  buttonClassName?: string
  dialogTitle?: string
  dialogDescription?: string
}

export function BookingModal({
  salonId,
  buttonText = "Randevu Al",
  buttonVariant = "default",
  buttonSize = "default",
  buttonClassName,
  dialogTitle = "Randevu Oluştur",
  dialogDescription = "Randevu oluşturmak için aşağıdaki formu doldurun."
}: BookingModalProps) {
  const [open, setOpen] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)
  const [formKey, setFormKey] = useState(0) // Form'u yeniden oluşturmak için key

  const handleBookingSuccess = () => {
    setBookingSuccess(true)
    // Keep the modal open to show success message
  }

  const handleOpenChange = (open: boolean) => {
    // Modalın açık/kapalı durumunu güncelle
    setOpen(open)

    // Eğer modal kapatılıyorsa
    if (!open) {
      // Başarı durumunu sıfırla
      setBookingSuccess(false)

      // Modal kapanma animasyonu tamamlandıktan sonra formu sıfırla
      setTimeout(() => {
        // Form bileşenini tamamen yeniden oluşturmak için key değerini artır
        setFormKey(prevKey => prevKey + 1)
      }, 300)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          className={buttonClassName}
        >
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
          <DialogDescription>
            {dialogDescription}
          </DialogDescription>
        </DialogHeader>

        {bookingSuccess ? (
          <div className="space-y-4 py-4">
            <div className="flex flex-col items-center justify-center text-center space-y-2">
              <div className="rounded-full bg-primary/10 p-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-primary"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold">Randevunuz Oluşturuldu!</h3>
              <p className="text-sm text-muted-foreground">
                Randevunuz başarıyla oluşturuldu. Randevu detayları e-posta adresinize gönderilecektir.
              </p>
            </div>
            <div className="flex justify-center">
              <Button onClick={() => handleOpenChange(false)}>
                Kapat
              </Button>
            </div>
          </div>
        ) : (
          <BookingForm key={formKey} salonId={salonId} onSuccess={handleBookingSuccess} />
        )}
      </DialogContent>
    </Dialog>
  )
}
