"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  AppointmentHeaderSkeleton, 
  WeeklyCalendarSkeleton, 
  DailyCalendarSkeleton, 
  MonthlyCalendarSkeleton,
  AppointmentsPageSkeleton
} from "@/components/ui/skeleton-loaders"

/**
 * Skeleton bileşenlerinin kullanımını gösteren örnek bileşen.
 * Bu bileşen, farklı skeleton bileşenlerini gösterir ve gerçek içerikle
 * karşılaştırma yapma imkanı sunar.
 */
export function SkeletonExample() {
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("header")

  // Yükleme durumunu simüle etmek için
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 3000) // 3 saniye sonra yükleme durumunu kapat

    return () => clearTimeout(timer)
  }, [])

  // Yükleme durumunu manuel olarak değiştirmek için
  const toggleLoading = () => {
    setIsLoading(!isLoading)
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Skeleton Bileşenleri Örneği</h1>
        <Button onClick={toggleLoading}>
          {isLoading ? "İçeriği Göster" : "Skeleton'ları Göster"}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Skeleton Bileşenleri</CardTitle>
          <CardDescription>
            Bu örnek, SalonFlow uygulamasında kullanılan skeleton bileşenlerini göstermektedir.
            Yükleme durumunu simüle etmek için yukarıdaki butonu kullanabilirsiniz.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="header">Başlık</TabsTrigger>
              <TabsTrigger value="weekly">Haftalık</TabsTrigger>
              <TabsTrigger value="daily">Günlük</TabsTrigger>
              <TabsTrigger value="monthly">Aylık</TabsTrigger>
              <TabsTrigger value="full">Tam Sayfa</TabsTrigger>
            </TabsList>

            <TabsContent value="header" className="mt-4 border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">Başlık Skeleton Bileşeni</h3>
              {isLoading ? (
                <AppointmentHeaderSkeleton />
              ) : (
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                  <div>
                    <h2 className="text-2xl font-bold">15 - 21 Mayıs 2025</h2>
                    <p className="text-muted-foreground">Randevu takvimi</p>
                  </div>
                  <div className="flex flex-wrap items-center gap-2">
                    <div className="flex items-center gap-2 bg-muted p-1 rounded-md">
                      <Button variant="ghost" size="icon" className="w-7 h-7 p-0">D</Button>
                      <Button variant="ghost" size="icon" className="w-7 h-7 p-0">H</Button>
                      <Button variant="ghost" size="icon" className="w-7 h-7 p-0">A</Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="icon" className="w-7 h-7 p-0">◀</Button>
                      <Button variant="outline" size="icon" className="w-7 h-7 p-0">•</Button>
                      <Button variant="outline" size="icon" className="w-7 h-7 p-0">▶</Button>
                      <Button variant="outline" size="icon" className="w-7 h-7 p-0">📅</Button>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="weekly" className="mt-4 border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">Haftalık Görünüm Skeleton Bileşeni</h3>
              {isLoading ? (
                <WeeklyCalendarSkeleton />
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
                  {["Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi", "Pazar"].map((day, index) => (
                    <Card key={index} className="h-full border">
                      <CardHeader className="p-3">
                        <div className="flex justify-between items-center">
                          <CardTitle className="text-sm font-medium">{day}</CardTitle>
                          {index === 0 && <div className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Tatil</div>}
                        </div>
                        <p className="text-xs text-muted-foreground">{`${15 + index} Mayıs`}</p>
                      </CardHeader>
                      <CardContent className="p-3 space-y-2">
                        <div className="text-xs text-muted-foreground p-2 rounded-md hover:bg-muted cursor-pointer">
                          Yeni randevu eklemek için tıklayın
                        </div>
                        {index % 2 === 0 && (
                          <div className="p-2 bg-muted rounded-md">
                            <div className="text-sm font-medium">Ahmet Yılmaz</div>
                            <div className="text-xs">10:00 - 10:30</div>
                            <div className="text-xs text-muted-foreground">Saç Kesimi</div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="daily" className="mt-4 border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">Günlük Görünüm Skeleton Bileşeni</h3>
              {isLoading ? (
                <DailyCalendarSkeleton />
              ) : (
                <Card>
                  <CardHeader className="py-3">
                    <div className="flex justify-between items-center">
                      <CardTitle>Perşembe, 18 Mayıs 2025</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {["09:00", "09:30", "10:00", "10:30", "11:00"].map((time, index) => (
                      <div key={time} className="flex items-center justify-between p-2 border rounded-md">
                        <div className="font-medium">{time}</div>
                        {index === 1 ? (
                          <div className="flex-1 ml-4">
                            <div className="text-sm font-medium">Ahmet Yılmaz - Saç Kesimi</div>
                            <div className="text-xs text-muted-foreground">0532 123 4567</div>
                          </div>
                        ) : (
                          <div className="text-muted-foreground text-sm ml-4">
                            Müsait - Randevu eklemek için tıklayın
                          </div>
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="monthly" className="mt-4 border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">Aylık Görünüm Skeleton Bileşeni</h3>
              {isLoading ? (
                <MonthlyCalendarSkeleton />
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-7 gap-1 text-center">
                    {["Pzt", "Sal", "Çar", "Per", "Cum", "Cmt", "Paz"].map((day) => (
                      <div key={day} className="font-medium text-sm py-2">
                        {day}
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-7 gap-1">
                    {Array.from({ length: 35 }).map((_, index) => {
                      const day = index + 1 - 3; // 1 Mayıs Çarşamba başlıyor varsayalım
                      const isCurrentMonth = day > 0 && day <= 31;
                      const displayDay = isCurrentMonth ? day : day <= 0 ? day + 30 : day - 31;
                      
                      return (
                        <Card key={index} className={`min-h-24 p-1 ${!isCurrentMonth ? 'opacity-40' : ''}`}>
                          <div className="flex justify-between items-start">
                            {index === 5 && <div className="bg-red-100 text-red-800 text-xs px-1 rounded-sm">Tatil</div>}
                            <span className={`inline-block rounded-full w-6 h-6 text-center leading-6 ${index === 15 ? 'bg-primary text-primary-foreground' : ''}`}>
                              {displayDay}
                            </span>
                          </div>
                          <div className="mt-2 space-y-1">
                            {index % 7 === 2 && (
                              <div className="text-xs bg-muted p-1 rounded">
                                2 randevu
                              </div>
                            )}
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="full" className="mt-4 border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">Tam Sayfa Skeleton Bileşeni</h3>
              {isLoading ? (
                <AppointmentsPageSkeleton viewType="weekly" />
              ) : (
                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                      <h2 className="text-2xl font-bold">15 - 21 Mayıs 2025</h2>
                      <p className="text-muted-foreground">Randevu takvimi</p>
                    </div>
                    <div className="flex flex-wrap items-center gap-2">
                      <div className="flex items-center gap-2 bg-muted p-1 rounded-md">
                        <Button variant="ghost" size="icon" className="w-7 h-7 p-0">D</Button>
                        <Button variant="ghost" size="icon" className="w-7 h-7 p-0">H</Button>
                        <Button variant="ghost" size="icon" className="w-7 h-7 p-0">A</Button>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="icon" className="w-7 h-7 p-0">◀</Button>
                        <Button variant="outline" size="icon" className="w-7 h-7 p-0">•</Button>
                        <Button variant="outline" size="icon" className="w-7 h-7 p-0">▶</Button>
                        <Button variant="outline" size="icon" className="w-7 h-7 p-0">📅</Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
                    {["Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi", "Pazar"].map((day, index) => (
                      <Card key={index} className="h-full border">
                        <CardHeader className="p-3">
                          <div className="flex justify-between items-center">
                            <CardTitle className="text-sm font-medium">{day}</CardTitle>
                            {index === 0 && <div className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Tatil</div>}
                          </div>
                          <p className="text-xs text-muted-foreground">{`${15 + index} Mayıs`}</p>
                        </CardHeader>
                        <CardContent className="p-3 space-y-2">
                          <div className="text-xs text-muted-foreground p-2 rounded-md hover:bg-muted cursor-pointer">
                            Yeni randevu eklemek için tıklayın
                          </div>
                          {index % 2 === 0 && (
                            <div className="p-2 bg-muted rounded-md">
                              <div className="text-sm font-medium">Ahmet Yılmaz</div>
                              <div className="text-xs">10:00 - 10:30</div>
                              <div className="text-xs text-muted-foreground">Saç Kesimi</div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <p className="text-sm text-muted-foreground">
            Not: Yükleme durumu 3 saniye sonra otomatik olarak kapanır. Manuel olarak değiştirmek için üstteki butonu kullanabilirsiniz.
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
