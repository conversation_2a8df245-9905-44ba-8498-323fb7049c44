"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, Check, Trash2 } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"

import { Button } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useNotifications, Notification } from "@/contexts/NotificationsContext"
import { cn } from "@/lib/utils"

export function NotificationPanel() {
  const [open, setOpen] = useState(false)
  const { notifications, unreadCount, markAsRead, markAllAsRead, clearNotifications, isLoading } = useNotifications()

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id)
    }
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-destructive text-[10px] text-destructive-foreground">
              {unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-72 p-0" align="end" alignOffset={-40} sideOffset={16}>
        <div className="flex items-center justify-between p-4">
          <div className="font-medium">Bildirimler</div>
          <div className="flex gap-1">
            {notifications.length > 0 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={markAllAsRead}
                  title="Tümünü okundu olarak işaretle"
                >
                  <Check className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={clearNotifications}
                  title="Tümünü temizle"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </>
            )}
          </div>
        </div>
        <Separator />
        {isLoading ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            Bildirimler yükleniyor...
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            Bildirim bulunmuyor
          </div>
        ) : (
          <ScrollArea className="h-[300px]">
            <div className="space-y-1 p-2">
              {notifications.map((notification) => {
                // Determine link based on notification type and data
                let href = "#"
                if (notification.data?.id) {
                  href = `/dashboard/appointments/${notification.data.id}`
                }

                // Determine notification color based on type
                const notificationColor = notification.type === 'new_booking'
                  ? 'bg-primary'
                  : notification.type === 'cancellation'
                    ? 'bg-destructive'
                    : 'bg-primary'

                return (
                  <Link
                    key={notification.id}
                    href={href}
                    onClick={() => handleNotificationClick(notification)}
                    className={cn(
                      "block rounded-md p-3 hover:bg-muted",
                      !notification.read && "bg-muted/50"
                    )}
                  >
                    <div className="flex items-start justify-between gap-2">
                      <div>
                        <div className="font-medium">
                          {notification.title}
                          {!notification.read && (
                            <span className={cn("ml-2 inline-flex h-2 w-2 rounded-full", notificationColor)}></span>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {notification.message}
                        </div>
                        <div className="mt-1 text-xs text-muted-foreground">
                          {format(notification.createdAt, "d MMMM, HH:mm", { locale: tr })}
                        </div>
                      </div>
                    </div>
                  </Link>
                )
              })}
            </div>
          </ScrollArea>
        )}
      </PopoverContent>
    </Popover>
  )
}
