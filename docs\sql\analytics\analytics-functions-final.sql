-- SalonFlow Analytics Functions
-- Created: 2024-09-01
-- Last Updated: 2024-09-01
--
-- <PERSON>u <PERSON>QL dosyası, SalonFlow uygulamasının analitik fonksiyonlarını içerir.
-- Salon sahipleri için randevu dağılımı, hiz<PERSON>, randevu trendi ve personel performansı
-- gibi analitik verileri sağlar.
--
-- Güvenlik:
-- - Tüm fonksiyonlar SECURITY DEFINER olarak işaretlenmiştir
-- - Kullanıcı admin veya salon sahibi olmalıdır
-- - is_admin() fonksiyonu kullanılarak admin kontrolü yapılır
-- - search_path = public ayarlanmıştır

-- 1. Barber Distribution fonksiyonu
-- Bu fonksiyon, belirli bir salon için personel bazında randevu dağılımını döndürür
CREATE OR REPLACE FUNCTION get_appointment_distribution_by_barber(
  p_salon_id UUID,
  p_start_date DATE,
  p_end_date DATE
) RETURNS TABLE (
  barber_id UUID,
  barber_name TEXT,
  appointment_count BIGINT,  -- COUNT() fonksiyonu BIGINT döndürür
  percentage NUMERIC
) SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  total_count BIGINT;  -- COUNT() fonksiyonu BIGINT döndürür
BEGIN
  -- Güvenlik kontrolü: Kullanıcı admin veya salon sahibi mi?
  IF NOT (is_admin() OR EXISTS (
    SELECT 1 FROM salons WHERE id = p_salon_id AND owner_id = auth.uid()
  )) THEN
    RAISE EXCEPTION 'Unauthorized access to salon data';
  END IF;

  -- Get total appointment count for the salon in the date range
  SELECT COUNT(*) INTO total_count
  FROM appointments
  WHERE salon_id = p_salon_id
    AND date BETWEEN p_start_date AND p_end_date
    AND status != 'cancelled';
  
  -- Return 0 if no appointments
  IF total_count = 0 THEN
    total_count := 1; -- Prevent division by zero
  END IF;
  
  -- Return appointment distribution by barber
  RETURN QUERY
  SELECT 
    b.id AS barber_id,
    b.name AS barber_name,
    COUNT(a.id) AS appointment_count,
    ROUND((COUNT(a.id)::NUMERIC / total_count) * 100, 2) AS percentage
  FROM 
    barbers b
    LEFT JOIN appointments a ON b.id = a.barber_id
      AND a.date BETWEEN p_start_date AND p_end_date
      AND a.status != 'cancelled'
  WHERE 
    b.salon_id = p_salon_id
  GROUP BY 
    b.id, b.name
  ORDER BY 
    appointment_count DESC;
END;
$$ LANGUAGE plpgsql;

-- 2. Service Popularity fonksiyonu
-- Bu fonksiyon, belirli bir salon için hizmet popülerliğini döndürür
CREATE OR REPLACE FUNCTION get_service_popularity(
  p_salon_id UUID,
  p_start_date DATE,
  p_end_date DATE,
  p_limit INTEGER DEFAULT 10
) RETURNS TABLE (
  service_id UUID,
  service_name TEXT,
  appointment_count BIGINT,  -- COUNT() fonksiyonu BIGINT döndürür
  percentage NUMERIC
) SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  total_count BIGINT;  -- COUNT() fonksiyonu BIGINT döndürür
BEGIN
  -- Güvenlik kontrolü: Kullanıcı admin veya salon sahibi mi?
  IF NOT (is_admin() OR EXISTS (
    SELECT 1 FROM salons WHERE id = p_salon_id AND owner_id = auth.uid()
  )) THEN
    RAISE EXCEPTION 'Unauthorized access to salon data';
  END IF;

  -- Get total appointment count for the salon in the date range
  SELECT COUNT(*) INTO total_count
  FROM appointments
  WHERE salon_id = p_salon_id
    AND date BETWEEN p_start_date AND p_end_date
    AND status != 'cancelled';
  
  -- Return 0 if no appointments
  IF total_count = 0 THEN
    total_count := 1; -- Prevent division by zero
  END IF;
  
  -- Return service popularity
  RETURN QUERY
  SELECT 
    s.id AS service_id,
    s.name AS service_name,
    COUNT(a.id) AS appointment_count,
    ROUND((COUNT(a.id)::NUMERIC / total_count) * 100, 2) AS percentage
  FROM 
    services s
    LEFT JOIN appointments a ON s.id = a.service_id
      AND a.date BETWEEN p_start_date AND p_end_date
      AND a.status != 'cancelled'
  WHERE 
    s.salon_id = p_salon_id
  GROUP BY 
    s.id, s.name
  ORDER BY 
    appointment_count DESC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- 3. Appointment Trends fonksiyonu
-- Bu fonksiyon, belirli bir salon için aylık randevu trendini döndürür
CREATE OR REPLACE FUNCTION get_appointment_trends(
  p_salon_id UUID,
  p_start_date DATE,
  p_end_date DATE
) RETURNS TABLE (
  month TEXT,
  year INTEGER,
  month_number INTEGER,
  appointment_count BIGINT  -- COUNT() fonksiyonu BIGINT döndürür
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Güvenlik kontrolü: Kullanıcı admin veya salon sahibi mi?
  IF NOT (is_admin() OR EXISTS (
    SELECT 1 FROM salons WHERE id = p_salon_id AND owner_id = auth.uid()
  )) THEN
    RAISE EXCEPTION 'Unauthorized access to salon data';
  END IF;

  RETURN QUERY
  SELECT 
    TO_CHAR(a.date, 'Mon') AS month,
    EXTRACT(YEAR FROM a.date)::INTEGER AS year,
    EXTRACT(MONTH FROM a.date)::INTEGER AS month_number,
    COUNT(a.id) AS appointment_count
  FROM 
    appointments a
  WHERE 
    a.salon_id = p_salon_id
    AND a.date BETWEEN p_start_date AND p_end_date
    AND a.status != 'cancelled'
  GROUP BY 
    month, year, month_number
  ORDER BY 
    year, month_number;
END;
$$ LANGUAGE plpgsql;

-- 4. Barber Performance fonksiyonu
-- Bu fonksiyon, belirli bir salon için personel performans metriklerini döndürür
CREATE OR REPLACE FUNCTION get_barber_performance(
  p_salon_id UUID,
  p_start_date DATE,
  p_end_date DATE
) RETURNS TABLE (
  barber_id UUID,
  barber_name TEXT,
  total_appointments BIGINT,  -- COUNT() fonksiyonu BIGINT döndürür
  completed_appointments BIGINT,  -- COUNT() fonksiyonu BIGINT döndürür
  cancelled_appointments BIGINT,  -- COUNT() fonksiyonu BIGINT döndürür
  no_show_appointments BIGINT,  -- COUNT() fonksiyonu BIGINT döndürür
  completion_rate NUMERIC,
  unique_customers BIGINT  -- COUNT() fonksiyonu BIGINT döndürür
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Güvenlik kontrolü: Kullanıcı admin veya salon sahibi mi?
  IF NOT (is_admin() OR EXISTS (
    SELECT 1 FROM salons WHERE id = p_salon_id AND owner_id = auth.uid()
  )) THEN
    RAISE EXCEPTION 'Unauthorized access to salon data';
  END IF;

  RETURN QUERY
  SELECT 
    b.id AS barber_id,
    b.name AS barber_name,
    COUNT(a.id) AS total_appointments,
    COUNT(CASE WHEN a.status = 'completed' THEN 1 ELSE NULL END) AS completed_appointments,
    COUNT(CASE WHEN a.status = 'cancelled' THEN 1 ELSE NULL END) AS cancelled_appointments,
    COUNT(CASE WHEN a.status = 'no-show' THEN 1 ELSE NULL END) AS no_show_appointments,
    CASE 
      WHEN COUNT(a.id) > 0 THEN 
        ROUND((COUNT(CASE WHEN a.status = 'completed' THEN 1 ELSE NULL END)::NUMERIC / COUNT(a.id)) * 100, 2)
      ELSE 0
    END AS completion_rate,
    COUNT(DISTINCT a.customer_id) AS unique_customers
  FROM 
    barbers b
    LEFT JOIN appointments a ON b.id = a.barber_id
      AND a.date BETWEEN p_start_date AND p_end_date
  WHERE 
    b.salon_id = p_salon_id
  GROUP BY 
    b.id, b.name
  ORDER BY 
    total_appointments DESC;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_appointment_distribution_by_barber(UUID, DATE, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION get_service_popularity(UUID, DATE, DATE, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_appointment_trends(UUID, DATE, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION get_barber_performance(UUID, DATE, DATE) TO authenticated;
