# Public Access Functions Kullanımı

**Tarih:** 25 Ağustos 2024
**Saat:** 15:35

## Genel Bakış

Bu do<PERSON>ü<PERSON>yon, kimlik doğrulaması olmayan kullanıcılar için güvenli veri erişimi sağlayan SECURITY DEFINER fonksiyonlarının frontend'de nasıl kullanılacağını açıklamaktadır.

## SECURITY DEFINER Fonksiyonları Nedir?

SECURITY DEFINER fonksiyonları, PostgreSQL'de güçlü bir özelliktir. Bu fonksiyonlar, fonksiyonu çağıran kullan<PERSON>, fonksiyonu oluşturan kullanıcının yetkileriyle çalışır. Bu, RLS politikalarını bypass etmek için kullanılabilir.

Bu yaklaşımın avantajları:
- <PERSON><PERSON> doğrulaması olmayan kullanıcılar için güvenli veri eri<PERSON><PERSON><PERSON>
- Hassas bilgileri korur
- Daha esnek ve güvenli bir çözüm sunar
- Parametreler alabilir ve karmaşık mantık içerebilir

## Frontend'de Kullanım

Supabase'de fonksiyonları çağırmak için `rpc` metodunu kullanabiliriz. Aşağıda, oluşturulan fonksiyonların frontend'de nasıl kullanılacağına dair örnekler bulunmaktadır.

### 1. Salon Bilgilerini Slug ile Getirme

```typescript
// src/lib/db/public/salons.ts
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için salon bilgilerini slug ile getir
 */
export async function getPublicSalonBySlug(slug: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_salon_by_slug', { p_slug: slug });

  if (error) throw error;
  return data;
}
```

### 2. Salon Bilgilerini ID ile Getirme

```typescript
/**
 * Kimlik doğrulaması olmayan kullanıcılar için salon bilgilerini ID ile getir
 */
export async function getPublicSalonById(id: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_salon_by_id', { p_id: id });

  if (error) throw error;
  return data;
}
```

### 3. Berber Bilgilerini Salon ID ile Getirme

```typescript
// src/lib/db/public/barbers.ts
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için berber bilgilerini salon ID ile getir
 */
export async function getPublicBarbersBySalonId(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_barbers_by_salon_id', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
```

### 4. Hizmet Bilgilerini Salon ID ile Getirme

```typescript
// src/lib/db/public/services.ts
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için hizmet bilgilerini salon ID ile getir
 */
export async function getPublicServicesBySalonId(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_services_by_salon_id', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
```

### 5. Çalışma Saatlerini Salon ID ile Getirme

```typescript
// src/lib/db/public/working-hours.ts
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için çalışma saatlerini salon ID ile getir
 */
export async function getPublicWorkingHoursBySalonId(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_working_hours_by_salon_id', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
```

### 6. Tatil Günlerini Salon ID ile Getirme

```typescript
// src/lib/db/public/holidays.ts
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için tatil günlerini salon ID ile getir
 */
export async function getPublicHolidaysBySalonId(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_holidays_by_salon_id', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
```

### 7. Aktif Ürünleri Salon ID ile Getirme

```typescript
// src/lib/db/public/products.ts
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için aktif ürünleri salon ID ile getir
 */
export async function getPublicActiveProductsBySalonId(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_active_products_by_salon_id', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
```

### 8. Randevu Bilgilerini Salon ID ve Tarih Aralığı ile Getirme

```typescript
// src/lib/db/public/appointments.ts
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Kimlik doğrulaması olmayan kullanıcılar için randevu bilgilerini salon ID ve tarih aralığı ile getir
 */
export async function getPublicAppointmentsBySalonId(salonId: string, startDate: string, endDate: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_appointments_by_salon_id', { 
      p_salon_id: salonId,
      p_start_date: startDate,
      p_end_date: endDate
    });

  if (error) throw error;
  return data;
}
```

### 9. Randevu Oluşturma

```typescript
/**
 * Kimlik doğrulaması olmayan kullanıcılar için randevu oluştur
 */
export async function createPublicAppointment(
  salonId: string,
  barberId: string,
  serviceId: string,
  date: string,
  startTime: string,
  endTime: string,
  fullname: string,
  phonenumber: string,
  email: string
) {
  const { data, error } = await supabaseClient
    .rpc('create_public_appointment', {
      p_salon_id: salonId,
      p_barber_id: barberId,
      p_service_id: serviceId,
      p_date: date,
      p_start_time: startTime,
      p_end_time: endTime,
      p_fullname: fullname,
      p_phonenumber: phonenumber,
      p_email: email
    });

  if (error) throw error;
  return data;
}
```

## Uygulama Örneği

Aşağıda, bu fonksiyonların bir sayfada nasıl kullanılabileceğine dair bir örnek bulunmaktadır:

```tsx
// src/app/[slug]/page.tsx
"use client"

import React, { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { getPublicSalonBySlug } from "@/lib/db/public/salons"
import { getPublicBarbersBySalonId } from "@/lib/db/public/barbers"
import { getPublicServicesBySalonId } from "@/lib/db/public/services"

export default function SalonPage() {
  const { slug } = useParams()
  const [salon, setSalon] = useState(null)
  const [barbers, setBarbers] = useState([])
  const [services, setServices] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadSalonData() {
      try {
        setIsLoading(true)
        // Salon bilgilerini getir
        const salonData = await getPublicSalonBySlug(slug as string)
        setSalon(salonData)

        if (salonData) {
          // Berber bilgilerini getir
          const barbersData = await getPublicBarbersBySalonId(salonData.id)
          setBarbers(barbersData)

          // Hizmet bilgilerini getir
          const servicesData = await getPublicServicesBySalonId(salonData.id)
          setServices(servicesData)
        }
      } catch (error) {
        console.error("Salon bilgileri yüklenirken hata:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (slug) {
      loadSalonData()
    }
  }, [slug])

  // Sayfa içeriği...
}
```

## Sonuç

Bu yaklaşım, kimlik doğrulaması olmayan kullanıcıların sadece ihtiyaç duydukları verilere erişmesini sağlarken, hassas bilgileri koruyacaktır. SECURITY DEFINER fonksiyonları, RLS politikalarını bypass ederek, güvenli bir şekilde veri erişimi sağlar.
