-- Appointment Creator Tracking
-- Bu SQL dosyası, randevu oluşturan kullanıcıyı takip etmek ve kendi oluşturdukları randevular için bildirim almalarını engellemek için gerekli değişiklikleri içerir.

-- 1. Appointments tablosuna created_by s<PERSON><PERSON><PERSON> ekle
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- 2. <PERSON><PERSON><PERSON> oluşturulduğunda created_by alan<PERSON><PERSON><PERSON> otomatik olarak dolduran trigger
CREATE OR REPLACE FUNCTION set_appointment_created_by()
RETURNS TRIGGER AS $$
BEGIN
  -- <PERSON><PERSON><PERSON> kullanıcı oturum açmışsa created_by alan<PERSON>n<PERSON> doldur
  IF auth.uid() IS NOT NULL THEN
    NEW.created_by := auth.uid();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_appointment_created_by_trigger ON appointments;
CREATE TRIGGER set_appointment_created_by_trigger
BEFORE INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION set_appointment_created_by();

-- 3. Bildirim trigger'larını güncelle

-- Yeni randevu oluşturulduğunda bildirim oluşturan fonksiyon (güncellenmiş)
CREATE OR REPLACE FUNCTION create_new_appointment_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  barber_user_id UUID;
  appointment_date TEXT;
  appointment_time TEXT;
BEGIN
  -- Salon sahibini bul
  SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;
  
  -- Berber kullanıcı ID'sini bul (eğer atanmışsa)
  IF NEW.barber_id IS NOT NULL THEN
    SELECT user_id INTO barber_user_id FROM barbers WHERE id = NEW.barber_id;
  END IF;
  
  -- Tarih ve saat formatla
  appointment_date := to_char(NEW.date, 'DD Month YYYY');
  appointment_time := to_char(NEW.start_time, 'HH24:MI');
  
  -- Salon sahibine bildirim gönder (eğer kendisi oluşturmadıysa)
  IF salon_owner_id IS NOT NULL AND salon_owner_id != NEW.created_by THEN
    INSERT INTO notifications (
      id, 
      salon_id, 
      user_id, 
      type, 
      title, 
      message, 
      read, 
      created_at, 
      data
    ) VALUES (
      gen_random_uuid(), 
      NEW.salon_id, 
      salon_owner_id, 
      'new_booking', 
      'Yeni Randevu', 
      format('%s tarafından yeni bir randevu oluşturuldu. (%s, %s)', 
             COALESCE(NEW.fullname, 'Bir müşteri'), 
             appointment_date, 
             appointment_time),
      false, 
      now(), 
      jsonb_build_object('id', NEW.id)
    );
  END IF;
  
  -- Berbere bildirim gönder (eğer atanmışsa, kullanıcı hesabı varsa ve kendisi oluşturmadıysa)
  IF barber_user_id IS NOT NULL AND barber_user_id != salon_owner_id AND barber_user_id != NEW.created_by THEN
    INSERT INTO notifications (
      id, 
      salon_id, 
      user_id, 
      type, 
      title, 
      message, 
      read, 
      created_at, 
      data
    ) VALUES (
      gen_random_uuid(), 
      NEW.salon_id, 
      barber_user_id, 
      'new_booking', 
      'Yeni Randevu', 
      format('%s tarafından yeni bir randevu oluşturuldu. (%s, %s)', 
             COALESCE(NEW.fullname, 'Bir müşteri'), 
             appointment_date, 
             appointment_time),
      false, 
      now(), 
      jsonb_build_object('id', NEW.id)
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Randevu iptal edildiğinde bildirim oluşturan fonksiyon (güncellenmiş)
CREATE OR REPLACE FUNCTION create_cancelled_appointment_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  barber_user_id UUID;
  appointment_date TEXT;
  appointment_time TEXT;
BEGIN
  -- Sadece durum 'cancelled' olarak değiştiğinde çalış
  IF OLD.status != 'cancelled' AND NEW.status = 'cancelled' THEN
    -- Salon sahibini bul
    SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;
    
    -- Berber kullanıcı ID'sini bul (eğer atanmışsa)
    IF NEW.barber_id IS NOT NULL THEN
      SELECT user_id INTO barber_user_id FROM barbers WHERE id = NEW.barber_id;
    END IF;
    
    -- Tarih ve saat formatla
    appointment_date := to_char(NEW.date, 'DD Month YYYY');
    appointment_time := to_char(NEW.start_time, 'HH24:MI');
    
    -- Salon sahibine bildirim gönder (eğer kendisi iptal etmediyse)
    IF salon_owner_id IS NOT NULL AND salon_owner_id != auth.uid() THEN
      INSERT INTO notifications (
        id, 
        salon_id, 
        user_id, 
        type, 
        title, 
        message, 
        read, 
        created_at, 
        data
      ) VALUES (
        gen_random_uuid(), 
        NEW.salon_id, 
        salon_owner_id, 
        'cancellation', 
        'Randevu İptali', 
        format('%s randevusu iptal edildi. (%s, %s)', 
               COALESCE(NEW.fullname, 'Bir müşteri'), 
               appointment_date, 
               appointment_time),
        false, 
        now(), 
        jsonb_build_object('id', NEW.id)
      );
    END IF;
    
    -- Berbere bildirim gönder (eğer atanmışsa, kullanıcı hesabı varsa ve kendisi iptal etmediyse)
    IF barber_user_id IS NOT NULL AND barber_user_id != salon_owner_id AND barber_user_id != auth.uid() THEN
      INSERT INTO notifications (
        id, 
        salon_id, 
        user_id, 
        type, 
        title, 
        message, 
        read, 
        created_at, 
        data
      ) VALUES (
        gen_random_uuid(), 
        NEW.salon_id, 
        barber_user_id, 
        'cancellation', 
        'Randevu İptali', 
        format('%s randevusu iptal edildi. (%s, %s)', 
               COALESCE(NEW.fullname, 'Bir müşteri'), 
               appointment_date, 
               appointment_time),
        false, 
        now(), 
        jsonb_build_object('id', NEW.id)
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ları yeniden oluştur
DROP TRIGGER IF EXISTS on_appointment_created ON appointments;
CREATE TRIGGER on_appointment_created
AFTER INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION create_new_appointment_notification();

DROP TRIGGER IF EXISTS on_appointment_cancelled ON appointments;
CREATE TRIGGER on_appointment_cancelled
AFTER UPDATE ON appointments
FOR EACH ROW
EXECUTE FUNCTION create_cancelled_appointment_notification();
