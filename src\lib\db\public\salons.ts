import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * <PERSON>lik doğrulaması olmayan kullanıcılar için salon bilgilerini slug ile getir
 */
export async function getPublicSalonBySlug(slug: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_salon_by_slug', { p_slug: slug });

  if (error) throw error;
  return data;
}

/**
 * Kimlik doğrulaması olmayan kullanıcılar için salon bilgilerini ID ile getir
 */
export async function getPublicSalonById(id: string) {
  const { data, error } = await supabaseClient
    .rpc('get_public_salon_by_id', { p_id: id });

  if (error) throw error;
  return data;
}
