"use client"

import { useState, useEffect, Suspense } from "react"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import Link from "next/link"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { supabase } from "@/lib/supabase"
import { referrals } from "@/lib/db"

const formSchema = z.object({
  email: z.string().email({
    message: "Lütfen geçerli bir e-posta adresi girin.",
  }),
  password: z.string().min(6, {
    message: "<PERSON><PERSON><PERSON> en az 6 karakter olmalıdır.",
  }),
  confirmPassword: z.string().min(6, {
    message: "Şifre en az 6 karakter olmalıdır.",
  }),
  referralCode: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Şifreler eşleşmiyor",
  path: ["confirmPassword"],
})

function RegisterForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)

  // Get referral code from URL if present
  const refCode = searchParams.get('ref')

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      referralCode: refCode || "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true)
    try {
      // Check if we're using a mock client (for development without Supabase)
      const isMockClient = !process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

      if (isMockClient) {
        // Simulate successful registration for development
        toast.success("Geliştirme modu: Kayıt başarılı! Gerçek bir ortamda, bir doğrulama e-postası alacaksınız.")
        router.push("/auth/login")
        return
      }

      // Real Supabase registration
      const { data, error } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        toast.error(error.message)
        return
      }

      // Store the referral code in the database to use after email verification
      if (values.referralCode && data.user) {
        try {
          // Referans kodunu veritabanında sakla
          await referrals.savePendingReferral(data.user.id, values.referralCode);
          console.log(`Referral code ${values.referralCode} saved for user ${data.user.id}`);

          toast.success("Kayıt başarılı! Hesabınızı doğruladıktan sonra referans kodunuz uygulanacaktır.");
        } catch (error) {
          console.error("Referans kodu kaydedilirken hata:", error);
          // Hata durumunda yedek olarak sessionStorage'a kaydet
          sessionStorage.setItem('referralCode', values.referralCode);
          console.log(`Referral code ${values.referralCode} saved in sessionStorage as fallback`);

          toast.success("Kayıt başarılı! Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.");
        }
      } else {
        toast.success("Kayıt başarılı! Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.");
      }

      router.push("/auth/login")
    } catch (error) {
      toast.error("Bir hata oluştu. Lütfen tekrar deneyin.")
      console.error(error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Kayıt Ol</CardTitle>
          <CardDescription>
            Başlamak için bir hesap oluşturun
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>E-posta</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Şifre</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Şifreyi Onayla</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="••••••••" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="referralCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Referans Kodu (İsteğe Bağlı)</FormLabel>
                    <FormControl>
                      <Input placeholder="Referans kodunuz varsa girin" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Hesap oluşturuluyor..." : "Kayıt Ol"}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <div className="text-sm">
            Zaten bir hesabınız var mı?{" "}
            <Link href="/auth/login" className="text-primary hover:underline">
              Giriş Yap
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}

export default function RegisterPage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>}>
      <RegisterForm />
    </Suspense>
  )
}
