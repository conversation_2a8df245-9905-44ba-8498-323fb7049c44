-- SalonFlow Public Access Functions
-- Oluşturulma Tarihi: 2024-08-25
-- <PERSON><PERSON> SQL dosyası, kimlik doğrulaması olmayan kullanıcılar için güvenli veri erişimi <PERSON>
-- SECURITY DEFINER fonksiyonlarını içerir.

-- SECURITY DEFINER fonksiyonları, fonksiyonu çağıran kullanıcının değil, fonksiyonu oluşturan
-- kullanıcının yetkileriyle çalışır. Bu, RLS politikalarını bypass etmek için kullanılabilir.

-- 1. Salon bilgilerini slug ile getiren fonksiyon
CREATE OR REPLACE FUNCTION get_public_salon_by_slug(p_slug TEXT)
RETURNS TABLE (
  id UUID,
  name TEXT,
  address TEXT,
  phone TEXT,
  website TEXT,
  description TEXT,
  logo_url TEXT,
  slug TEXT
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT s.id, s.name, s.address, s.phone, s.website, s.description, s.logo_url, s.slug
  FROM salons s
  WHERE s.slug = p_slug;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_salon_by_slug(TEXT) TO anon;

-- 2. Salon bilgilerini ID ile getiren fonksiyon
CREATE OR REPLACE FUNCTION get_public_salon_by_id(p_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  address TEXT,
  phone TEXT,
  website TEXT,
  description TEXT,
  logo_url TEXT,
  slug TEXT
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT s.id, s.name, s.address, s.phone, s.website, s.description, s.logo_url, s.slug
  FROM salons s
  WHERE s.id = p_id;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_salon_by_id(UUID) TO anon;

-- 3. Berber bilgilerini salon ID ile getiren fonksiyon
CREATE OR REPLACE FUNCTION get_public_barbers_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  name TEXT,
  profile_image_url TEXT
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT b.id, b.salon_id, b.name, b.profile_image_url
  FROM barbers b
  WHERE b.salon_id = p_salon_id
  ORDER BY b.name;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_barbers_by_salon_id(UUID) TO anon;

-- 4. Hizmet bilgilerini salon ID ile getiren fonksiyon
drop function if exists get_public_services_by_salon_id;

CREATE OR REPLACE FUNCTION get_public_services_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  name TEXT,
  description TEXT,
  duration INTEGER
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
RETURN QUERY
SELECT s.id, s.salon_id, s.name, s.description, s.duration
FROM services s
WHERE s.salon_id = p_salon_id
ORDER BY s.name;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_services_by_salon_id(UUID) TO anon;
-- 5. Çalışma saatlerini salon ID ile getiren fonksiyon
CREATE OR REPLACE FUNCTION get_public_working_hours_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  barber_id UUID,
  day_of_week INTEGER,
  open_time TIME,
  close_time TIME,
  is_closed BOOLEAN,
  lunch_start_time TIME,
  lunch_end_time TIME,
  has_lunch_break BOOLEAN
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT w.id, w.salon_id, w.barber_id, w.day_of_week, w.open_time, w.close_time, w.is_closed,
         w.lunch_start_time, w.lunch_end_time, w.has_lunch_break
  FROM working_hours w
  WHERE w.salon_id = p_salon_id
  ORDER BY w.day_of_week;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_working_hours_by_salon_id(UUID) TO anon;

-- 6. Tatil günlerini salon ID ile getiren fonksiyon
CREATE OR REPLACE FUNCTION get_public_holidays_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  date DATE,
  description TEXT,
  is_recurring BOOLEAN
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT h.id, h.salon_id, h.date, h.description, h.is_recurring
  FROM holidays h
  WHERE h.salon_id = p_salon_id
  ORDER BY h.date;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_holidays_by_salon_id(UUID) TO anon;

-- 7. Aktif ürünleri salon ID ile getiren fonksiyon
CREATE OR REPLACE FUNCTION get_public_active_products_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  name TEXT,
  description TEXT,
  price NUMERIC,
  image_url TEXT,
  category TEXT
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT p.id, p.salon_id, p.name, p.description, p.price, p.image_url, p.category
  FROM products p
  WHERE p.salon_id = p_salon_id AND p.is_active = TRUE
  ORDER BY p.name;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_active_products_by_salon_id(UUID) TO anon;

-- 8. Randevu bilgilerini salon ID ve tarih aralığı ile getiren fonksiyon
CREATE OR REPLACE FUNCTION get_public_appointments_by_salon_id(p_salon_id UUID, p_start_date DATE, p_end_date DATE)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  barber_id UUID,
  service_id UUID,
  date DATE,
  start_time TIME,
  end_time TIME,
  status TEXT
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT a.id, a.salon_id, a.barber_id, a.service_id, a.date, a.start_time, a.end_time, a.status
  FROM appointments a
  WHERE a.salon_id = p_salon_id
    AND a.date BETWEEN p_start_date AND p_end_date
  ORDER BY a.date, a.start_time;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_appointments_by_salon_id(UUID, DATE, DATE) TO anon;

-- 9. Randevu oluşturan fonksiyon
CREATE OR REPLACE FUNCTION create_public_appointment(
  p_salon_id UUID,
  p_barber_id UUID,
  p_service_id UUID,
  p_date DATE,
  p_start_time TIME,
  p_end_time TIME,
  p_fullname TEXT,
  p_phonenumber TEXT,
  p_email TEXT
)
RETURNS UUID
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_appointment_id UUID;
BEGIN
  INSERT INTO appointments (
    salon_id,
    barber_id,
    service_id,
    date,
    start_time,
    end_time,
    fullname,
    phonenumber,
    email,
    status
  ) VALUES (
    p_salon_id,
    p_barber_id,
    p_service_id,
    p_date,
    p_start_time,
    p_end_time,
    p_fullname,
    p_phonenumber,
    p_email,
    'booked'
  ) RETURNING id INTO v_appointment_id;
  
  RETURN v_appointment_id;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION create_public_appointment(UUID, UUID, UUID, DATE, TIME, TIME, TEXT, TEXT, TEXT) TO anon;

-- Mevcut "Anyone can view" politikalarını kaldır
DROP POLICY IF EXISTS "Anyone can view salons" ON salons;
DROP POLICY IF EXISTS "Anyone can view barbers" ON barbers;
DROP POLICY IF EXISTS "Anyone can view services" ON services;
DROP POLICY IF EXISTS "Anyone can view appointments" ON appointments;
DROP POLICY IF EXISTS "Anyone can view working_hours" ON working_hours;
DROP POLICY IF EXISTS "Anyone can view active products" ON products;
DROP POLICY IF EXISTS "Anyone can view holidays" ON holidays;
DROP POLICY IF EXISTS "Anyone can insert appointments" ON appointments;

-- Sadece kimlik doğrulaması olan kullanıcıların erişimine izin ver
CREATE POLICY "Authenticated users can view salons" ON salons
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can view barbers" ON barbers
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can view services" ON services
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can view working_hours" ON working_hours
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can view holidays" ON holidays
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can view products" ON products
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can view appointments" ON appointments
  FOR SELECT
  TO authenticated
  USING (true);
