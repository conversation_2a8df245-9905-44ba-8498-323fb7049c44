"use client"

import { motion } from "framer-motion"
import { Clock, TrendingUp } from "lucide-react"
import { BookingButton } from "./booking-button"

interface ServiceCardProps {
  service: {
    id: string
    name: string
    description?: string
    duration: number
  }
  salonId: string
  index: number
}

export function ServiceCard({ service, index }: ServiceCardProps) {
  const formatDuration = (duration: number) => {
    if (duration >= 60) {
      const hours = Math.floor(duration / 60)
      const minutes = duration % 60
      if (minutes === 0) {
        return `${hours} saat`
      }
      return `${hours}s ${minutes}dk`
    }
    return `${duration} dk`
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        ease: "easeOut"
      }}
      viewport={{ once: true }}
      whileHover={{ y: -8 }}
      className="group relative"
    >
      <div className="relative bg-background border border-border rounded-2xl p-6 shadow-sm hover:shadow-xl transition-all duration-500 overflow-hidden h-full">
        {/* Background Gradient on Hover */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Content */}
        <div className="relative h-full flex flex-col">
          {/* Header */}
          <div className="space-y-3 flex-grow">
            <div className="flex items-start justify-between">
              <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-300">
                {service.name}
              </h3>
              <motion.div
                whileHover={{ scale: 1.1 }}
                className="p-2 bg-primary/10 rounded-full group-hover:bg-primary/20 transition-colors duration-300 flex-shrink-0"
              >
                <TrendingUp className="w-5 h-5 text-primary" />
              </motion.div>
            </div>

            {/* Description with fixed height */}
            <div className="min-h-[3rem] flex items-start">
              <p className="text-muted-foreground leading-relaxed text-sm">
                {service.description || "Profesyonel hizmet"}
              </p>
            </div>
          </div>

          {/* Service Details */}
          <div className="flex items-center justify-between py-3 border-t border-border/50 mt-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                <span>{formatDuration(service.duration)}</span>
              </div>
            </div>

          </div>

          {/* CTA Button */}
          <div className="pt-4">
            <BookingButton
              buttonText="Randevu Al"
              buttonClassName="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-md hover:shadow-lg transition-all duration-300 group-hover:scale-105"
              buttonSize="default"
            />
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-secondary/10 to-transparent rounded-tr-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      </div>
    </motion.div>
  )
}
