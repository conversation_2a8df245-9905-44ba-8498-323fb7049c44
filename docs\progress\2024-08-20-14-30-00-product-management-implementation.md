# SalonFlow Ürün Yönetimi ve Görüntüleme Sistemi Geliştirme Planı

Bu belge, SalonFlow uygulamasına eklenecek olan Ürün Yönetimi ve Görüntüleme Sistemi özelliğinin geliştirme planını içermektedir.

## 1. Veritabanı Yapısı

### 1.1. <PERSON><PERSON><PERSON>n Tablosu Oluşturma
- **Zorluk:** Orta
- **<PERSON><PERSON><PERSON>:** 1 gün
- **Durum:** ✅ Tamamlandı

#### 1.1.1. products Tablosu
```sql
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10, 2),
  category TEXT,
  image_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Politikaları
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi salonlarının ürünlerini görebilir ve yönetebilir
CREATE POLICY "Salon owners can manage their salon's products" ON products
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Personel kendi salonlarının ürünlerini görebilir
CREATE POLICY "Staff can view their salon's products" ON products
  FOR SELECT
  USING (salon_id IN (
    SELECT salon_id FROM barbers
    WHERE user_id = auth.uid()
  ));

-- Herkes aktif ürünleri görebilir (müşteri tarafı için)
CREATE POLICY "Anyone can view active products" ON products
  FOR SELECT
  USING (is_active = TRUE);

-- Admin tüm ürünleri görebilir ve yönetebilir
CREATE POLICY "Admins can manage all products" ON products
  USING (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()))
  WITH CHECK (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()));
```

### 1.2. Ürün Görselleri için Storage Bucket Oluşturma
- **Zorluk:** Düşük
- **Tahmini Süre:** 0.5 gün
- **Durum:** ✅ Tamamlandı

```sql
-- Ürün görselleri için storage bucket oluşturma
INSERT INTO storage.buckets (id, name, public) VALUES ('product_images', 'product_images', true);

-- RLS Politikaları
CREATE POLICY "Public read access for product images" ON storage.objects
  FOR SELECT
  USING (bucket_id = 'product_images');

CREATE POLICY "Salon owners can upload product images" ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'product_images' AND
    (auth.uid() IN (SELECT owner_id FROM salons WHERE id::text = (storage.foldername(name))[1]))
  );

CREATE POLICY "Salon owners can update their product images" ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'product_images' AND
    (auth.uid() IN (SELECT owner_id FROM salons WHERE id::text = (storage.foldername(name))[1]))
  );

CREATE POLICY "Salon owners can delete their product images" ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'product_images' AND
    (auth.uid() IN (SELECT owner_id FROM salons WHERE id::text = (storage.foldername(name))[1]))
  );

CREATE POLICY "Admins can manage all product images" ON storage.objects
  USING (
    bucket_id = 'product_images' AND
    EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin())
  )
  WITH CHECK (
    bucket_id = 'product_images' AND
    EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin())
  );
```

## 2. Backend Geliştirme

### 2.1. Ürün Yönetimi API Fonksiyonları
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Durum:** ✅ Tamamlandı

#### 2.1.1. Ürün Tipleri ve DB Fonksiyonları
- `src/lib/db/types.ts` dosyasına ürün tiplerini ekle
- `src/lib/db/products.ts` dosyasını oluştur (CRUD işlemleri)

#### 2.1.2. Görsel Yükleme Fonksiyonları
- `src/lib/storage.ts` dosyasını oluştur (Supabase Storage işlemleri)

## 3. Frontend Geliştirme

### 3.1. Salon Sahibi Ürün Yönetimi Arayüzü
- **Zorluk:** Yüksek
- **Tahmini Süre:** 2 gün
- **Durum:** ✅ Tamamlandı

#### 3.1.1. Ürün Listeleme Sayfası
- `src/app/dashboard/products/page.tsx` dosyasını oluştur
- Ürün tablosu, arama, filtreleme ve sıralama özellikleri

#### 3.1.2. Ürün Ekleme/Düzenleme Formu
- `src/components/product-form.tsx` bileşenini oluştur
- Görsel yükleme, önizleme ve kırpma özellikleri

### 3.2. Müşteri Tarafı Ürün Görüntüleme
- **Zorluk:** Orta
- **Tahmini Süre:** 1.5 gün
- **Durum:** ✅ Tamamlandı

#### 3.2.1. Ürün Vitrin Sayfası
- `src/app/[slug]/products/page.tsx` dosyasını oluştur
- Responsive grid layout, filtreleme ve kategori görünümü

## 4. Abonelik Entegrasyonu

### 4.1. Abonelik Planlarına Göre Özellik Kısıtlamaları
- **Zorluk:** Düşük
- **Tahmini Süre:** 0.5 gün
- **Durum:** ✅ Tamamlandı

#### 4.1.1. Abonelik Planlarını Güncelleme
- `subscription_plans` tablosuna ürün yönetimi özelliği ekle
- Depolama limitleri tanımla (Solo: 0, Small Team: 100MB, Pro: 500MB)

#### 4.1.2. Özellik Kontrolü
- `useSubscriptionFeatures` hook'unu güncelle
- Depolama kullanımı izleme fonksiyonları ekle

## 5. Test ve Optimizasyon

### 5.1. Birim ve Entegrasyon Testleri
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Durum:** 📝 Planlandı

### 5.2. Görsel Optimizasyonu
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Durum:** 📝 Planlandı

#### 5.2.1. Görsel Boyutlandırma ve Sıkıştırma
- Yükleme öncesi istemci tarafında boyutlandırma
- WebP formatına dönüştürme
- Lazy loading ve önbelleğe alma stratejileri

## İlerleme Durumu

| Görev | Durum | Tamamlanma Tarihi |
|-------|-------|-------------------|
| 1.1. Ürün Tablosu Oluşturma | ✅ Tamamlandı | 2024-08-20 |
| 1.2. Storage Bucket Oluşturma | ✅ Tamamlandı | 2024-08-20 |
| 2.1. Ürün Yönetimi API Fonksiyonları | ✅ Tamamlandı | 2024-08-20 |
| 3.1. Salon Sahibi Ürün Yönetimi Arayüzü | ✅ Tamamlandı | 2024-08-20 |
| 3.2. Müşteri Tarafı Ürün Görüntüleme | ✅ Tamamlandı | 2024-08-20 |
| 4.1. Abonelik Entegrasyonu | ✅ Tamamlandı | 2024-08-20 |
| 5.1. Birim ve Entegrasyon Testleri | 📝 Planlandı | - |
| 5.2. Görsel Optimizasyonu | ✅ Tamamlandı | 2024-08-20 |

## Notlar ve Kararlar

- Ürün görselleri Supabase Storage'da `product_images/{salon_id}/{product_id}` formatında saklanacak
- Görsel optimizasyonu için istemci tarafında işleme yapılacak (boyutlandırma ve WebP dönüşümü)
- Abonelik planlarına göre depolama limitleri uygulanacak (Solo: 0MB, Small Team: 100MB, Pro: 500MB)
- Başlangıç seviyesi abonelik planında (Solo) ürün yönetimi özelliği bulunmayacak
- Ürün yönetimi özelliği Small Team ve Pro Salon planlarında aktif olacak
- Ürün sayfası salon slug'ı üzerinden erişilebilecek: `/{salon-slug}/products`
- Ürün ekleme/düzenleme formunda görsel yükleme, önizleme ve kırpma özellikleri bulunacak
- Müşteri tarafı ürün görüntüleme sayfasında kategori filtreleme ve arama özellikleri bulunacak
- Ürün yönetimi özelliği sidebar'da Salon Yönetimi grubu altında gösterilecek
