-- barbers tablosu için SELECT politikası
CREATE POLICY "Anyone can view barbers" ON barbers
  FOR SELECT
  USING (true);

-- services tablosu için SELECT politikası
CREATE POLICY "Anyone can view services" ON services
  FOR SELECT
  USING (true);

-- appointments tablosu için politikaları düzenleme
DROP POLICY IF EXISTS "Salon owners can manage appointments at their salon" ON appointments;

-- Salon sahipleri kendi salonlarındaki tüm randevuları yönetebilir
CREATE POLICY "Salon owners can manage appointments at their salon" ON appointments
  FOR ALL
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Her<PERSON> randevu oluşturabilir
CREATE POLICY "Anyone can insert appointments" ON appointments
  FOR INSERT
  WITH CHECK (true);

-- <PERSON><PERSON> randevuları görüntüleyebilir (müsait saatleri kontrol etmek için)
CREATE POLICY "Anyone can view appointments" ON appointments
  FOR SELECT
  USING (true);

-- customers tablosu için politikaları düzenleme
DROP POLICY IF EXISTS "Anyone can do anything with customers" ON customers;
DROP POLICY IF EXISTS "Salon owners can manage their salon's customers" ON customers;

-- Herkes müşteri ekleyebilir
CREATE POLICY "Anyone can insert customers" ON customers
  FOR INSERT
  WITH CHECK (true);

-- Salon sahipleri kendi müşterilerini görebilir, güncelleyebilir ve silebilir
CREATE POLICY "Salon owners can view their salon's customers" ON customers
  FOR SELECT
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

CREATE POLICY "Salon owners can update their salon's customers" ON customers
  FOR UPDATE
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

CREATE POLICY "Salon owners can delete their salon's customers" ON customers
  FOR DELETE
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
