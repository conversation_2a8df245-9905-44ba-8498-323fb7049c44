# Toast Bildirimi Filtreleme - 2024-07-23

## Gene<PERSON> Bakış

B<PERSON> belge, berber/salon sahiplerinin kendi oluşturdukları randevuların toast bildirimlerini görmemelerini sağlamak için yapılan değişiklikleri belgelemektedir.

## Sorun

Daha önce yaptığımız değişikliklerle, kullanıcılar kendi oluşturdukları randevular için bildirim panelinde bildirim görmüyorlar. <PERSON><PERSON><PERSON>, toast bildirimleri hala gösteriliyor. Bu da kullanıcıların kendi yaptıkları işlemler için gereksiz bildirimler almalarına neden oluyor.

## Çözüm

NotificationsContext.tsx dosyasında, appointments tablosundaki değişiklikleri dinleyen kısmı güncelledik:

1. Yeni randevu oluşturulduğunda:
   - <PERSON><PERSON><PERSON>u oluşturan kullanıcı ile mevcut kullanıcı aynı ise toast bildirimi göstermiyoruz.

2. <PERSON><PERSON><PERSON> iptal edildiğinde:
   - <PERSON>ptal eden kullanıcıyı belirlemek için şu an bir mekanizmamız yok, bu nedenle bu kontrolü şimdilik devre dışı bıraktık.
   - İleride, iptal eden kullanıcıyı belirlemek için daha iyi bir çözüm bulunabilir.

## Teknik Detaylar

### Toast Bildirimlerini Filtreleme

```typescript
// Yeni randevu oluşturulduğunda
const newAppointment = payload.new

// Kendi oluşturduğumuz randevular için toast mesajı gösterme
if (newAppointment.created_by === user.id) {
  return;
}

// Toast mesajı göster
const toastMessage = (
  <div className="space-y-1">
    <p className="font-medium">{newAppointment.fullname || 'Bir müşteri'} tarafından yeni bir randevu oluşturuldu.</p>
    <div className="text-sm text-muted-foreground">
      <div>Randevu detayları için bildirimlerinizi kontrol edin.</div>
    </div>
  </div>
)

toast.success(toastMessage, {
  action: {
    label: "Görüntüle",
    onClick: () => window.location.href = `/dashboard/appointments/${newAppointment.id}`,
  },
  duration: 5000, // Show for 5 seconds
})
```

## Sonuç

Bu değişikliklerle birlikte, berber/salon sahipleri kendi oluşturdukları randevular için toast bildirimi almayacaklardır. Bu, kullanıcı deneyimini iyileştirecek ve gereksiz bildirimleri önleyecektir.

## Gelecek İyileştirmeler

İptal edilen randevular için, iptal eden kullanıcıyı belirlemek için daha iyi bir çözüm bulunabilir. Şu an için, iptal edilen randevular için toast bildirimleri herkese gösteriliyor.
