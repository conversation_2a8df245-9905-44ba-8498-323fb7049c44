import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import { decryptChannelId } from '@/lib/utils/encryption';
import { authenticateUser, checkSalonAccess, validateAppointmentAccess, getClientIP, sanitizeInput } from '@/lib/middleware/auth';
import rateLimitService from '@/lib/services/rate-limit';
import notificationDeduplicationService from '@/lib/services/notification-deduplication';
import { AppointmentNotificationData, NotificationType } from '@/lib/utils/notification-hash';

// Telegram Bot API client
class TelegramClient {
  private botToken: string;
  private baseUrl: string;

  constructor(botToken: string) {
    if (!botToken) {
      throw new Error('Telegram bot token is required');
    }
    this.botToken = botToken;
    this.baseUrl = `https://api.telegram.org/bot${botToken}`;
  }

  async sendMessage(chatId: string, text: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          text: text,
          parse_mode: 'HTML',
          disable_web_page_preview: true,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Telegram API error:', data);
        return {
          success: false,
          error: data.description || 'Telegram API error',
        };
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to send Telegram message:', error);
      return {
        success: false,
        error: 'Network error or invalid response',
      };
    }
  }
}

// Message formatter
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
}

function formatTime(timeString: string): string {
  try {
    let time: Date;
    if (timeString.includes('T') || timeString.includes(' ')) {
      time = new Date(timeString);
    } else {
      time = new Date(`1970-01-01T${timeString}`);
    }

    return time.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  } catch (error) {
    return timeString;
  }
}

function formatNewAppointmentMessage(appointment: any): string {
  const formattedDate = formatDate(appointment.date);
  const startTime = formatTime(appointment.start_time);
  const endTime = formatTime(appointment.end_time);

  let message = `📅 <b>Yeni Randevu</b>\n\n`;
  if (appointment.salon_name) {
    message += `🏪 <b>Salon:</b> ${appointment.salon_name}\n`;
  }
  message += `👤 <b>Müşteri:</b> ${appointment.customer_name}\n`;

  if (appointment.customer_phone) {
    message += `📞 <b>Telefon:</b> ${appointment.customer_phone}\n`;
  }

  message += `💇‍♂️ <b>Berber:</b> ${appointment.barber_name}\n`;
  message += `✂️ <b>Hizmet:</b> ${appointment.service_name}\n`;
  message += `📅 <b>Tarih:</b> ${formattedDate}\n`;
  message += `🕐 <b>Saat:</b> ${startTime} - ${endTime}\n`;

  if (appointment.notes && appointment.notes.trim()) {
    message += `📝 <b>Notlar:</b> ${appointment.notes}\n`;
  }

  message += `\n🆔 Randevu ID: ${appointment.id}`;

  return message;
}

function formatCancelledAppointmentMessage(appointment: any): string {
  const formattedDate = formatDate(appointment.date);
  const startTime = formatTime(appointment.start_time);

  let message = `❌ <b>Randevu İptal Edildi</b>\n\n`;
  if (appointment.salon_name) {
    message += `🏪 <b>Salon:</b> ${appointment.salon_name}\n`;
  }
  message += `👤 <b>Müşteri:</b> ${appointment.customer_name}\n`;

  if (appointment.customer_phone) {
    message += `📞 <b>Telefon:</b> ${appointment.customer_phone}\n`;
  }

  message += `💇‍♂️ <b>Berber:</b> ${appointment.barber_name}\n`;
  message += `✂️ <b>Hizmet:</b> ${appointment.service_name}\n`;
  message += `📅 <b>Tarih:</b> ${formattedDate}\n`;
  message += `🕐 <b>Saat:</b> ${startTime}\n`;

  message += `\n🆔 Randevu ID: ${appointment.id}`;

  return message;
}

function formatTestMessage(salonName: string): string {
  const now = new Date();
  const formattedDate = now.toLocaleDateString('tr-TR', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const formattedTime = now.toLocaleTimeString('tr-TR', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });

  let message = `🧪 <b>Test Bildirimi</b>\n\n`;
  message += `🏪 <b>Salon:</b> ${salonName}\n`;
  message += `📅 <b>Tarih:</b> ${formattedDate}\n`;
  message += `🕐 <b>Saat:</b> ${formattedTime}\n\n`;
  message += `✅ Telegram bildirimleri başarıyla çalışıyor!\n`;
  message += `Bu bir test mesajıdır.`;

  return message;
}

// Validation schemas
const appointmentNotificationSchema = z.object({
  type: z.literal('appointment_notification'),
  appointment: z.object({
    id: z.string(),
    salon_id: z.string(),
    salon_name: z.string().optional(),
    customer_name: z.string(),
    customer_phone: z.string().nullable().optional(),
    barber_name: z.string(),
    service_name: z.string(),
    date: z.string(),
    start_time: z.string(),
    end_time: z.string(),
    status: z.string(),
    notes: z.string().nullable().optional(),
  }),
  notification_type: z.enum(['new_appointment', 'cancelled_appointment', 'updated_appointment']),
});

const testNotificationSchema = z.object({
  type: z.literal('test_notification'),
  salon_id: z.string(),
  salon_name: z.string(),
});

// POST /api/telegram/notify
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let salonId: string = '';
  let userId: string = 'unauthenticated';
  const clientIP = getClientIP(request);

  try {
    // 1. AUTHENTICATION - Check if user is authenticated (OPTIONAL)
    const authResult = await authenticateUser(request);
    const isAuthenticated = authResult.success && authResult.user;

    if (isAuthenticated) {
      userId = authResult.user!.id;
    }

    // Note: We continue with both authenticated and unauthenticated requests
    // Different security levels will be applied based on authentication status

    // 2. PARSE AND SANITIZE REQUEST BODY
    const rawBody = await request.json();
    const body = sanitizeInput(rawBody);

    // Validate request type
    if (!body.type) {
      return NextResponse.json(
        { success: false, error: 'Notification type is required' },
        { status: 400 }
      );
    }

    // 3. VALIDATE REQUEST DATA AND EXTRACT SALON ID
    let requestSalonId: string;
    let message: string;

    if (body.type === 'appointment_notification') {
      const validation = appointmentNotificationSchema.safeParse(body);
      if (!validation.success) {
        console.error('Validation error:', validation.error.errors);
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid appointment notification data',
            details: validation.error.errors
          },
          { status: 400 }
        );
      }

      const { appointment, notification_type } = validation.data;
      requestSalonId = appointment.salon_id;

      // Format message based on notification type
      switch (notification_type) {
        case 'new_appointment':
          message = formatNewAppointmentMessage(appointment);
          break;
        case 'cancelled_appointment':
          message = formatCancelledAppointmentMessage(appointment);
          break;
        default:
          message = formatNewAppointmentMessage(appointment);
      }
    } else if (body.type === 'test_notification') {
      const validation = testNotificationSchema.safeParse(body);
      if (!validation.success) {
        return NextResponse.json(
          { success: false, error: 'Invalid test notification data' },
          { status: 400 }
        );
      }

      const { salon_id, salon_name } = validation.data;
      requestSalonId = salon_id;
      message = formatTestMessage(salon_name);
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid notification type' },
        { status: 400 }
      );
    }

    // Set salonId for rate limiting and access control
    salonId = requestSalonId;

    // 4. SALON ACCESS CONTROL - Apply different levels based on authentication
    if (isAuthenticated) {
      // For authenticated users: Verify salon access
      const accessResult = await checkSalonAccess(authResult.user!, salonId);
      if (!accessResult.success) {
        return NextResponse.json(
          { success: false, error: accessResult.error || 'Failed to verify salon access' },
          { status: accessResult.statusCode || 500 }
        );
      }

      if (!accessResult.hasAccess) {
        // Log unauthorized access attempt
        rateLimitService.logAttempt({
          salonId,
          userId,
          notificationType: body.type,
          success: false,
          error: 'Unauthorized salon access',
          timestamp: new Date(),
          ipAddress: clientIP
        });

        return NextResponse.json(
          { success: false, error: 'Access denied to salon' },
          { status: 403 }
        );
      }
    } else {
      // For unauthenticated users: Basic salon existence check
      const supabase = createRouteHandlerClient({ cookies });
      const { data: salon, error: salonError } = await supabase
        .from('salons')
        .select('id, name')
        .eq('id', salonId)
        .single();

      if (salonError || !salon) {
        return NextResponse.json(
          { success: false, error: 'Invalid salon' },
          { status: 400 }
        );
      }
    }

    // 5. RATE LIMITING - Apply different limits based on authentication
    if (!isAuthenticated) {
      // For unauthenticated users: Apply stricter rate limiting
      const rateLimitCheck = rateLimitService.checkLimit(salonId);
      if (!rateLimitCheck.allowed) {
        // Log rate limit violation
        rateLimitService.logAttempt({
          salonId,
          userId,
          notificationType: body.type,
          success: false,
          error: 'Rate limit exceeded (unauthenticated)',
          timestamp: new Date(),
          ipAddress: clientIP
        });

        return NextResponse.json(
          {
            success: false,
            error: 'Rate limit exceeded. Please try again later.',
            retryAfter: Math.ceil((rateLimitCheck.resetTime - Date.now()) / 1000)
          },
          {
            status: 429,
            headers: {
              'Retry-After': Math.ceil((rateLimitCheck.resetTime - Date.now()) / 1000).toString(),
              'X-RateLimit-Limit': '10',
              'X-RateLimit-Remaining': rateLimitCheck.remaining.toString(),
              'X-RateLimit-Reset': rateLimitCheck.resetTime.toString()
            }
          }
        );
      }

      // Increment rate limit counter for unauthenticated users
      rateLimitService.incrementCount(salonId);
    } else {
      // For authenticated users: Light rate limiting (higher limits)
      // We still apply some rate limiting but with more generous limits
      const rateLimitCheck = rateLimitService.checkLimit(`auth_${salonId}`);
      if (!rateLimitCheck.allowed) {
        rateLimitService.logAttempt({
          salonId,
          userId,
          notificationType: body.type,
          success: false,
          error: 'Rate limit exceeded (authenticated)',
          timestamp: new Date(),
          ipAddress: clientIP
        });

        return NextResponse.json(
          {
            success: false,
            error: 'Rate limit exceeded. Please try again later.',
            retryAfter: Math.ceil((rateLimitCheck.resetTime - Date.now()) / 1000)
          },
          { status: 429 }
        );
      }

      // Increment rate limit counter for authenticated users
      rateLimitService.incrementCount(`auth_${salonId}`);
    }

    // 6. VALIDATE APPOINTMENT ACCESS (for appointment notifications)
    if (body.type === 'appointment_notification' && isAuthenticated) {
      // Only validate appointment access for authenticated users
      // Unauthenticated users (public booking) are creating new appointments, so validation isn't needed
      const appointmentValidation = await validateAppointmentAccess(salonId, body.appointment.id);
      if (!appointmentValidation.success) {
        return NextResponse.json(
          { success: false, error: appointmentValidation.error || 'Invalid appointment access' },
          { status: appointmentValidation.statusCode || 404 }
        );
      }
    }

    // 7. DEDUPLICATION CHECK - Prevent duplicate notifications
    let deduplicationResult;

    if (body.type === 'appointment_notification') {
      // Check for duplicate appointment notifications
      const appointmentData: AppointmentNotificationData = body.appointment;
      const notificationType: NotificationType = body.notification_type;

      deduplicationResult = await notificationDeduplicationService.checkAndLogNotification(
        appointmentData,
        notificationType
      );
    } else if (body.type === 'test_notification') {
      // Test notifications are always allowed but logged
      deduplicationResult = await notificationDeduplicationService.checkAndLogTestNotification(
        salonId,
        body.salon_name
      );
    }

    if (deduplicationResult?.isDuplicate) {
      // Log successful deduplication
      rateLimitService.logAttempt({
        salonId,
        userId,
        notificationType: body.type,
        success: true,
        error: 'Duplicate notification prevented',
        timestamp: new Date(),
        ipAddress: clientIP
      });

      console.log(`Duplicate notification prevented for salon ${salonId}, hash: ${deduplicationResult.hash} (${isAuthenticated ? 'authenticated' : 'unauthenticated'})`);
      return NextResponse.json({
        success: true,
        message: 'Duplicate notification prevented',
        isDuplicate: true,
        isAuthenticated,
        hash: deduplicationResult.hash
      });
    }

    if (deduplicationResult?.error) {
      console.error('Deduplication error:', deduplicationResult.error);
      // Continue with notification despite deduplication error to avoid blocking
    }

    // 8. GET TELEGRAM CONFIGURATION
    // Get Telegram bot token from environment
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    if (!botToken) {
      console.error('TELEGRAM_BOT_TOKEN environment variable not set');
      return NextResponse.json(
        { success: false, error: 'Telegram bot not configured' },
        { status: 500 }
      );
    }

    // Create Supabase client
    const supabase = createRouteHandlerClient({ cookies });

    // Get Telegram settings for the salon using SECURITY DEFINER function
    console.log('Looking for salon_id:', salonId);

    const { data: settings, error: settingsError } = await supabase
      .rpc('get_salon_telegram_settings', { p_salon_id: salonId });

    console.log('Settings:', settings);
    console.log('Settings error:', settingsError);

    if (settingsError || !settings || settings.length === 0) {
      console.log(`Telegram settings not found or disabled for salon ${salonId}`);
      return NextResponse.json({
        success: true,
        message: 'Telegram not configured or disabled for this salon'
      });
    }

    const telegramSettings = settings[0]; // RPC returns array

    if (!telegramSettings.telegram_channel_id) {
      return NextResponse.json({
        success: false,
        error: 'Telegram channel ID not configured'
      });
    }

    // Decrypt channel ID
    let channelId: string;
    try {
      channelId = decryptChannelId(telegramSettings.telegram_channel_id);
    } catch (error) {
      console.error('Failed to decrypt channel ID:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to decrypt channel ID'
      });
    }

    // 9. SEND TELEGRAM NOTIFICATION
    const telegramClient = new TelegramClient(botToken);
    const result = await telegramClient.sendMessage(channelId, message);

    // 10. LOG NOTIFICATION ATTEMPT
    const processingTime = Date.now() - startTime;

    rateLimitService.logAttempt({
      salonId,
      userId,
      notificationType: body.type,
      success: result.success,
      error: result.error,
      timestamp: new Date(),
      ipAddress: clientIP
    });

    if (result.success) {
      console.log(`Telegram notification sent for salon ${salonId} in ${processingTime}ms (${isAuthenticated ? 'authenticated' : 'unauthenticated'})`);
      return NextResponse.json({
        success: true,
        message: 'Notification sent successfully',
        processingTime,
        isAuthenticated,
        deduplication: {
          hash: deduplicationResult?.hash,
          logId: deduplicationResult?.logId,
          isDuplicate: false
        }
      });
    } else {
      console.error(`Failed to send Telegram notification: ${result.error}`);
      return NextResponse.json({
        success: false,
        error: result.error || 'Failed to send notification'
      }, { status: 500 });
    }

  } catch (error) {
    // Log error attempt
    if (salonId && userId) {
      rateLimitService.logAttempt({
        salonId,
        userId,
        notificationType: 'unknown',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
        ipAddress: clientIP
      });
    }

    console.error('Error in POST /api/telegram/notify:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
