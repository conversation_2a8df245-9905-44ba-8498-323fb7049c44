# Türkçe Çeviri <PERSON> - 15.05.2024

## <PERSON><PERSON>lanan Çeviriler

Aşağıdaki dosyalardaki tüm İngilizce metinler Türkçeye çevrilmiştir:

### Ana Sayfa ve Layout
- src/app/layout.tsx (metadata ve lang attribute)
- src/app/page.tsx (ana sayfa metinleri, özel<PERSON>ler, fiya<PERSON><PERSON><PERSON>, footer)

### Dashboard Sayfaları
- src/app/dashboard/page.tsx (dashboard kartları)
- src/app/dashboard/layout.tsx (sidebar menü öğeleri)
- src/app/dashboard/appointments/page.tsx (kalan İngilizce metinler)
- src/app/dashboard/services/page.tsx (form alanları, butonlar, açıklamalar)
- src/app/dashboard/staff/page.tsx (form alanları, butonlar, açıklamalar, davet metinleri)
- src/app/dashboard/customers/page.tsx (form alanları, butonlar, açıklamalar)

### Auth Sayfaları
- src/app/auth/register/page.tsx (kayıt sayfası metinleri)
- src/app/auth/login/page.tsx (giriş sayfası metinleri)
- src/app/auth/forgot-password/page.tsx (şifre sıfırlama metinleri)
- src/app/auth/layout.tsx (footer metni)

### Bileşenler
- src/components/header.tsx (header metinleri)
- src/components/theme-toggle.tsx (tema değiştirme metni)
- src/components/appointment-form.tsx (randevu formu metinleri)
- src/components/customer-form.tsx (müşteri formu metinleri)
- src/components/appointment-calendar.tsx (takvim metinleri)
- src/components/booking-form.tsx (booking formu metinleri)
- src/components/booking-modal.tsx (booking modal metinleri)

## Özet

Tüm kullanıcı arayüzü metinleri Türkçeye çevrilmiştir. Çeviriler aşağıdaki alanlarda yapılmıştır:

1. Form etiketleri ve placeholder metinleri
2. Buton metinleri
3. Başlıklar ve alt başlıklar
4. Hata ve başarı mesajları
5. Açıklama metinleri
6. Doğrulama (validation) mesajları
7. Durum bildirimleri
8. Arayüz öğeleri (menü, sidebar, vb.)

Artık uygulama tamamen Türkçe olarak kullanılabilir durumdadır ve Türk kullanıcılar için daha erişilebilir hale gelmiştir.
