-- Notifications table for persistent notifications
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('new_booking', 'cancellation', 'update')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  data JSONB,
  CONSTRAINT fk_salon FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE,
  CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create index for faster queries
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_salon_id ON notifications(salon_id);
CREATE INDEX idx_notifications_read ON notifications(read);

-- Enable Row Level Security
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Salon owners can see notifications for their salon
CREATE POLICY "Salon owners can see their salon notifications" ON notifications
  FOR SELECT
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Salon owners can manage their salon notifications
CREATE POLICY "Salon owners can manage their salon notifications" ON notifications
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Staff can see their own notifications
CREATE POLICY "Staff can see their own notifications" ON notifications
  FOR SELECT
  USING (user_id = auth.uid() OR salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Staff can manage their own notifications
CREATE POLICY "Staff can manage their own notifications" ON notifications
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());
