# SalonFlow CMS - Technical Implementation Documentation

## Architecture Overview

The SalonFlow Content Management System (CMS) is built using a modern React/Next.js architecture with Supabase as the backend. The system follows a component-based approach with proper separation of concerns and TypeScript for type safety.

### Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **UI Components**: Shadcn UI, Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **State Management**: React Context API
- **Authentication**: Supabase Auth
- **Security**: Row Level Security (RLS)

## Database Schema

### Tables

#### salon_content
```sql
CREATE TABLE public.salon_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES public.salons(id) ON DELETE CASCADE,
    section TEXT NOT NULL, -- 'hero', 'about', 'services', 'contact'
    content_key TEXT NOT NULL,
    content_value TEXT,
    content_type TEXT DEFAULT 'text' CHECK (content_type IN ('text', 'number', 'boolean', 'json')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(salon_id, section, content_key)
);
```

#### salon_testimonials
```sql
CREATE TABLE public.salon_testimonials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES public.salons(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    service_name TEXT,
    date_text TEXT,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Indexes
- `idx_salon_content_salon_section` on (salon_id, section)
- `idx_salon_testimonials_salon_active` on (salon_id, is_active)
- `idx_salon_testimonials_display_order` on (display_order)

### Row Level Security (RLS)

All tables implement RLS policies ensuring:
- Users can only access content for their own salon
- Admin users have full access via `is_admin()` function
- Public read access for customer-facing pages

## File Structure

### Database Services
```
src/lib/db/
├── salon-content.ts          # Authenticated CRUD operations
├── salon-testimonials.ts     # Testimonials management
├── public/
│   ├── salon-content.ts      # Public read-only access
│   └── index.ts              # Public exports
└── types.ts                  # TypeScript definitions
```

### React Contexts
```
src/contexts/
├── ContentContext.tsx        # CMS content state management
└── PreviewContext.tsx        # Preview mode state
```

### CMS Components
```
src/components/cms/
├── hero-content-editor.tsx
├── about-content-editor.tsx
├── services-content-editor.tsx
├── testimonials-manager.tsx
├── contact-content-editor.tsx
└── content-preview.tsx
```

### Customer Components
```
src/components/customer/
├── hero-section.tsx
├── about-section.tsx
├── services-section.tsx
├── testimonials-section.tsx
└── contact-section.tsx
```

## Core Components

### ContentContext

Manages CMS content state across the application:

```typescript
interface ContentContextType {
  content: SalonContent[]
  testimonials: SalonTestimonial[]
  loading: boolean
  error: string | null
  getContentValue: (section: string, key: string, defaultValue?: string) => string
  updateContentValue: (section: string, key: string, value: string) => void
  refreshContent: () => Promise<void>
}
```

**Key Features:**
- Centralized content state management
- Optimistic updates for better UX
- Error handling and loading states
- Automatic content refresh

### Database Service Layer

#### Authenticated Operations (salon-content.ts)
- `getAllSalonContent(salonId)`: Get all content for a salon
- `getSalonContentBySection(salonId, section)`: Get content by section
- `upsertSalonContent()`: Create or update content
- `bulkUpsertSalonContent()`: Bulk content operations
- `deleteSalonContent()`: Remove content

#### Public Operations (public/salon-content.ts)
- `getPublicSalonContent(salonId)`: Public read-only access
- `getPublicSalonTestimonials(salonId)`: Public testimonials
- `parseSalonContent()`: Parse raw data into structured format
- `getCompleteSalonLandingData()`: Complete landing page data

## Content Structure

### Content Sections

#### Hero Section
```typescript
interface HeroContent {
  badgeText: string
  tagline: string
  description: string
  ctaPrimary: string
  ctaSecondary: string
  stats: {
    customers: { value: string; label: string }
    experience: { value: string; label: string }
    rating: { value: string; label: string }
    support: { value: string; label: string }
  }
}
```

#### About Section
```typescript
interface AboutContent {
  badgeText: string
  title: string
  description: string
  features: Array<{ title: string; description: string }>
  stats: Array<{ number: string; label: string }>
}
```

### Content Keys Mapping

The system uses a flexible key-value structure:

```typescript
// Hero section keys
'hero_badge_text' → badgeText
'hero_tagline' → tagline
'hero_description' → description
'hero_cta_primary' → ctaPrimary
'hero_cta_secondary' → ctaSecondary
'hero_stats_customers' → stats.customers.value
// ... etc
```

## Security Implementation

### Row Level Security Policies

```sql
-- salon_content policies
CREATE POLICY "Users can view own salon content" ON salon_content
  FOR SELECT USING (
    salon_id IN (
      SELECT id FROM salons WHERE owner_id = auth.uid()
    ) OR is_admin()
  );

CREATE POLICY "Users can modify own salon content" ON salon_content
  FOR ALL USING (
    salon_id IN (
      SELECT id FROM salons WHERE owner_id = auth.uid()
    ) OR is_admin()
  );
```

### Content Validation

- Input sanitization for XSS prevention
- Content length limits
- Type validation for different content types
- Required field validation

## Performance Optimizations

### Caching Strategy
- ContentContext caches content in memory
- Optimistic updates for immediate UI feedback
- Debounced auto-save functionality

### Database Optimizations
- Proper indexing for fast queries
- Bulk operations for multiple content updates
- Efficient RLS policies

### React Optimizations
- React.memo for component memoization
- useMemo for expensive calculations
- useCallback for stable function references

## API Patterns

### Frontend-First Approach
The CMS uses Supabase client directly from frontend components rather than API routes:

```typescript
// Direct Supabase usage
const { data, error } = await supabase
  .from('salon_content')
  .upsert(contentData)
  .select()
```

### Error Handling
```typescript
try {
  const result = await updateContent(data)
  // Optimistic update
} catch (error) {
  // Revert optimistic update
  // Show error message
}
```

## Testing Strategy

### Unit Tests
- Database service functions
- Content parsing logic
- Validation functions

### Integration Tests
- ContentContext operations
- Component interactions
- Database operations

### E2E Tests
- Complete CMS workflows
- Content preview functionality
- Cross-device compatibility

## Deployment Considerations

### Database Migrations
- Schema changes via SQL scripts
- RLS policy updates
- Index creation/modification

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
```

### Monitoring
- Content update tracking
- Error logging
- Performance metrics

## Future Enhancements

### Planned Features
- Content versioning and rollback
- Rich text editor integration
- Image upload and management
- Content templates
- A/B testing capabilities

### Technical Improvements
- GraphQL integration
- Real-time collaboration
- Advanced caching strategies
- Content CDN integration

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**: Ensure user has proper salon ownership
2. **Content Not Updating**: Check ContentContext refresh
3. **Preview Not Working**: Verify PreviewContext setup
4. **Performance Issues**: Review database queries and indexes

### Debug Tools
- Supabase dashboard for database inspection
- React DevTools for context debugging
- Network tab for API call monitoring

---

**Version**: 1.0  
**Last Updated**: 2025-05-23  
**Maintainer**: SalonFlow Development Team
