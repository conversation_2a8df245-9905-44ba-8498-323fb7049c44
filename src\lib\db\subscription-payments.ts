import { supabaseClient } from '../supabase-singleton';
import { SubscriptionPayment, SubscriptionPaymentInsert, SubscriptionPaymentUpdate } from './types';
import * as referrals from './referrals';

const supabase = supabaseClient;

/**
 * Get all payments for a subscription
 */
export async function getSubscriptionPayments(subscriptionId: string) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .select('*')
    .eq('subscription_id', subscriptionId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as SubscriptionPayment[];
}

/**
 * Get all payments for a salon
 */
export async function getSalonPayments(salonId: string) {
  const { data: subscriptions, error: subError } = await supabase
    .from('salon_subscriptions')
    .select('id')
    .eq('salon_id', salonId);

  if (subError) throw subError;

  if (!subscriptions || subscriptions.length === 0) {
    return [];
  }

  const subscriptionIds = subscriptions.map(sub => sub.id);

  const { data, error } = await supabase
    .from('subscription_payments')
    .select('*')
    .in('subscription_id', subscriptionIds)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data as SubscriptionPayment[];
}

/**
 * Get a payment by ID
 */
export async function getSubscriptionPaymentById(id: string) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as SubscriptionPayment;
}

/**
 * Get the next payment period for a subscription
 */
export async function getNextPaymentPeriod(subscriptionId: string) {
  const { data, error } = await supabase.rpc('calculate_next_payment_period', {
    p_subscription_id: subscriptionId
  });

  if (error) throw error;

  return {
    nextPeriodStartDate: data[0]?.next_period_start_date || null,
    nextPeriodEndDate: data[0]?.next_period_end_date || null
  };
}

/**
 * Check if a payment period overlaps with existing payments
 */
export async function checkPaymentPeriodOverlap(
  subscriptionId: string,
  periodStartDate: string,
  periodEndDate: string
) {
  const { data, error } = await supabase.rpc('check_payment_period_overlap', {
    p_subscription_id: subscriptionId,
    p_period_start_date: periodStartDate,
    p_period_end_date: periodEndDate
  });

  if (error) throw error;

  return data as boolean;
}

/**
 * Calculate payment amount based on subscription plan and period
 */
export async function calculatePaymentAmount(
  subscriptionId: string,
  periodStartDate: string,
  periodEndDate: string
) {
  // Önce dönem çakışması kontrolü yap
  const hasOverlap = await checkPaymentPeriodOverlap(subscriptionId, periodStartDate, periodEndDate);
  if (hasOverlap) {
    throw new Error('Bu dönem için zaten bir ödeme kaydı bulunmaktadır. Lütfen farklı bir dönem seçin.');
  }

  const { data, error } = await supabase.rpc('calculate_payment_amount', {
    subscription_id: subscriptionId,
    start_date: periodStartDate,
    end_date: periodEndDate
  });

  if (error) throw error;

  return {
    originalAmount: data[0]?.original_amount || 0,
    planName: data[0]?.plan_name || '',
    planPrice: data[0]?.plan_price || 0,
    dayCount: data[0]?.day_count || 0
  };
}

/**
 * Check available discounts for a salon
 */
export async function checkAvailableDiscounts(salonId: string) {
  // Referans indirimi kontrolü
  const referralDiscount = await referrals.checkReferralDiscount(salonId);

  if (referralDiscount) {
    return {
      hasDiscount: true,
      discountType: 'referral',
      discountAmount: referralDiscount.discount_amount,
      discountId: referralDiscount.id
    };
  }

  return {
    hasDiscount: false
  };
}

/**
 * Create a new payment record
 */
export async function createSubscriptionPayment(payment: SubscriptionPaymentInsert) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .insert(payment)
    .select()
    .single();

  if (error) throw error;

  // Eğer referans indirimi uygulandıysa, referans faydasını güncelle
  if (payment.discount_type === 'referral' && payment.discount_reference_id) {
    await referrals.applyReferralDiscount(payment.discount_reference_id, data.id);
  }

  return data as SubscriptionPayment;
}

/**
 * Update a payment record
 */
export async function updateSubscriptionPayment({ id, ...payment }: SubscriptionPaymentUpdate) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .update(payment)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SubscriptionPayment;
}

/**
 * Delete a payment record (admin only)
 */
export async function deleteSubscriptionPayment(id: string) {
  const { error } = await supabase
    .from('subscription_payments')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Mark a payment as completed
 */
export async function completePayment(id: string, invoiceNumber?: string) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .update({
      status: 'completed',
      invoice_number: invoiceNumber
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SubscriptionPayment;
}

/**
 * Mark a payment as failed
 */
export async function failPayment(id: string, notes?: string) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .update({
      status: 'failed',
      notes: notes
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SubscriptionPayment;
}

/**
 * Generate a new invoice number
 */
export function generateInvoiceNumber(): string {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');

  return `INV-${year}${month}-${random}`;
}
