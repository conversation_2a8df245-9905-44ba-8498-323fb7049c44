-- Barbers tablosuna yeni sütunlar ekleme
ALTER TABLE barbers 
ADD COLUMN user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
ADD COLUMN invitation_token TEXT,
ADD COLUMN invitation_sent_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN invitation_accepted_at TIMESTAMP WITH TIME ZONE;

-- Customers tablosuna salon_id sütunu ekleme
ALTER TABLE customers 
ADD COLUMN salon_id UUID REFERENCES salons(id) ON DELETE CASCADE;

-- Mevcut müşterileri bir salon ile ilişkilendirme (varsayılan olarak ilk salon)
UPDATE customers
SET salon_id = (SELECT id FROM salons ORDER BY created_at LIMIT 1);

-- salon_id sütununu NOT NULL yapma
ALTER TABLE customers 
ALTER COLUMN salon_id SET NOT NULL;

-- Row Level Security Politikalarını Güncelleme

-- Salons: Staff üyelerin kendi salonlarını görüntülemesine izin verme
CREATE POLICY "Staff can view their salon" ON salons
  FOR SELECT
  USING (id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Working hours: Staff üyelerin salon çalışma saatlerini görüntülemesine izin verme
CREATE POLICY "Staff can view salon working hours" ON working_hours
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Holidays: Staff üyelerin salon tatil günlerini görüntülemesine izin verme
CREATE POLICY "Staff can view salon holidays" ON holidays
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Barbers: Staff üyelerin salon berberlerini görüntülemesine ve kendi profillerini güncellemesine izin verme
CREATE POLICY "Staff can view salon barbers" ON barbers
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

CREATE POLICY "Staff can update their own profile" ON barbers
  FOR UPDATE
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid() AND salon_id = (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Barber working hours: Staff üyelerin tüm berber çalışma saatlerini görüntülemesine ve kendi çalışma saatlerini yönetmesine izin verme
CREATE POLICY "Staff can view all barber working hours" ON barber_working_hours
  FOR SELECT
  USING (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid())));

CREATE POLICY "Staff can manage their own working hours" ON barber_working_hours
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

-- Services: Staff üyelerin salon hizmetlerini görüntülemesine izin verme
CREATE POLICY "Staff can view salon services" ON services
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Barber services: Staff üyelerin berber hizmetlerini görüntülemesine izin verme
CREATE POLICY "Staff can view barber services" ON barber_services
  FOR SELECT
  USING (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid())));

-- Customers: Staff üyelerin salon müşterilerini görüntülemesine izin verme
CREATE POLICY "Staff can view salon customers" ON customers
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Appointments: Staff üyelerin tüm salon randevularını görüntülemesine ve kendi randevularını yönetmesine izin verme
CREATE POLICY "Staff can view all salon appointments" ON appointments
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

CREATE POLICY "Staff can manage their own appointments" ON appointments
  FOR INSERT
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

CREATE POLICY "Staff can update their own appointments" ON appointments
  FOR UPDATE
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

CREATE POLICY "Staff can delete their own appointments" ON appointments
  FOR DELETE
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));
