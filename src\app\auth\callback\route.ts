import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  // URL'den kodu al
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  console.log("Auth callback: İstek alındı, kod:", code ? "Var" : "Yok")

  if (code) {
    // Supabase client oluştur
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    try {
      // Kodu oturum için değiştir
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error("Auth callback hatası:", error)
        return NextResponse.redirect(new URL('/auth/login', request.url))
      }

      console.log("Auth callback: Kod başarıyla oturum iç<PERSON>, session:", data.session ? "Var" : "Yok")

      // Oturum kontrolü
      if (!data.session) {
        console.log("Auth callback: Oturum oluşturulamadı")
        return NextResponse.redirect(new URL('/auth/login', request.url))
      }

      // Dashboard'a yönlendir
      console.log("Auth callback: Dashboard'a yönlendiriliyor")

      // Referans kodunu kontrol etmek için query parametresi ekle
      // Bu, client-side'da ReferralCodeChecker komponentinin çalışmasını sağlar
      return NextResponse.redirect(new URL('/dashboard?check_referral=true', request.url))
    } catch (error) {
      console.error("Auth callback beklenmeyen hata:", error)
      // Hata durumunda login sayfasına yönlendir
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }

  // Kod yoksa login sayfasına yönlendir
  console.log("Auth callback: Kod yok, login sayfasına yönlendiriliyor")
  return NextResponse.redirect(new URL('/auth/login', request.url))
}
