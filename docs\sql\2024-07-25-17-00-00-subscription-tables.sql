-- SalonFlow Abonelik Sistemi Tabloları
-- Oluşturulma Tarihi: 2024-07-25

-- 1. subscription_plans tablosu
CREATE TABLE IF NOT EXISTS subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  price_monthly DECIMAL(10, 2) NOT NULL,
  price_yearly DECIMAL(10, 2) NOT NULL,
  max_staff INTEGER NOT NULL,
  features JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>ars<PERSON><PERSON>lan planları ekle
INSERT INTO subscription_plans (name, price_monthly, price_yearly, max_staff, features) VALUES
('Solo', 750.00, 8100.00, 1, '{"analytics": false, "finance": false, "custom_domain": false}'),
('Small Team', 1500.00, 16200.00, 5, '{"analytics": true, "finance": true, "custom_domain": false}'),
('Pro Salon', 5000.00, 54000.00, 20, '{"analytics": true, "finance": true, "custom_domain": true}');

-- 2. salon_subscriptions tablosunu güncelleme
-- Önce mevcut tabloyu yedekleyelim
CREATE TABLE salon_subscriptions_backup AS SELECT * FROM salon_subscriptions;

-- Mevcut tabloyu değiştirme
ALTER TABLE salon_subscriptions 
  ADD COLUMN IF NOT EXISTS plan_id UUID REFERENCES subscription_plans(id),
  ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('trial', 'active', 'past_due', 'suspended', 'cancelled')),
  ADD COLUMN IF NOT EXISTS trial_end_date DATE,
  ADD COLUMN IF NOT EXISTS payment_method TEXT,
  ADD COLUMN IF NOT EXISTS is_yearly BOOLEAN DEFAULT FALSE;

-- Mevcut verileri güncelleme
-- 'basic' planını 'Solo' ile eşleştirme
UPDATE salon_subscriptions 
SET plan_id = (SELECT id FROM subscription_plans WHERE name = 'Solo')
WHERE plan = 'basic';

-- 'standard' planını 'Small Team' ile eşleştirme
UPDATE salon_subscriptions 
SET plan_id = (SELECT id FROM subscription_plans WHERE name = 'Small Team')
WHERE plan = 'standard';

-- 'premium' planını 'Pro Salon' ile eşleştirme
UPDATE salon_subscriptions 
SET plan_id = (SELECT id FROM subscription_plans WHERE name = 'Pro Salon')
WHERE plan = 'premium';

-- 3. subscription_payments tablosu
CREATE TABLE IF NOT EXISTS subscription_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  subscription_id UUID NOT NULL REFERENCES salon_subscriptions(id) ON DELETE CASCADE,
  amount DECIMAL(10, 2) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method TEXT,
  status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  invoice_number TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. referral_codes tablosu
CREATE TABLE IF NOT EXISTS referral_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  code TEXT NOT NULL UNIQUE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. referral_benefits tablosu
CREATE TABLE IF NOT EXISTS referral_benefits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  referrer_salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  referred_salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  referral_code_id UUID NOT NULL REFERENCES referral_codes(id) ON DELETE CASCADE,
  benefit_type TEXT NOT NULL CHECK (benefit_type IN ('discount', 'free_month', 'feature_unlock')),
  benefit_value TEXT,
  is_applied BOOLEAN DEFAULT FALSE,
  applied_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Güncelleme tetikleyicileri
-- salon_subscriptions tablosu için updated_at güncellemesi
CREATE OR REPLACE FUNCTION update_salon_subscriptions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_salon_subscriptions_updated_at
BEFORE UPDATE ON salon_subscriptions
FOR EACH ROW
EXECUTE FUNCTION update_salon_subscriptions_updated_at();

-- subscription_payments tablosu için updated_at güncellemesi
CREATE OR REPLACE FUNCTION update_subscription_payments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_subscription_payments_updated_at
BEFORE UPDATE ON subscription_payments
FOR EACH ROW
EXECUTE FUNCTION update_subscription_payments_updated_at();

-- referral_codes tablosu için updated_at güncellemesi
CREATE OR REPLACE FUNCTION update_referral_codes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_referral_codes_updated_at
BEFORE UPDATE ON referral_codes
FOR EACH ROW
EXECUTE FUNCTION update_referral_codes_updated_at();

-- referral_benefits tablosu için updated_at güncellemesi
CREATE OR REPLACE FUNCTION update_referral_benefits_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_referral_benefits_updated_at
BEFORE UPDATE ON referral_benefits
FOR EACH ROW
EXECUTE FUNCTION update_referral_benefits_updated_at();
