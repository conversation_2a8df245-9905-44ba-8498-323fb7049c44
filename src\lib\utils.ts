import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { parse, isWithinInterval, areIntervalsOverlapping } from "date-fns"
import { Appointment } from "@/lib/db/types"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Check if an appointment overlaps with existing appointments for the same barber
 * @param appointment The appointment being moved
 * @param targetDate The target date for the appointment
 * @param targetStartTime The target start time for the appointment
 * @param targetEndTime The target end time for the appointment
 * @param existingAppointments List of existing appointments
 * @returns True if there is an overlap, false otherwise
 */
export function checkAppointmentOverlap(
  appointment: Appointment,
  targetDate: string,
  targetStartTime: string,
  targetEndTime: string,
  existingAppointments: Appointment[]
): boolean {
  // Filter appointments for the same barber on the target date
  const barberAppointments = existingAppointments.filter(apt =>
    apt.barber_id === appointment.barber_id &&
    apt.date === targetDate &&
    apt.id !== appointment.id // Exclude the appointment being moved
  );

  if (barberAppointments.length === 0) {
    return false; // No other appointments for this barber on this date
  }

  // Create a base date for comparison (the actual date doesn't matter, just the time)
  const baseDate = new Date();
  const baseStr = baseDate.toISOString().split('T')[0]; // YYYY-MM-DD

  // Parse the target time interval
  const targetStart = parse(`${baseStr}T${targetStartTime}`, "yyyy-MM-dd'T'HH:mm", new Date());
  const targetEnd = parse(`${baseStr}T${targetEndTime}`, "yyyy-MM-dd'T'HH:mm", new Date());

  const targetInterval = { start: targetStart, end: targetEnd };

  // Check for overlaps with existing appointments
  return barberAppointments.some(apt => {
    const existingStart = parse(`${baseStr}T${apt.start_time}`, "yyyy-MM-dd'T'HH:mm", new Date());
    const existingEnd = parse(`${baseStr}T${apt.end_time}`, "yyyy-MM-dd'T'HH:mm", new Date());

    const existingInterval = { start: existingStart, end: existingEnd };

    return areIntervalsOverlapping(targetInterval, existingInterval);
  });
}

/**
 * Generate a random token for staff invitations
 * @param length Length of the token
 * @returns Random token string
 */
export function generateToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let token = '';

  for (let i = 0; i < length; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return token;
}

/**
 * Format a date string to a human-readable format
 * @param dateString Date string to format
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format a time string to a human-readable format
 * @param timeString Time string to format (HH:MM:SS)
 * @returns Formatted time string (HH:MM AM/PM)
 */
export function formatTime(timeString: string): string {
  // Parse the time string (assuming format is HH:MM:SS)
  const [hours, minutes] = timeString.split(':').map(Number);

  // Convert to 12-hour format
  const period = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM

  // Format the time
  return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
}

/**
 * Debounce function to limit how often a function can be called
 * @param func The function to debounce
 * @param wait Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout !== null) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(later, wait);
  };
}

/**
 * Format a number as currency (TL)
 * @param amount Amount to format
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}
