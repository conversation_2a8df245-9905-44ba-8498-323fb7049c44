// Notification Hash Generation Utility for SalonFlow
// Generates unique hashes for Telegram notifications to prevent duplicates

import crypto from 'crypto';

export interface AppointmentNotificationData {
  id: string;
  salon_id: string;
  salon_name?: string;
  customer_name: string;
  customer_phone?: string | null;
  barber_name: string;
  service_name: string;
  date: string;
  start_time: string;
  end_time: string;
  status: string;
  notes?: string | null;
}

export type NotificationType = 'new_appointment' | 'cancelled_appointment' | 'updated_appointment';

/**
 * Generate a unique hash for notification deduplication
 * 
 * The hash is based on key appointment data that should trigger new notifications
 * when changed. This ensures that:
 * - Same appointment with same data = same hash (duplicate prevention)
 * - Same appointment with different data = different hash (allows updates)
 * - Different appointments = different hashes (no false positives)
 */
export function generateNotificationHash(
  appointment: AppointmentNotificationData,
  notificationType: NotificationType
): string {
  // Create a deterministic key from critical appointment data
  // Include fields that, when changed, should trigger a new notification
  const keyComponents = [
    appointment.id,
    appointment.salon_id,
    appointment.date,
    appointment.start_time,
    appointment.end_time,
    appointment.status,
    appointment.customer_name,
    appointment.barber_name,
    appointment.service_name,
    notificationType,
    // Include notes only if they exist and are not empty
    appointment.notes && appointment.notes.trim() ? appointment.notes.trim() : '',
    // Include phone only if it exists
    appointment.customer_phone || ''
  ];

  // Join components with a delimiter that won't appear in the data
  const key = keyComponents.join('|#|');

  // Generate SHA256 hash
  return crypto.createHash('sha256').update(key, 'utf8').digest('hex');
}

/**
 * Generate hash for test notifications
 */
export function generateTestNotificationHash(
  salonId: string,
  salonName: string,
  timestamp: Date = new Date()
): string {
  // For test notifications, include timestamp to ensure uniqueness
  // This allows multiple test notifications to be sent
  const key = [
    'test_notification',
    salonId,
    salonName,
    timestamp.toISOString()
  ].join('|#|');

  return crypto.createHash('sha256').update(key, 'utf8').digest('hex');
}

/**
 * Validate notification hash format
 */
export function isValidNotificationHash(hash: string): boolean {
  // SHA256 hash should be 64 characters long and contain only hex characters
  return /^[a-f0-9]{64}$/i.test(hash);
}

/**
 * Compare two notification hashes
 */
export function compareNotificationHashes(hash1: string, hash2: string): boolean {
  if (!isValidNotificationHash(hash1) || !isValidNotificationHash(hash2)) {
    return false;
  }
  return hash1.toLowerCase() === hash2.toLowerCase();
}

/**
 * Generate hash for appointment update notifications
 * 
 * This function compares old and new appointment data to determine
 * if a notification should be sent for an update
 */
export function shouldSendUpdateNotification(
  oldAppointment: Partial<AppointmentNotificationData>,
  newAppointment: AppointmentNotificationData
): {
  shouldSend: boolean;
  changedFields: string[];
  hash: string;
} {
  // Fields that trigger update notifications when changed
  const criticalFields: (keyof AppointmentNotificationData)[] = [
    'date',
    'start_time',
    'end_time',
    'barber_name',
    'service_name',
    'status'
  ];

  const changedFields: string[] = [];

  // Check which critical fields have changed
  for (const field of criticalFields) {
    const oldValue = oldAppointment[field];
    const newValue = newAppointment[field];
    
    if (oldValue !== newValue) {
      changedFields.push(field);
    }
  }

  const shouldSend = changedFields.length > 0;
  const hash = shouldSend 
    ? generateNotificationHash(newAppointment, 'updated_appointment')
    : '';

  return {
    shouldSend,
    changedFields,
    hash
  };
}

/**
 * Debug function to show hash components (for development/testing)
 */
export function debugNotificationHash(
  appointment: AppointmentNotificationData,
  notificationType: NotificationType
): {
  hash: string;
  components: string[];
  key: string;
} {
  const components = [
    appointment.id,
    appointment.salon_id,
    appointment.date,
    appointment.start_time,
    appointment.end_time,
    appointment.status,
    appointment.customer_name,
    appointment.barber_name,
    appointment.service_name,
    notificationType,
    appointment.notes && appointment.notes.trim() ? appointment.notes.trim() : '',
    appointment.customer_phone || ''
  ];

  const key = components.join('|#|');
  const hash = crypto.createHash('sha256').update(key, 'utf8').digest('hex');

  return {
    hash,
    components,
    key
  };
}

/**
 * Extract notification type from hash context (for logging purposes)
 */
export function getNotificationContext(
  appointment: AppointmentNotificationData,
  notificationType: NotificationType
): {
  appointmentId: string;
  salonId: string;
  notificationType: NotificationType;
  customerName: string;
  barberName: string;
  serviceName: string;
  appointmentDate: string;
  appointmentTime: string;
} {
  return {
    appointmentId: appointment.id,
    salonId: appointment.salon_id,
    notificationType,
    customerName: appointment.customer_name,
    barberName: appointment.barber_name,
    serviceName: appointment.service_name,
    appointmentDate: appointment.date,
    appointmentTime: `${appointment.start_time} - ${appointment.end_time}`
  };
}
