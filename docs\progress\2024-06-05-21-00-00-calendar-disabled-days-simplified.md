# Takvimde Çalış<PERSON>lmayan Günleri Devre Dışı Bırakma (Basitleştirilmiş) - 2024-06-05

## Yapılan İşlemler

1. DatePicker bileşeni güncellendi:
   - `disabledDates` prop'u Date[] tipinde eklendi
   - Calendar bileşenine `disabled` prop'u geçirildi

2. Booking-form.tsx dosyası güncellendi:
   - Devre dışı bırakılacak tarihleri saklamak için `disabledDates` state'i eklendi
   - Berber çalışma saatlerini yükleyen ve çalışılmayan günleri içeren bir tarih dizisi oluşturan `loadBarberWorkingHours` fonksiyonu güncellendi
   - DatePicker bileşenine `disabledDates` state'i geçirildi

## Teknik Detaylar

1. DatePicker bileşeni (`src/components/ui/date-picker.tsx`):
   ```typescript
   interface DatePickerProps {
     date: Date | undefined
     setDate: (date: Date | undefined) => void
     className?: string
     disabled?: boolean
     disabledDates?: Date[]
   }
   ```

2. Booking-form.tsx dosyasında:
   ```typescript
   // Devre dışı bırakılacak tarihleri saklamak için state
   const [disabledDates, setDisabledDates] = useState<Date[]>([])

   // Berber çalışma saatlerini yükleyen ve çalışılmayan günleri içeren bir tarih dizisi oluşturan fonksiyon
   const loadBarberWorkingHours = async (barberId: string) => {
     try {
       // Get barber working hours
       const { data, error } = await supabase
         .from('barber_working_hours')
         .select('day_of_week, is_closed')
         .eq('barber_id', barberId)
         .order('day_of_week')

       if (error) throw error

       // Find days when barber doesn't work
       const closedDays = (data || [])
         .filter(day => day.is_closed)
         .map(day => day.day_of_week)

       // Generate disabled dates for the next 3 months
       const disabledDatesArray: Date[] = []
       const today = new Date()
       const threeMonthsLater = new Date()
       threeMonthsLater.setMonth(today.getMonth() + 3)

       // Loop through each day in the next 3 months
       const currentDate = new Date(today)
       while (currentDate <= threeMonthsLater) {
         const dayOfWeek = currentDate.getDay()
         
         // If the barber doesn't work on this day of the week, add it to disabled dates
         if (closedDays.includes(dayOfWeek)) {
           disabledDatesArray.push(new Date(currentDate.getTime()))
         }
         
         // Move to the next day
         currentDate.setDate(currentDate.getDate() + 1)
       }

       setDisabledDates(disabledDatesArray)
     } catch (error) {
       console.error("Error loading barber working hours:", error)
       toast.error("Berber çalışma saatleri yüklenirken bir hata oluştu.")
     }
   }
   ```

## Sonuç

Bu değişikliklerle birlikte, müşteriler artık berberin çalışmadığı günleri takvimde seçemeyecekler. Bu, kullanıcı deneyimini iyileştirecek ve kullanıcıların müsait olmayan günleri seçmesini önleyecek.
