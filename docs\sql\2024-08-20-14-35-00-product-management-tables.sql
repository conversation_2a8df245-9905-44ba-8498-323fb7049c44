-- SalonFlow Ürün Yönetimi Tabloları
-- Oluşturulma Tarihi: 2024-08-20

-- 1. products tablosu
CREATE TABLE IF NOT EXISTS products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10, 2),
  category TEXT,
  image_url TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- R<PERSON>iti<PERSON>arı
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi salonlarının ürünlerini görebilir ve yönetebilir
CREATE POLICY "Salon owners can manage their salon's products" ON products
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- <PERSON><PERSON> kendi salonlarının ürünlerini görebilir
CREATE POLICY "Staff can view their salon's products" ON products
  FOR SELECT
  USING (salon_id IN (
    SELECT salon_id FROM barbers 
    WHERE user_id = auth.uid()
  ));

-- Herkes aktif ürünleri görebilir (müşteri tarafı için)
CREATE POLICY "Anyone can view active products" ON products
  FOR SELECT
  USING (is_active = TRUE);

-- Admin tüm ürünleri görebilir ve yönetebilir
CREATE POLICY "Admins can manage all products" ON products
  USING (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()))
  WITH CHECK (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()));

-- 2. Ürün görselleri için storage bucket oluşturma
INSERT INTO storage.buckets (id, name, public) VALUES ('product_images', 'product_images', true);

-- RLS Politikaları
CREATE POLICY "Public read access for product images" ON storage.objects
  FOR SELECT
  USING (bucket_id = 'product_images');

CREATE POLICY "Salon owners can upload product images" ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'product_images' AND
    (auth.uid() IN (SELECT owner_id FROM salons WHERE id::text = (storage.foldername(name))[1]))
  );

CREATE POLICY "Salon owners can update their product images" ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'product_images' AND
    (auth.uid() IN (SELECT owner_id FROM salons WHERE id::text = (storage.foldername(name))[1]))
  );

CREATE POLICY "Salon owners can delete their product images" ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'product_images' AND
    (auth.uid() IN (SELECT owner_id FROM salons WHERE id::text = (storage.foldername(name))[1]))
  );

CREATE POLICY "Admins can manage all product images" ON storage.objects
  USING (
    bucket_id = 'product_images' AND
    EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin())
  )
  WITH CHECK (
    bucket_id = 'product_images' AND
    EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin())
  );

-- 3. subscription_plans tablosunu güncelleme - ürün yönetimi özelliği ekleme
UPDATE subscription_plans
SET features = features || 
  jsonb_build_object(
    'product_management', CASE 
      WHEN name = 'Solo' THEN false
      WHEN name = 'Small Team' THEN true
      WHEN name = 'Pro Salon' THEN true
    END,
    'storage_limit_mb', CASE 
      WHEN name = 'Solo' THEN 0
      WHEN name = 'Small Team' THEN 100
      WHEN name = 'Pro Salon' THEN 500
    END
  );

-- 4. Ürün kategorileri için referans tablo (opsiyonel)
CREATE TABLE IF NOT EXISTS product_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Politikaları
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi salonlarının kategorilerini görebilir ve yönetebilir
CREATE POLICY "Salon owners can manage their salon's product categories" ON product_categories
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Personel kendi salonlarının kategorilerini görebilir
CREATE POLICY "Staff can view their salon's product categories" ON product_categories
  FOR SELECT
  USING (salon_id IN (
    SELECT salon_id FROM barbers 
    WHERE user_id = auth.uid()
  ));

-- Admin tüm kategorileri görebilir ve yönetebilir
CREATE POLICY "Admins can manage all product categories" ON product_categories
  USING (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()))
  WITH CHECK (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()));
