-- Referans sistemi deneme süreci kısıtlaması için SQL script
-- Tarih: 2024-08-05

-- Referans kodu oluşturma işleminde deneme süreci kontrolü için açıklama ekleme
COMMENT ON FUNCTION check_referral_discount(UUID) IS 'Bir salon için kullanılabilir referans indirimi olup olmadığını kontrol eder. Deneme sürecindeki salonlar referans kodu oluşturamaz.';

-- Referans kodu oluşturma işleminde deneme süreci kontrolü için trigger oluşturma
CREATE OR REPLACE FUNCTION check_trial_before_referral_code()
RETURNS TRIGGER AS $$
DECLARE
  is_trial BOOLEAN;
BEGIN
  -- Salonun deneme sürecinde olup olmadığını kontrol et
  SELECT (status = 'trial') INTO is_trial
  FROM salon_subscriptions
  WHERE salon_id = NEW.salon_id
  AND is_active = TRUE;

  -- <PERSON><PERSON><PERSON> den<PERSON><PERSON> s<PERSON>, hata fırlat
  IF is_trial THEN
    RAISE EXCEPTION 'Deneme sürecindeki salonlar referans kodu oluşturamaz. Lütfen abonelik planınızı aktifleştirin.';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ı referral_codes tablosuna bağla
DROP TRIGGER IF EXISTS check_trial_before_referral_code_trigger ON referral_codes;
CREATE TRIGGER check_trial_before_referral_code_trigger
BEFORE INSERT ON referral_codes
FOR EACH ROW
EXECUTE FUNCTION check_trial_before_referral_code();

-- Trigger açıklaması
COMMENT ON TRIGGER check_trial_before_referral_code_trigger ON referral_codes IS 'Deneme sürecindeki salonların referans kodu oluşturmasını engeller';
