# Dashboard Sidebar Güncellemesi

## <PERSON><PERSON><PERSON><PERSON>ğişiklikler

### 1. Yeni Sidebar Bileşeni Oluşturuldu

Shadcn UI'ın inset sidebar yapısını kullanarak özel bir sidebar bileşeni oluşturuldu. Bu bileşen:

- Kullanıcı rolüne göre dinamik menü öğeleri gösteriyor (owner veya staff)
- UserContext entegrasyonu ile kullanıcı bilgilerini ve salon bilgilerini gösteriyor
- Çıkış yapma fonksiyonunu içeriyor
- Modern ve kullanıcı dostu bir arayüz sunuyor

```tsx
// src/components/custom-app-sidebar.tsx
export function CustomAppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const { isMobile } = useSidebar()
  
  // UserContext'ten kullanıcı bilgilerini al
  const { user, userRole, isLoading, signOut, salon } = useUser()

  // Kullanıcı rolüne göre navigasyon öğelerini oluştur
  const navigationItems = React.useMemo(() => {
    const commonItems: NavigationItem[] = [
      {
        title: "Randevular",
        href: "/dashboard",
        icon: Calendar,
        isActive: pathname === "/dashboard",
      },
      // ...diğer öğeler
    ];

    if (userRole === 'owner') {
      // Salon sahibi için menü öğeleri
    } else if (userRole === 'staff') {
      // Personel için menü öğeleri
    }

    return commonItems;
  }, [pathname, userRole]);

  // ...bileşen içeriği
}
```

### 2. Dashboard Layout Güncellendi

Dashboard layout'u yeni sidebar yapısını kullanacak şekilde güncellendi:

```tsx
// src/app/dashboard/layout.tsx
export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  // UserContext'ten kullanıcı bilgilerini al
  const { user, isLoading } = useUser()

  // Sadece gerçekten yükleme durumundaysa loading göster
  const showLoading = useMemo(() => {
    return isLoading && !user;
  }, [isLoading, user]);

  if (showLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <CustomAppSidebar />
      <SidebarInset>
        {children}
      </SidebarInset>
    </SidebarProvider>
  )
}
```

### 3. Dashboard Sayfası Güncellendi

Dashboard ana sayfası, yeni sidebar yapısına uygun olarak güncellendi ve kullanıcı rolüne göre farklı içerikler gösterecek şekilde düzenlendi:

- Salon sahibi için salon yönetimi kartları
- Personel için programını görüntüleme kartı
- Tüm kullanıcılar için hızlı erişim kartı
- Randevu takvimi

### 4. Diğer Sayfalar Güncellendi

Appointments sayfası gibi diğer sayfalar da yeni yapıya uygun olarak güncellendi:

- SidebarTrigger eklendi
- Header yapısı standardize edildi
- Sayfa içerikleri korundu

## Sonraki Adımlar

- Diğer tüm dashboard sayfalarının yeni yapıya uygun olarak güncellenmesi
- Mobil görünümün test edilmesi
- Kullanıcı deneyiminin iyileştirilmesi için ek düzenlemeler yapılması
