"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Plus, 
  Edit, 
  Trash2, 
  Star, 
  Eye, 
  EyeOff, 
  GripVertical,
  Save,
  X
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { useContent } from "@/contexts/ContentContext"
import { SalonTestimonial, SalonTestimonialInsert } from "@/lib/db/types"
import { validateTestimonialData } from "@/lib/db/salon-testimonials"

interface TestimonialsManagerProps {
  onContentChange?: () => void
}

interface TestimonialFormData {
  customer_name: string
  rating: number
  comment: string
  service_name: string
  date_text: string
  is_active: boolean
  display_order: number
}

const initialFormData: TestimonialFormData = {
  customer_name: '',
  rating: 5,
  comment: '',
  service_name: '',
  date_text: '',
  is_active: true,
  display_order: 0
}

export function TestimonialsManager({ onContentChange }: TestimonialsManagerProps) {
  const { 
    testimonials, 
    isLoading, 
    addTestimonial, 
    updateTestimonial, 
    deleteTestimonial, 
    toggleTestimonial 
  } = useContent()

  // State
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingTestimonial, setEditingTestimonial] = useState<SalonTestimonial | null>(null)
  const [formData, setFormData] = useState<TestimonialFormData>(initialFormData)
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formErrors, setFormErrors] = useState<string[]>([])

  // Handle form input changes
  const handleInputChange = (field: keyof TestimonialFormData, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear errors when user starts typing
    if (formErrors.length > 0) {
      setFormErrors([])
    }
  }

  // Open dialog for new testimonial
  const handleAddNew = () => {
    setEditingTestimonial(null)
    setFormData({
      ...initialFormData,
      display_order: testimonials.length
    })
    setFormErrors([])
    setIsDialogOpen(true)
  }

  // Open dialog for editing testimonial
  const handleEdit = (testimonial: SalonTestimonial) => {
    setEditingTestimonial(testimonial)
    setFormData({
      customer_name: testimonial.customer_name,
      rating: testimonial.rating,
      comment: testimonial.comment,
      service_name: testimonial.service_name || '',
      date_text: testimonial.date_text || '',
      is_active: testimonial.is_active,
      display_order: testimonial.display_order
    })
    setFormErrors([])
    setIsDialogOpen(true)
  }

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form data
    const validation = validateTestimonialData(formData)
    if (!validation.isValid) {
      setFormErrors(validation.errors)
      return
    }

    setIsSubmitting(true)
    try {
      if (editingTestimonial) {
        // Update existing testimonial
        await updateTestimonial(editingTestimonial.id, formData)
      } else {
        // Create new testimonial
        await addTestimonial(formData)
      }
      
      setIsDialogOpen(false)
      onContentChange?.()
    } catch (error) {
      console.error('Testimonial save error:', error)
      setFormErrors(['Kaydetme sırasında bir hata oluştu'])
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle delete confirmation
  const handleDelete = async (id: string) => {
    try {
      await deleteTestimonial(id)
      setDeleteConfirmId(null)
      onContentChange?.()
    } catch (error) {
      console.error('Testimonial delete error:', error)
    }
  }

  // Handle toggle active status
  const handleToggleActive = async (testimonial: SalonTestimonial) => {
    try {
      await toggleTestimonial(testimonial.id, !testimonial.is_active)
      onContentChange?.()
    } catch (error) {
      console.error('Testimonial toggle error:', error)
    }
  }

  // Render star rating
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating 
            ? "text-yellow-400 fill-current" 
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ))
  }

  // Render star rating selector
  const renderStarSelector = (currentRating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {Array.from({ length: 5 }, (_, i) => (
          <button
            key={i}
            type="button"
            onClick={() => handleInputChange('rating', i + 1)}
            className="focus:outline-none"
          >
            <Star
              className={`w-6 h-6 transition-colors ${
                i < currentRating 
                  ? "text-yellow-400 fill-current hover:text-yellow-500" 
                  : "text-gray-300 dark:text-gray-600 hover:text-yellow-300"
              }`}
            />
          </button>
        ))}
        <span className="ml-2 text-sm text-muted-foreground">
          {currentRating}/5
        </span>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Müşteri Yorumları</h3>
          <p className="text-sm text-muted-foreground">
            Salon sayfanızda görünecek müşteri yorumlarını yönetin
          </p>
        </div>
        <Button onClick={handleAddNew}>
          <Plus className="mr-2 h-4 w-4" />
          Yeni Yorum Ekle
        </Button>
      </div>

      {/* Testimonials List */}
      {testimonials.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                <Star className="w-8 h-8 text-muted-foreground" />
              </div>
              <div>
                <h3 className="font-medium">Henüz yorum yok</h3>
                <p className="text-sm text-muted-foreground">
                  İlk müşteri yorumunu ekleyerek başlayın
                </p>
              </div>
              <Button onClick={handleAddNew}>
                <Plus className="mr-2 h-4 w-4" />
                İlk Yorumu Ekle
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`${!testimonial.is_active ? 'opacity-60' : ''}`}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-3">
                      {/* Header */}
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{testimonial.customer_name}</h4>
                          {!testimonial.is_active && (
                            <Badge variant="secondary">Gizli</Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-1">
                          {renderStars(testimonial.rating)}
                        </div>
                      </div>

                      {/* Service and Date */}
                      {(testimonial.service_name || testimonial.date_text) && (
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          {testimonial.service_name && (
                            <span>{testimonial.service_name}</span>
                          )}
                          {testimonial.date_text && (
                            <span>{testimonial.date_text}</span>
                          )}
                        </div>
                      )}

                      {/* Comment */}
                      <p className="text-muted-foreground leading-relaxed">
                        "{testimonial.comment}"
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(testimonial)}
                      >
                        {testimonial.is_active ? (
                          <Eye className="h-4 w-4" />
                        ) : (
                          <EyeOff className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(testimonial)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeleteConfirmId(testimonial.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingTestimonial ? 'Yorumu Düzenle' : 'Yeni Yorum Ekle'}
            </DialogTitle>
            <DialogDescription>
              Müşteri yorumunu ekleyin veya düzenleyin
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Form Errors */}
            {formErrors.length > 0 && (
              <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
                <ul className="text-sm text-destructive space-y-1">
                  {formErrors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Customer Name */}
            <div className="space-y-2">
              <Label htmlFor="customer_name">Müşteri Adı *</Label>
              <Input
                id="customer_name"
                value={formData.customer_name}
                onChange={(e) => handleInputChange('customer_name', e.target.value)}
                placeholder="Ahmet Yılmaz"
                maxLength={100}
              />
            </div>

            {/* Rating */}
            <div className="space-y-2">
              <Label>Puan *</Label>
              {renderStarSelector(formData.rating)}
            </div>

            {/* Comment */}
            <div className="space-y-2">
              <Label htmlFor="comment">Yorum *</Label>
              <Textarea
                id="comment"
                value={formData.comment}
                onChange={(e) => handleInputChange('comment', e.target.value)}
                placeholder="Harika bir deneyim yaşadım..."
                rows={4}
                maxLength={500}
              />
              <p className="text-xs text-muted-foreground">
                {formData.comment.length}/500 karakter
              </p>
            </div>

            {/* Service Name */}
            <div className="space-y-2">
              <Label htmlFor="service_name">Hizmet Adı</Label>
              <Input
                id="service_name"
                value={formData.service_name}
                onChange={(e) => handleInputChange('service_name', e.target.value)}
                placeholder="Saç Kesimi & Sakal Düzenleme"
              />
            </div>

            {/* Date Text */}
            <div className="space-y-2">
              <Label htmlFor="date_text">Tarih Metni</Label>
              <Input
                id="date_text"
                value={formData.date_text}
                onChange={(e) => handleInputChange('date_text', e.target.value)}
                placeholder="2 hafta önce"
              />
            </div>

            {/* Active Status */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => handleInputChange('is_active', e.target.checked)}
                className="rounded border-gray-300"
              />
              <Label htmlFor="is_active">Aktif (Sayfada görünsün)</Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              İptal
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {editingTestimonial ? 'Güncelle' : 'Kaydet'}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteConfirmId} onOpenChange={() => setDeleteConfirmId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Yorumu Sil</AlertDialogTitle>
            <AlertDialogDescription>
              Bu yorumu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>İptal</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteConfirmId && handleDelete(deleteConfirmId)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
