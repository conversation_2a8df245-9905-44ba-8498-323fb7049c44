-- Add salon_id column to customers table
ALTER TABLE customers ADD COLUMN salon_id UUID REFERENCES salons(id) ON DELETE CASCADE;

-- Create index on salon_id column
CREATE INDEX idx_customers_salon_id ON customers (salon_id);

-- Update RLS policy for customers table
DROP POLICY IF EXISTS "Salon owners can see customers with appointments at their salon" ON customers;

-- Create new RLS policy for customers table
CREATE POLICY "Salon owners can manage their salon's customers" ON customers
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
