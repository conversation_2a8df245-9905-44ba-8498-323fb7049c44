# SalonFlow Abonelik Sistemi Geliştirme Planı

## Abonelik Sistemi Özeti

### Ödeme Yöntemi
- Başlangıçta manuel havale/EFT
- İleride iyzico/PayTR entegrasyonu

### Fiyatlandırma Kademeleri
- **Solo**: 750 TL/ay
- **Small Team**: 1.500 TL/ay
- **Pro Salon**: 5.000 TL/ay

### Ödeme Döngüsü
- Aylık ve yıllık (yıllık ödemede indirim)

### Deneme Süresi
- Tüm planlarda 14 gün ücretsiz deneme

### Plan Değişikliği
- Sadece yükseltme mümkün, düşürme yok

### Referans Sistemi
- Salon sahiplerine referans kuponu
- Yeni üyelere 1 ay indirim

### Veri Saklama
- İptal sonrası 1 ay
- Müşteri isterse hemen silme

### Ödeme Başarısızlığı
- 1 hafta devam + bildirim
- <PERSON>ra askıya alma
- 1 ay sonra veri silme

## Geliştirme Görevleri

### 1. Veritabanı Yapısı

#### Abonelik Tabloları
- **subscription_plans**: Plan detayları
  - `id`: UUID
  - `name`: Plan adı (Solo, Small Team, Pro Salon)
  - `price_monthly`: Aylık fiyat
  - `price_yearly`: Yıllık fiyat
  - `max_staff`: İzin verilen maksimum personel sayısı
  - `features`: Plan özellikleri (JSONB)
  - `created_at`: Oluşturulma tarihi

- **salon_subscriptions**: Salon abonelikleri
  - `id`: UUID
  - `salon_id`: Salon ID (salons tablosuna referans)
  - `plan_id`: Plan ID (subscription_plans tablosuna referans)
  - `status`: Abonelik durumu (trial, active, past_due, canceled, suspended)
  - `start_date`: Başlangıç tarihi
  - `end_date`: Bitiş tarihi
  - `trial_end_date`: Deneme süresi bitiş tarihi
  - `payment_method`: Ödeme yöntemi (manual, iyzico, paytr)
  - `is_yearly`: Yıllık mı? (boolean)
  - `created_at`: Oluşturulma tarihi
  - `updated_at`: Güncellenme tarihi

- **subscription_payments**: Ödeme kayıtları
  - `id`: UUID
  - `subscription_id`: Abonelik ID (salon_subscriptions tablosuna referans)
  - `amount`: Ödeme tutarı
  - `status`: Ödeme durumu (pending, completed, failed)
  - `payment_date`: Ödeme tarihi
  - `payment_method`: Ödeme yöntemi (manual, iyzico, paytr)
  - `invoice_number`: Fatura numarası
  - `created_at`: Oluşturulma tarihi

- **referral_codes**: Referans kodları
  - `id`: UUID
  - `salon_id`: Salon ID (salons tablosuna referans)
  - `code`: Referans kodu
  - `uses`: Kullanım sayısı
  - `created_at`: Oluşturulma tarihi

- **referral_benefits**: Referans faydaları
  - `id`: UUID
  - `referrer_id`: Referans veren salon ID
  - `referred_id`: Referans alan salon ID
  - `status`: Durum (pending, applied, expired)
  - `benefit_amount`: Fayda tutarı
  - `created_at`: Oluşturulma tarihi

#### RLS Politikaları
- Salon sahipleri sadece kendi abonelik bilgilerini görebilir
- Admin tüm abonelikleri yönetebilir

### 2. Backend İşlevselliği

#### Abonelik Yönetimi
- Abonelik oluşturma
- Deneme süresi yönetimi
- Abonelik yenileme
- Abonelik yükseltme
- Abonelik iptal etme
- Abonelik durumu kontrolü

#### Ödeme İşleme
- Manuel ödeme kaydı (admin tarafından)
- Ödeme durumu güncelleme
- Ödeme geçmişi sorgulama

#### Bildirim Sistemi
- Deneme süresi bitimine yaklaşma bildirimi
- Ödeme hatırlatma bildirimi
- Abonelik durumu değişiklik bildirimi
- Ödeme başarısızlığı bildirimi

#### Referans Sistemi
- Referans kodu oluşturma
- Referans kodu kullanımı
- Referans faydalarının uygulanması

### 3. Frontend Bileşenleri

#### Salon Sahibi Paneli
- Abonelik durumu görüntüleme
- Plan detayları ve karşılaştırma
- Abonelik yükseltme
- Ödeme geçmişi
- Referans kodu yönetimi

#### Admin Paneli
- Tüm abonelikleri görüntüleme ve yönetme
- Manuel ödeme onaylama
- Abonelik durumu değiştirme
- Abonelik analitiği

#### Kayıt/Deneme Süreci
- Plan seçimi sayfası
- Deneme kaydı formu
- Referans kodu girişi

### 4. Özellik Erişim Kontrolü

#### Erişim Kontrol Mekanizması
- Abonelik planına göre personel sayısı sınırlaması
- Abonelik durumuna göre özellik erişimi kontrolü
- Deneme süresi ve aktif abonelik kontrolü

#### Uygulama Noktaları
- Backend: API isteklerinde abonelik durumu kontrolü
- Frontend: UI bileşenlerinin koşullu gösterimi

### 5. Analitik ve Raporlama

#### Temel Metrikler
- MRR (Monthly Recurring Revenue)
- Churn Rate (Abonelik İptal Oranı)
- Conversion Rate (Deneme -> Ücretli Dönüşüm Oranı)
- Plan dağılımı

#### Admin Raporları
- Aylık gelir raporu
- Abonelik durumu dağılımı
- Referans sistemi etkinliği

## Uygulama Aşamaları

### Aşama 1: Temel Abonelik Altyapısı
- Veritabanı tablolarının oluşturulması
- Temel abonelik yönetimi API'leri
- Salon sahibi abonelik görüntüleme sayfası
- Admin için manuel ödeme onaylama

### Aşama 2: Deneme Süreci ve Plan Yükseltme
- Deneme süreci implementasyonu
- Plan yükseltme işlevselliği
- Bildirim sistemi entegrasyonu
- Özellik erişim kontrolü

### Aşama 3: Referans Sistemi ve Analitik
- Referans kodu oluşturma ve kullanma
- Referans faydalarının uygulanması
- Temel analitik dashboard
- Raporlama özellikleri

### Aşama 4: Ödeme Entegrasyonu (İleride)
- iyzico/PayTR entegrasyonu
- Otomatik ödeme işleme
- Fatura oluşturma

## Özellik Kısıtlamaları Açıklaması

Özellik kısıtlamaları iki seviyede uygulanacaktır:

1. **Frontend Kısıtlama**: Kullanıcı arayüzünde, abonelik planına göre bazı özelliklerin görünürlüğünü veya erişilebilirliğini kısıtlama. Örneğin, Pro Salon planında olmayan bir salon sahibine ileri analitik raporları göstermeme.

2. **Backend Kısıtlama**: API isteklerinde, kullanıcının abonelik planına göre bazı işlemleri engelleme. Örneğin, Solo planda olan bir salon sahibinin, izin verilen personel sayısından fazla personel eklemesini engelleme.

Her iki seviyede de kısıtlama uygulanarak hem kullanıcı deneyimi iyileştirilecek hem de güvenlik sağlanacaktır.

## Orantılı Fiyatlandırma (Pro-rating) Açıklaması

"Pro-rating" terimi, abonelik değişikliklerinde kalan süreye göre fiyat ayarlaması yapılmasını ifade eder. Örneğin:

- Bir salon sahibi ayın ortasında Solo'dan Small Team'e yükseltme yaparsa, kalan 15 gün için Solo ve Small Team arasındaki fiyat farkının yarısını öder.
- Bir sonraki ay başından itibaren tam Small Team fiyatı uygulanır.

Bu, kullanıcıların plan değişikliği yaparken adil bir şekilde ücretlendirilmesini sağlar.
