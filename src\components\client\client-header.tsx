"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Menu, X } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

import { Button } from "@/components/ui/button"
import { BookingButton } from "@/components/customer/booking-button"
import { useSalon } from "@/contexts/SalonContext"

interface ClientHeaderProps {
  currentPage?: string
}

export function ClientHeader({
  currentPage
}: ClientHeaderProps) {
  const pathname = usePathname()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // SalonContext'ten bilgileri al
  const {
    salon,
    salonId,
    slug,
    showProductsMenu
  } = useSalon()

  // Menü öğeleri
  const menuItems = [
    { href: `/${slug}#services`, label: "Hizmetler" },
    { href: `/${slug}#about`, label: "<PERSON>kk<PERSON>m<PERSON><PERSON>" },
    { href: `/${slug}#contact`, label: "<PERSON>letişim" },
  ]

  // Ürünler menü öğesini ekle
  if (showProductsMenu) {
    menuItems.push({ href: `/${slug}/products`, label: "Ürünler" })
  }

  if (!salon || !salonId) {
    return null
  }

  return (
    <header className="border-b sticky top-0 z-40 backdrop-blur-md bg-background/80">
      <div className="container mx-auto flex justify-between items-center py-4">
        {/* Salon Adı */}
        <Link href={`/${slug}`} className="text-2xl font-bold tracking-tight">
          <motion.span
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {salon.name}
          </motion.span>
        </Link>

        <div className="flex items-center gap-4">
          {/* Masaüstü Menü */}
          <nav className="hidden md:flex gap-8">
            {menuItems.map((item) => {
              const isActive =
                (item.href === `/${slug}` && pathname === `/${slug}`) ||
                (item.href === `/${slug}/products` && pathname === `/${slug}/products`) ||
                (pathname === `/${slug}` && item.href.includes('#'));

              return (
                <Link
                  key={item.label}
                  href={item.href}
                  className="relative font-medium text-sm tracking-wide uppercase transition-colors hover:text-primary group"
                >
                  {item.label}
                  <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  {isActive && (
                    <motion.span
                      layoutId="activeIndicator"
                      className="absolute -bottom-1 left-0 h-0.5 w-full bg-primary"
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    />
                  )}
                </Link>
              )
            })}
          </nav>

          {/* Randevu Butonu */}
          <BookingButton
            buttonText="Randevu Al"
            buttonVariant="default"
            buttonSize="sm"
          />

          {/* Mobil Menü Butonu */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>

      {/* Mobil Menü */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden border-t"
          >
            <div className="container mx-auto py-4 flex flex-col gap-4">
              <nav className="flex flex-col gap-4">
                {menuItems.map((item) => (
                  <Link
                    key={item.label}
                    href={item.href}
                    className="hover:text-primary"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                ))}
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
