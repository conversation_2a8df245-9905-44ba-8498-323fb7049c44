import { supabaseClient } from '../supabase-singleton';
import { SubscriptionPlan, SubscriptionPlanInsert, SubscriptionPlanUpdate } from './types';

const supabase = supabaseClient;

/**
 * Get all subscription plans
 */
export async function getSubscriptionPlans() {
  const { data, error } = await supabase
    .from('subscription_plans')
    .select('*')
    .order('price_monthly');

  if (error) throw error;
  return data as SubscriptionPlan[];
}

/**
 * Get a subscription plan by ID
 */
export async function getSubscriptionPlanById(id: string) {
  const { data, error } = await supabase
    .from('subscription_plans')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as SubscriptionPlan;
}

/**
 * Get a subscription plan by name
 */
export async function getSubscriptionPlanByName(name: string) {
  const { data, error } = await supabase
    .from('subscription_plans')
    .select('*')
    .eq('name', name)
    .single();

  if (error) throw error;
  return data as SubscriptionPlan;
}

/**
 * Create a new subscription plan (admin only)
 */
export async function createSubscriptionPlan(plan: SubscriptionPlanInsert) {
  const { data, error } = await supabase
    .from('subscription_plans')
    .insert(plan)
    .select()
    .single();

  if (error) throw error;
  return data as SubscriptionPlan;
}

/**
 * Update a subscription plan (admin only)
 */
export async function updateSubscriptionPlan({ id, ...plan }: SubscriptionPlanUpdate) {
  const { data, error } = await supabase
    .from('subscription_plans')
    .update(plan)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SubscriptionPlan;
}

/**
 * Delete a subscription plan (admin only)
 */
export async function deleteSubscriptionPlan(id: string) {
  const { error } = await supabase
    .from('subscription_plans')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Check if a plan allows a specific feature
 */
export function planHasFeature(plan: SubscriptionPlan, featureName: string): boolean {
  if (!plan || !plan.features) return false;
  return !!plan.features[featureName];
}

/**
 * Get maximum staff count for a plan
 */
export function getPlanMaxStaff(plan: SubscriptionPlan): number {
  if (!plan) return 0;
  return plan.max_staff || 0;
}

/**
 * Calculate yearly price with discount
 */
export function calculateYearlyPrice(monthlyPrice: number): number {
  // 10% discount for yearly payments
  return monthlyPrice * 12 * 0.9;
}
