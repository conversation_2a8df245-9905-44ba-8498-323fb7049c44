import { createClient } from '@supabase/supabase-js';
import { addDays, format } from 'date-fns';

// Bu kod sunucu tarafında çalışacak bir cron job için
export async function sendTrialEndingReminders() {
  // Supabase servis anahtarı ile istemci oluştur
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );

  const today = new Date();
  const threeDaysLater = addDays(today, 3);
  const formattedDate = format(threeDaysLater, 'yyyy-MM-dd');

  // Deneme süresi 3 gün içinde bitecek abonelikler
  const { data: subscriptions, error } = await supabase
    .from('salon_subscriptions')
    .select('*, salons(id, owner_id)')
    .eq('status', 'trial')
    .eq('trial_end_date', formattedDate)
    .eq('is_active', true);

  if (error) {
    console.error('Error fetching trial subscriptions:', error);
    return;
  }

  console.log(`Found ${subscriptions?.length || 0} subscriptions with trial ending in 3 days`);

  // Her abonelik için bildirim oluştur
  for (const subscription of subscriptions || []) {
    try {
      await supabase
        .from('notifications')
        .insert({
          salon_id: subscription.salon_id,
          user_id: subscription.salons?.owner_id,
          type: 'subscription_reminder',
          title: 'Deneme Süreniz Bitiyor',
          message: 'Deneme sürenizin bitmesine 3 gün kaldı. Kesintisiz hizmet için aboneliğinizi aktifleştirin.',
          read: false,
          data: { 
            subscription_id: subscription.id, 
            trial_end_date: subscription.trial_end_date 
          }
        });
      
      console.log(`Created reminder notification for salon ${subscription.salon_id}`);
    } catch (err) {
      console.error(`Error creating notification for salon ${subscription.salon_id}:`, err);
    }
  }
}

// Ödeme hatırlatma bildirimleri
export async function sendPaymentReminders() {
  // Supabase servis anahtarı ile istemci oluştur
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );

  const today = new Date();
  const formattedDate = format(today, 'yyyy-MM-dd');

  // Ödeme bekleyen abonelikler
  const { data: subscriptions, error } = await supabase
    .from('salon_subscriptions')
    .select('*, salons(id, owner_id)')
    .eq('status', 'past_due')
    .eq('is_active', true);

  if (error) {
    console.error('Error fetching past due subscriptions:', error);
    return;
  }

  console.log(`Found ${subscriptions?.length || 0} subscriptions with past due payments`);

  // Her abonelik için bildirim oluştur
  for (const subscription of subscriptions || []) {
    try {
      // Son 24 saat içinde bildirim gönderilmiş mi kontrol et
      const { data: recentNotifications, error: notifError } = await supabase
        .from('notifications')
        .select('*')
        .eq('salon_id', subscription.salon_id)
        .eq('type', 'payment_reminder')
        .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());
      
      if (notifError) {
        console.error(`Error checking recent notifications for salon ${subscription.salon_id}:`, notifError);
        continue;
      }
      
      // Son 24 saat içinde bildirim gönderilmemişse yeni bildirim oluştur
      if (!recentNotifications || recentNotifications.length === 0) {
        await supabase
          .from('notifications')
          .insert({
            salon_id: subscription.salon_id,
            user_id: subscription.salons?.owner_id,
            type: 'payment_reminder',
            title: 'Ödeme Hatırlatması',
            message: 'Abonelik ödemeniz gecikti. Lütfen en kısa sürede ödemenizi yapın.',
            read: false,
            data: { 
              subscription_id: subscription.id, 
              status: subscription.status 
            }
          });
        
        console.log(`Created payment reminder notification for salon ${subscription.salon_id}`);
      } else {
        console.log(`Skipped notification for salon ${subscription.salon_id} - already sent in last 24 hours`);
      }
    } catch (err) {
      console.error(`Error creating notification for salon ${subscription.salon_id}:`, err);
    }
  }
}
