"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format, parse } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"
import { ArrowLeft, Save } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { getSalonSubscriptionById, updateSalonSubscription, isAdmin } from "@/lib/db/admin"
import { getSubscriptionPlans } from "@/lib/db/subscription-plans"

export default function EditSubscriptionPage({ params }: { params: { id: string } }) {
  const id = params.id as string

  const router = useRouter()
  const [subscription, setSubscription] = useState<any>(null)
  const [plans, setPlans] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isAdminUser, setIsAdminUser] = useState(false)

  // Form state
  const [planId, setPlanId] = useState("")
  const [status, setStatus] = useState("")
  const [isYearly, setIsYearly] = useState(false)
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")
  const [trialEndDate, setTrialEndDate] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("")
  const [isActive, setIsActive] = useState(true)

  useEffect(() => {
    async function checkAdmin() {
      try {
        const admin = await isAdmin()
        setIsAdminUser(admin)
        if (!admin) {
          router.push("/dashboard")
          toast.error("Bu sayfaya erişim yetkiniz yok.")
        }
      } catch (error) {
        console.error("Admin kontrolü hatası:", error)
        router.push("/dashboard")
      }
    }

    checkAdmin()
  }, [router])

  useEffect(() => {
    async function loadData() {
      if (!isAdminUser) return

      try {
        setIsLoading(true)

        // Abonelik detaylarını yükle
        const subscriptionData = await getSalonSubscriptionById(id)
        setSubscription(subscriptionData)

        // Form state'i güncelle
        setPlanId(subscriptionData.plan_id || "")
        setStatus(subscriptionData.status || "")
        setIsYearly(subscriptionData.is_yearly || false)
        setStartDate(subscriptionData.start_date || "")
        setEndDate(subscriptionData.end_date || "")
        setTrialEndDate(subscriptionData.trial_end_date || "")
        setPaymentMethod(subscriptionData.payment_method || "")
        setIsActive(subscriptionData.is_active || true)

        // Abonelik planlarını yükle
        const plansData = await getSubscriptionPlans()
        setPlans(plansData)
      } catch (error) {
        console.error("Abonelik detayları yüklenirken hata:", error)
        toast.error("Abonelik detayları yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    if (id) {
      loadData()
    }
  }, [isAdminUser, id])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!subscription) return

    try {
      setIsSubmitting(true)

      // Abonelik bilgilerini güncelle
      await updateSalonSubscription(id, {
        id: id,
        plan_id: planId,
        status,
        is_yearly: isYearly,
        start_date: startDate,
        end_date: endDate || null,
        trial_end_date: trialEndDate || null,
        payment_method: paymentMethod,
        is_active: isActive
      })

      toast.success("Abonelik başarıyla güncellendi.")
      router.push(`/admin/subscriptions/${id}`)
    } catch (error) {
      console.error("Abonelik güncellenirken hata:", error)
      toast.error("Abonelik güncellenirken bir hata oluştu.")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isAdminUser) {
    return null
  }

  if (isLoading) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href={`/admin/subscriptions/${id}`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Abonelik Düzenle</h1>
        </header>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-64 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <Button variant="outline" size="icon" asChild className="mr-2">
          <Link href={`/admin/subscriptions/${id}`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">Abonelik Düzenle</h1>
      </header>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>{subscription?.salons?.name || 'Bilinmeyen Salon'}</CardTitle>
            <CardDescription>Abonelik ID: {subscription?.id}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Plan Seçimi */}
              <div className="space-y-2">
                <Label htmlFor="plan">Abonelik Planı</Label>
                <Select value={planId} onValueChange={setPlanId}>
                  <SelectTrigger id="plan">
                    <SelectValue placeholder="Plan seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    {plans.map((plan) => (
                      <SelectItem key={plan.id} value={plan.id}>
                        {plan.name} - {plan.price_monthly} TL/ay
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Abonelik Durumu */}
              <div className="space-y-2">
                <Label htmlFor="status">Abonelik Durumu</Label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Durum seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="trial">Deneme Süresi</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="past_due">Ödeme Gecikti</SelectItem>
                    <SelectItem value="canceled">İptal Edildi</SelectItem>
                    <SelectItem value="suspended">Askıya Alındı</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Ödeme Döngüsü */}
              <div className="space-y-2">
                <Label>Ödeme Döngüsü</Label>
                <RadioGroup value={isYearly ? "yearly" : "monthly"} onValueChange={(value) => setIsYearly(value === "yearly")}>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="monthly" id="monthly" />
                    <Label htmlFor="monthly">Aylık</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yearly" id="yearly" />
                    <Label htmlFor="yearly">Yıllık</Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Ödeme Yöntemi */}
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Ödeme Yöntemi</Label>
                <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                  <SelectTrigger id="paymentMethod">
                    <SelectValue placeholder="Ödeme yöntemi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manual">Manuel (Havale/EFT)</SelectItem>
                    <SelectItem value="iyzico">iyzico</SelectItem>
                    <SelectItem value="paytr">PayTR</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              {/* Tarihler */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Başlangıç Tarihi</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">Bitiş Tarihi (Opsiyonel)</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trialEndDate">Deneme Süresi Bitiş (Opsiyonel)</Label>
                  <Input
                    id="trialEndDate"
                    type="date"
                    value={trialEndDate}
                    onChange={(e) => setTrialEndDate(e.target.value)}
                  />
                </div>
              </div>

              <Separator />

              {/* Aktif/Pasif Durumu */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={isActive}
                  onCheckedChange={setIsActive}
                />
                <Label htmlFor="isActive">Abonelik Aktif</Label>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" asChild>
              <Link href={`/admin/subscriptions/${id}`}>İptal</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-b-0 border-r-0 rounded-full"></div>
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Kaydet
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
}
