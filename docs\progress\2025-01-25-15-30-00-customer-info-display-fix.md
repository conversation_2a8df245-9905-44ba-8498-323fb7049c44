# Customer Information Display Fix
**Date:** 2025-01-25 15:30:00
**Issue:** Randevularda salon adı ve müşteri bilgilerinin boş gelme sorunu

## Problem Description
Kullanıcı bildirdiği sorunlar:
1. Randevularda salon adı her zaman boş geliyor
2. Client tarafında randevu oluşturduğumuz zaman müşteri bilgisi boş geliyor
3. Dashboard'dan randevu iptal edildiği zaman müşteri bilgisi boş geliyor
4. "Bilinmeyen müşteri" şeklinde geldiğinde randevu ID'si de boş geliyor

## Root Cause Analysis
1. **Salon adı sorunu**: Kullanıcı belirtti ki salon adına gerek yok çünkü her telegram kanalı zaten belli salona ait olacak
2. **Müşteri bilgisi öncelik sorunu**: Appointment tablosunda hem customer_id hem de fullname, phonenumber, email kolonları var. <PERSON>dda öncelik sırası yanlıştı.
3. **Appointment ID sorunu**: Client booking form'da `newAppointment.id || ''` kullanılıyordu, bu da boş ID'lere sebep oluyordu.

## Solution Implemented

### 1. formatAppointmentForNotification Function Fix
**File:** `src/lib/utils/telegram-notifications.ts`

**Before:**
```typescript
customer_name: appointment.fullname || 'Bilinmeyen Müşteri',
customer_phone: appointment.phonenumber,
```

**After:**
```typescript
// Get customer name with proper fallback logic
const customerName = appointment.fullname ||
  (appointment.customers ? `${appointment.customers.name} ${appointment.customers.surname}` : 'İsimsiz Müşteri');

// Get customer phone with proper fallback logic
const customerPhone = appointment.phonenumber ||
  (appointment.customers ? appointment.customers.phone : '');
```

### 2. Client Booking Form Fix
**File:** `src/components/client/client-booking-form.tsx`

**Before:**
```typescript
if (selectedBarberData && selectedService) {
  const notificationData = formatAppointmentForNotification({
    id: newAppointment.id || '',
    // ...
  })
}
```

**After:**
```typescript
if (selectedBarberData && selectedService && newAppointment.id) {
  const notificationData = formatAppointmentForNotification({
    id: newAppointment.id,
    fullname: values.name + ' ' + values.surname,
    phonenumber: values.phone,
    email: values.email || '',
    // ...
  })
}
```

### 3. Simplified Notification Calls
**Files:**
- `src/components/calendar/AppointmentActionMenu.tsx`
- `src/app/dashboard/appointments/[id]/page.tsx`

**Before:**
```typescript
const notificationData = formatAppointmentForNotification({
  id: appointment.id,
  salon_id: appointment.salon_id,
  customer_name: appointment.fullname || 'Bilinmeyen Müşteri',
  // ... manual object construction
}, ...)
```

**After:**
```typescript
const notificationData = formatAppointmentForNotification(
  appointment, // Pass the full appointment object with all data
  '', // salon name will be fetched in the API
  appointment.barbers?.name || 'Bilinmeyen Berber',
  appointment.services?.name || 'Bilinmeyen Hizmet'
)
```

## Customer Information Priority Logic
Şimdi müşteri bilgileri için doğru öncelik sırası:

1. **İsim**: `appointment.fullname` → `customers.name + customers.surname` → `"İsimsiz Müşteri"`
2. **Telefon**: `appointment.phonenumber` → `customers.phone` → `""`
3. **Email**: `appointment.email` → `customers.email` → `""`

## Files Modified
1. `src/lib/utils/telegram-notifications.ts` - formatAppointmentForNotification function
2. `src/components/client/client-booking-form.tsx` - Client booking notification
3. `src/components/calendar/AppointmentActionMenu.tsx` - Calendar cancellation notification
4. `src/app/dashboard/appointments/[id]/page.tsx` - Appointment detail cancellation
5. `docs/progress/2025-05-25-03-53-25-telegram-notifications.md` - Updated progress log

## Testing Required
- [x] Test client booking with new customer (fullname should be used) ✅
- [x] Test client booking with existing customer (should still work) ✅
- [x] Test appointment cancellation from calendar ✅
- [x] Test appointment cancellation from detail page ✅
- [x] Verify appointment IDs are not empty in notifications ✅
- [x] Verify customer names display correctly in all scenarios ✅

## Additional Fix - API Validation Schema
**Issue Found**: API validation schema'sında `notes` field'ı `null` değerleri kabul etmiyordu.

**Solution**:
- `notes: z.string().nullable().optional()` olarak güncellendi
- `customer_phone: z.string().nullable().optional()` olarak güncellendi

**Files Modified**:
- `src/app/api/telegram/notify/route.ts` - Validation schema updated

## Notes
- Salon adı sorunu çözüldü - her telegram kanalı zaten belli salona ait olacak
- Appointment tablosundaki direkt kolonlar (fullname, phonenumber, email) artık öncelikli kullanılıyor
- Appointment ID'si boş olduğunda notification gönderilmiyor
- Tüm notification çağrıları artık full appointment object'i kullanıyor
