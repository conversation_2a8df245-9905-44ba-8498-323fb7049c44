/**
 * Encryption utilities for sensitive data
 * Used for encrypting Telegram channel IDs and other sensitive information
 */

import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16; // For GCM, this is always 16
const SALT_LENGTH = 64;
const TAG_LENGTH = 16;
const KEY_LENGTH = 32;

/**
 * Get encryption key from environment variable
 */
function getEncryptionKey(): Buffer {
  const key = process.env.TELEGRAM_ENCRYPTION_KEY;
  if (!key) {
    throw new Error('TELEGRAM_ENCRYPTION_KEY environment variable is not set');
  }

  // If the key is a hex string, convert it to buffer
  if (key.length === 64) {
    return Buffer.from(key, 'hex');
  }

  // Otherwise, derive a key from the string
  return crypto.scryptSync(key, 'salt', KEY_LENGTH);
}

/**
 * Encrypt a string using AES-256-GCM
 * @param text - The text to encrypt
 * @returns Encrypted string in format: iv:salt:tag:encryptedData (all base64 encoded)
 */
export function encryptText(text: string): string {
  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const salt = crypto.randomBytes(SALT_LENGTH);

    // Create cipher
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    cipher.setAAD(salt);

    // Encrypt the text
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');

    // Get the authentication tag
    const tag = cipher.getAuthTag();

    // Combine iv, salt, tag, and encrypted data
    const result = [
      iv.toString('base64'),
      salt.toString('base64'),
      tag.toString('base64'),
      encrypted
    ].join(':');

    return result;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt a string using AES-256-GCM
 * @param encryptedText - The encrypted text in format: iv:salt:tag:encryptedData
 * @returns Decrypted string
 */
export function decryptText(encryptedText: string): string {
  try {
    const key = getEncryptionKey();
    const parts = encryptedText.split(':');

    if (parts.length !== 4) {
      throw new Error('Invalid encrypted data format');
    }

    const [ivBase64, saltBase64, tagBase64, encrypted] = parts;

    // Convert from base64
    const iv = Buffer.from(ivBase64, 'base64');
    const salt = Buffer.from(saltBase64, 'base64');
    const tag = Buffer.from(tagBase64, 'base64');

    // Create decipher
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    decipher.setAAD(salt);
    decipher.setAuthTag(tag);

    // Decrypt the data
    let decrypted = decipher.update(encrypted, 'base64', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Encrypt a Telegram channel ID
 * @param channelId - The Telegram channel ID to encrypt
 * @returns Encrypted channel ID
 */
export function encryptChannelId(channelId: string): string {
  if (!channelId || channelId.trim() === '') {
    throw new Error('Channel ID cannot be empty');
  }

  return encryptText(channelId.trim());
}

/**
 * Decrypt a Telegram channel ID
 * @param encryptedChannelId - The encrypted channel ID
 * @returns Decrypted channel ID
 */
export function decryptChannelId(encryptedChannelId: string): string {
  if (!encryptedChannelId || encryptedChannelId.trim() === '') {
    throw new Error('Encrypted channel ID cannot be empty');
  }

  return decryptText(encryptedChannelId.trim());
}

/**
 * Validate Telegram channel ID format
 * @param channelId - The channel ID to validate
 * @returns true if valid, false otherwise
 */
export function validateChannelId(channelId: string): boolean {
  if (!channelId || typeof channelId !== 'string') {
    return false;
  }

  const trimmed = channelId.trim();

  // Telegram channel IDs can be:
  // - Numeric (e.g., -1001234567890)
  // - Username (e.g., @channelname)
  // - Chat ID (positive or negative numbers)

  // Check for username format
  if (trimmed.startsWith('@')) {
    return /^@[a-zA-Z][a-zA-Z0-9_]{4,31}$/.test(trimmed);
  }

  // Check for numeric ID format
  if (/^-?\d+$/.test(trimmed)) {
    const num = parseInt(trimmed, 10);
    return !isNaN(num) && num !== 0;
  }

  return false;
}

/**
 * Generate a secure encryption key for environment variable
 * This is a utility function for setup, not used in production
 * @returns A 32-byte hex string suitable for TELEGRAM_ENCRYPTION_KEY
 */
export function generateEncryptionKey(): string {
  return crypto.randomBytes(KEY_LENGTH).toString('hex');
}

/**
 * Test encryption/decryption functionality
 * @param testText - Text to test with
 * @returns true if encryption/decryption works correctly
 */
export function testEncryption(testText: string = 'test-channel-id'): boolean {
  try {
    const encrypted = encryptText(testText);
    const decrypted = decryptText(encrypted);
    return decrypted === testText;
  } catch (error) {
    console.error('Encryption test failed:', error);
    return false;
  }
}
