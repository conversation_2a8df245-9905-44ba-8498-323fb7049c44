"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  Award, Users, Clock, Heart, CheckCircle, Star, TrendingUp,
  Shield, Zap, Target, Sparkles, Plus, Trash2, Edit3
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { useContent } from "@/contexts/ContentContext"

interface AboutContentEditorProps {
  onContentChange?: () => void
}

interface Feature {
  icon: string
  title: string
  description: string
}

interface Stat {
  number: string
  label: string
}

interface TrustItem {
  text: string
}

// Available icons for features
const AVAILABLE_ICONS = [
  { value: 'Award', label: '<PERSON><PERSON><PERSON><PERSON>', icon: Award },
  { value: 'Users', label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', icon: Users },
  { value: 'Clock', label: 'Saat', icon: Clock },
  { value: 'Heart', label: 'Kalp', icon: Heart },
  { value: 'Star', label: 'Yıldız', icon: Star },
  { value: 'TrendingUp', label: 'Trend', icon: TrendingUp },
  { value: 'Shield', label: 'Kalkan', icon: Shield },
  { value: 'Zap', label: 'Şimşek', icon: Zap },
  { value: 'Target', label: 'Hedef', icon: Target },
  { value: 'Sparkles', label: 'Parıltı', icon: Sparkles },
  { value: 'CheckCircle', label: 'Onay', icon: CheckCircle }
]

export function AboutContentEditor({ onContentChange }: AboutContentEditorProps) {
  const { getContentValue, updateContentValue } = useContent()

  // Form state
  const [formData, setFormData] = useState({
    badgeText: '',
    title: '',
    description: '',
    ctaButtonText: ''
  })

  const [features, setFeatures] = useState<Feature[]>([])
  const [stats, setStats] = useState<Stat[]>([])
  const [trustTitle, setTrustTitle] = useState('')
  const [trustItems, setTrustItems] = useState<TrustItem[]>([])

  // Load initial data
  useEffect(() => {
    setFormData({
      badgeText: getContentValue('about', 'badge_text', 'Hakkımızda'),
      title: getContentValue('about', 'title', 'Farkını Yaşayın'),
      description: getContentValue('about', 'description', 'Yılların deneyimi ve modern yaklaşımımızla, her müşterimize özel hizmet sunuyoruz. Kalite, güven ve memnuniyet bizim önceliğimiz.'),
      ctaButtonText: getContentValue('about', 'cta_button_text', 'Hemen Randevu Al')
    })

    // Load features
    const featuresData = getContentValue('about', 'features', JSON.stringify([
      { icon: 'Award', title: 'Uzman Ekip', description: 'Alanında deneyimli ve sertifikalı berberlerimiz' },
      { icon: 'Clock', title: 'Esnek Saatler', description: 'Size uygun saatlerde randevu imkanı' },
      { icon: 'Heart', title: 'Müşteri Memnuniyeti', description: 'Her müşterimize özel ilgi ve kaliteli hizmet' },
      { icon: 'Users', title: 'Modern Yaklaşım', description: 'Güncel trendler ve tekniklerle hizmet' }
    ]))
    setFeatures(JSON.parse(featuresData))

    // Load stats
    const statsData = getContentValue('about', 'stats', JSON.stringify([
      { number: '500+', label: 'Mutlu Müşteri' },
      { number: '5+', label: 'Yıl Deneyim' },
      { number: '4.9', label: 'Müşteri Puanı' },
      { number: '24/7', label: 'Online Destek' }
    ]))
    setStats(JSON.parse(statsData))

    // Load trust indicators
    setTrustTitle(getContentValue('about', 'trust_title', 'Güvenilir Hizmet'))
    const trustItemsData = getContentValue('about', 'trust_items', JSON.stringify([
      { text: 'Hijyen ve temizlik standartları' },
      { text: 'Kaliteli ürün ve ekipman kullanımı' },
      { text: 'Müşteri memnuniyeti garantisi' }
    ]))
    setTrustItems(JSON.parse(trustItemsData))
  }, [getContentValue])

  // Handle input changes
  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }))

    const contentKeyMap: Record<string, string> = {
      badgeText: 'badge_text',
      title: 'title',
      description: 'description',
      ctaButtonText: 'cta_button_text'
    }

    const contentKey = contentKeyMap[key]
    if (contentKey) {
      updateContentValue('about', contentKey, value)
      onContentChange?.()
    }
  }

  // Handle features
  const updateFeatures = (newFeatures: Feature[]) => {
    setFeatures(newFeatures)
    updateContentValue('about', 'features', JSON.stringify(newFeatures))
    onContentChange?.()
  }

  const addFeature = () => {
    const newFeature: Feature = {
      icon: 'Award',
      title: 'Yeni Özellik',
      description: 'Özellik açıklaması'
    }
    updateFeatures([...features, newFeature])
  }

  const updateFeature = (index: number, field: keyof Feature, value: string) => {
    const newFeatures = [...features]
    newFeatures[index] = { ...newFeatures[index], [field]: value }
    updateFeatures(newFeatures)
  }

  const removeFeature = (index: number) => {
    updateFeatures(features.filter((_, i) => i !== index))
  }

  // Handle stats
  const updateStats = (newStats: Stat[]) => {
    setStats(newStats)
    updateContentValue('about', 'stats', JSON.stringify(newStats))
    onContentChange?.()
  }

  const addStat = () => {
    const newStat: Stat = {
      number: '0',
      label: 'Yeni İstatistik'
    }
    updateStats([...stats, newStat])
  }

  const updateStat = (index: number, field: keyof Stat, value: string) => {
    const newStats = [...stats]
    newStats[index] = { ...newStats[index], [field]: value }
    updateStats(newStats)
  }

  const removeStat = (index: number) => {
    updateStats(stats.filter((_, i) => i !== index))
  }

  // Handle trust items
  const updateTrustItems = (newItems: TrustItem[]) => {
    setTrustItems(newItems)
    updateContentValue('about', 'trust_items', JSON.stringify(newItems))
    onContentChange?.()
  }

  const addTrustItem = () => {
    updateTrustItems([...trustItems, { text: 'Yeni güven göstergesi' }])
  }

  const updateTrustItem = (index: number, value: string) => {
    const newItems = [...trustItems]
    newItems[index] = { text: value }
    updateTrustItems(newItems)
  }

  const removeTrustItem = (index: number) => {
    updateTrustItems(trustItems.filter((_, i) => i !== index))
  }

  const handleTrustTitleChange = (value: string) => {
    setTrustTitle(value)
    updateContentValue('about', 'trust_title', value)
    onContentChange?.()
  }

  return (
    <div className="space-y-6">
      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Ana İçerik</CardTitle>
          <CardDescription>
            Hakkımızda bölümünün ana başlık, açıklama ve buton metinleri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label htmlFor="badgeText">Rozet Metni</Label>
              <Input
                id="badgeText"
                value={formData.badgeText}
                onChange={(e) => handleInputChange('badgeText', e.target.value)}
                placeholder="Hakkımızda"
              />
              <p className="text-xs text-muted-foreground">
                Bölümün üst kısmında görünen küçük rozet metni
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Ana Başlık</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Farkını Yaşayın"
              />
              <p className="text-xs text-muted-foreground">
                Salon adından sonra gelen ana başlık metni
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Yılların deneyimi ve modern yaklaşımımızla..."
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                Ana başlığın altında görünen açıklama metni
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ctaButtonText">Buton Metni</Label>
              <Input
                id="ctaButtonText"
                value={formData.ctaButtonText}
                onChange={(e) => handleInputChange('ctaButtonText', e.target.value)}
                placeholder="Hemen Randevu Al"
              />
              <p className="text-xs text-muted-foreground">
                Randevu alma butonunun metni
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Özellikler</CardTitle>
              <CardDescription>
                Salonunuzun öne çıkan özelliklerini tanımlayın
              </CardDescription>
            </div>
            <Button onClick={addFeature} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Özellik Ekle
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {features.map((feature, index) => (
            <div key={index} className="p-4 border rounded-lg space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Özellik {index + 1}</h4>
                <Button
                  onClick={() => removeFeature(index)}
                  variant="ghost"
                  size="sm"
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>İkon</Label>
                  <Select
                    value={feature.icon}
                    onValueChange={(value) => updateFeature(index, 'icon', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {AVAILABLE_ICONS.map((icon) => (
                        <SelectItem key={icon.value} value={icon.value}>
                          <div className="flex items-center space-x-2">
                            <icon.icon className="w-4 h-4" />
                            <span>{icon.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Başlık</Label>
                  <Input
                    value={feature.title}
                    onChange={(e) => updateFeature(index, 'title', e.target.value)}
                    placeholder="Özellik başlığı"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Açıklama</Label>
                  <Input
                    value={feature.description}
                    onChange={(e) => updateFeature(index, 'description', e.target.value)}
                    placeholder="Özellik açıklaması"
                  />
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Statistics */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>İstatistikler</CardTitle>
              <CardDescription>
                Salonunuzun başarılarını gösteren sayısal veriler
              </CardDescription>
            </div>
            <Button onClick={addStat} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              İstatistik Ekle
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {stats.map((stat, index) => (
            <div key={index} className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium">İstatistik {index + 1}</h4>
                <Button
                  onClick={() => removeStat(index)}
                  variant="ghost"
                  size="sm"
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Sayı/Değer</Label>
                  <Input
                    value={stat.number}
                    onChange={(e) => updateStat(index, 'number', e.target.value)}
                    placeholder="500+"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Etiket</Label>
                  <Input
                    value={stat.label}
                    onChange={(e) => updateStat(index, 'label', e.target.value)}
                    placeholder="Mutlu Müşteri"
                  />
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Trust Indicators */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Güven Göstergeleri</CardTitle>
              <CardDescription>
                Müşterilerinizin güvenini artıran özellikler
              </CardDescription>
            </div>
            <Button onClick={addTrustItem} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Gösterge Ekle
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="trustTitle">Bölüm Başlığı</Label>
            <Input
              id="trustTitle"
              value={trustTitle}
              onChange={(e) => handleTrustTitleChange(e.target.value)}
              placeholder="Güvenilir Hizmet"
            />
          </div>

          <Separator />

          <div className="space-y-4">
            {trustItems.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className="flex-1">
                  <Input
                    value={item.text}
                    onChange={(e) => updateTrustItem(index, e.target.value)}
                    placeholder="Güven göstergesi metni"
                  />
                </div>
                <Button
                  onClick={() => removeTrustItem(index)}
                  variant="ghost"
                  size="sm"
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
