import { useSubscription, SubscriptionFeatures } from '@/contexts/SubscriptionContext';

// Re-export SubscriptionFeatures interface for backward compatibility
export type { SubscriptionFeatures };

/**
 * Hook to access subscription features
 * This is now a wrapper around useSubscription context hook for backward compatibility
 */
export function useSubscriptionFeatures() {
  const {
    features,
    plan,
    isLoading,
    error,
    hasFeature,
    canAddMoreStaff
  } = useSubscription();

  return {
    features,
    plan,
    isLoading,
    error,
    hasFeature,
    canAddMoreStaff
  };
}
