import { supabaseClient } from '../supabase-singleton';
import { BarberService, BarberServiceInsert } from './types';

const supabase = supabaseClient;
  

/**
 * Get all services for a barber
 */
export async function getBarberServices(barberId: string) {
  const { data, error } = await supabase
    .from('barber_services')
    .select(`
      *,
      services(*)
    `)
    .eq('barber_id', barberId);
  
  if (error) throw error;
  return data;
}

/**
 * Check if a barber can perform a service
 */
export async function canBarberPerformService(barberId: string, serviceId: string) {
  const { data, error } = await supabase
    .from('barber_services')
    .select('*')
    .eq('barber_id', barberId)
    .eq('service_id', serviceId);
  
  if (error) throw error;
  return data.length > 0;
}

/**
 * Assign a service to a barber
 */
export async function assignServiceToBarber(barberService: BarberServiceInsert) {
  const { data, error } = await supabase
    .from('barber_services')
    .insert(barberService)
    .select()
    .single();
  
  if (error) throw error;
  return data as BarberService;
}

/**
 * Remove a service from a barber
 */
export async function removeServiceFromBarber(barberId: string, serviceId: string) {
  const { error } = await supabase
    .from('barber_services')
    .delete()
    .eq('barber_id', barberId)
    .eq('service_id', serviceId);
  
  if (error) throw error;
  return true;
}

/**
 * Assign multiple services to a barber
 */
export async function assignServicesToBarber(barberId: string, serviceIds: string[]) {
  const barberServices = serviceIds.map(serviceId => ({
    barber_id: barberId,
    service_id: serviceId
  }));
  
  const { data, error } = await supabase
    .from('barber_services')
    .insert(barberServices)
    .select();
  
  if (error) throw error;
  return data as BarberService[];
}

/**
 * Remove all services from a barber
 */
export async function removeAllServicesFromBarber(barberId: string) {
  const { error } = await supabase
    .from('barber_services')
    .delete()
    .eq('barber_id', barberId);
  
  if (error) throw error;
  return true;
}
