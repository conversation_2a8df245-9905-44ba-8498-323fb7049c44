# SalonFlow Abonelik Sistemi Geliştirme Görevleri

Bu belge, SalonFlow abonelik sistemi için detaylı geliştirme görevlerini içermektedir. Gö<PERSON>v<PERSON>, [Abonelik Sistemi Özeti](docs\progress\subs\2024-07-25-10-00-00-subscriptions.md) ve [Detaylı Geliştirme Planı](docs\progress\subs\2024-07-25-15-30-00-subscriptions-detailed.md) dokümanlarına dayanmaktadır.

## Hata Düzeltmeleri
- [2024-07-28] Next.js 15 params.id erişim hatası ve ambiguous salon_id hatası düzeltildi. [Detaylar](docs\progress\subs\2024-07-28-10-00-00-subscription-bugfixes.md) ✅

## Yeni İyileştirmeler
- [2024-08-05] Referans sistemi ve ödeme yönetimi iyileştirmeleri planlandı. [Detaylar](docs\progress\subs\2024-08-05-10-00-00-referral-payment-improvements.md)
- [2025-05-20] Ödeme sonrası abonelik bitiş tarihi otomatik güncelleme özelliği eklendi. [Detaylar](docs\sql\2025-05-20-21-36-07-subscription-payment-end-date-trigger.sql) ✅

## 1. Veritabanı Yapısı Geliştirme

### 1.1. Abonelik Tabloları Oluşturma ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün

#### 1.1.1. subscription_plans Tablosu Oluşturma ✅
- SQL script hazırlama: `subscription_plans` tablosunu oluşturacak SQL kodunu yazma ✅
- Varsayılan planları ekleme: Solo, Small Team ve Pro Salon planlarını tanımlama ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.2. salon_subscriptions Tablosu Güncelleme ✅
- Mevcut `salon_subscriptions` tablosunu güncelleyecek SQL kodunu yazma ✅
- Yeni sütunlar ekleme: `plan_id`, `status`, `trial_end_date`, `payment_method`, `is_yearly` ✅
- Mevcut verileri yeni yapıya uygun şekilde dönüştürme ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.3. subscription_payments Tablosu Oluşturma ✅
- SQL script hazırlama: `subscription_payments` tablosunu oluşturacak SQL kodunu yazma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.4. referral_codes Tablosu Oluşturma ✅
- SQL script hazırlama: `referral_codes` tablosunu oluşturacak SQL kodunu yazma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.5. referral_benefits Tablosu Oluşturma ✅
- SQL script hazırlama: `referral_benefits` tablosunu oluşturacak SQL kodunu yazma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

### 1.2. RLS Politikaları Oluşturma ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 4 saat
- **Bağımlılıklar:** 1.1. Abonelik Tabloları Oluşturma

#### 1.2.1. subscription_plans Tablosu için RLS ✅
- Herkesin görüntüleyebileceği, ancak sadece admin'in düzenleyebileceği politikalar oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.2.2. salon_subscriptions Tablosu için RLS ✅
- Salon sahiplerinin kendi aboneliklerini görebileceği politika oluşturma ✅
- Admin'in tüm abonelikleri görebileceği politika oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.2.3. subscription_payments Tablosu için RLS ✅
- Salon sahiplerinin kendi ödemelerini görebileceği politika oluşturma ✅
- Admin'in tüm ödemeleri görebileceği politika oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.2.4. referral_codes ve referral_benefits Tabloları için RLS ✅
- Salon sahiplerinin kendi referans kodlarını ve faydalarını görebileceği politikalar oluşturma ✅
- Admin'in tüm referans kodlarını ve faydalarını görebileceği politikalar oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

### 1.3. TypeScript Tip Tanımlamaları Güncelleme ✅
- **Zorluk:** Kolay
- **Tahmini Süre:** 2 saat
- **Bağımlılıklar:** 1.1. Abonelik Tabloları Oluşturma

#### 1.3.1. Yeni Tip Tanımlamaları Oluşturma ✅
- `SubscriptionPlan` interface'i oluşturma ✅
- Güncellenmiş `SalonSubscription` interface'i oluşturma ✅
- `SubscriptionPayment` interface'i oluşturma ✅
- `ReferralCode` ve `ReferralBenefit` interface'leri oluşturma ✅

#### 1.3.2. Insert ve Update Tipleri Oluşturma ✅
- Tüm yeni tipler için Insert ve Update tiplerini tanımlama ✅
- Mevcut `src/lib/db/types.ts` dosyasını güncelleme ✅

## 2. Backend İşlevselliği Geliştirme

### 2.1. Abonelik Yönetimi API'leri ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** 1.3. TypeScript Tip Tanımlamaları Güncelleme

#### 2.1.1. Abonelik Planları API'si Geliştirme ✅
- `src/lib/db/subscription-plans.ts` dosyasını oluşturma ✅
- `getSubscriptionPlans()` fonksiyonunu geliştirme ✅
- `getSubscriptionPlanById()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

#### 2.1.2. Salon Abonelikleri API'si Güncelleme ✅
- Mevcut `src/lib/db/subscriptions.ts` dosyasını güncelleme ✅
- `getActiveSalonSubscription()` fonksiyonunu güncelleme (plan detaylarını içerecek şekilde) ✅
- `createTrialSubscription()` fonksiyonunu geliştirme ✅
- `upgradeSubscription()` fonksiyonunu geliştirme ✅
- `isTrialExpired()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

#### 2.1.3. Ödeme Yönetimi API'si Geliştirme ✅
- `src/lib/db/subscription-payments.ts` dosyasını oluşturma ✅
- `getSubscriptionPayments()` fonksiyonunu geliştirme ✅
- `createSubscriptionPayment()` fonksiyonunu geliştirme ✅
- `updateSubscriptionPayment()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

#### 2.1.4. Referans Sistemi API'si Geliştirme ✅
- `src/lib/db/referrals.ts` dosyasını oluşturma ✅
- `getSalonReferralCode()` fonksiyonunu geliştirme ✅
- `createReferralCode()` fonksiyonunu geliştirme ✅
- `applyReferralCode()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

### 2.2. Abonelik Durumu Kontrolü ve Kısıtlama Mekanizması ✅
- **Zorluk:** Zor
- **Tahmini Süre:** 3 gün
- **Bağımlılıklar:** 2.1. Abonelik Yönetimi API'leri

#### 2.2.1. Abonelik Durumu Kontrolü Middleware Geliştirme ✅
- `src/middleware/subscription-check.ts` dosyasını oluşturma ✅
- Kullanıcı oturumu ve salon ID'si kontrolü ✅
- Abonelik durumu kontrolü (trial, active, past_due, suspended) ✅
- Deneme süresi kontrolü ✅
- Ödeme gecikmesi kontrolü ✅
- Middleware'i `src/middleware.ts` dosyasına entegre etme ✅
- Test etme

#### 2.2.2. Özellik Erişim Kontrolü Hook Geliştirme ✅
- `src/hooks/useSubscriptionFeatures.ts` dosyasını oluşturma ✅
- Abonelik planına göre özellikleri yükleme ✅
- Özellik durumlarını (maxStaff, hasAnalytics, hasFinance, hasCustomDomain) belirleme ✅
- Hook'u test etme

### 2.3. Bildirim Sistemi Entegrasyonu ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** 2.1. Abonelik Yönetimi API'leri

#### 2.3.1. Abonelik Bildirimleri Trigger'ı Geliştirme ✅
- SQL script hazırlama: `create_subscription_notification()` fonksiyonunu oluşturma ✅
- Farklı abonelik durumları için bildirim içeriklerini tanımlama ✅
- Trigger'ı `salon_subscriptions` tablosuna bağlama ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 2.3.2. Deneme Süresi Bitimine Yaklaşma Bildirimi Geliştirme ✅
- `src/lib/cron/subscription-reminders.ts` dosyasını oluşturma ✅
- Deneme süresi yaklaşan abonelikleri bulma ✅
- Bildirim oluşturma ✅
- Cron job'ı ayarlama (Supabase Edge Functions veya harici bir servis kullanarak)
- Test etme

## 3. Frontend Bileşenleri Geliştirme

### 3.1. Salon Sahibi Abonelik Paneli ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 3 gün
- **Bağımlılıklar:** 2.1. Abonelik Yönetimi API'leri

#### 3.1.1. Abonelik Durumu Sayfası Geliştirme ✅
- `src/app/dashboard/subscription/page.tsx` dosyasını oluşturma ✅
- Abonelik durumu görüntüleme bileşenini geliştirme ✅
- Plan karşılaştırma bileşenini geliştirme ✅
- Abonelik durumuna göre uyarı mesajlarını gösterme ✅
- Responsive tasarım uygulaması ✅
- Test etme

#### 3.1.2. Plan Yükseltme Sayfası Geliştirme ✅
- `src/app/dashboard/subscription/upgrade/page.tsx` dosyasını oluşturma ✅
- Plan seçimi ve ödeme döngüsü (aylık/yıllık) seçimi bileşenlerini geliştirme ✅
- Ödeme özeti bileşenini geliştirme ✅
- Yükseltme işlemi için API entegrasyonu ✅
- Responsive tasarım uygulaması ✅
- Test etme

#### 3.1.3. Ödeme Geçmişi Sayfası Geliştirme ✅
- `src/app/dashboard/subscription/history/page.tsx` dosyasını oluşturma ✅
- Ödeme geçmişi tablosunu geliştirme ✅
- Ödeme durumu gösterimini geliştirme ✅
- Responsive tasarım uygulaması ✅
- Test etme

### 3.2. Admin Paneli ✅
- **Zorluk:** Zor
- **Tahmini Süre:** 4 gün
- **Bağımlılıklar:** 2.1. Abonelik Yönetimi API'leri

#### 3.2.1. Admin Abonelik Yönetimi Sayfası Geliştirme ✅
- `src/app/admin/subscriptions/page.tsx` dosyasını oluşturma ✅
- Admin yetkisi kontrolü ✅
- Abonelik listesi tablosunu geliştirme ✅
- Arama ve filtreleme özelliklerini geliştirme ✅
- Sayfalama özelliğini geliştirme ✅
- Responsive tasarım uygulaması ✅
- Test etme

#### 3.2.2. Abonelik Detay Sayfası Geliştirme ✅
- `src/app/admin/subscriptions/[id]/page.tsx` dosyasını oluşturma ✅
- Abonelik detaylarını görüntüleme bileşenini geliştirme ✅
- Ödeme geçmişini görüntüleme bileşenini geliştirme ✅
- Responsive tasarım uygulaması ✅
- Test etme

#### 3.2.3. Abonelik Düzenleme Sayfası Geliştirme ✅
- `src/app/admin/subscriptions/[id]/edit/page.tsx` dosyasını oluşturma ✅
- Abonelik düzenleme formunu geliştirme ✅
- Abonelik durumu değiştirme özelliğini geliştirme ✅
- Abonelik planı değiştirme özelliğini geliştirme ✅
- Responsive tasarım uygulaması ✅
- Test etme

#### 3.2.4. Ödeme Ekleme Sayfası Geliştirme ✅
- `src/app/admin/subscriptions/[id]/payment/page.tsx` dosyasını oluşturma ✅
- Ödeme ekleme formunu geliştirme ✅
- Manuel ödeme kaydı oluşturma özelliğini geliştirme ✅
- Responsive tasarım uygulaması ✅
- Test etme

## 4. Özellik Erişim Kontrolü Geliştirme

### 4.1. Personel Sayısı Kısıtlaması ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** 2.2.2. Özellik Erişim Kontrolü Hook Geliştirme

#### 4.1.1. Personel Ekleme Kontrolü ✅
- `src/app/dashboard/staff/page.tsx` dosyasını güncelleme ✅
- `useSubscriptionFeatures` hook'unu entegre etme ✅
- Personel sayısı kontrolü ekleyerek kısıtlama uygulama ✅
- Kısıtlama mesajlarını geliştirme ✅
- Test etme

#### 4.1.2. Personel Listesi UI Güncellemesi ✅
- Personel listesi sayfasına abonelik planı bilgisi ekleme ✅
- Maksimum personel sayısı gösterimi ekleme ✅
- Plan yükseltme CTA (Call to Action) ekleme ✅
- Test etme ✅

### 4.2. Özellik Kısıtlamaları ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** 2.2.2. Özellik Erişim Kontrolü Hook Geliştirme

#### 4.2.1. Sidebar Menü Kısıtlamaları ✅
- `src/components/custom-app-sidebar.tsx` dosyasını güncelleme ✅
- `useSubscriptionFeatures` hook'unu entegre etme ✅
- Finans özelliği kontrolü ekleyerek menü öğelerini koşullu gösterme ✅
- Analitik özelliği kontrolü ekleyerek menü öğelerini koşullu gösterme ✅
- Test etme ✅

#### 4.2.2. Sayfa Erişim Kısıtlamaları ✅
- Finans sayfası erişim kontrolü ekleme ✅
- Analitik sayfası erişim kontrolü ekleme ✅
- Özel alan adı sayfası erişim kontrolü ekleme ✅
- Erişim reddedildiğinde plan yükseltme sayfasına yönlendirme ✅
- Test etme ✅

#### 4.2.3. UI Bileşenleri Kısıtlamaları ✅
- Dashboard sayfasında özellik kartlarını koşullu gösterme ✅
- Ayarlar sayfasında özellik seçeneklerini koşullu gösterme ✅
- Test etme ✅

## 5. Referans Sistemi Geliştirme

### 5.1. Referans Kodu Yönetimi ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** 2.1.4. Referans Sistemi API'si Geliştirme

#### 5.1.1. Referans Kodu Sayfası Geliştirme ✅
- `src/app/dashboard/referrals/page.tsx` dosyasını oluşturma ✅
- Referans kodu görüntüleme ve kopyalama bileşenini geliştirme ✅
- Referans kullanım istatistikleri bileşenini geliştirme ✅
- Responsive tasarım uygulaması ✅
- Test etme

#### 5.1.2. Referans Faydaları Görüntüleme ✅
- Referans faydalarını görüntüleme bileşenini geliştirme ✅
- Fayda durumu gösterimini geliştirme ✅
- Test etme

### 5.2. Kayıt Sürecine Referans Kodu Entegrasyonu ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** 5.1. Referans Kodu Yönetimi

#### 5.2.1. Kayıt Formuna Referans Kodu Alanı Ekleme ✅
- `src/app/auth/register/page.tsx` dosyasını güncelleme ✅
- Referans kodu giriş alanı ekleme ✅
- Referans kodu doğrulama ✅
- Test etme

#### 5.2.2. Referans Kodu İşleme ✅
- Kayıt işlemi sırasında referans kodunu işleme ✅
- Referans faydalarını uygulama ✅
- Test etme

### 5.3. Referans Sistemi İyileştirmeleri ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** 5.2. Kayıt Sürecine Referans Kodu Entegrasyonu

#### 5.3.1. Salon Oluşturma Sürecinde Referans Kodu Uygulama ✅
- `src/app/dashboard/settings/page.tsx` dosyasını güncelleme ✅
- Salon oluşturma sırasında sessionStorage'dan referans kodunu alma ✅
- Referans kodunu uygulama ve faydaları etkinleştirme ✅
- Test etme

#### 5.3.2. Uzatılmış Deneme Süresi Desteği ✅
- `src/lib/db/subscriptions.ts` dosyasında `createTrialSubscription` fonksiyonunu güncelleme ✅
- Referans ile gelen kullanıcılar için uzatılmış deneme süresi (30 gün) ekleme ✅
- Test etme

#### 5.3.3. Referans Sistemi Kullanıcı Yolculuğu Dokümantasyonu ✅
- Referans veren ve alan kullanıcılar için tam kullanıcı yolculuğunu belgeleme ✅
- Teknik uygulama detaylarını dokümante etme ✅
- Hata durumları ve çözümlerini belgeleme ✅

## 6. Test ve Dokümantasyon

### 6.1. Birim Testleri ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 3 gün
- **Bağımlılıklar:** Tüm geliştirme görevleri

#### 6.1.1. API Testleri ✅
- Abonelik Yönetimi API'leri için birim testleri yazma ✅
- Ödeme Yönetimi API'si için birim testleri yazma ✅
- Referans Sistemi API'si için birim testleri yazma ✅

#### 6.1.2. Hook Testleri ✅
- `useSubscriptionFeatures` hook'u için birim testleri yazma ✅

#### 6.1.3. Middleware Testleri ✅
- Abonelik durumu kontrolü middleware'i için birim testleri yazma ✅

### 6.2. Entegrasyon Testleri
- **Zorluk:** Zor
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** Tüm geliştirme görevleri

#### 6.2.1. Abonelik İşlemleri Testleri
- Deneme süreci akışı testi
- Plan yükseltme akışı testi
- Ödeme işleme akışı testi

#### 6.2.2. Özellik Erişim Kontrolü Testleri
- Personel sayısı kısıtlaması testi
- Özellik kısıtlamaları testi

### 6.3. Dokümantasyon ✅
- **Zorluk:** Kolay
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** Tüm geliştirme görevleri

#### 6.3.1. Teknik Dokümantasyon ✅
- API dokümantasyonu oluşturma ✅
- Veritabanı şeması dokümantasyonu güncelleme ✅
- Abonelik sistemi mimarisi dokümantasyonu oluşturma ✅

#### 6.3.2. Kullanıcı Dokümantasyonu ✅
- Salon sahibi için abonelik yönetimi kılavuzu oluşturma ✅
- Admin için abonelik yönetimi kılavuzu oluşturma ✅

## 7. Uygulama Aşamaları ve Öncelikler

### 7.1. Aşama 1: Temel Abonelik Altyapısı (1 hafta)
- **Öncelik:** Yüksek
- **Görevler:**
  - 1.1. Abonelik Tabloları Oluşturma
  - 1.2. RLS Politikaları Oluşturma
  - 1.3. TypeScript Tip Tanımlamaları Güncelleme
  - 2.1.1. Abonelik Planları API'si Geliştirme
  - 2.1.2. Salon Abonelikleri API'si Güncelleme
  - 3.1.1. Abonelik Durumu Sayfası Geliştirme

### 7.2. Aşama 2: Deneme Süreci ve Plan Yükseltme (1 hafta)
- **Öncelik:** Yüksek
- **Görevler:**
  - 2.1.3. Ödeme Yönetimi API'si Geliştirme
  - 2.2.1. Abonelik Durumu Kontrolü Middleware Geliştirme
  - 3.1.2. Plan Yükseltme Sayfası Geliştirme
  - 3.1.3. Ödeme Geçmişi Sayfası Geliştirme
  - 2.2.2. Özellik Erişim Kontrolü Hook Geliştirme
  - 4.1. Personel Sayısı Kısıtlaması

### 7.3. Aşama 3: Admin Paneli ve Özellik Kısıtlamaları (1 hafta)
- **Öncelik:** Orta
- **Görevler:**
  - 3.2.1. Admin Abonelik Yönetimi Sayfası Geliştirme ✅
  - 3.2.2. Abonelik Detay Sayfası Geliştirme ✅
  - 3.2.3. Abonelik Düzenleme Sayfası Geliştirme ✅
  - 3.2.4. Ödeme Ekleme Sayfası Geliştirme ✅
  - 4.2. Özellik Kısıtlamaları ✅
  - 2.3. Bildirim Sistemi Entegrasyonu ✅

### 7.4. Aşama 4: Referans Sistemi ve Test (1 hafta)
- **Öncelik:** Düşük
- **Görevler:**
  - 2.1.4. Referans Sistemi API'si Geliştirme ✅
  - 5.1. Referans Kodu Yönetimi ✅
  - 5.2. Kayıt Sürecine Referans Kodu Entegrasyonu ✅
  - 6.1. Birim Testleri
  - 6.2. Entegrasyon Testleri
  - 6.3. Dokümantasyon

## 8. Geliştirme Süreci Takibi ✅

### 8.1. Görev Durumu Takibi ✅
- Görevlerin durumunu takip etmek için bir Kanban board oluşturma ✅
- Görevleri "Yapılacak", "Devam Ediyor", "Test Ediliyor", "Tamamlandı" durumlarında izleme ✅
- Haftalık ilerleme raporları oluşturma ✅

### 8.2. Kod İnceleme Süreci ✅
- Her görev için kod incelemesi yapma ✅
- Kod kalitesi ve standartlara uygunluk kontrolü ✅
- Performans ve güvenlik değerlendirmesi ✅

### 8.3. Sürüm Yönetimi ✅
- Geliştirme, test ve üretim ortamları için sürüm yönetimi ✅
- Sürüm numaralandırma standardı belirleme ✅
- Sürüm notları oluşturma ✅

## 9. Referans Sistemi ve Ödeme Yönetimi İyileştirmeleri

### 9.1. Referans Sistemi Değişikliği
- **Zorluk:** Orta
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** 5. Referans Sistemi Geliştirme

#### 9.1.1. Veritabanı Değişiklikleri
- `referral_benefits` tablosuna yeni alanlar ekleme: `discount_amount`, `discount_applied`, `discount_applied_payment_id`
- Mevcut kayıtları güncelleme: `benefit_type` değerini 'referred_discount' olarak güncelleme ve indirim tutarını güncel en düşük plan ücreti olarak ayarlama
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 9.1.2. API Değişiklikleri
- `ReferralBenefit` interface'ini güncelleme
- `applyReferralCode` fonksiyonunu güncelleme (güncel en düşük plan ücretini alacak şekilde)
- Yeni fonksiyonlar ekleme: `checkReferralDiscount`, `applyReferralDiscount`
- İlk ödeme kontrolü ekleme (referans indirimi sadece ilk ödemede uygulanacak)
- Birim testleri yazma ve test etme

#### 9.1.3. Frontend Değişiklikleri
- Referans sistemi açıklamalarını güncelleme
- Referans faydaları görüntüleme bileşenini güncelleme
- Test etme

### 9.2. Admin Ödeme Ekleme Sürecinin İyileştirilmesi
- **Zorluk:** Zor
- **Tahmini Süre:** 3 gün
- **Bağımlılıklar:** 3.2.4. Ödeme Ekleme Sayfası Geliştirme

#### 9.2.1. Veritabanı Değişiklikleri
- `subscription_payments` tablosuna yeni alanlar ekleme: `period_start_date`, `period_end_date`, `original_amount`, `discount_amount`, `discount_type`, `discount_reference_id`
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 9.2.2. API Değişiklikleri
- `SubscriptionPayment` interface'ini güncelleme
- Yeni fonksiyonlar ekleme: `calculatePaymentAmount`, `checkAvailableDiscounts`
- `checkAvailableDiscounts` fonksiyonuna ilk ödeme kontrolü ekleme
- `createSubscriptionPayment` fonksiyonunu güncelleme
- Birim testleri yazma ve test etme

#### 9.2.3. Frontend Değişiklikleri
- Ödeme ekleme formunu güncelleme
- Tarih aralığı seçimi için DatePicker bileşenleri ekleme
- Otomatik tutar hesaplama mantığı ekleme
- İndirim kontrolü ve gösterimi ekleme
- Ödeme özeti bileşeni ekleme
- Test etme
