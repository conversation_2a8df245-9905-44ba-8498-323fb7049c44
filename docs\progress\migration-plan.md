# Customer Data Migration Plan

## Overview
This document outlines the plan to move customer data from the `customers` table to the `appointments` table. After this migration, the `customers` table will be obsolete, and all customer information will be stored directly in the `appointments` table.

## Database Changes
1. Add new columns to the `appointments` table:
   - `fullname` (TEXT)
   - `phonenumber` (TEXT)
   - `email` (TEXT)
2. Add indexes to the new columns:
   - `idx_appointments_fullname` on `fullname`
   - `idx_appointments_phonenumber` on `phonenumber`
3. Make the `customer_id` column nullable
4. Update the foreign key constraint to set NULL on delete
5. Copy data from the `customers` table to the `appointments` table for existing appointments

## Code Changes
1. Update TypeScript types in `src/lib/db/types.ts`:
   - Update the `Appointment` interface to include the new fields
   - Update the `AppointmentInsert` and `AppointmentUpdate` types
2. Update database functions in `src/lib/db/appointments.ts`:
   - Update functions that create or update appointments
   - Update queries that fetch appointment data
3. Update frontend components:
   - `src/components/appointment-form.tsx`: Collect customer info directly
   - `src/components/booking-form.tsx`: Store customer info directly in the appointment
   - Update any components that display appointment data

## Migration Steps
1. Apply the database changes using the SQL migration script
2. Update the TypeScript types and database functions
3. Update the frontend components
4. Test the changes to ensure everything works correctly
5. Deploy the changes to production

## Future Considerations
- The `customers` table will be kept for now but will be deprecated
- Consider removing the `customers` table in a future update once all systems have been updated to use the new schema

## Progress Tracking
- [x] Create SQL migration script
- [x] Execute SQL migration in Supabase
- [x] Update TypeScript types
- [x] Update appointment-form.tsx
- [x] Update booking-form.tsx
- [x] Update any components that display appointment data
- [x] Test the changes
- [x] Deploy to production
