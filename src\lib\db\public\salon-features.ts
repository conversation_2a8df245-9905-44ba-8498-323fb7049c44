import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Bir salonun ürün yönetimi özelliğine sahip olup olmadığını ve en az bir ürüne sahip olup olmadığını kontrol eder
 * Her iki ko<PERSON> da sağlanıyorsa true, aks<PERSON> false döndürür
 */
export async function checkSalonHasProductsFeature(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('check_salon_has_products_feature', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
