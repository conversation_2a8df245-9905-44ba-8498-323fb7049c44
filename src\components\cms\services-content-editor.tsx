"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Sc<PERSON><PERSON>, Sparkles } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"

import { useContent } from "@/contexts/ContentContext"

interface ServicesContentEditorProps {
  onContentChange?: () => void
}

export function ServicesContentEditor({ onContentChange }: ServicesContentEditorProps) {
  const { getContentValue, updateContentValue } = useContent()
  
  // Form state
  const [formData, setFormData] = useState({
    badgeText: '',
    titleLine1: '',
    titleLine2: '',
    description: '',
    ctaTitle: '',
    ctaDescription: '',
    ctaButtonText: ''
  })

  // Load initial data
  useEffect(() => {
    setFormData({
      badgeText: getContentValue('services', 'badge_text', 'Hizmetlerimiz'),
      titleLine1: getContentValue('services', 'title_line1', 'Profesyonel'),
      titleLine2: getContentValue('services', 'title_line2', 'Berber Hizmetleri'),
      description: getContentValue('services', 'description', 'Uzman ekibimizle size özel tasarlanmış hizmetler. Modern teknikler ve kaliteli ürünlerle tarzınızı yansıtın.'),
      ctaTitle: getContentValue('services', 'cta_title', 'Özel İhtiyaçlarınız mı Var?'),
      ctaDescription: getContentValue('services', 'cta_description', 'Listelenen hizmetler dışında özel talepleriniz için bizimle iletişime geçin. Size özel çözümler sunmaktan mutluluk duyarız.'),
      ctaButtonText: getContentValue('services', 'cta_button_text', 'İletişime Geç')
    })
  }, [getContentValue])

  // Handle input changes
  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }))
    
    // Map form keys to content keys
    const contentKeyMap: Record<string, string> = {
      badgeText: 'badge_text',
      titleLine1: 'title_line1',
      titleLine2: 'title_line2',
      description: 'description',
      ctaTitle: 'cta_title',
      ctaDescription: 'cta_description',
      ctaButtonText: 'cta_button_text'
    }

    const contentKey = contentKeyMap[key]
    if (contentKey) {
      updateContentValue('services', contentKey, value)
      onContentChange?.()
    }
  }

  return (
    <div className="space-y-6">
      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Ana İçerik</CardTitle>
          <CardDescription>
            Hizmetler bölümünün ana başlık, açıklama ve rozet metinleri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label htmlFor="badgeText">Rozet Metni</Label>
              <Input
                id="badgeText"
                value={formData.badgeText}
                onChange={(e) => handleInputChange('badgeText', e.target.value)}
                placeholder="Hizmetlerimiz"
              />
              <p className="text-xs text-muted-foreground">
                Bölümün üst kısmında görünen küçük rozet metni
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="titleLine1">Ana Başlık (1. Satır)</Label>
                <Input
                  id="titleLine1"
                  value={formData.titleLine1}
                  onChange={(e) => handleInputChange('titleLine1', e.target.value)}
                  placeholder="Profesyonel"
                />
                <p className="text-xs text-muted-foreground">
                  Ana başlığın ilk satırı (normal renk)
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="titleLine2">Ana Başlık (2. Satır)</Label>
                <Input
                  id="titleLine2"
                  value={formData.titleLine2}
                  onChange={(e) => handleInputChange('titleLine2', e.target.value)}
                  placeholder="Berber Hizmetleri"
                />
                <p className="text-xs text-muted-foreground">
                  Ana başlığın ikinci satırı (gradient renk)
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Uzman ekibimizle size özel tasarlanmış hizmetler..."
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                Ana başlığın altında görünen açıklama metni
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bottom CTA Section */}
      <Card>
        <CardHeader>
          <CardTitle>Alt Çağrı Bölümü (CTA)</CardTitle>
          <CardDescription>
            Hizmetler listesinin altında görünen özel talep bölümü
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label htmlFor="ctaTitle">CTA Başlığı</Label>
              <Input
                id="ctaTitle"
                value={formData.ctaTitle}
                onChange={(e) => handleInputChange('ctaTitle', e.target.value)}
                placeholder="Özel İhtiyaçlarınız mı Var?"
              />
              <p className="text-xs text-muted-foreground">
                Alt bölümün ana başlığı
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ctaDescription">CTA Açıklaması</Label>
              <Textarea
                id="ctaDescription"
                value={formData.ctaDescription}
                onChange={(e) => handleInputChange('ctaDescription', e.target.value)}
                placeholder="Listelenen hizmetler dışında özel talepleriniz için..."
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                CTA başlığının altında görünen açıklama metni
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ctaButtonText">Buton Metni</Label>
              <Input
                id="ctaButtonText"
                value={formData.ctaButtonText}
                onChange={(e) => handleInputChange('ctaButtonText', e.target.value)}
                placeholder="İletişime Geç"
              />
              <p className="text-xs text-muted-foreground">
                İletişim bölümüne yönlendiren butonun metni
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Section */}
      <Card>
        <CardHeader>
          <CardTitle>Önizleme</CardTitle>
          <CardDescription>
            Hizmetler bölümünün nasıl görüneceğinin önizlemesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6 p-6 bg-muted/20 rounded-lg">
            {/* Header Preview */}
            <div className="text-center space-y-4">
              <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
                <Scissors className="w-4 h-4" />
                <span>{formData.badgeText}</span>
              </div>

              <h2 className="text-2xl md:text-3xl font-bold text-foreground">
                {formData.titleLine1}
                <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  {formData.titleLine2}
                </span>
              </h2>

              <p className="text-muted-foreground max-w-2xl mx-auto">
                {formData.description}
              </p>
            </div>

            <Separator />

            {/* CTA Preview */}
            <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl p-6 border border-primary/20">
              <h3 className="text-xl font-bold text-foreground mb-3">
                {formData.ctaTitle}
              </h3>
              <p className="text-muted-foreground mb-4">
                {formData.ctaDescription}
              </p>
              <button className="inline-flex items-center px-4 py-2 bg-background border border-border rounded-lg font-medium text-sm">
                {formData.ctaButtonText}
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
