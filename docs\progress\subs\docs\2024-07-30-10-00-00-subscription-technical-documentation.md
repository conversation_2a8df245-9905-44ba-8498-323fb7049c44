# SalonFlow Abonelik Sistemi Teknik Dokümantasyonu

**Tarih:** 30 Temmuz 2024
**Saat:** 10:00

## 1. Veritabanı Şeması

### 1.1. subscription_plans Tablosu

| Sütun | Tip | Açıklama |
|-------|-----|----------|
| id | UUID | Birincil anahtar, otomatik oluşturulur |
| name | TEXT | Plan adı (Solo, Small Team, Pro Salon) |
| price_monthly | DECIMAL(10, 2) | Aylık fiyat |
| price_yearly | DECIMAL(10, 2) | Yıllık fiyat |
| max_staff | INTEGER | İzin verilen maksimum personel sayısı |
| features | JSONB | Plan özellikleri (analytics, finance, custom_domain) |
| created_at | TIMESTAMP WITH TIME ZONE | Oluşturulma tarihi |

### 1.2. salon_subscriptions Tablosu

| Sütun | Tip | Açıklama |
|-------|-----|----------|
| id | UUID | Birincil anahtar, otomatik oluşturulur |
| salon_id | UUID | Salon ID'si, salons tablosuna referans |
| plan_id | UUID | Plan ID'si, subscription_plans tablosuna referans |
| plan | TEXT | Eski alan, geriye uyumluluk için (basic, standard, premium) |
| status | TEXT | Abonelik durumu (trial, active, past_due, suspended, cancelled) |
| start_date | TIMESTAMP WITH TIME ZONE | Abonelik başlangıç tarihi |
| end_date | TIMESTAMP WITH TIME ZONE | Abonelik bitiş tarihi (opsiyonel) |
| trial_end_date | TIMESTAMP WITH TIME ZONE | Deneme süresi bitiş tarihi (opsiyonel) |
| payment_method | TEXT | Ödeme yöntemi (bank_transfer, credit_card) |
| is_yearly | BOOLEAN | Yıllık ödeme yapılıp yapılmadığı |
| is_active | BOOLEAN | Aboneliğin aktif olup olmadığı |
| created_at | TIMESTAMP WITH TIME ZONE | Oluşturulma tarihi |
| updated_at | TIMESTAMP WITH TIME ZONE | Güncellenme tarihi |

### 1.3. subscription_payments Tablosu

| Sütun | Tip | Açıklama |
|-------|-----|----------|
| id | UUID | Birincil anahtar, otomatik oluşturulur |
| subscription_id | UUID | Abonelik ID'si, salon_subscriptions tablosuna referans |
| amount | DECIMAL(10, 2) | Ödeme tutarı |
| payment_date | TIMESTAMP WITH TIME ZONE | Ödeme tarihi |
| payment_method | TEXT | Ödeme yöntemi (bank_transfer, credit_card) |
| status | TEXT | Ödeme durumu (pending, completed, failed, refunded) |
| transaction_id | TEXT | Dış ödeme sistemi işlem ID'si (opsiyonel) |
| notes | TEXT | Ödeme notları (opsiyonel) |
| created_at | TIMESTAMP WITH TIME ZONE | Oluşturulma tarihi |
| updated_at | TIMESTAMP WITH TIME ZONE | Güncellenme tarihi |

### 1.4. referral_codes Tablosu

| Sütun | Tip | Açıklama |
|-------|-----|----------|
| id | UUID | Birincil anahtar, otomatik oluşturulur |
| salon_id | UUID | Salon ID'si, salons tablosuna referans |
| code | TEXT | Benzersiz referans kodu |
| is_active | BOOLEAN | Kodun aktif olup olmadığı |
| created_at | TIMESTAMP WITH TIME ZONE | Oluşturulma tarihi |
| updated_at | TIMESTAMP WITH TIME ZONE | Güncellenme tarihi |

### 1.5. referral_benefits Tablosu

| Sütun | Tip | Açıklama |
|-------|-----|----------|
| id | UUID | Birincil anahtar, otomatik oluşturulur |
| referrer_salon_id | UUID | Referans veren salon ID'si |
| referred_salon_id | UUID | Referans alan salon ID'si |
| code_id | UUID | Referans kodu ID'si, referral_codes tablosuna referans |
| benefit_type | TEXT | Fayda tipi (discount, extension) |
| benefit_amount | INTEGER | Fayda miktarı (gün veya yüzde) |
| is_applied | BOOLEAN | Faydanın uygulanıp uygulanmadığı |
| applied_at | TIMESTAMP WITH TIME ZONE | Uygulanma tarihi (opsiyonel) |
| created_at | TIMESTAMP WITH TIME ZONE | Oluşturulma tarihi |

## 2. İlişki Şeması

```
subscription_plans <-- salon_subscriptions --> salons
                        |
                        v
                  subscription_payments

salons <-- referral_codes
  |         |
  |         v
  +-----> referral_benefits
```

## 3. Abonelik Sistemi Mimarisi

### 3.1. Genel Mimari

SalonFlow abonelik sistemi, aşağıdaki bileşenlerden oluşmaktadır:

1. **Veritabanı Tabloları**: Abonelik planları, salon abonelikleri, ödemeler ve referans sistemi için tablolar.
2. **Backend API'leri**: Abonelik işlemlerini yönetmek için TypeScript fonksiyonları.
3. **Frontend Bileşenleri**: Kullanıcı arayüzü için React bileşenleri.
4. **Middleware**: Abonelik durumu kontrolü için Next.js middleware.
5. **Hooks**: Abonelik özelliklerini kontrol etmek için React hooks.
6. **Bildirim Sistemi**: Abonelik durumu değişikliklerini bildirmek için trigger'lar.

### 3.2. Veri Akışı

```
+----------------+     +-------------------+     +------------------+
| Kullanıcı      |     | Frontend          |     | Backend API'leri |
| Etkileşimi     | --> | Bileşenleri       | --> | (TypeScript      |
| (UI)           |     | (React)           |     | Fonksiyonları)   |
+----------------+     +-------------------+     +------------------+
                                                          |
                                                          v
                                                 +------------------+
                                                 | Veritabanı       |
                                                 | (Supabase)       |
                                                 +------------------+
                                                          |
                                                          v
                                                 +------------------+
                                                 | Bildirim         |
                                                 | Sistemi          |
                                                 | (Trigger'lar)    |
                                                 +------------------+
```

### 3.3. Abonelik Yaşam Döngüsü

1. **Deneme Süreci**:
   - Salon kaydı sırasında otomatik olarak 14 günlük deneme aboneliği oluşturulur.
   - Deneme süresi boyunca tüm özellikler kullanılabilir.
   - Deneme süresinin bitimine 3 gün kala bildirim gönderilir.

2. **Abonelik Aktivasyonu**:
   - Kullanıcı bir plan seçer ve ödeme yapar.
   - Ödeme onaylandıktan sonra abonelik "active" durumuna geçer.
   - Abonelik durumu ve plan bilgileri veritabanında güncellenir.

3. **Abonelik Yenileme**:
   - Abonelik süresi dolmadan önce bildirim gönderilir.
   - Kullanıcı ödeme yapar ve abonelik yenilenir.
   - Ödeme yapılmazsa abonelik "past_due" durumuna geçer.

4. **Ödeme Gecikmesi**:
   - Ödeme 7 gün gecikirse kullanıcıya hatırlatma bildirimleri gönderilir.
   - 7 günden fazla gecikme durumunda abonelik "suspended" durumuna geçer.
   - Askıya alınan abonelikler için özellik erişimi kısıtlanır.

5. **Abonelik İptali**:
   - Kullanıcı aboneliğini iptal edebilir.
   - İptal edilen abonelikler "cancelled" durumuna geçer.
   - İptal sonrası veriler 1 ay saklanır, sonra silinir.

### 3.4. Güvenlik Mekanizmaları

1. **RLS Politikaları**:
   - Her tablo için Row Level Security politikaları tanımlanmıştır.
   - Salon sahipleri sadece kendi aboneliklerini görebilir.
   - Admin kullanıcılar tüm abonelikleri yönetebilir.

2. **Yetkilendirme Kontrolleri**:
   - Frontend'de abonelik durumuna göre UI kısıtlamaları uygulanır.
   - Backend'de abonelik durumu ve plan özellikleri kontrol edilir.
   - Middleware ile yetkisiz erişimler engellenir.

## 4. API Dokümantasyonu

### 4.1. Abonelik Planları API'si (`src/lib/db/subscription-plans.ts`)

#### `getSubscriptionPlans()`
- **Açıklama:** Tüm abonelik planlarını getirir.
- **Parametreler:** Yok
- **Dönüş Değeri:** `SubscriptionPlan[]` - Abonelik planları dizisi
- **Örnek Kullanım:**
  ```typescript
  const plans = await getSubscriptionPlans();
  console.log(plans); // Tüm abonelik planlarını gösterir
  ```

#### `getSubscriptionPlanById(planId: string)`
- **Açıklama:** Belirli bir ID'ye sahip abonelik planını getirir.
- **Parametreler:**
  - `planId` (string): Abonelik planı ID'si
- **Dönüş Değeri:** `SubscriptionPlan | null` - Abonelik planı veya bulunamazsa null
- **Örnek Kullanım:**
  ```typescript
  const plan = await getSubscriptionPlanById('plan-123');
  if (plan) {
    console.log(plan.name); // Plan adını gösterir
  }
  ```

#### `planHasFeature(plan: SubscriptionPlan | null, featureName: string)`
- **Açıklama:** Bir planın belirli bir özelliğe sahip olup olmadığını kontrol eder.
- **Parametreler:**
  - `plan` (SubscriptionPlan | null): Kontrol edilecek abonelik planı
  - `featureName` (string): Özellik adı
- **Dönüş Değeri:** `boolean` - Plan özelliğe sahipse true, değilse false
- **Örnek Kullanım:**
  ```typescript
  const hasAnalytics = planHasFeature(plan, 'analytics');
  if (hasAnalytics) {
    // Analitik özelliğine sahip planlar için işlemler
  }
  ```

#### `getPlanMaxStaff(plan: SubscriptionPlan | null)`
- **Açıklama:** Bir planın izin verdiği maksimum personel sayısını döndürür.
- **Parametreler:**
  - `plan` (SubscriptionPlan | null): Abonelik planı
- **Dönüş Değeri:** `number` - Maksimum personel sayısı
- **Örnek Kullanım:**
  ```typescript
  const maxStaff = getPlanMaxStaff(plan);
  if (currentStaffCount >= maxStaff) {
    // Personel sayısı limitine ulaşıldığında yapılacak işlemler
  }
  ```

#### `calculateYearlyPrice(monthlyPrice: number)`
- **Açıklama:** Aylık fiyattan yıllık fiyatı hesaplar (yıllık ödemede %10 indirim).
- **Parametreler:**
  - `monthlyPrice` (number): Aylık fiyat
- **Dönüş Değeri:** `number` - Yıllık fiyat
- **Örnek Kullanım:**
  ```typescript
  const yearlyPrice = calculateYearlyPrice(1000);
  console.log(yearlyPrice); // 10800 (1000 * 12 * 0.9)
  ```

### 4.2. Salon Abonelikleri API'si (`src/lib/db/subscriptions.ts`)

#### `getActiveSalonSubscription(salonId: string)`
- **Açıklama:** Bir salonun aktif aboneliğini getirir.
- **Parametreler:**
  - `salonId` (string): Salon ID'si
- **Dönüş Değeri:** `SalonSubscription | null` - Aktif abonelik veya bulunamazsa null
- **Örnek Kullanım:**
  ```typescript
  const subscription = await getActiveSalonSubscription('salon-123');
  if (subscription) {
    console.log(subscription.status); // Abonelik durumunu gösterir
  }
  ```

#### `createSalonSubscription(subscription: SalonSubscriptionInsert)`
- **Açıklama:** Yeni bir salon aboneliği oluşturur.
- **Parametreler:**
  - `subscription` (SalonSubscriptionInsert): Oluşturulacak abonelik bilgileri
- **Dönüş Değeri:** `SalonSubscription` - Oluşturulan abonelik
- **Örnek Kullanım:**
  ```typescript
  const newSubscription = await createSalonSubscription({
    salon_id: 'salon-123',
    plan_id: 'plan-456',
    status: 'trial',
    is_active: true,
    start_date: new Date().toISOString(),
    trial_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
    is_yearly: false
  });
  ```

#### `updateSalonSubscription(subscription: SalonSubscriptionUpdate)`
- **Açıklama:** Mevcut bir salon aboneliğini günceller.
- **Parametreler:**
  - `subscription` (SalonSubscriptionUpdate): Güncellenecek abonelik bilgileri
- **Dönüş Değeri:** `SalonSubscription` - Güncellenen abonelik
- **Örnek Kullanım:**
  ```typescript
  const updatedSubscription = await updateSalonSubscription({
    id: 'subscription-123',
    status: 'active',
    is_yearly: true
  });
  ```

#### `createTrialSubscription(salonId: string, planId: string)`
- **Açıklama:** Bir salon için deneme aboneliği oluşturur.
- **Parametreler:**
  - `salonId` (string): Salon ID'si
  - `planId` (string): Plan ID'si
- **Dönüş Değeri:** `SalonSubscription` - Oluşturulan deneme aboneliği
- **Örnek Kullanım:**
  ```typescript
  const trialSubscription = await createTrialSubscription('salon-123', 'plan-456');
  ```

#### `upgradeSubscription(subscriptionId: string, newPlanId: string, isYearly: boolean)`
- **Açıklama:** Bir aboneliği yeni bir plana yükseltir.
- **Parametreler:**
  - `subscriptionId` (string): Abonelik ID'si
  - `newPlanId` (string): Yeni plan ID'si
  - `isYearly` (boolean): Yıllık ödeme yapılıp yapılmayacağı
- **Dönüş Değeri:** `SalonSubscription` - Güncellenen abonelik
- **Örnek Kullanım:**
  ```typescript
  const upgradedSubscription = await upgradeSubscription('subscription-123', 'plan-789', true);
  ```

#### `isTrialExpired(salonId: string)`
- **Açıklama:** Bir salonun deneme süresinin dolup dolmadığını kontrol eder.
- **Parametreler:**
  - `salonId` (string): Salon ID'si
- **Dönüş Değeri:** `boolean` - Deneme süresi dolduysa true, dolmadıysa false
- **Örnek Kullanım:**
  ```typescript
  const expired = await isTrialExpired('salon-123');
  if (expired) {
    // Deneme süresi dolduğunda yapılacak işlemler
  }
  ```

### 4.3. Ödeme Yönetimi API'si (`src/lib/db/subscription-payments.ts`)

#### `getSubscriptionPayments(subscriptionId: string)`
- **Açıklama:** Bir aboneliğe ait tüm ödemeleri getirir.
- **Parametreler:**
  - `subscriptionId` (string): Abonelik ID'si
- **Dönüş Değeri:** `SubscriptionPayment[]` - Ödeme kayıtları dizisi
- **Örnek Kullanım:**
  ```typescript
  const payments = await getSubscriptionPayments('subscription-123');
  console.log(payments.length); // Ödeme sayısını gösterir
  ```

#### `createSubscriptionPayment(payment: SubscriptionPaymentInsert)`
- **Açıklama:** Yeni bir abonelik ödemesi oluşturur.
- **Parametreler:**
  - `payment` (SubscriptionPaymentInsert): Oluşturulacak ödeme bilgileri
- **Dönüş Değeri:** `SubscriptionPayment` - Oluşturulan ödeme kaydı
- **Örnek Kullanım:**
  ```typescript
  const newPayment = await createSubscriptionPayment({
    subscription_id: 'subscription-123',
    amount: 750,
    payment_date: new Date().toISOString(),
    payment_method: 'bank_transfer',
    status: 'completed',
    notes: 'Aylık ödeme'
  });
  ```

#### `updateSubscriptionPayment(payment: SubscriptionPaymentUpdate)`
- **Açıklama:** Mevcut bir ödeme kaydını günceller.
- **Parametreler:**
  - `payment` (SubscriptionPaymentUpdate): Güncellenecek ödeme bilgileri
- **Dönüş Değeri:** `SubscriptionPayment` - Güncellenen ödeme kaydı
- **Örnek Kullanım:**
  ```typescript
  const updatedPayment = await updateSubscriptionPayment({
    id: 'payment-123',
    status: 'completed',
    notes: 'Ödeme onaylandı'
  });
  ```

### 4.4. Referans Sistemi API'si (`src/lib/db/referrals.ts`)

#### `getSalonReferralCode(salonId: string)`
- **Açıklama:** Bir salonun referans kodunu getirir.
- **Parametreler:**
  - `salonId` (string): Salon ID'si
- **Dönüş Değeri:** `ReferralCode | null` - Referans kodu veya bulunamazsa null
- **Örnek Kullanım:**
  ```typescript
  const referralCode = await getSalonReferralCode('salon-123');
  if (referralCode) {
    console.log(referralCode.code); // Referans kodunu gösterir
  }
  ```

#### `createReferralCode(salonId: string)`
- **Açıklama:** Bir salon için yeni bir referans kodu oluşturur.
- **Parametreler:**
  - `salonId` (string): Salon ID'si
- **Dönüş Değeri:** `ReferralCode` - Oluşturulan referans kodu
- **Örnek Kullanım:**
  ```typescript
  const newReferralCode = await createReferralCode('salon-123');
  ```

#### `applyReferralCode(code: string, newSalonId: string)`
- **Açıklama:** Yeni bir salon için referans kodunu uygular.
- **Parametreler:**
  - `code` (string): Referans kodu
  - `newSalonId` (string): Yeni salon ID'si
- **Dönüş Değeri:** `ReferralBenefit | null` - Oluşturulan fayda kaydı veya hata durumunda null
- **Örnek Kullanım:**
  ```typescript
  const benefit = await applyReferralCode('REF123', 'new-salon-456');
  if (benefit) {
    console.log('Referans kodu başarıyla uygulandı');
  }
  ```
