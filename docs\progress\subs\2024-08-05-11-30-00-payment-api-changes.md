# Ödeme Yönetimi API Değişiklikleri

Bu belge, ödeme yönetimi iyileştirmeleri için yapılması gereken API değişikliklerini içermektedir.

## 1. `src/lib/db/types.ts` Dosyası Değişiklikleri

```typescript
// Subscription payment type
export interface SubscriptionPayment {
  id: string;
  subscription_id: string;
  amount: number;
  original_amount?: number; // Yeni alan
  discount_amount?: number; // Yeni alan
  discount_type?: string; // Yeni alan
  discount_reference_id?: string; // Yeni alan
  period_start_date?: string; // Yeni alan
  period_end_date?: string; // Yeni alan
  payment_date: string;
  payment_method?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  invoice_number?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type SubscriptionPaymentInsert = Omit<SubscriptionPayment, 'id' | 'created_at' | 'updated_at'>;
export type SubscriptionPaymentUpdate = Partial<Omit<SubscriptionPayment, 'id' | 'created_at' | 'updated_at'>> & { id: string };
```

## 2. `src/lib/db/subscription-payments.ts` Dosyası Değişiklikleri

### 2.1. Yeni Fonksiyon: `calculatePaymentAmount`

```typescript
/**
 * Calculate payment amount based on subscription plan and period
 */
export async function calculatePaymentAmount(
  subscriptionId: string,
  periodStartDate: string,
  periodEndDate: string
) {
  // Abonelik bilgilerini al
  const { data: subscription, error: subError } = await supabase
    .from('salon_subscriptions')
    .select('*, plans(*)')
    .eq('id', subscriptionId)
    .single();

  if (subError) throw subError;

  // Tarih aralığını hesapla
  const startDate = new Date(periodStartDate);
  const endDate = new Date(periodEndDate);
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Aylık fiyat
  const monthlyPrice = subscription.plans.price_monthly;

  // Gün başına fiyat
  const dailyPrice = monthlyPrice / 30;

  // Toplam fiyat
  const totalAmount = Math.round(dailyPrice * daysDiff);

  return {
    originalAmount: totalAmount,
    planName: subscription.plans.name,
    planPrice: monthlyPrice,
    dayCount: daysDiff
  };
}
```

### 2.2. Yeni Fonksiyon: `checkAvailableDiscounts`

```typescript
/**
 * Check available discounts for a salon
 */
export async function checkAvailableDiscounts(salonId: string) {
  // Referans indirimi kontrolü
  const referralDiscount = await referrals.checkReferralDiscount(salonId);

  // Salon için ödeme yapılmış mı kontrol et
  const { data: payments, error: paymentsError } = await supabase
    .from('subscription_payments')
    .select('id')
    .eq('subscription_id', subscriptionId)
    .eq('status', 'completed')
    .limit(1);

  if (paymentsError) throw paymentsError;

  // Eğer daha önce ödeme yapılmamışsa ve referans indirimi varsa
  if (payments.length === 0 && referralDiscount) {
    return {
      hasDiscount: true,
      discountType: 'referral',
      discountAmount: referralDiscount.discount_amount,
      discountId: referralDiscount.id
    };
  }

  return {
    hasDiscount: false
  };
}
```

### 2.3. `createSubscriptionPayment` Fonksiyonu Güncelleme

```typescript
/**
 * Create a new subscription payment
 */
export async function createSubscriptionPayment(payment: SubscriptionPaymentInsert) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .insert(payment)
    .select()
    .single();

  if (error) throw error;

  // Eğer referans indirimi uygulandıysa, referans faydasını güncelle
  if (payment.discount_type === 'referral' && payment.discount_reference_id) {
    await referrals.applyReferralDiscount(payment.discount_reference_id, data.id);
  }

  return data as SubscriptionPayment;
}
```

## 3. Admin Ödeme Ekleme Sayfası İyileştirmeleri

### 3.1. `src/app/admin/subscriptions/[id]/payment/page.tsx` Dosyası Değişiklikleri

```tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { subscriptions, subscriptionPayments } from "@/lib/db";
import { formatCurrency } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

export default function AddPaymentPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [subscription, setSubscription] = useState<any>(null);
  const [periodStartDate, setPeriodStartDate] = useState<Date | undefined>(new Date());
  const [periodEndDate, setPeriodEndDate] = useState<Date | undefined>(
    new Date(new Date().setMonth(new Date().getMonth() + 1))
  );
  const [paymentDate, setPaymentDate] = useState<Date | undefined>(new Date());
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [notes, setNotes] = useState("");
  const [calculatedAmount, setCalculatedAmount] = useState(0);
  const [originalAmount, setOriginalAmount] = useState(0);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [discountInfo, setDiscountInfo] = useState<any>(null);
  const [calculating, setCalculating] = useState(false);

  // Abonelik bilgilerini yükle
  useEffect(() => {
    const loadSubscription = async () => {
      try {
        const data = await subscriptions.getSubscriptionById(params.id);
        setSubscription(data);
      } catch (error) {
        console.error("Abonelik yüklenirken hata:", error);
        toast.error("Abonelik bilgileri yüklenemedi.");
      }
    };

    loadSubscription();
  }, [params.id]);

  // Tarih değiştiğinde ödeme tutarını hesapla
  useEffect(() => {
    const calculateAmount = async () => {
      if (!periodStartDate || !periodEndDate || !subscription) return;

      try {
        setCalculating(true);

        // Ödeme tutarını hesapla
        const result = await subscriptionPayments.calculatePaymentAmount(
          params.id,
          periodStartDate.toISOString().split('T')[0],
          periodEndDate.toISOString().split('T')[0]
        );

        setOriginalAmount(result.originalAmount);

        // İndirimleri kontrol et
        const discounts = await subscriptionPayments.checkAvailableDiscounts(subscription.salon_id);
        setDiscountInfo(discounts);

        if (discounts.hasDiscount) {
          setDiscountAmount(discounts.discountAmount);
          // İndirim tutarı, orijinal tutardan büyükse, ödeme tutarı 0 olur
          const finalAmount = Math.max(0, result.originalAmount - discounts.discountAmount);
          setCalculatedAmount(finalAmount);
        } else {
          setDiscountAmount(0);
          setCalculatedAmount(result.originalAmount);
        }
      } catch (error) {
        console.error("Ödeme tutarı hesaplanırken hata:", error);
        toast.error("Ödeme tutarı hesaplanamadı.");
      } finally {
        setCalculating(false);
      }
    };

    calculateAmount();
  }, [periodStartDate, periodEndDate, subscription, params.id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!subscription || !periodStartDate || !periodEndDate || !paymentDate) {
      toast.error("Lütfen tüm alanları doldurun.");
      return;
    }

    try {
      setLoading(true);

      const paymentData = {
        subscription_id: params.id,
        amount: calculatedAmount,
        original_amount: originalAmount,
        discount_amount: discountAmount,
        period_start_date: periodStartDate.toISOString().split('T')[0],
        period_end_date: periodEndDate.toISOString().split('T')[0],
        payment_date: paymentDate.toISOString().split('T')[0],
        payment_method: "manual",
        status: "completed",
        invoice_number: invoiceNumber || undefined,
        notes: notes || undefined
      };

      // Eğer indirim varsa, indirim bilgilerini ekle
      if (discountInfo?.hasDiscount) {
        paymentData.discount_type = discountInfo.discountType;
        paymentData.discount_reference_id = discountInfo.discountId;
      }

      await subscriptionPayments.createSubscriptionPayment(paymentData);

      // Abonelik durumunu güncelle
      await subscriptions.updateSubscription({
        id: params.id,
        status: "active",
        end_date: periodEndDate.toISOString().split('T')[0]
      });

      toast.success("Ödeme başarıyla eklendi.");
      router.push(`/admin/subscriptions/${params.id}`);
      router.refresh();
    } catch (error) {
      console.error("Ödeme eklenirken hata:", error);
      toast.error("Ödeme eklenirken bir hata oluştu.");
    } finally {
      setLoading(false);
    }
  };
}
```
