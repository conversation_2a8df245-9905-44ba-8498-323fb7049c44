# Sidebar Navigation Groups Implementation

## Overview
Implemented grouped navigation in the sidebar to better organize menu items and reduce clutter, similar to the example provided in the reference image.

## Changes Made

### 1. Added Navigation Group Type
Added a new type definition for navigation groups:

```typescript
// Navigasyon grubu tipi
type NavigationGroup = {
  label: string
  items: NavigationItem[]
}
```

### 2. Reorganized Navigation Items into Logical Groups
Restructured the navigation items into logical groups based on functionality:

For salon owners:
- **Ana Menü**: Core functionality (Appointments, Customers)
- **Salon Yönetimi**: Salon management (Services, Staff)
- **Finans**: Business operations (Finance)
- **Program Yönetimi**: Schedule management (Working Hours, Holidays)
- **Sistem**: System settings (Settings)

For staff:
- **Ana Menü**: Core functionality (Appointments, Customers)
- **Kişisel**: Personal (My Schedule, Profile)

### 3. Updated Sidebar Rendering
Modified the sidebar content to render multiple groups with appropriate spacing:

```tsx
<SidebarContent>
  {navigationGroups.map((group, index) => (
    <SidebarGroup key={group.label} className={index > 0 ? "mt-4" : ""}>
      <SidebarGroupLabel>{group.label}</SidebarGroupLabel>
      <SidebarMenu>
        {group.items.map((item) => (
          <SidebarMenuItem key={item.href}>
            <SidebarMenuButton asChild tooltip={item.title} data-active={item.isActive}>
              <Link href={item.href}>
                <item.icon />
                <span>{item.title}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  ))}
</SidebarContent>
```

## Benefits
- Improved organization of navigation items
- Reduced visual clutter in the sidebar
- Better user experience with logically grouped functionality
- More scalable structure for adding new menu items in the future
