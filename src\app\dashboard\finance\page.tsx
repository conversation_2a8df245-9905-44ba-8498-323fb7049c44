"use client"

import React, { use<PERSON>tate, useEffect } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useUser } from "@/contexts/UserContext"
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"
import { format, subDays } from "date-fns"
import { tr } from "date-fns/locale"
import { BarC<PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts"
import { Plus, ArrowUpRight, ArrowDownRight, Wallet, CreditCard, DollarSign, Banknote, Loader2, Calendar } from "lucide-react"
import { toast } from "sonner"
import { FinanceTransaction } from "@/lib/db/types"
import { financeTransactions } from "@/lib/db"

// Custom hook to detect screen size
function useMediaQuery(query: string) {
  const [matches, setMatches] = React.useState(false)

  React.useEffect(() => {
    const media = window.matchMedia(query)
    if (media.matches !== matches) {
      setMatches(media.matches)
    }

    const listener = () => setMatches(media.matches)
    media.addEventListener("change", listener)
    return () => media.removeEventListener("change", listener)
  }, [matches, query])

  return matches
}

export default function FinancePage() {
  const router = useRouter()
  const { salon } = useUser()
  const salonId = salon?.id

  // Abonelik özelliklerini kontrol et
  const { features, hasFeature } = useSubscriptionFeatures()

  // Check if screen is mobile
  const isMobile = useMediaQuery("(max-width: 640px)")

  // Default date range (last 30 days)
  const [dateRange, setDateRange] = useState({
    from: subDays(new Date(), 30),
    to: new Date(),
  })

  // State for financial data
  const [summary, setSummary] = useState({
    total_income: 0,
    total_expense: 0,
    net_profit: 0
  })
  const [categoryBreakdown, setCategoryBreakdown] = useState<any[]>([])
  const [recentTransactions, setRecentTransactions] = useState<FinanceTransaction[]>([])
  const [transactionCount, setTransactionCount] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  // Finans özelliğine erişim kontrolü
  useEffect(() => {
    if (!hasFeature('finance')) {
      router.push('/dashboard/subscription/upgrade')
      toast.error("Bu özelliği kullanmak için abonelik planınızı yükseltmeniz gerekiyor.")
    }
  }, [hasFeature, router])

  // Fetch data when component mounts or date range changes
  useEffect(() => {
    if (salonId && dateRange.from && dateRange.to && hasFeature('finance')) {
      fetchFinancialData()
    }
  }, [salonId, dateRange, hasFeature])

  // Fetch financial data
  const fetchFinancialData = async () => {
    if (!salonId || !dateRange.from || !dateRange.to) return

    setIsLoading(true)
    try {
      // Format dates for API requests
      const startDate = format(dateRange.from, "yyyy-MM-dd")
      const endDate = format(dateRange.to, "yyyy-MM-dd")

      console.log(`Fetching financial data for salon: ${salonId} from ${startDate} to ${endDate}`)

      // Fetch recent transactions first - this seems to be working
      const transactionsData = await financeTransactions.getFinanceTransactionsInRange(salonId, startDate, endDate)
      console.log(`Retrieved ${transactionsData.length} transactions`)
      setRecentTransactions(transactionsData.slice(0, 5)) // Get only the 5 most recent transactions
      setTransactionCount(transactionsData.length)

      // Fetch financial summary
      try {
        const summaryData = await financeTransactions.getFinancialSummary(salonId, startDate, endDate)
        console.log('Financial summary data after processing:', summaryData)

        if (summaryData) {
          // Ensure we have valid numbers for all fields
          setSummary({
            total_income: Number(summaryData.total_income) || 0,
            total_expense: Number(summaryData.total_expense) || 0,
            net_profit: Number(summaryData.net_profit) || 0
          })
          console.log('Set summary state:', {
            total_income: Number(summaryData.total_income) || 0,
            total_expense: Number(summaryData.total_expense) || 0,
            net_profit: Number(summaryData.net_profit) || 0
          })
        } else {
          console.warn('Summary data is null or undefined')
          setSummary({ total_income: 0, total_expense: 0, net_profit: 0 })
        }
      } catch (summaryError) {
        console.error("Error in summary data:", summaryError)
        toast.error("Finansal özet verileri yüklenirken bir hata oluştu")
        setSummary({ total_income: 0, total_expense: 0, net_profit: 0 })
      }

      // Fetch category breakdown
      try {
        const breakdownData = await financeTransactions.getCategoryBreakdown(salonId, startDate, endDate)
        console.log('Category breakdown data after processing:', breakdownData)

        // Ensure breakdownData is an array and has items
        if (Array.isArray(breakdownData) && breakdownData.length > 0) {
          setCategoryBreakdown(breakdownData)
          console.log('Set category breakdown state with', breakdownData.length, 'items')
        } else {
          console.warn('Category breakdown data is empty or not an array')
          setCategoryBreakdown([])
        }
      } catch (breakdownError) {
        console.error("Error in category breakdown:", breakdownError)
        toast.error("Kategori dağılımı verileri yüklenirken bir hata oluştu")
        setCategoryBreakdown([])
      }
    } catch (error) {
      console.error("Error fetching financial data:", error)
      toast.error("Finansal veriler yüklenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }



  // Filter income and expense categories
  const incomeCategories = categoryBreakdown.filter(cat => cat.category_type === "income")
  const expenseCategories = categoryBreakdown.filter(cat => cat.category_type === "expense")

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Finans Yönetimi</h1>
        </div>
      </header>

      {/* Finans özelliği erişim kontrolü */}
      {!hasFeature('finance') && (
        <Alert className="mb-4">
          <AlertTitle>Abonelik Planı Gerekli</AlertTitle>
          <AlertDescription>
            Finans yönetimi özelliğini kullanmak için abonelik planınızı yükseltmeniz gerekiyor.
            <Link href="/dashboard/subscription/upgrade" className="ml-1 font-medium underline">
              Planınızı yükseltin
            </Link>
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-2">
        <div className="w-full sm:w-auto">
          <DatePickerWithRange
            dateRange={dateRange}
            setDateRange={(range) => {
              if (range?.from && range?.to) {
                setDateRange({ from: range.from, to: range.to });
              }
            }}
          />
        </div>
        <div className="flex w-full sm:w-auto gap-2 justify-between sm:justify-end">
          <Button asChild variant="outline" className="flex-1 sm:flex-none min-h-[44px]">
            <Link href="/dashboard/finance/categories">
              Kategoriler
            </Link>
          </Button>
          <Button asChild className="flex-1 sm:flex-none min-h-[44px]">
            <Link href="/dashboard/finance/transactions/new">
              <Plus className="mr-2 h-4 w-4" />
              Yeni İşlem
            </Link>
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8 sm:py-12">
          <Loader2 className="h-6 w-6 sm:h-8 sm:w-8 animate-spin mr-2" />
          <span className="text-sm sm:text-base">Finansal veriler yükleniyor...</span>
        </div>
      ) : (
        <div className="grid gap-3 sm:gap-4 grid-cols-2 md:grid-cols-2 lg:grid-cols-4">
          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 p-3 sm:p-4">
              <CardTitle className="text-xs sm:text-sm font-medium">
                Toplam Gelir
              </CardTitle>
              <Wallet className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 sm:p-4 pt-0 sm:pt-0">
              <div className="text-base sm:text-2xl font-bold">{(Number(summary.total_income) || 0).toLocaleString('tr-TR')} ₺</div>
              <p className="text-[10px] sm:text-xs text-muted-foreground truncate">
                {dateRange.from && dateRange.to
                  ? `${format(dateRange.from, "d MMM", { locale: tr })} - ${format(dateRange.to, "d MMM", { locale: tr })}`
                  : "Son 30 gün"}
              </p>
            </CardContent>
          </Card>
          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 p-3 sm:p-4">
              <CardTitle className="text-xs sm:text-sm font-medium">
                Toplam Gider
              </CardTitle>
              <CreditCard className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 sm:p-4 pt-0 sm:pt-0">
              <div className="text-base sm:text-2xl font-bold">{(Number(summary.total_expense) || 0).toLocaleString('tr-TR')} ₺</div>
              <p className="text-[10px] sm:text-xs text-muted-foreground truncate">
                {dateRange.from && dateRange.to
                  ? `${format(dateRange.from, "d MMM", { locale: tr })} - ${format(dateRange.to, "d MMM", { locale: tr })}`
                  : "Son 30 gün"}
              </p>
            </CardContent>
          </Card>
          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 p-3 sm:p-4">
              <CardTitle className="text-xs sm:text-sm font-medium">
                Net Kar
              </CardTitle>
              <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 sm:p-4 pt-0 sm:pt-0">
              <div className="text-base sm:text-2xl font-bold">{(Number(summary.net_profit) || 0).toLocaleString('tr-TR')} ₺</div>
              <div className="flex items-center pt-0 sm:pt-1">
                {summary.total_income > 0 ? (
                  <>
                    {summary.net_profit >= 0 ? (
                      <ArrowUpRight className="h-3 w-3 sm:h-4 sm:w-4 text-emerald-500" />
                    ) : (
                      <ArrowDownRight className="h-3 w-3 sm:h-4 sm:w-4 text-red-500" />
                    )}
                    <span className={`text-[10px] sm:text-xs ${summary.net_profit >= 0 ? 'text-emerald-500' : 'text-red-500'}`}>
                      {summary.net_profit >= 0 ? '+' : ''}{(summary.total_income > 0 ? ((Number(summary.net_profit) / Number(summary.total_income)) * 100).toFixed(1) : 0)}%
                    </span>
                    <span className="text-[10px] sm:text-xs text-muted-foreground ml-1">kar marjı</span>
                  </>
                ) : (
                  <span className="text-[10px] sm:text-xs text-muted-foreground">Gelir bulunmamaktadır</span>
                )}
              </div>
            </CardContent>
          </Card>
          <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2 p-3 sm:p-4">
              <CardTitle className="text-xs sm:text-sm font-medium">
                İşlemler
              </CardTitle>
              <Banknote className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-3 sm:p-4 pt-0 sm:pt-0">
              <div className="text-base sm:text-2xl font-bold">{transactionCount}</div>
              <div className="flex items-center pt-0 sm:pt-1">
                <Link href="/dashboard/finance/transactions" className="text-[10px] sm:text-xs text-blue-500 flex items-center">
                  Tüm işlemleri görüntüle
                  <ArrowUpRight className="h-2 w-2 sm:h-3 sm:w-3 ml-1" />
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="overview" className="space-y-2 sm:space-y-4">
        <TabsList className="w-full overflow-x-auto flex">
          <TabsTrigger value="overview" className="flex-1 min-h-[40px]">Genel Bakış</TabsTrigger>
          <TabsTrigger value="income" className="flex-1 min-h-[40px]">Gelirler</TabsTrigger>
          <TabsTrigger value="expense" className="flex-1 min-h-[40px]">Giderler</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-3 sm:space-y-4">
          <div className="grid gap-3 sm:gap-4 grid-cols-1 md:grid-cols-2">
            <Card className="overflow-hidden">
              <CardHeader className="p-3 sm:p-6 pb-1 sm:pb-2">
                <CardTitle className="text-sm sm:text-base">Gelir ve Gider Grafiği</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  Seçilen tarih aralığındaki gelir ve gider dağılımı
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0 sm:pl-2">
                <ResponsiveContainer width="100%" height={250} className="sm:h-[300px] md:h-[350px]">
                  <BarChart
                    data={[
                      { name: 'Gelir', value: Number(summary.total_income) || 0, color: '#4CAF50' },
                      { name: 'Gider', value: Number(summary.total_expense) || 0, color: '#F44336' },
                      { name: 'Net Kar', value: Number(summary.net_profit) || 0, color: '#2196F3' },
                    ]}
                    margin={{ top: 10, right: 10, left: 10, bottom: 5, ...(!isMobile && { top: 20, right: 30, left: 20 }) }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" tick={{ fontSize: isMobile ? 10 : 12 }} />
                    <YAxis tick={{ fontSize: isMobile ? 10 : 12 }} />
                    <Tooltip
                      formatter={(value) => {
                        // Ensure value is a number before formatting
                        const numValue = Number(value) || 0;
                        return `${numValue.toLocaleString('tr-TR')} ₺`;
                      }}
                    />
                    <Legend wrapperStyle={{ fontSize: isMobile ? 10 : 12 }} />
                    <Bar dataKey="value" name="Tutar (₺)">
                      {[
                        { name: 'Gelir', value: Number(summary.total_income) || 0, color: '#4CAF50' },
                        { name: 'Gider', value: Number(summary.total_expense) || 0, color: '#F44336' },
                        { name: 'Net Kar', value: Number(summary.net_profit) || 0, color: '#2196F3' },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            <Card className="overflow-hidden">
              <CardHeader className="p-3 sm:p-6 pb-1 sm:pb-2">
                <CardTitle className="text-sm sm:text-base">Son İşlemler</CardTitle>
                <CardDescription className="text-xs sm:text-sm">
                  <Link href="/dashboard/finance/transactions" className="text-blue-500">
                    Tüm işlemleri görüntüle
                  </Link>
                </CardDescription>
              </CardHeader>
              <CardContent className="p-3 sm:p-6 pt-1 sm:pt-2">
                {recentTransactions.length === 0 ? (
                  <div className="py-2 sm:py-4 text-center text-muted-foreground text-xs sm:text-sm">
                    Bu tarih aralığında işlem bulunmamaktadır.
                  </div>
                ) : (
                  <div className="space-y-2 sm:space-y-4">
                    {recentTransactions.map((transaction) => {
                      const category = categoryBreakdown.find(cat => cat.category_id === transaction.category_id)
                      const isIncome = category?.category_type === "income"

                      return (
                        <div key={transaction.id} className="flex items-center justify-between">
                          <div className="flex items-center space-x-2 sm:space-x-4">
                            <div
                              className={`w-2 h-2 rounded-full ${isIncome ? 'bg-green-500' : 'bg-red-500'}`}
                              style={{ backgroundColor: category?.category_color }}
                            />
                            <div className="overflow-hidden">
                              <p className="text-xs sm:text-sm font-medium truncate max-w-[150px] sm:max-w-none">
                                {category?.category_name || "Kategori bulunamadı"}
                                {transaction.description && ` - ${transaction.description}`}
                              </p>
                              <p className="text-[10px] sm:text-xs text-muted-foreground flex items-center">
                                <Calendar className="h-2 w-2 sm:h-3 sm:w-3 mr-1" />
                                {format(new Date(transaction.transaction_date), "d MMM yyyy", { locale: tr })}
                              </p>
                            </div>
                          </div>
                          <div className={`text-xs sm:text-sm font-medium ${isIncome ? 'text-green-500' : 'text-red-500'}`}>
                            {isIncome ? '+' : '-'}{(Number(transaction.amount) || 0).toLocaleString('tr-TR')} ₺
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="income" className="space-y-3 sm:space-y-4">
          <Card className="overflow-hidden">
            <CardHeader className="p-3 sm:p-6 pb-1 sm:pb-2">
              <CardTitle className="text-sm sm:text-base">Gelir Dağılımı</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Gelir kategorilerine göre dağılım
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3 sm:p-6 pt-1 sm:pt-2">
              <div className="grid gap-3 sm:gap-4 grid-cols-1 md:grid-cols-2">
                <div>
                  <ResponsiveContainer width="100%" height={200} className="sm:h-[250px] md:h-[300px]">
                    <PieChart>
                      <Pie
                        data={incomeCategories}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={isMobile ? 60 : 80}
                        fill="#8884d8"
                        dataKey="total_amount"
                        nameKey="category_name"
                        label={isMobile ? undefined : ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {incomeCategories.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.category_color} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => {
                          const numValue = Number(value) || 0;
                          return `${numValue.toLocaleString('tr-TR')} ₺`;
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="space-y-2 sm:space-y-4">
                  {incomeCategories.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 sm:space-x-4">
                        <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full" style={{ backgroundColor: category.category_color }} />
                        <div>
                          <p className="text-xs sm:text-sm font-medium">{category.category_name}</p>
                        </div>
                      </div>
                      <div className="text-xs sm:text-sm font-medium">{(Number(category.total_amount) || 0).toLocaleString('tr-TR')} ₺</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="expense" className="space-y-3 sm:space-y-4">
          <Card className="overflow-hidden">
            <CardHeader className="p-3 sm:p-6 pb-1 sm:pb-2">
              <CardTitle className="text-sm sm:text-base">Gider Dağılımı</CardTitle>
              <CardDescription className="text-xs sm:text-sm">
                Gider kategorilerine göre dağılım
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3 sm:p-6 pt-1 sm:pt-2">
              <div className="grid gap-3 sm:gap-4 grid-cols-1 md:grid-cols-2">
                <div>
                  <ResponsiveContainer width="100%" height={200} className="sm:h-[250px] md:h-[300px]">
                    <PieChart>
                      <Pie
                        data={expenseCategories}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={isMobile ? 60 : 80}
                        fill="#8884d8"
                        dataKey="total_amount"
                        nameKey="category_name"
                        label={isMobile ? undefined : ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {expenseCategories.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.category_color} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => {
                          const numValue = Number(value) || 0;
                          return `${numValue.toLocaleString('tr-TR')} ₺`;
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="space-y-2 sm:space-y-4">
                  {expenseCategories.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 sm:space-x-4">
                        <div className="w-2 h-2 sm:w-3 sm:h-3 rounded-full" style={{ backgroundColor: category.category_color }} />
                        <div>
                          <p className="text-xs sm:text-sm font-medium">{category.category_name}</p>
                        </div>
                      </div>
                      <div className="text-xs sm:text-sm font-medium">{(Number(category.total_amount) || 0).toLocaleString('tr-TR')} ₺</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
