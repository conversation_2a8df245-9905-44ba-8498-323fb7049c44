# Takvimde Geçmiş Günleri Devre Dışı Bırakma - 2024-06-05

## Ya<PERSON><PERSON><PERSON> İşlemler

DatePicker bileşeni güncellenerek, takvimde bugünden önceki tüm günlerin devre dışı bırakılması sağlandı.

## Teknik Detaylar

```typescript
export function DatePicker({ date, setDate, className, disabled = false, disabledDates }: DatePickerProps) {
  // Today's date for fromDate prop
  const today = new Date()

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP") : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
          disabled={disabledDates}
          fromDate={today} // Disable all dates before today
        />
      </PopoverContent>
    </Popover>
  )
}
```

Bu değişiklikle, react-day-picker kütüphanesinin `fromDate` özelliği kullanılarak, bugünden önceki tüm günler otomatik olarak devre dışı bırakıldı. Ayrıca, `disabledDates` prop'u ile berberin çalışmadığı günler de devre dışı bırakılmaya devam ediyor.

## Sonuç

Bu değişiklikle birlikte, müşteriler artık geçmiş günleri ve berberin çalışmadığı günleri takvimde seçemeyecekler. Bu, kullanıcı deneyimini iyileştirecek ve kullanıcıların geçmiş veya müsait olmayan günleri seçmesini önleyecek.
