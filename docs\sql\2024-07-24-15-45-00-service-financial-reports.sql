-- Function to get financial summary by service
CREATE OR REPLACE FUNCTION get_financial_summary_by_service(
  p_salon_id UUID,
  p_start_date DATE,
  p_end_date DATE
) RETURNS TABLE (
  service_id UUID,
  service_name TEXT,
  total_income DECIMAL(10, 2),
  transaction_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.id AS service_id,
    s.name AS service_name,
    COALESCE(SUM(ft.amount), 0) AS total_income,
    COUNT(ft.id) AS transaction_count
  FROM
    services s
    LEFT JOIN finance_transactions ft ON s.id = ft.service_id
      AND ft.transaction_date BETWEEN p_start_date AND p_end_date
      AND ft.salon_id = p_salon_id
  WHERE
    s.salon_id = p_salon_id
  GROUP BY
    s.id, s.name
  ORDER BY
    total_income DESC;
END;
$$ LANGUAGE plpgsql;