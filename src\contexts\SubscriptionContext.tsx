"use client"

import { createContext, useContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react'
import { useUser } from './UserContext'
import { getActiveSalonSubscription } from '@/lib/db/subscriptions'
import { getSubscriptionPlanById, getSubscriptionPlans } from '@/lib/db/subscription-plans'
import { SubscriptionPlan, SalonSubscription } from '@/lib/db/types'

// Subscription features type
export interface SubscriptionFeatures {
  maxStaff: number;
  hasAnalytics: boolean;
  hasFinance: boolean;
  hasCustomDomain: boolean;
  hasProductManagement: boolean;
  storageLimitMB: number;
  [key: string]: any;
}

// Context type
type SubscriptionContextType = {
  features: SubscriptionFeatures;
  plan: SubscriptionPlan | null;
  subscription: SalonSubscription | null;
  plans: SubscriptionPlan[];
  isLoading: boolean;
  error: Error | null;
  hasFeature: (featureName: string) => boolean;
  canAddMoreStaff: (currentStaffCount: number) => boolean;
  refreshSubscription: () => Promise<void>;
}

// Default values
const defaultFeatures: SubscriptionFeatures = {
  maxStaff: 1,
  hasAnalytics: false,
  hasFinance: false,
  hasCustomDomain: false,
  hasProductManagement: false,
  storageLimitMB: 0
}

// Default context
const defaultContext: SubscriptionContextType = {
  features: defaultFeatures,
  plan: null,
  subscription: null,
  plans: [],
  isLoading: true,
  error: null,
  hasFeature: () => false,
  canAddMoreStaff: () => false,
  refreshSubscription: async () => {}
}

// Create context
const SubscriptionContext = createContext<SubscriptionContextType>(defaultContext)

// Provider component
export function SubscriptionProvider({ children }: { children: ReactNode }) {
  const { salonId } = useUser()
  const [features, setFeatures] = useState<SubscriptionFeatures>(defaultFeatures)
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null)
  const [subscription, setSubscription] = useState<SalonSubscription | null>(null)
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  const [dataFetched, setDataFetched] = useState(false)

  // Load subscription data
  const loadSubscriptionData = useCallback(async () => {
    if (!salonId) {
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Fetch all plans
      const plansData = await getSubscriptionPlans()
      setPlans(plansData)

      // Fetch subscription data
      const subscriptionData = await getActiveSalonSubscription(salonId)
      setSubscription(subscriptionData)

      if (!subscriptionData || !subscriptionData.plan_id) {
        setFeatures(defaultFeatures)
        setPlan(null)
        return
      }

      // If plans join field exists, use it directly
      let planData: SubscriptionPlan
      if (subscriptionData.plans) {
        planData = subscriptionData.plans
      } else {
        // Otherwise fetch plan data separately
        planData = await getSubscriptionPlanById(subscriptionData.plan_id)
      }

      setPlan(planData)
      setFeatures({
        maxStaff: planData.max_staff,
        hasAnalytics: planData.features.analytics,
        hasFinance: planData.features.finance,
        hasCustomDomain: planData.features.custom_domain,
        hasProductManagement: planData.features.product_management || false,
        storageLimitMB: planData.features.storage_limit_mb || 0,
        ...planData.features
      })
    } catch (err) {
      console.error('Abonelik özellikleri yüklenirken hata:', err)
      setError(err as Error)
    } finally {
      setIsLoading(false)
      setDataFetched(true)
    }
  }, [salonId])

  // Refresh subscription data
  const refreshSubscription = useCallback(async () => {
    setDataFetched(false)
    await loadSubscriptionData()
  }, [loadSubscriptionData])

  // Load data when salonId changes or on initial load
  useEffect(() => {
    if (!dataFetched || !salonId) {
      loadSubscriptionData()
    }
  }, [salonId, dataFetched, loadSubscriptionData])

  // Helper functions
  const hasFeature = useCallback((featureName: string): boolean => {
    return !!features[featureName]
  }, [features])

  const canAddMoreStaff = useCallback((currentStaffCount: number): boolean => {
    return currentStaffCount < features.maxStaff
  }, [features])

  // Memoize context value
  const contextValue = useMemo(() => ({
    features,
    plan,
    subscription,
    plans,
    isLoading,
    error,
    hasFeature,
    canAddMoreStaff,
    refreshSubscription
  }), [
    features,
    plan,
    subscription,
    plans,
    isLoading,
    error,
    hasFeature,
    canAddMoreStaff,
    refreshSubscription
  ])

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  )
}

// Custom hook
export function useSubscription() {
  return useContext(SubscriptionContext)
}
