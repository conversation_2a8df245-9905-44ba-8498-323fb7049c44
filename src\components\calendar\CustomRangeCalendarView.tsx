"use client"

import { useState } from "react"
import { format, parseISO } from "date-fns"
import { tr } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Appointment, BarberWorkingHours } from "@/lib/db/types"
import { HolidayDate } from "@/components/ui/holiday-calendar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { DraggableAppointment } from "./DraggableAppointment"
import { DroppableTimeSlot } from "./DroppableTimeSlot"
import { DndContext, DragEndEvent, DragStartEvent, useSensor, useSensors, PointerSensor } from "@dnd-kit/core"
import { WeeklyCalendarSkeleton } from "@/components/ui/skeleton-loaders"

interface CustomRangeCalendarViewProps {
  startDate: Date
  endDate: Date
  appointments: Appointment[]
  isLoading: boolean
  onDateClick: (date: Date) => void
  holidayDates?: HolidayDate[]
  barberWorkingHours?: BarberWorkingHours[]
  onAppointmentUpdated?: () => void
}

export function CustomRangeCalendarView({
  startDate,
  endDate,
  appointments,
  isLoading,
  onDateClick,
  holidayDates = [],
  barberWorkingHours = [],
  onAppointmentUpdated
}: CustomRangeCalendarViewProps) {
  const [activeAppointment, setActiveAppointment] = useState<Appointment | null>(null)
  const [isDragging, setIsDragging] = useState(false)

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px movement required before drag starts
      },
    })
  )

  // Group appointments by date
  const appointmentsByDate = appointments.reduce<Record<string, Appointment[]>>((acc, appointment) => {
    const date = appointment.date
    if (!acc[date]) {
      acc[date] = []
    }
    acc[date].push(appointment)
    return acc
  }, {})

  // Sort dates
  const sortedDates = Object.keys(appointmentsByDate).sort()

  // Check if a date is a holiday
  const isHoliday = (dateStr: string) => {
    const date = parseISO(dateStr)
    return holidayDates.some(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === dateStr
    )
  }

  // Get holiday description
  const getHolidayDescription = (dateStr: string) => {
    const date = parseISO(dateStr)
    const holiday = holidayDates.find(
      (holiday) => format(holiday.date, "yyyy-MM-dd") === dateStr
    )
    return holiday?.description || "Tatil Günü"
  }

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    const appointmentId = active.id.toString()
    const appointment = appointments.find(apt => apt.id === appointmentId)
    if (appointment) {
      setActiveAppointment(appointment)
      setIsDragging(true)
    }
  }

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setIsDragging(false)
    setActiveAppointment(null)

    if (!over) return

    const appointmentId = active.id.toString()
    const appointment = appointments.find(apt => apt.id === appointmentId)
    if (!appointment) return

    // Sadece aktif randevular taşınabilir
    if (appointment.status !== 'booked') {
      let statusMessage = "";
      switch (appointment.status) {
        case 'cancelled':
          statusMessage = "İptal edilmiş";
          break;
        case 'completed':
          statusMessage = "Tamamlanmış";
          break;
        case 'no-show':
          statusMessage = "Gelmemiş";
          break;
        default:
          statusMessage = "Bu durumdaki";
      }
      alert(`${statusMessage} randevular taşınamaz`)
      return
    }

    // Extract date from the droppable ID (format: date-YYYY-MM-DD)
    const match = over.id.toString().match(/date-(\d{4}-\d{2}-\d{2})/)
    if (!match) return

    const targetDate = match[1]

    // Don't do anything if the date is the same
    if (appointment.date === targetDate) return

    // Check if the date is a holiday
    if (isHoliday(targetDate)) {
      alert("Bu tarih tatil günü olarak işaretlenmiş. Randevu taşınamaz.")
      return
    }

    // Update the appointment with the new date
    // This would typically call a function to update the appointment in the database
    if (onAppointmentUpdated) {
      onAppointmentUpdated()
    }
  }



  if (isLoading) {
    return <WeeklyCalendarSkeleton />;
  }

  if (sortedDates.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Seçilen tarih aralığında randevu bulunamadı.</p>
      </div>
    )
  }

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-4">
        {sortedDates.map((dateStr) => {
          const isHolidayDate = isHoliday(dateStr)
          const holidayDescription = isHolidayDate ? getHolidayDescription(dateStr) : ""
          const dateAppointments = appointmentsByDate[dateStr] || []
          const formattedDate = format(parseISO(dateStr), "d MMMM yyyy, EEEE", { locale: tr })
          const dayId = `date-${dateStr}`

          return (
            <DroppableTimeSlot
              key={dayId}
              id={dayId}
              date={parseISO(dateStr)}
              isAvailable={!isHolidayDate}
              unavailableReason={isHolidayDate ? "Tatil günü" : ""}
              className="p-0"
            >
              <Card className={cn(
                isHolidayDate && "border-red-500 dark:border-red-700"
              )}>
                <CardHeader className="flex flex-row items-center justify-between py-2">
                  <CardTitle className="text-lg font-medium">
                    {formattedDate}
                    {isHolidayDate && (
                      <Badge variant="destructive" className="ml-2">
                        {holidayDescription}
                      </Badge>
                    )}
                  </CardTitle>
                  <div className="text-sm text-muted-foreground">
                    {dateAppointments.length} randevu
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  {dateAppointments.length === 0 ? (
                    <div className="text-center text-muted-foreground py-4">
                      Bu tarihte randevu bulunmuyor.
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {dateAppointments.map((appointment) => (
                        <DraggableAppointment key={appointment.id} appointment={appointment} />
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </DroppableTimeSlot>
          )
        })}
      </div>
    </DndContext>
  )
}
