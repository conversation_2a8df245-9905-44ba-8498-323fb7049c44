"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"
import { tr } from "date-fns/locale"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerWithRangeProps {
  className?: string
  date?: DateRange
  dateRange?: DateRange
  onDateChange?: (dateRange: DateRange) => void
  setDateRange?: (dateRange: DateRange) => void
  disabled?: boolean
}

// Custom hook to detect screen size
function useMediaQuery(query: string) {
  const [matches, setMatches] = React.useState(false)

  React.useEffect(() => {
    const media = window.matchMedia(query)
    if (media.matches !== matches) {
      setMatches(media.matches)
    }

    const listener = () => setMatches(media.matches)
    media.addEventListener("change", listener)
    return () => media.removeEventListener("change", listener)
  }, [matches, query])

  return matches
}

export function DatePickerWithRange({
  className,
  date,
  dateRange,
  onDateChange,
  setDateRange,
  disabled = false,
}: DatePickerWithRangeProps) {
  // Check if screen is mobile
  const isMobile = useMediaQuery("(max-width: 640px)")

  // Use either date or dateRange prop
  const selectedDateRange = date || dateRange

  // Use either onDateChange or setDateRange function
  const handleDateChange = onDateChange || setDateRange

  if (!handleDateChange) {
    console.error("Either onDateChange or setDateRange must be provided")
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal min-h-[44px]",
              !selectedDateRange && "text-muted-foreground"
            )}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {selectedDateRange?.from ? (
              selectedDateRange.to ? (
                <>
                  {format(selectedDateRange.from, "LLL dd, y", { locale: tr })} -{" "}
                  {format(selectedDateRange.to, "LLL dd, y", { locale: tr })}
                </>
              ) : (
                format(selectedDateRange.from, "LLL dd, y", { locale: tr })
              )
            ) : (
              <span>Tarih aralığı seçin</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 max-w-[95vw] sm:max-w-none" align={isMobile ? "center" : "start"}>
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={selectedDateRange?.from}
            selected={selectedDateRange}
            onSelect={handleDateChange}
            numberOfMonths={isMobile ? 1 : 2}
            locale={tr}
            weekStartsOn={1}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
