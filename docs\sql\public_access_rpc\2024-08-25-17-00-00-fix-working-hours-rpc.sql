-- SalonFlow Public Access Functions - Working Hours Fix
-- Oluşturulma Tarihi: 2024-08-25
-- Bu SQL dosyası, working_hours RPC fonksiyonunu düzeltmek için oluşturulmuştur.

-- Mevcut fonksiyonu kaldır
DROP FUNCTION IF EXISTS get_public_working_hours_by_salon_id(UUID);

-- 5. Çalışma saatlerini salon ID ile getiren fonksiyon (düzeltilmiş)
CREATE OR REPLACE FUNCTION get_public_working_hours_by_salon_id(p_salon_id UUID)
RETURNS TABLE (
  id UUID,
  salon_id UUID,
  barber_id UUID,
  day_of_week INTEGER,
  open_time TIME,
  close_time TIME,
  is_closed BOOLEAN,
  lunch_start_time TIME,
  lunch_end_time TIME,
  has_lunch_break BOOLEAN
) SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Önce salon çalışma saatlerini getir (barber_id NULL olarak)
  RETURN QUERY
  SELECT 
    w.id, 
    w.salon_id, 
    NULL::UUID as barber_id, 
    w.day_of_week, 
    w.open_time, 
    w.close_time, 
    w.is_closed,
    NULL::TIME as lunch_start_time,
    NULL::TIME as lunch_end_time,
    FALSE as has_lunch_break
  FROM working_hours w
  WHERE w.salon_id = p_salon_id
  
  UNION ALL
  
  -- Sonra berber çalışma saatlerini getir
  SELECT 
    bw.id, 
    s.id as salon_id, 
    bw.barber_id, 
    bw.day_of_week, 
    bw.open_time, 
    bw.close_time, 
    bw.is_closed,
    bw.lunch_start_time,
    bw.lunch_end_time,
    bw.has_lunch_break
  FROM barber_working_hours bw
  JOIN barbers b ON bw.barber_id = b.id
  JOIN salons s ON b.salon_id = s.id
  WHERE s.id = p_salon_id
  
  ORDER BY barber_id NULLS FIRST, day_of_week;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION get_public_working_hours_by_salon_id(UUID) TO anon;
