import { Customer, CustomerInsert, CustomerUpdate } from './types';
import { supabaseClient } from '../supabase-singleton';
const supabase = supabaseClient;

/**
 * Get all customers for a salon
 */
export async function getCustomers(salonId: string) {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('salon_id', salonId)
    .order('surname', { ascending: true })
    .order('name', { ascending: true });

  if (error) throw error;
  return data as Customer[];
}

/**
 * Search customers by name, surname, or phone for a salon
 */
export async function searchCustomers(salonId: string, query: string) {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('salon_id', salonId)
    .or(`name.ilike.%${query}%,surname.ilike.%${query}%,phone.ilike.%${query}%`)
    .order('surname', { ascending: true })
    .order('name', { ascending: true });

  if (error) throw error;
  return data as Customer[];
}

/**
 * Get a customer by ID
 */
export async function getCustomerById(id: string) {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as Customer;
}

/**
 * Create a new customer
 */
export async function createCustomer(customer: CustomerInsert) {
  const { data, error } = await supabase
    .from('customers')
    .insert(customer)
    .select()
    .single();

  if (error) throw error;
  return data as Customer;
}

/**
 * Update a customer
 */
export async function updateCustomer({ id, ...customer }: CustomerUpdate) {
  const { data, error } = await supabase
    .from('customers')
    .update(customer)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Customer;
}

/**
 * Delete a customer
 */
export async function deleteCustomer(id: string) {
  const { error } = await supabase
    .from('customers')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Get customer appointment history
 */
export async function getCustomerAppointmentHistory(customerId: string) {
  const { data, error } = await supabase
    .from('appointments')
    .select(`
      *,
      services(name, duration),
      barbers(name)
    `)
    .eq('customer_id', customerId)
    .order('date', { ascending: false })
    .order('start_time', { ascending: false });

  if (error) throw error;
  return data;
}

/**
 * Find a customer by phone number for a salon
 */
export async function findCustomerByPhone(salonId: string, phone: string) {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('salon_id', salonId)
    .eq('phone', phone)
    .single();

  if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "No rows returned" error
  return error && error.code === 'PGRST116' ? null : data as Customer;
}

/**
 * Update customer notes
 */
export async function updateCustomerNotes(id: string, notes: string) {
  const { data, error } = await supabase
    .from('customers')
    .update({ notes })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Customer;
}