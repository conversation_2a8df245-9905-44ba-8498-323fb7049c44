// Test file for subscription middleware
require('dotenv').config({ path: '.env.local' });
const { expect } = require('chai');
const sinon = require('sinon');
const { NextRequest, NextResponse } = require('next/server');

// Mock Next.js server components
jest.mock('next/server', () => {
  const originalModule = jest.requireActual('next/server');
  return {
    ...originalModule,
    NextRequest: jest.fn(),
    NextResponse: {
      next: jest.fn(() => ({ headers: new Map() })),
      redirect: jest.fn((url) => ({ url }))
    }
  };
});

// Mock Supabase middleware client
const mockSupabaseMiddleware = {
  auth: {
    getSession: jest.fn()
  },
  from: jest.fn()
};

jest.mock('@supabase/auth-helpers-nextjs', () => ({
  createMiddlewareClient: jest.fn(() => mockSupabaseMiddleware)
}));

// Import the middleware after mocking
const { subscriptionMiddleware } = require('../../src/middleware/subscription-check');

describe('Subscription Middleware', () => {
  let sandbox;
  let mockReq;
  let mockRes;
  
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    
    // Reset all mocks
    NextResponse.next.mockClear();
    NextResponse.redirect.mockClear();
    mockSupabaseMiddleware.auth.getSession.mockReset();
    mockSupabaseMiddleware.from.mockReset();
    
    // Mock request and response
    mockReq = {
      nextUrl: {
        pathname: '/dashboard',
        href: 'http://localhost:3000/dashboard',
        toString: () => 'http://localhost:3000/dashboard'
      },
      url: 'http://localhost:3000/dashboard'
    };
    
    mockRes = NextResponse.next();
    
    // Default mock implementation for getSession
    mockSupabaseMiddleware.auth.getSession.mockResolvedValue({
      data: {
        session: {
          user: {
            id: 'test-user-id',
            email: '<EMAIL>'
          }
        }
      }
    });
    
    // Default mock implementation for from()
    const mockSelect = jest.fn();
    const mockEq = jest.fn();
    const mockSingle = jest.fn();
    
    mockSelect.mockReturnValue({ eq: mockEq });
    mockEq.mockReturnValue({ single: mockSingle });
    mockSingle.mockResolvedValue({ data: null, error: null });
    
    mockSupabaseMiddleware.from.mockReturnValue({
      select: mockSelect
    });
  });
  
  afterEach(() => {
    sandbox.restore();
  });
  
  it('should allow access if no session exists', async () => {
    // Mock no session
    mockSupabaseMiddleware.auth.getSession.mockResolvedValue({
      data: { session: null }
    });
    
    const result = await subscriptionMiddleware(mockReq);
    
    expect(NextResponse.next).toHaveBeenCalled();
    expect(result).toEqual(mockRes);
  });
  
  it('should allow access if no salon exists', async () => {
    // Mock salon query with no results
    const mockSelect = jest.fn();
    const mockEq = jest.fn();
    const mockSingle = jest.fn();
    
    mockSelect.mockReturnValue({ eq: mockEq });
    mockEq.mockReturnValue({ single: mockSingle });
    mockSingle.mockResolvedValue({ data: null, error: { message: 'No salon found' } });
    
    mockSupabaseMiddleware.from.mockReturnValue({
      select: mockSelect
    });
    
    const result = await subscriptionMiddleware(mockReq);
    
    expect(NextResponse.next).toHaveBeenCalled();
    expect(result).toEqual(mockRes);
  });
  
  it('should redirect if trial period has expired', async () => {
    // Mock salon query with results
    const mockSalonSelect = jest.fn();
    const mockSalonEq = jest.fn();
    const mockSalonSingle = jest.fn();
    
    mockSalonSelect.mockReturnValue({ eq: mockSalonEq });
    mockSalonEq.mockReturnValue({ single: mockSalonSingle });
    mockSalonSingle.mockResolvedValue({
      data: { id: 'salon-1', name: 'Test Salon' },
      error: null
    });
    
    // Mock subscription query with trial subscription
    const mockSubSelect = jest.fn();
    const mockSubEq = jest.fn();
    const mockSubSingle = jest.fn();
    
    mockSubSelect.mockReturnValue({ eq: mockSubEq });
    mockSubEq.mockReturnValue({ single: mockSubSingle });
    mockSubSingle.mockResolvedValue({
      data: {
        id: 'sub-1',
        salon_id: 'salon-1',
        status: 'trial',
        is_active: true,
        trial_end_date: '2023-01-01', // Past date
        updated_at: '2023-01-01',
        plans: {
          id: 'plan-1',
          name: 'Solo'
        }
      },
      error: null
    });
    
    mockSupabaseMiddleware.from.mockImplementation((table) => {
      if (table === 'salons') {
        return { select: mockSalonSelect };
      } else if (table === 'salon_subscriptions') {
        return { select: mockSubSelect };
      }
      return { select: jest.fn().mockReturnValue({ eq: jest.fn() }) };
    });
    
    const result = await subscriptionMiddleware(mockReq);
    
    expect(NextResponse.redirect).toHaveBeenCalledWith(
      expect.objectContaining({
        pathname: '/dashboard/subscription'
      })
    );
  });
  
  it('should redirect if payment is past due for more than 7 days', async () => {
    // Mock salon query with results
    const mockSalonSelect = jest.fn();
    const mockSalonEq = jest.fn();
    const mockSalonSingle = jest.fn();
    
    mockSalonSelect.mockReturnValue({ eq: mockSalonEq });
    mockSalonEq.mockReturnValue({ single: mockSalonSingle });
    mockSalonSingle.mockResolvedValue({
      data: { id: 'salon-1', name: 'Test Salon' },
      error: null
    });
    
    // Calculate a date 8 days ago
    const eightDaysAgo = new Date();
    eightDaysAgo.setDate(eightDaysAgo.getDate() - 8);
    
    // Mock subscription query with past_due subscription
    const mockSubSelect = jest.fn();
    const mockSubEq = jest.fn();
    const mockSubSingle = jest.fn();
    
    mockSubSelect.mockReturnValue({ eq: mockSubEq });
    mockSubEq.mockReturnValue({ single: mockSubSingle });
    mockSubSingle.mockResolvedValue({
      data: {
        id: 'sub-1',
        salon_id: 'salon-1',
        status: 'past_due',
        is_active: true,
        updated_at: eightDaysAgo.toISOString(),
        plans: {
          id: 'plan-1',
          name: 'Solo'
        }
      },
      error: null
    });
    
    mockSupabaseMiddleware.from.mockImplementation((table) => {
      if (table === 'salons') {
        return { select: mockSalonSelect };
      } else if (table === 'salon_subscriptions') {
        return { select: mockSubSelect };
      }
      return { select: jest.fn().mockReturnValue({ eq: jest.fn() }) };
    });
    
    const result = await subscriptionMiddleware(mockReq);
    
    expect(NextResponse.redirect).toHaveBeenCalledWith(
      expect.objectContaining({
        pathname: '/dashboard/subscription'
      })
    );
  });
  
  it('should allow access for active subscription', async () => {
    // Mock salon query with results
    const mockSalonSelect = jest.fn();
    const mockSalonEq = jest.fn();
    const mockSalonSingle = jest.fn();
    
    mockSalonSelect.mockReturnValue({ eq: mockSalonEq });
    mockSalonEq.mockReturnValue({ single: mockSalonSingle });
    mockSalonSingle.mockResolvedValue({
      data: { id: 'salon-1', name: 'Test Salon' },
      error: null
    });
    
    // Mock subscription query with active subscription
    const mockSubSelect = jest.fn();
    const mockSubEq = jest.fn();
    const mockSubSingle = jest.fn();
    
    mockSubSelect.mockReturnValue({ eq: mockSubEq });
    mockSubEq.mockReturnValue({ single: mockSubSingle });
    mockSubSingle.mockResolvedValue({
      data: {
        id: 'sub-1',
        salon_id: 'salon-1',
        status: 'active',
        is_active: true,
        plans: {
          id: 'plan-1',
          name: 'Solo'
        }
      },
      error: null
    });
    
    mockSupabaseMiddleware.from.mockImplementation((table) => {
      if (table === 'salons') {
        return { select: mockSalonSelect };
      } else if (table === 'salon_subscriptions') {
        return { select: mockSubSelect };
      }
      return { select: jest.fn().mockReturnValue({ eq: jest.fn() }) };
    });
    
    const result = await subscriptionMiddleware(mockReq);
    
    expect(NextResponse.next).toHaveBeenCalled();
    expect(result).toEqual(mockRes);
  });
});
