-- Önce RLS'yi devre dışı bırakalım
ALTER TABLE customers DISABLE ROW LEVEL SECURITY;

-- Mevcut politikaları temizleyelim
DROP POLICY IF EXISTS "Salon owners can manage their salon's customers" ON customers;
DROP POLICY IF EXISTS "Staff can view their salon's customers" ON customers;
DROP POLICY IF EXISTS "Staff can insert customers for their salon" ON customers;
DROP POLICY IF EXISTS "Staff can update customers for their salon" ON customers;
DROP POLICY IF EXISTS "Anyone can insert customers" ON customers;
DROP POLICY IF EXISTS "Authenticated users can manage customers" ON customers;
DROP POLICY IF EXISTS "Service role bypass" ON customers;
DROP POLICY IF EXISTS "Enable read access for all users" ON customers;
DROP POLICY IF EXISTS "Bypass RLS for all users" ON customers;

-- RLS'yi tekrar etkinleştirelim
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri için politika
CREATE POLICY "Salon owners can manage their salon's customers" ON customers
  FOR ALL
  TO authenticated
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Berberler için görüntüleme politikası
CREATE POLICY "Staff can view their salon's customers" ON customers
  FOR SELECT
  TO authenticated
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Berberler için ekleme politikası
CREATE POLICY "Staff can insert customers for their salon" ON customers
  FOR INSERT
  TO authenticated
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Berberler için güncelleme politikası
CREATE POLICY "Staff can update customers for their salon" ON customers
  FOR UPDATE
  TO authenticated
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Servis rolü için bypass politikası
CREATE POLICY "Service role bypass" ON customers
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Supabase'in dahili servis rolü için bypass politikası
CREATE POLICY "Supabase service bypass" ON customers
  FOR ALL
  TO postgres
  USING (true)
  WITH CHECK (true);
