// Test setup file
require('dotenv').config({ path: '.env.local' });

// Mock Next.js modules
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    reload: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/',
    events: {
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn()
    }
  })
}));

// Mock Next.js server components
jest.mock('next/server', () => {
  return {
    NextRequest: jest.fn(),
    NextResponse: {
      next: jest.fn(() => ({ headers: new Map() })),
      redirect: jest.fn((url) => ({ url }))
    }
  };
});

// Mock Supabase
const mockSupabaseClient = {
  from: jest.fn(),
  auth: {
    getUser: jest.fn(),
    getSession: jest.fn().mockResolvedValue({ data: {}, error: null })
  }
};

jest.mock('@/lib/supabase', () => ({
  // Return the same instance for singleton behavior
  getSupabaseBrowser: jest.fn(() => mockSupabaseClient),
  getSupabaseServer: jest.fn(() => ({
    from: jest.fn(),
    auth: {
      getUser: jest.fn()
    }
  })),
  supabase: {
    from: jest.fn(),
    auth: {
      getUser: jest.fn()
    }
  }
}));

// Mock UserContext
jest.mock('@/contexts/UserContext', () => ({
  useUser: jest.fn(() => ({
    user: null,
    salonId: null,
    salon: null,
    userRole: null,
    isLoading: false,
    error: null
  }))
}));

// Global console overrides to reduce noise during tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

// Filter out specific expected warnings/errors during tests
console.error = (...args) => {
  // Ignore certain expected errors
  const errorMessage = args[0]?.toString() || '';
  if (
    errorMessage.includes('Warning: ReactDOM.render is no longer supported') ||
    errorMessage.includes('Warning: useLayoutEffect does nothing on the server')
  ) {
    return;
  }
  originalConsoleError(...args);
};

console.warn = (...args) => {
  // Ignore certain expected warnings
  const warnMessage = args[0]?.toString() || '';
  if (
    warnMessage.includes('Warning: ReactDOM.render is no longer supported') ||
    warnMessage.includes('Warning: useLayoutEffect does nothing on the server')
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Reduce console.log noise during tests
console.log = (...args) => {
  // Only log if explicitly enabled
  if (process.env.DEBUG_TESTS === 'true') {
    originalConsoleLog(...args);
  }
};

// Restore console methods after tests
afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;
});
