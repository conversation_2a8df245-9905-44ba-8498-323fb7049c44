-- Notification Creator Tracking
-- <PERSON>u SQL dosyası, bildirim oluşturan kullanıcıyı takip etmek ve kendi oluşturdukları bildirimleri görmemelerini sağlamak için gerekli değişiklikleri içerir.

-- 1. Notifications tablosuna created_by s<PERSON><PERSON><PERSON> ekle
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- 2. Notification trigger'larını güncelle

-- <PERSON><PERSON> randevu oluşturulduğunda bildirim oluşturan fonksiyon (güncellenmiş)
CREATE OR REPLACE FUNCTION create_new_appointment_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  barber_user_id UUID;
  appointment_date TEXT;
  appointment_time TEXT;
  current_user_id UUID;
BEGIN
  -- Mevcut kullanıcı ID'sini al
  current_user_id := auth.uid();
  
  -- Salon sahibini bul
  SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;
  
  -- <PERSON><PERSON> kullanıcı ID'sini bul (eğer atanmışsa)
  IF NEW.barber_id IS NOT NULL THEN
    SELECT user_id INTO barber_user_id FROM barbers WHERE id = NEW.barber_id;
  END IF;
  
  -- Tarih ve saat formatla
  appointment_date := to_char(NEW.date, 'DD Month YYYY');
  appointment_time := to_char(NEW.start_time, 'HH24:MI');
  
  -- Salon sahibine bildirim gönder
  IF salon_owner_id IS NOT NULL THEN
    INSERT INTO notifications (
      id, 
      salon_id, 
      user_id, 
      type, 
      title, 
      message, 
      read, 
      created_at, 
      data,
      created_by
    ) VALUES (
      gen_random_uuid(), 
      NEW.salon_id, 
      salon_owner_id, 
      'new_booking', 
      'Yeni Randevu', 
      format('%s tarafından yeni bir randevu oluşturuldu. (%s, %s)', 
             COALESCE(NEW.fullname, 'Bir müşteri'), 
             appointment_date, 
             appointment_time),
      false, 
      now(), 
      jsonb_build_object('id', NEW.id),
      current_user_id
    );
  END IF;
  
  -- Berbere bildirim gönder (eğer atanmışsa ve kullanıcı hesabı varsa)
  IF barber_user_id IS NOT NULL AND barber_user_id != salon_owner_id THEN
    INSERT INTO notifications (
      id, 
      salon_id, 
      user_id, 
      type, 
      title, 
      message, 
      read, 
      created_at, 
      data,
      created_by
    ) VALUES (
      gen_random_uuid(), 
      NEW.salon_id, 
      barber_user_id, 
      'new_booking', 
      'Yeni Randevu', 
      format('%s tarafından yeni bir randevu oluşturuldu. (%s, %s)', 
             COALESCE(NEW.fullname, 'Bir müşteri'), 
             appointment_date, 
             appointment_time),
      false, 
      now(), 
      jsonb_build_object('id', NEW.id),
      current_user_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Randevu iptal edildiğinde bildirim oluşturan fonksiyon (güncellenmiş)
CREATE OR REPLACE FUNCTION create_cancelled_appointment_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  barber_user_id UUID;
  appointment_date TEXT;
  appointment_time TEXT;
  current_user_id UUID;
BEGIN
  -- Sadece durum 'cancelled' olarak değiştiğinde çalış
  IF OLD.status != 'cancelled' AND NEW.status = 'cancelled' THEN
    -- Mevcut kullanıcı ID'sini al
    current_user_id := auth.uid();
    
    -- Salon sahibini bul
    SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;
    
    -- Berber kullanıcı ID'sini bul (eğer atanmışsa)
    IF NEW.barber_id IS NOT NULL THEN
      SELECT user_id INTO barber_user_id FROM barbers WHERE id = NEW.barber_id;
    END IF;
    
    -- Tarih ve saat formatla
    appointment_date := to_char(NEW.date, 'DD Month YYYY');
    appointment_time := to_char(NEW.start_time, 'HH24:MI');
    
    -- Salon sahibine bildirim gönder
    IF salon_owner_id IS NOT NULL THEN
      INSERT INTO notifications (
        id, 
        salon_id, 
        user_id, 
        type, 
        title, 
        message, 
        read, 
        created_at, 
        data,
        created_by
      ) VALUES (
        gen_random_uuid(), 
        NEW.salon_id, 
        salon_owner_id, 
        'cancellation', 
        'Randevu İptali', 
        format('%s randevusu iptal edildi. (%s, %s)', 
               COALESCE(NEW.fullname, 'Bir müşteri'), 
               appointment_date, 
               appointment_time),
        false, 
        now(), 
        jsonb_build_object('id', NEW.id),
        current_user_id
      );
    END IF;
    
    -- Berbere bildirim gönder (eğer atanmışsa ve kullanıcı hesabı varsa)
    IF barber_user_id IS NOT NULL AND barber_user_id != salon_owner_id THEN
      INSERT INTO notifications (
        id, 
        salon_id, 
        user_id, 
        type, 
        title, 
        message, 
        read, 
        created_at, 
        data,
        created_by
      ) VALUES (
        gen_random_uuid(), 
        NEW.salon_id, 
        barber_user_id, 
        'cancellation', 
        'Randevu İptali', 
        format('%s randevusu iptal edildi. (%s, %s)', 
               COALESCE(NEW.fullname, 'Bir müşteri'), 
               appointment_date, 
               appointment_time),
        false, 
        now(), 
        jsonb_build_object('id', NEW.id),
        current_user_id
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ları yeniden oluştur
DROP TRIGGER IF EXISTS on_appointment_created ON appointments;
CREATE TRIGGER on_appointment_created
AFTER INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION create_new_appointment_notification();

DROP TRIGGER IF EXISTS on_appointment_cancelled ON appointments;
CREATE TRIGGER on_appointment_cancelled
AFTER UPDATE ON appointments
FOR EACH ROW
EXECUTE FUNCTION create_cancelled_appointment_notification();
