-- Telegram Notification Log Schema for SalonFlow
-- Date: 2025-05-25-23-47-00
-- Description: Database schema for preventing duplicate Telegram notifications with deduplication tracking

-- =====================================================
-- 1. CREATE NOTIFICATION LOG TABLE
-- =====================================================

-- Create telegram_notification_log table for tracking sent notifications
CREATE TABLE telegram_notification_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID NOT NULL REFERENCES salons(id) ON DELETE CASCADE,
  appointment_id UUID NOT NULL,
  notification_type TEXT NOT NULL, -- 'new_appointment', 'cancelled_appointment', 'updated_appointment'
  notification_hash TEXT NOT NULL, -- SHA256 hash of key appointment data
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Prevent duplicate notifications with composite unique constraint
  CONSTRAINT unique_notification UNIQUE (salon_id, appointment_id, notification_type, notification_hash)
);

-- =====================================================
-- 2. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for salon-based queries (most common access pattern)
CREATE INDEX idx_telegram_notification_log_salon_id ON telegram_notification_log(salon_id);

-- Index for appointment-based queries
CREATE INDEX idx_telegram_notification_log_appointment_id ON telegram_notification_log(appointment_id);

-- Index for time-based queries and cleanup operations
CREATE INDEX idx_telegram_notification_log_sent_at ON telegram_notification_log(sent_at);

-- Composite index for deduplication checks (most critical performance path)
CREATE INDEX idx_telegram_notification_log_dedup ON telegram_notification_log(salon_id, appointment_id, notification_type, notification_hash);

-- =====================================================
-- 3. ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS for multi-tenant security
ALTER TABLE telegram_notification_log ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 4. CREATE RLS POLICIES
-- =====================================================

-- Policy: Salon owners can view their notification logs
CREATE POLICY "Salon owners can view notification logs" ON telegram_notification_log
  FOR SELECT USING (
    salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())
  );

-- Policy: Salon staff can view their salon's notification logs
CREATE POLICY "Salon staff can view notification logs" ON telegram_notification_log
  FOR SELECT USING (
    salon_id IN (
      SELECT salon_id FROM barbers WHERE user_id = auth.uid()
    )
  );

-- Policy: Admins can access all notification logs
CREATE POLICY "Admins can access all notification logs" ON telegram_notification_log
  FOR ALL USING (is_admin());

-- Policy: System can insert notification logs (for API routes)
CREATE POLICY "System can insert notification logs" ON telegram_notification_log
  FOR INSERT WITH CHECK (true);

-- =====================================================
-- 5. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to check if notification already sent
CREATE OR REPLACE FUNCTION check_notification_sent(
  p_salon_id UUID,
  p_appointment_id UUID,
  p_notification_type TEXT,
  p_notification_hash TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM telegram_notification_log
    WHERE salon_id = p_salon_id
      AND appointment_id = p_appointment_id
      AND notification_type = p_notification_type
      AND notification_hash = p_notification_hash
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log successful notification
CREATE OR REPLACE FUNCTION log_notification_sent(
  p_salon_id UUID,
  p_appointment_id UUID,
  p_notification_type TEXT,
  p_notification_hash TEXT
) RETURNS UUID AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO telegram_notification_log (
    salon_id, appointment_id, notification_type, notification_hash
  ) VALUES (
    p_salon_id, p_appointment_id, p_notification_type, p_notification_hash
  ) RETURNING id INTO log_id;

  RETURN log_id;
EXCEPTION
  WHEN unique_violation THEN
    -- If duplicate constraint is violated, return NULL (notification already sent)
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old notification logs (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_notification_logs() RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM telegram_notification_log
  WHERE sent_at < NOW() - INTERVAL '30 days';

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- Log cleanup operation
  RAISE NOTICE 'Cleaned up % old notification log entries', deleted_count;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get notification statistics for a salon
CREATE OR REPLACE FUNCTION get_notification_stats(
  p_salon_id UUID,
  p_days INTEGER DEFAULT 7
) RETURNS TABLE (
  notification_type TEXT,
  total_count BIGINT,
  unique_appointments BIGINT,
  last_sent TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    tnl.notification_type,
    COUNT(*) as total_count,
    COUNT(DISTINCT tnl.appointment_id) as unique_appointments,
    MAX(tnl.sent_at) as last_sent
  FROM telegram_notification_log tnl
  WHERE tnl.salon_id = p_salon_id
    AND tnl.sent_at >= NOW() - (p_days || ' days')::INTERVAL
  GROUP BY tnl.notification_type
  ORDER BY total_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions for the functions
GRANT EXECUTE ON FUNCTION check_notification_sent(UUID, UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION log_notification_sent(UUID, UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_notification_logs() TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_stats(UUID, INTEGER) TO authenticated;

-- =====================================================
-- 7. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

-- Table comments
COMMENT ON TABLE telegram_notification_log IS 'Tracks sent Telegram notifications to prevent duplicates and provide audit trail';
COMMENT ON COLUMN telegram_notification_log.salon_id IS 'Reference to the salon that owns this notification';
COMMENT ON COLUMN telegram_notification_log.appointment_id IS 'Reference to the appointment that triggered this notification';
COMMENT ON COLUMN telegram_notification_log.notification_type IS 'Type of notification: new_appointment, cancelled_appointment, updated_appointment';
COMMENT ON COLUMN telegram_notification_log.notification_hash IS 'SHA256 hash of key appointment data for deduplication';
COMMENT ON COLUMN telegram_notification_log.sent_at IS 'Timestamp when the notification was successfully sent';

-- Function comments
COMMENT ON FUNCTION check_notification_sent(UUID, UUID, TEXT, TEXT) IS 'Checks if a notification with the same hash was already sent';
COMMENT ON FUNCTION log_notification_sent(UUID, UUID, TEXT, TEXT) IS 'Logs a successful notification send, returns NULL if duplicate';
COMMENT ON FUNCTION cleanup_old_notification_logs() IS 'Removes notification logs older than 30 days to manage table size';
COMMENT ON FUNCTION get_notification_stats(UUID, INTEGER) IS 'Returns notification statistics for a salon over specified days';

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================

-- Verify table creation
SELECT 
  table_name, 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'telegram_notification_log' 
ORDER BY ordinal_position;

-- Verify indexes
SELECT 
  indexname, 
  indexdef 
FROM pg_indexes 
WHERE tablename = 'telegram_notification_log';

-- Verify RLS policies
SELECT 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual 
FROM pg_policies 
WHERE tablename = 'telegram_notification_log';

-- Verify functions
SELECT 
  routine_name, 
  routine_type, 
  security_type 
FROM information_schema.routines 
WHERE routine_name LIKE '%notification%' 
  AND routine_schema = 'public';
