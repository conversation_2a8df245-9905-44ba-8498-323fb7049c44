-- <PERSON><PERSON><PERSON><PERSON>'ları
-- Bu SQL dosyası, randevu oluşturma ve iptal etme işlemlerinde otomatik olarak bildirim oluşturan trigger'ları içerir.

-- Önce notifications tablosunu oluşturalım (<PERSON><PERSON><PERSON> yoksa)
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('new_booking', 'cancellation', 'update')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  read BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  data JSONB,
  CONSTRAINT fk_salon FOREIGN KEY (salon_id) REFERENCES salons(id) ON DELETE CASCADE,
  CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- <PERSON><PERSON><PERSON> zaten varsa, index'leri oluşturalım
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_salon_id ON notifications(salon_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);

-- RLS politikalarını oluşturalım (eğer yoksa)
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Mevcut politikaları temizleyelim
DROP POLICY IF EXISTS "Salon owners can see their salon notifications" ON notifications;
DROP POLICY IF EXISTS "Salon owners can manage their salon notifications" ON notifications;
DROP POLICY IF EXISTS "Staff can see their own notifications" ON notifications;
DROP POLICY IF EXISTS "Staff can manage their own notifications" ON notifications;

-- Yeni politikaları oluşturalım
CREATE POLICY "Salon owners can see their salon notifications" ON notifications
  FOR SELECT
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

CREATE POLICY "Salon owners can manage their salon notifications" ON notifications
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

CREATE POLICY "Staff can see their own notifications" ON notifications
  FOR SELECT
  USING (user_id = auth.uid() OR salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

CREATE POLICY "Staff can manage their own notifications" ON notifications
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Yeni randevu oluşturulduğunda bildirim oluşturan fonksiyon
CREATE OR REPLACE FUNCTION create_new_appointment_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  barber_user_id UUID;
  appointment_date TEXT;
  appointment_time TEXT;
BEGIN
  -- Salon sahibini bul
  SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;
  
  -- Berber kullanıcı ID'sini bul (eğer atanmışsa)
  IF NEW.barber_id IS NOT NULL THEN
    SELECT user_id INTO barber_user_id FROM barbers WHERE id = NEW.barber_id;
  END IF;
  
  -- Tarih ve saat formatla
  appointment_date := to_char(NEW.date, 'DD Month YYYY');
  appointment_time := substring(NEW.start_time, 1, 5);
  
  -- Salon sahibine bildirim gönder
  IF salon_owner_id IS NOT NULL THEN
    INSERT INTO notifications (
      id, 
      salon_id, 
      user_id, 
      type, 
      title, 
      message, 
      read, 
      created_at, 
      data
    ) VALUES (
      gen_random_uuid(), 
      NEW.salon_id, 
      salon_owner_id, 
      'new_booking', 
      'Yeni Randevu', 
      format('%s tarafından yeni bir randevu oluşturuldu. (%s, %s)', 
             COALESCE(NEW.fullname, 'Bir müşteri'), 
             appointment_date, 
             appointment_time),
      false, 
      now(), 
      jsonb_build_object('id', NEW.id)
    );
  END IF;
  
  -- Berbere bildirim gönder (eğer atanmışsa ve kullanıcı hesabı varsa)
  IF barber_user_id IS NOT NULL AND barber_user_id != salon_owner_id THEN
    INSERT INTO notifications (
      id, 
      salon_id, 
      user_id, 
      type, 
      title, 
      message, 
      read, 
      created_at, 
      data
    ) VALUES (
      gen_random_uuid(), 
      NEW.salon_id, 
      barber_user_id, 
      'new_booking', 
      'Yeni Randevu', 
      format('%s tarafından yeni bir randevu oluşturuldu. (%s, %s)', 
             COALESCE(NEW.fullname, 'Bir müşteri'), 
             appointment_date, 
             appointment_time),
      false, 
      now(), 
      jsonb_build_object('id', NEW.id)
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Randevu iptal edildiğinde bildirim oluşturan fonksiyon
CREATE OR REPLACE FUNCTION create_cancelled_appointment_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  barber_user_id UUID;
  appointment_date TEXT;
  appointment_time TEXT;
BEGIN
  -- Sadece durum 'cancelled' olarak değiştiğinde çalış
  IF OLD.status != 'cancelled' AND NEW.status = 'cancelled' THEN
    -- Salon sahibini bul
    SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;
    
    -- Berber kullanıcı ID'sini bul (eğer atanmışsa)
    IF NEW.barber_id IS NOT NULL THEN
      SELECT user_id INTO barber_user_id FROM barbers WHERE id = NEW.barber_id;
    END IF;
    
    -- Tarih ve saat formatla
    appointment_date := to_char(NEW.date, 'DD Month YYYY');
    appointment_time := substring(NEW.start_time, 1, 5);
    
    -- Salon sahibine bildirim gönder
    IF salon_owner_id IS NOT NULL THEN
      INSERT INTO notifications (
        id, 
        salon_id, 
        user_id, 
        type, 
        title, 
        message, 
        read, 
        created_at, 
        data
      ) VALUES (
        gen_random_uuid(), 
        NEW.salon_id, 
        salon_owner_id, 
        'cancellation', 
        'Randevu İptali', 
        format('%s randevusu iptal edildi. (%s, %s)', 
               COALESCE(NEW.fullname, 'Bir müşteri'), 
               appointment_date, 
               appointment_time),
        false, 
        now(), 
        jsonb_build_object('id', NEW.id)
      );
    END IF;
    
    -- Berbere bildirim gönder (eğer atanmışsa ve kullanıcı hesabı varsa)
    IF barber_user_id IS NOT NULL AND barber_user_id != salon_owner_id THEN
      INSERT INTO notifications (
        id, 
        salon_id, 
        user_id, 
        type, 
        title, 
        message, 
        read, 
        created_at, 
        data
      ) VALUES (
        gen_random_uuid(), 
        NEW.salon_id, 
        barber_user_id, 
        'cancellation', 
        'Randevu İptali', 
        format('%s randevusu iptal edildi. (%s, %s)', 
               COALESCE(NEW.fullname, 'Bir müşteri'), 
               appointment_date, 
               appointment_time),
        false, 
        now(), 
        jsonb_build_object('id', NEW.id)
      );
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ları oluştur
DROP TRIGGER IF EXISTS on_appointment_created ON appointments;
CREATE TRIGGER on_appointment_created
AFTER INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION create_new_appointment_notification();

DROP TRIGGER IF EXISTS on_appointment_cancelled ON appointments;
CREATE TRIGGER on_appointment_cancelled
AFTER UPDATE ON appointments
FOR EACH ROW
EXECUTE FUNCTION create_cancelled_appointment_notification();
