"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Plus, BarChart3, DollarSign, Globe, Settings } from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { AppointmentCalendar } from "@/components/appointment-calendar"
import { useUser } from "@/contexts/UserContext"
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function Page() {
  // Artık salonId UserContext'ten geliyor
  const [isLoading, setIsLoading] = useState(true)

  // UserContext'ten salon ve kullanıcı bilgilerini al
  const { salonId, salonLoading, userRole, isAdminUser } = useUser()

  // Abonelik özelliklerini al
  const { features, plan, isLoading: featuresLoading } = useSubscriptionFeatures()

  // Update loading state based on salon loading and features loading
  useEffect(() => {
    if (!salonLoading && !featuresLoading) {
      setIsLoading(false)
    }
  }, [salonLoading, featuresLoading])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Yükleniyor...</p>
      </div>
    )
  }

  // Admin kullanıcısı için salon oluşturma ekranını gösterme
  // isAdminUser artık UserContext'ten geliyor

  if (!salonId && !isAdminUser && (userRole === 'new_user' || userRole === 'owner')) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Kontrol Paneli</h1>
          </div>
        </header>

        <div className="flex flex-col items-center justify-center h-64 space-y-6">
          <div className="text-center space-y-2">
            <h2 className="text-2xl font-bold">Hoş Geldiniz!</h2>
            <p className="text-muted-foreground">SalonFlow'a hoş geldiniz. Başlamak için lütfen salonunuzu oluşturun.</p>
          </div>
          <div className="flex flex-col items-center space-y-4">
            <Button size="lg" asChild className="w-64">
              <Link href="/dashboard/settings">
                <Settings className="mr-2 h-5 w-5" />
                Salon Oluştur
              </Link>
            </Button>
            <p className="text-xs text-muted-foreground">Salon oluşturduktan sonra randevu almaya başlayabilirsiniz.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Kontrol Paneli</h1>
        </div>
      </header>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold">Randevular</h2>
          <Button asChild size="sm">
            <Link href="/dashboard/appointments/new">
              <Plus className="mr-2 h-4 w-4" />
              Yeni Randevu
            </Link>
          </Button>
        </div>

        {salonId && (
          <div className="space-y-6">
            <AppointmentCalendar salonId={salonId} />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Hızlı Erişim</CardTitle>
                  <CardDescription>Sık kullanılan işlemler</CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button className="w-full" asChild>
                    <Link href="/dashboard/appointments/new">Yeni Randevu Oluştur</Link>
                  </Button>
                  <Button className="w-full" variant="outline" asChild>
                    <Link href="/dashboard/customers">Müşterileri Görüntüle</Link>
                  </Button>
                </CardContent>
              </Card>

              {userRole === 'owner' && (
                <>
                  <Card>
                    <CardHeader>
                      <CardTitle>Salon Yönetimi</CardTitle>
                      <CardDescription>Salon ayarlarınızı yönetin</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <Button className="w-full" variant="outline" asChild>
                        <Link href="/dashboard/services">Hizmetleri Yönet</Link>
                      </Button>
                      <Button className="w-full" variant="outline" asChild>
                        <Link href="/dashboard/staff">Personeli Yönet</Link>
                      </Button>
                    </CardContent>
                    <CardFooter className="pt-0">
                      <div className="text-xs text-muted-foreground">
                        Personel Limiti: <span className="font-medium">{features.maxStaff}</span>
                      </div>
                    </CardFooter>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Çalışma Saatleri</CardTitle>
                      <CardDescription>Çalışma saatlerinizi ayarlayın</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Button className="w-full" variant="outline" asChild>
                        <Link href="/dashboard/working-hours">Saatleri Düzenle</Link>
                      </Button>
                    </CardContent>
                  </Card>
                </>
              )}

              {/* Premium Özellikler - Abonelik planına göre göster */}
              {userRole === 'owner' && (
                <div className="col-span-1 md:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  {/* Finans Kartı */}
                  <Card className={!features.hasFinance ? "opacity-70" : ""}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-base">Finans Yönetimi</CardTitle>
                        {!features.hasFinance && (
                          <Badge variant="outline" className="text-xs">Premium</Badge>
                        )}
                      </div>
                      <CardDescription>Gelir ve gider takibi</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center h-12 w-12 rounded-full bg-primary/10 mb-4">
                        <DollarSign className="h-6 w-6 text-primary" />
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        Salon gelir ve giderlerinizi takip edin, finansal raporlar oluşturun.
                      </p>
                    </CardContent>
                    <CardFooter>
                      {features.hasFinance ? (
                        <Button className="w-full" asChild>
                          <Link href="/dashboard/finance">
                            Finans Yönetimine Git
                          </Link>
                        </Button>
                      ) : (
                        <Button className="w-full" variant="outline" asChild>
                          <Link href="/dashboard/subscription/upgrade">
                            Planınızı Yükseltin
                          </Link>
                        </Button>
                      )}
                    </CardFooter>
                  </Card>

                  {/* Analitik Kartı */}
                  <Card className={!features.hasAnalytics ? "opacity-70" : ""}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-base">Analitik</CardTitle>
                        {!features.hasAnalytics && (
                          <Badge variant="outline" className="text-xs">Premium</Badge>
                        )}
                      </div>
                      <CardDescription>Detaylı raporlar ve analizler</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center h-12 w-12 rounded-full bg-primary/10 mb-4">
                        <BarChart3 className="h-6 w-6 text-primary" />
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        Randevu istatistikleri, personel performansı ve müşteri analizleri.
                      </p>
                    </CardContent>
                    <CardFooter>
                      {features.hasAnalytics ? (
                        <Button className="w-full" asChild>
                          <Link href="/dashboard/analytics">
                            Analitik Paneline Git
                          </Link>
                        </Button>
                      ) : (
                        <Button className="w-full" variant="outline" asChild>
                          <Link href="/dashboard/subscription/upgrade">
                            Planınızı Yükseltin
                          </Link>
                        </Button>
                      )}
                    </CardFooter>
                  </Card>

                  {/* Özel Alan Adı Kartı */}
                  <Card className={!features.hasCustomDomain ? "opacity-70" : ""}>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-base">Özel Alan Adı</CardTitle>
                        {!features.hasCustomDomain && (
                          <Badge variant="outline" className="text-xs">Pro</Badge>
                        )}
                      </div>
                      <CardDescription>Kendi alan adınızı kullanın</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center h-12 w-12 rounded-full bg-primary/10 mb-4">
                        <Globe className="h-6 w-6 text-primary" />
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">
                        Salonunuz için özel bir alan adı kullanarak profesyonel bir görünüm kazanın.
                      </p>
                    </CardContent>
                    <CardFooter>
                      {features.hasCustomDomain ? (
                        <Button className="w-full" asChild>
                          <Link href="/dashboard/custom-domain">
                            Alan Adı Ayarları
                          </Link>
                        </Button>
                      ) : (
                        <Button className="w-full" variant="outline" asChild>
                          <Link href="/dashboard/subscription/upgrade">
                            Planınızı Yükseltin
                          </Link>
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                </div>
              )}

              {userRole === 'staff' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Programım</CardTitle>
                    <CardDescription>Çalışma programınızı yönetin</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full" variant="outline" asChild>
                      <Link href="/dashboard/my-schedule">Programı Görüntüle</Link>
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
