-- Müşteri yönetimi için PostgreSQL fonksiyonu
CREATE OR REPLACE FUNCTION manage_customer_for_appointment()
RETURNS TRIGGER AS $$
DECLARE
  v_customer_id UUID;
  v_name TEXT;
  v_surname TEXT;
  v_fullname_parts TEXT[];
BEGIN
  -- <PERSON>ğer fullname ve phonenumber alanları doluysa işlem yap
  IF NEW.fullname IS NOT NULL AND NEW.phonenumber IS NOT NULL THEN
    -- Fullname'i ad ve soyad olarak ayır
    v_fullname_parts := regexp_split_to_array(NEW.fullname, ' ');
    
    IF array_length(v_fullname_parts, 1) > 0 THEN
      v_name := v_fullname_parts[1];
      
      -- Soyad için kalan kısmı birleştir
      IF array_length(v_fullname_parts, 1) > 1 THEN
        v_surname := array_to_string(v_fullname_parts[2:], ' ');
      ELSE
        v_surname := ''; -- Soyad yoksa boş string
      END IF;
      
      -- Aynı telefon numarasına sahip ve aynı salona ait müşteri var mı kontrol et
      SELECT id INTO v_customer_id
      FROM customers
      WHERE phone = NEW.phonenumber AND salon_id = NEW.salon_id
      LIMIT 1;
      
      -- Müşteri varsa güncelle, yoksa yeni müşteri oluştur
      IF v_customer_id IS NOT NULL THEN
        -- Müşteri bilgilerini güncelle
        UPDATE customers
        SET 
          name = v_name,
          surname = v_surname,
          email = NEW.email
        WHERE id = v_customer_id;
      ELSE
        -- Yeni müşteri oluştur
        INSERT INTO customers (
          salon_id,
          name,
          surname,
          phone,
          email
        ) VALUES (
          NEW.salon_id,
          v_name,
          v_surname,
          NEW.phonenumber,
          NEW.email
        )
        RETURNING id INTO v_customer_id;
      END IF;
      
      -- Appointment kaydının customer_id alanını güncelle
      NEW.customer_id := v_customer_id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger oluştur (INSERT için)
DROP TRIGGER IF EXISTS appointment_customer_insert_trigger ON appointments;
CREATE TRIGGER appointment_customer_insert_trigger
BEFORE INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION manage_customer_for_appointment();

-- Trigger oluştur (UPDATE için)
DROP TRIGGER IF EXISTS appointment_customer_update_trigger ON appointments;
CREATE TRIGGER appointment_customer_update_trigger
BEFORE UPDATE ON appointments
FOR EACH ROW
WHEN (OLD.fullname IS DISTINCT FROM NEW.fullname OR 
      OLD.phonenumber IS DISTINCT FROM NEW.phonenumber OR 
      OLD.email IS DISTINCT FROM NEW.email)
EXECUTE FUNCTION manage_customer_for_appointment();
