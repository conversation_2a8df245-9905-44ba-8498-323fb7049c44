"use client"

import { use<PERSON><PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back } from "react"
import Link from "next/link"
import { Plus, Mail, Phone, Search, ChevronDown, ArrowUpDown, X, Filter, MoreHorizontal, Pencil, Info, Trash } from "lucide-react"
import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Pagin<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>lli<PERSON>,
  <PERSON><PERSON>ationI<PERSON>,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { customers, salons } from "@/lib/db"
import type { Customer as CustomerType } from "@/lib/db/types"
import { useUser } from "@/contexts/UserContext"
import { debounce } from "@/lib/utils"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }),
  surname: z.string().min(2, {
    message: "Soyisim en az 2 karakter olmalıdır.",
  }),
  email: z.string().email({
    message: "Lütfen geçerli bir e-posta adresi girin.",
  }).optional().or(z.literal('')),
  phone: z.string().min(5, {
    message: "Telefon numarası en az 5 karakter olmalıdır.",
  }),
})

// We're using the Customer type from lib/db/types.ts

export default function CustomersPage() {
  const [customersList, setCustomersList] = useState<CustomerType[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<CustomerType[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<CustomerType | null>(null)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [totalPages, setTotalPages] = useState(1)

  // Sorting state
  const [sortField, setSortField] = useState<'name' | 'surname' | 'email' | 'phone'>('surname')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  // UserContext'ten kullanıcı ve salon bilgilerini al
  const { salonId, salonLoading } = useUser()

  // Load customers
  useEffect(() => {
    async function loadCustomers() {
      if (!salonId || salonLoading) return

      setLoading(true)
      try {
        const data = await customers.getCustomers(salonId)
        setCustomersList(data)
        setSearchResults(data)

        // Calculate total pages
        setTotalPages(Math.ceil(data.length / itemsPerPage))
      } catch (error) {
        console.error("Error loading customers:", error)
        toast.error("Müşteriler yüklenirken bir hata oluştu.")
      } finally {
        setLoading(false)
      }
    }

    loadCustomers()
  }, [salonId, salonLoading, itemsPerPage])

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!salonId || query.length < 2) {
        setSearchResults(customersList)
        setTotalPages(Math.ceil(customersList.length / itemsPerPage))
        setIsSearching(false)
        return
      }

      try {
        const results = await customers.searchCustomers(salonId, query)
        setSearchResults(results)
        setTotalPages(Math.ceil(results.length / itemsPerPage))
      } catch (error) {
        console.error("Error searching customers:", error)
        toast.error("Müşteri arama sırasında bir hata oluştu.")
      } finally {
        setIsSearching(false)
      }
    }, 300),
    [customersList, salonId, itemsPerPage]
  )

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    setIsSearching(true)
    setCurrentPage(1) // Reset to first page on new search
    debouncedSearch(query)
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // Handle items per page change
  const handleItemsPerPageChange = (value: string) => {
    const newItemsPerPage = parseInt(value)
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1) // Reset to first page when changing items per page
  }

  // Handle sort change
  const handleSortChange = (field: 'name' | 'surname' | 'email' | 'phone') => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      // Set new field and default to ascending
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Reset all filters
  const resetFilters = () => {
    setSearchQuery("")
    setSearchResults(customersList)
    setSortField('surname')
    setSortDirection('asc')
    setCurrentPage(1)
    setTotalPages(Math.ceil(customersList.length / itemsPerPage))
  }

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      surname: "",
      email: "",
      phone: "",
    },
  })

  // Set form values when editing
  useEffect(() => {
    if (editingCustomer) {
      form.reset({
        name: editingCustomer.name,
        surname: editingCustomer.surname,
        email: editingCustomer.email || "",
        phone: editingCustomer.phone,
      })
      setShowForm(true)
    }
  }, [editingCustomer, form])

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!salonId) {
      toast.error("Salon bilgisi bulunamadı.")
      return
    }

    try {
      if (editingCustomer) {
        // Update existing customer
        await customers.updateCustomer({
          id: editingCustomer.id,
          name: values.name,
          surname: values.surname,
          email: values.email || undefined,
          phone: values.phone,
        })

        toast.success("Müşteri başarıyla güncellendi!")
      } else {
        // Create new customer
        await customers.createCustomer({
          salon_id: salonId,
          name: values.name,
          surname: values.surname,
          email: values.email || undefined,
          phone: values.phone,
        })

        toast.success("Müşteri başarıyla eklendi!")
      }

      // Reload customers
      const data = await customers.getCustomers(salonId)
      setCustomersList(data)

      // Reset form and state
      form.reset()
      setShowForm(false)
      setEditingCustomer(null)
    } catch (error) {
      console.error("Error saving customer:", error)
      toast.error("Müşteri kaydedilirken bir hata oluştu.")
    }
  }

  // Handle customer deletion
  async function handleDelete(id: string) {
    if (!salonId) {
      toast.error("Salon bilgisi bulunamadı.")
      return
    }

    if (!confirm("Bu müşteriyi silmek istediğinizden emin misiniz?")) {
      return
    }

    try {
      await customers.deleteCustomer(id)
      toast.success("Müşteri başarıyla silindi!")

      // Reload customers
      const data = await customers.getCustomers(salonId)
      setCustomersList(data)
    } catch (error) {
      console.error("Error deleting customer:", error)
      toast.error("Müşteri silinirken bir hata oluştu.")
    }
  }

  // Handle edit button click
  function handleEdit(customer: CustomerType) {
    setEditingCustomer(customer)
  }

  // Handle cancel button click
  function handleCancel() {
    setShowForm(false)
    setEditingCustomer(null)
    form.reset()
  }

  // Sort customers
  const sortedCustomers = [...searchResults].sort((a, b) => {
    let valueA: string = '';
    let valueB: string = '';

    // Handle different sort fields
    if (sortField === 'name') {
      valueA = a.name.toLowerCase();
      valueB = b.name.toLowerCase();
    } else if (sortField === 'surname') {
      valueA = a.surname.toLowerCase();
      valueB = b.surname.toLowerCase();
    } else if (sortField === 'email') {
      valueA = (a.email || '').toLowerCase();
      valueB = (b.email || '').toLowerCase();
    } else if (sortField === 'phone') {
      valueA = a.phone.toLowerCase();
      valueB = b.phone.toLowerCase();
    }

    // Apply sort direction
    if (sortDirection === 'asc') {
      return valueA.localeCompare(valueB);
    } else {
      return valueB.localeCompare(valueA);
    }
  });

  // Apply pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedCustomers = sortedCustomers.slice(startIndex, endIndex);

  // Generate pagination items
  const renderPaginationItems = () => {
    const items = [];

    // First page is always shown
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink
          onClick={() => handlePageChange(1)}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // If total pages is 7 or less, show all pages
    if (totalPages <= 7) {
      for (let i = 2; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={`page-${i}`}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
      return items;
    }

    // If current page is less than 4, show first 5 pages, then ellipsis, then last page
    if (currentPage < 4) {
      for (let i = 2; i <= 5; i++) {
        items.push(
          <PaginationItem key={`page-${i}`}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
      items.push(
        <PaginationItem key={`page-${totalPages}`}>
          <PaginationLink
            onClick={() => handlePageChange(totalPages)}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
      return items;
    }

    // If current page is one of the last 3 pages, show first page, then ellipsis, then last 5 pages
    if (currentPage > totalPages - 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
      for (let i = totalPages - 4; i <= totalPages; i++) {
        if (i > 1) {
          items.push(
            <PaginationItem key={`page-${i}`}>
              <PaginationLink
                onClick={() => handlePageChange(i)}
                isActive={currentPage === i}
              >
                {i}
              </PaginationLink>
            </PaginationItem>
          );
        }
      }
      return items;
    }

    // For other cases, show first page, ellipsis, current page and surrounding pages, ellipsis, last page
    items.push(
      <PaginationItem key="ellipsis-1">
        <PaginationEllipsis />
      </PaginationItem>
    );
    for (let i = currentPage - 1; i <= currentPage + 1; i++) {
      items.push(
        <PaginationItem key={`page-${i}`}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={currentPage === i}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    items.push(
      <PaginationItem key="ellipsis-2">
        <PaginationEllipsis />
      </PaginationItem>
    );
    items.push(
      <PaginationItem key={`page-${totalPages}`}>
        <PaginationLink
          onClick={() => handlePageChange(totalPages)}
          isActive={currentPage === totalPages}
        >
          {totalPages}
        </PaginationLink>
      </PaginationItem>
    );
    return items;
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex items-center sm:h-16 shrink-0 gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Müşteriler</h1>
        </div>
      </header>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative w-full sm:max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Müşteri ara..."
            className="pl-10 w-full"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </div>

        <div className="flex items-center gap-2 w-full sm:w-auto justify-start sm:justify-end">
          {searchQuery && (
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="gap-1"
            >
              <X className="h-4 w-4" />
              <span className="hidden sm:inline">Filtreleri Temizle</span>
              <span className="sm:hidden">Temizle</span>
            </Button>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-1">
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Sayfa Başına:</span> {itemsPerPage}
                <ChevronDown className="h-4 w-4 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {[5, 10, 20, 50].map(value => (
                <DropdownMenuItem
                  key={value}
                  onClick={() => handleItemsPerPageChange(value.toString())}
                >
                  {value}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingCustomer ? "Müşteriyi Düzenle" : "Yeni Müşteri Ekle"}</CardTitle>
            <CardDescription>
              {editingCustomer ? "Mevcut bir müşteriyi düzenleyin." : "Salon veritabanınıza yeni bir müşteri ekleyin."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>İsim</FormLabel>
                        <FormControl>
                          <Input placeholder="Ahmet" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="surname"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Soyisim</FormLabel>
                        <FormControl>
                          <Input placeholder="Yılmaz" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefon</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="+90 (555) 123 45 67"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>E-posta (İsteğe bağlı)</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                  >
                    İptal
                  </Button>
                  <Button type="submit">
                    {editingCustomer ? "Müşteriyi Güncelle" : "Müşteriyi Kaydet"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {loading ? (
        <div className="w-full space-y-3">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </div>
      ) : searchResults.length === 0 ? (
        <>
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">Müşteri Listesi</h2>
            <Button
              onClick={() => {
                setEditingCustomer(null)
                form.reset()
                setShowForm(!showForm)
              }}
              disabled={loading}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              size="sm"
            >
              <Plus className="mr-2 h-4 w-4" />
              Müşteri Ekle
            </Button>
          </div>
          <div className="text-center py-12 border rounded-md bg-muted/10">
            {searchQuery ? (
              <div className="space-y-2">
                <p className="text-muted-foreground">
                  "{searchQuery}" ile eşleşen müşteri bulunamadı.
                </p>
                <Button variant="outline" size="sm" onClick={resetFilters} className="mt-2">
                  <X className="mr-2 h-4 w-4" />
                  Filtreleri Temizle
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <p className="text-muted-foreground">
                  Henüz müşteri eklenmemiş.
                </p>
                <Button
                  onClick={() => {
                    setEditingCustomer(null)
                    form.reset()
                    setShowForm(true)
                  }}
                  className="mt-2"
                  size="sm"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  İlk Müşterinizi Ekleyin
                </Button>
              </div>
            )}
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-lg font-medium">Müşteri Listesi</h2>
            <Button
              onClick={() => {
                setEditingCustomer(null)
                form.reset()
                setShowForm(!showForm)
              }}
              disabled={loading}
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              size="sm"
            >
              <Plus className="mr-2 h-4 w-4" />
              Müşteri Ekle
            </Button>
          </div>
          <div className="rounded-md border overflow-x-auto bg-card">
            <Table>
              <TableHeader className="bg-muted/30">
                <TableRow>
                  <TableHead className="w-[250px] cursor-pointer whitespace-nowrap font-medium" onClick={() => handleSortChange('surname')}>
                    <div className="flex items-center gap-1">
                      İsim Soyisim
                      {sortField === 'surname' && (
                        <ArrowUpDown className={`h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer whitespace-nowrap hidden sm:table-cell font-medium" onClick={() => handleSortChange('phone')}>
                    <div className="flex items-center gap-1">
                      Telefon
                      {sortField === 'phone' && (
                        <ArrowUpDown className={`h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="cursor-pointer whitespace-nowrap hidden md:table-cell font-medium" onClick={() => handleSortChange('email')}>
                    <div className="flex items-center gap-1">
                      E-posta
                      {sortField === 'email' && (
                        <ArrowUpDown className={`h-4 w-4 ${sortDirection === 'desc' ? 'rotate-180' : ''}`} />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="text-right font-medium">İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell className="font-medium">
                      <div>
                        {customer.name} {customer.surname}
                        <div className="sm:hidden text-sm text-muted-foreground flex items-center gap-1 mt-1">
                          <Phone className="h-3 w-3" /> {customer.phone}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        {customer.phone}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {customer.email ? (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          {customer.email}
                        </div>
                      ) : (
                        <span className="text-muted-foreground text-sm">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">İşlemler</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(customer)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            Düzenle
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/customers/${customer.id}`} className="flex items-center">
                              <Info className="mr-2 h-4 w-4" />
                              Detaylar
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(customer.id)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Sil
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination and Summary */}
          {totalPages > 1 && (
            <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-muted-foreground order-2 sm:order-1">
                Toplam {searchResults.length} müşteri, {currentPage}/{totalPages} sayfa
              </div>

              <div className="overflow-x-auto order-1 sm:order-2">
                <Pagination>
                  <PaginationContent className="flex-wrap justify-center">
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                        aria-disabled={currentPage === 1}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>

                    {/* Mobil cihazlarda daha az sayfa numarası göster */}
                    <div className="hidden sm:flex">
                      {renderPaginationItems()}
                    </div>

                    {/* Mobil cihazlarda sadece mevcut sayfa numarasını göster */}
                    <div className="sm:hidden flex items-center">
                      <PaginationItem>
                        <PaginationLink isActive={true}>
                          {currentPage} / {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </div>

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                        aria-disabled={currentPage === totalPages}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}

          {totalPages <= 1 && (
            <div className="text-sm text-muted-foreground mt-2 text-center">
              Toplam {searchResults.length} müşteri
            </div>
          )}
        </>
      )}
    </div>
  )
}
