"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { barbers } from "@/lib/db"
import type { Barber } from "@/lib/db/types"
import { useUser } from "@/contexts/UserContext"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phone: z.string().optional(),
})

export default function ProfilePage() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [barber, setBarber] = useState<Barber | null>(null)

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
    },
  })

  // UserContext'ten kullanıcı bilgilerini al
  const { user, isLoading: userLoading } = useUser()

  // Load staff profile
  useEffect(() => {
    async function loadProfile() {
      if (userLoading) return

      try {
        if (!user) {
          //router.push("/auth/login") TODO
          return
        }

        // Get the barber profile for this user
        const barberData = await barbers.getBarberByUserId(user.id)

        if (!barberData) {
          toast.error("Staff profile not found")
          //router.push("/dashboard")TODO
          return
        }

        setBarber(barberData)

        // Set form values
        form.reset({
          name: barberData.name,
          email: barberData.email || "",
          phone: barberData.phone || "",
        })
      } catch (error) {
        console.error("Error loading profile:", error)
        toast.error("Failed to load profile")
      } finally {
        setLoading(false)
      }
    }

    loadProfile()
  }, [user, userLoading, router, form])

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!barber) {
      toast.error("Staff profile not found")
      return
    }

    try {
      // Update barber profile
      await barbers.updateBarber({
        id: barber.id,
        name: values.name,
        email: values.email,
        phone: values.phone || undefined,
      })

      toast.success("Profile updated successfully")
    } catch (error) {
      console.error("Error updating profile:", error)
      toast.error("Failed to update profile")
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Profilim</h1>
        </div>
      </header>

      <Card>
        <CardHeader>
          <CardTitle>Profil Bilgileri</CardTitle>
          <CardDescription>
            Kişisel bilgilerinizi güncelleyin
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ad Soyad</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>E-posta</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        disabled
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Telefon (İsteğe Bağlı)</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="+****************"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end">
                <Button type="submit">
                  Profili Güncelle
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Şifre</CardTitle>
          <CardDescription>
            Şifrenizi değiştirin
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="outline"
            onClick={() => router.push("/auth/forgot-password")}
          >
            Şifreyi Sıfırla
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
