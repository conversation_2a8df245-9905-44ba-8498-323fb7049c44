"use client"

import React, { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { format, parseISO } from "date-fns"
import { tr } from "date-fns/locale"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"
import { ArrowLeft, ArrowDownAZ, ArrowUpZA, Calendar, Clock, Filter, Mail, Phone, Scissors, User, ChevronDown } from "lucide-react"

import { Button } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { customers, barbers as barbersDb, services as servicesDb } from "@/lib/db"
import type { Customer as CustomerType } from "@/lib/db/types"
import { useUser } from "@/contexts/UserContext"

// Define the form schema for notes
const notesFormSchema = z.object({
  notes: z.string().optional(),
})

export default function CustomerDetailsPage() {
  const params = useParams()
  const customerId = params.id as string

  const { salon } = useUser()

  const [customer, setCustomer] = useState<CustomerType | null>(null)
  const [appointmentHistory, setAppointmentHistory] = useState<any[]>([])
  const [filteredAppointments, setFilteredAppointments] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isNotesLoading, setIsNotesLoading] = useState(false)

  // Filtreleme state'leri
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [dateRangeFilter, setDateRangeFilter] = useState<{from?: Date, to?: Date}>({})
  const [barberFilter, setBarberFilter] = useState<string>("all")
  const [serviceFilter, setServiceFilter] = useState<string>("all")

  // Sayfalama state'leri
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [paginatedAppointments, setPaginatedAppointments] = useState<any[]>([])
  const [totalPages, setTotalPages] = useState(1)

  // Berber ve hizmet listesi
  const [barbers, setBarbers] = useState<{id: string, name: string}[]>([])
  const [services, setServices] = useState<{id: string, name: string}[]>([])

  // Initialize the form
  const form = useForm<z.infer<typeof notesFormSchema>>({
    resolver: zodResolver(notesFormSchema),
    defaultValues: {
      notes: "",
    },
  })

  // Load customer details and appointment history
  useEffect(() => {
    async function loadCustomerData() {
      setIsLoading(true)
      try {
        // Load customer details
        const customerData = await customers.getCustomerById(customerId)
        setCustomer(customerData)

        // Set form default values
        form.reset({
          notes: customerData.notes || "",
        })

        // Load appointment history
        const historyData = await customers.getCustomerAppointmentHistory(customerId)
        setAppointmentHistory(historyData)
        setFilteredAppointments(historyData)

        // Calculate total pages
        setTotalPages(Math.ceil(historyData.length / itemsPerPage))
      } catch (error) {
        console.error("Error loading customer data:", error)
        toast.error("Müşteri bilgileri yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadCustomerData()
  }, [customerId, form, itemsPerPage])

  // Load berber ve hizmet listesi
  useEffect(() => {
    async function loadFiltersData() {
      if (!salon?.id) return

      try {
        // Load barbers
        const barbersData = await barbersDb.getBarbers(salon.id)
        setBarbers(barbersData.map(b => ({ id: b.id, name: b.name })))

        // Load services
        const servicesData = await servicesDb.getServices(salon.id)
        setServices(servicesData.map(s => ({ id: s.id, name: s.name })))
      } catch (error) {
        console.error("Error loading filters data:", error)
      }
    }

    loadFiltersData()
  }, [salon?.id])

  // Filter and sort appointments when filters or appointment history changes
  useEffect(() => {
    if (!appointmentHistory.length) return

    let filtered = [...appointmentHistory]

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(appointment => appointment.status === statusFilter)
    }

    // Apply date range filter
    if (dateRangeFilter.from) {
      filtered = filtered.filter(appointment => {
        const appointmentDate = new Date(appointment.date)
        if (dateRangeFilter.from && dateRangeFilter.to) {
          return appointmentDate >= dateRangeFilter.from && appointmentDate <= dateRangeFilter.to
        } else if (dateRangeFilter.from) {
          return appointmentDate >= dateRangeFilter.from
        }
        return true
      })
    }

    // Apply barber filter
    if (barberFilter !== "all") {
      filtered = filtered.filter(appointment => appointment.barber_id === barberFilter)
    }

    // Apply service filter
    if (serviceFilter !== "all") {
      filtered = filtered.filter(appointment => appointment.service_id === serviceFilter)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      // First compare by date
      const dateA = new Date(a.date)
      const dateB = new Date(b.date)

      if (dateA.getTime() !== dateB.getTime()) {
        return sortOrder === "asc"
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime()
      }

      // If dates are the same, compare by time
      const timeA = a.start_time
      const timeB = b.start_time

      return sortOrder === "asc"
        ? timeA.localeCompare(timeB)
        : timeB.localeCompare(timeA)
    })

    setFilteredAppointments(filtered)

    // Update total pages
    setTotalPages(Math.ceil(filtered.length / itemsPerPage))

    // Reset to first page when filters change
    setCurrentPage(1)
  }, [appointmentHistory, statusFilter, sortOrder, dateRangeFilter, barberFilter, serviceFilter, itemsPerPage])

  // Apply pagination
  useEffect(() => {
    if (!filteredAppointments.length) {
      setPaginatedAppointments([])
      return
    }

    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    const paginatedItems = filteredAppointments.slice(startIndex, endIndex)

    setPaginatedAppointments(paginatedItems)
  }, [filteredAppointments, currentPage, itemsPerPage])

  // Handle notes form submission
  async function onSubmit(values: z.infer<typeof notesFormSchema>) {
    setIsNotesLoading(true)
    try {
      await customers.updateCustomerNotes(customerId, values.notes || "")
      toast.success("Müşteri notları başarıyla güncellendi!")

      // Update customer state
      const updatedCustomer = await customers.getCustomerById(customerId)
      setCustomer(updatedCustomer)
    } catch (error) {
      console.error("Error updating customer notes:", error)
      toast.error("Müşteri notları güncellenirken bir hata oluştu.")
    } finally {
      setIsNotesLoading(false)
    }
  }

  // Format appointment status for display
  const formatStatus = (status: string) => {
    switch (status) {
      case "booked":
        return <Badge variant="outline">Rezerve</Badge>
      case "completed":
        return <Badge variant="success">Tamamlandı</Badge>
      case "cancelled":
        return <Badge variant="destructive">İptal Edildi</Badge>
      case "no-show":
        return <Badge variant="warning">Gelmedi</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Sayfalama yardımcı fonksiyonları
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value))
  }

  // Filtreleri sıfırlama
  const resetFilters = () => {
    setStatusFilter("all")
    setDateRangeFilter({})
    setBarberFilter("all")
    setServiceFilter("all")
    setSortOrder("desc")
  }

  // Sayfalama bileşeni için sayfa numaralarını oluşturma
  const renderPaginationItems = () => {
    const items = []

    // İlk sayfa her zaman gösterilir
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink
          onClick={() => handlePageChange(1)}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    )

    // Toplam sayfa sayısı 7'den azsa, tüm sayfaları göster
    if (totalPages <= 7) {
      for (let i = 2; i <= totalPages; i++) {
        items.push(
          <PaginationItem key={`page-${i}`}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        )
      }
      return items
    }

    // Mevcut sayfa 4'ten küçükse, ilk 5 sayfayı göster, sonra ellipsis, sonra son sayfayı
    if (currentPage < 4) {
      for (let i = 2; i <= 5; i++) {
        items.push(
          <PaginationItem key={`page-${i}`}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        )
      }
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      )
      items.push(
        <PaginationItem key={`page-${totalPages}`}>
          <PaginationLink
            onClick={() => handlePageChange(totalPages)}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      )
      return items
    }

    // Mevcut sayfa son 3 sayfadan biriyse, ilk sayfayı göster, sonra ellipsis, sonra son 5 sayfayı
    if (currentPage > totalPages - 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      )
      for (let i = totalPages - 4; i <= totalPages; i++) {
        if (i > 1) {
          items.push(
            <PaginationItem key={`page-${i}`}>
              <PaginationLink
                onClick={() => handlePageChange(i)}
                isActive={currentPage === i}
              >
                {i}
              </PaginationLink>
            </PaginationItem>
          )
        }
      }
      return items
    }

    // Diğer durumlarda, ilk sayfayı göster, sonra ellipsis, sonra mevcut sayfanın etrafındaki 2 sayfayı,
    // sonra ellipsis, sonra son sayfayı
    items.push(
      <PaginationItem key="ellipsis-1">
        <PaginationEllipsis />
      </PaginationItem>
    )
    for (let i = currentPage - 1; i <= currentPage + 1; i++) {
      items.push(
        <PaginationItem key={`page-${i}`}>
          <PaginationLink
            onClick={() => handlePageChange(i)}
            isActive={currentPage === i}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      )
    }
    items.push(
      <PaginationItem key="ellipsis-2">
        <PaginationEllipsis />
      </PaginationItem>
    )
    items.push(
      <PaginationItem key={`page-${totalPages}`}>
        <PaginationLink
          onClick={() => handlePageChange(totalPages)}
          isActive={currentPage === totalPages}
        >
          {totalPages}
        </PaginationLink>
      </PaginationItem>
    )
    return items
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Yükleniyor...</p>
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-lg mb-4">Müşteri bulunamadı.</p>
        <Button asChild>
          <Link href="/dashboard/customers">Müşteri Listesine Dön</Link>
        </Button>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/customers">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Müşteri Detayları</h1>
        </div>
      </header>

      <Tabs defaultValue="details" className="w-full">
        <TabsList>
          <TabsTrigger value="details">Müşteri Bilgileri</TabsTrigger>
          <TabsTrigger value="appointments">Randevu Geçmişi</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{customer.name} {customer.surname}</CardTitle>
              <CardDescription>Müşteri Bilgileri</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">İletişim Bilgileri</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{customer.phone}</span>
                    </div>
                    {customer.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{customer.email}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Salon</h3>
                  <p>{salon?.name || "Bilinmiyor"}</p>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">Müşteri Notları</h3>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder="Müşteri hakkında notlar..."
                              className="min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" disabled={isNotesLoading}>
                      {isNotesLoading ? "Kaydediliyor..." : "Notları Kaydet"}
                    </Button>
                  </form>
                </Form>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appointments" className="space-y-6 mt-6">
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Randevu Geçmişi</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="flex items-center gap-1"
              >
                Filtreleri Sıfırla
              </Button>
            </div>

            {/* Gelişmiş Filtreleme Bölümü */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              {/* Durum Filtresi */}
              <div>
                <label className="text-sm font-medium mb-1 block">Durum</label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <Filter className="h-4 w-4" />
                        <span>
                          {statusFilter === "all" ? "Tüm Durumlar" :
                           statusFilter === "booked" ? "Rezerve" :
                           statusFilter === "completed" ? "Tamamlandı" :
                           statusFilter === "cancelled" ? "İptal Edildi" :
                           statusFilter === "no-show" ? "Gelmedi" : "Filtrele"}
                        </span>
                      </div>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    <DropdownMenuItem onClick={() => setStatusFilter("all")}>
                      Tüm Durumlar
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setStatusFilter("booked")}>
                      Rezerve
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setStatusFilter("completed")}>
                      Tamamlandı
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setStatusFilter("cancelled")}>
                      İptal Edildi
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setStatusFilter("no-show")}>
                      Gelmedi
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Tarih Aralığı Filtresi */}
              <div>
                <label className="text-sm font-medium mb-1 block">Tarih Aralığı</label>
                <DatePickerWithRange
                  dateRange={dateRangeFilter}
                  setDateRange={setDateRangeFilter}
                />
              </div>

              {/* Berber Filtresi */}
              <div>
                <label className="text-sm font-medium mb-1 block">Berber</label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-between">
                      <span>
                        {barberFilter === "all"
                          ? "Tüm Berberler"
                          : barbers.find(b => b.id === barberFilter)?.name || "Berber Seçin"}
                      </span>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    <DropdownMenuItem onClick={() => setBarberFilter("all")}>
                      Tüm Berberler
                    </DropdownMenuItem>
                    {barbers.map(barber => (
                      <DropdownMenuItem
                        key={barber.id}
                        onClick={() => setBarberFilter(barber.id)}
                      >
                        {barber.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Hizmet Filtresi */}
              <div>
                <label className="text-sm font-medium mb-1 block">Hizmet</label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-between">
                      <span>
                        {serviceFilter === "all"
                          ? "Tüm Hizmetler"
                          : services.find(s => s.id === serviceFilter)?.name || "Hizmet Seçin"}
                      </span>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[200px]">
                    <DropdownMenuItem onClick={() => setServiceFilter("all")}>
                      Tüm Hizmetler
                    </DropdownMenuItem>
                    {services.map(service => (
                      <DropdownMenuItem
                        key={service.id}
                        onClick={() => setServiceFilter(service.id)}
                      >
                        {service.name}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Sıralama ve Sayfa Başına Öğe Sayısı */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Sayfa başına:</span>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                      {itemsPerPage}
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {[5, 10, 20, 50].map(value => (
                      <DropdownMenuItem
                        key={value}
                        onClick={() => handleItemsPerPageChange(value.toString())}
                      >
                        {value}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
                className="flex items-center gap-1"
              >
                {sortOrder === "asc" ? (
                  <>
                    <ArrowUpZA className="h-4 w-4" />
                    <span>Eskiden Yeniye</span>
                  </>
                ) : (
                  <>
                    <ArrowDownAZ className="h-4 w-4" />
                    <span>Yeniden Eskiye</span>
                  </>
                )}
              </Button>
            </div>

            {/* Randevu Tablosu */}
            {appointmentHistory.length === 0 ? (
              <Card>
                <CardContent className="p-6">
                  <p className="text-center text-muted-foreground">Bu müşteriye ait randevu bulunamadı.</p>
                </CardContent>
              </Card>
            ) : filteredAppointments.length === 0 ? (
              <Card>
                <CardContent className="p-6">
                  <p className="text-center text-muted-foreground">Seçilen filtrelere uygun randevu bulunamadı.</p>
                </CardContent>
              </Card>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tarih</TableHead>
                      <TableHead>Saat</TableHead>
                      <TableHead>Hizmet</TableHead>
                      <TableHead>Berber</TableHead>
                      <TableHead>Durum</TableHead>
                      <TableHead className="text-right">İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedAppointments.map((appointment) => (
                      <TableRow key={appointment.id}>
                        <TableCell>
                          {format(parseISO(appointment.date), "d MMMM yyyy", { locale: tr })}
                        </TableCell>
                        <TableCell>
                          {appointment.start_time.substring(0, 5)} - {appointment.end_time.substring(0, 5)}
                        </TableCell>
                        <TableCell>
                          {appointment.services?.name || "Bilinmeyen Hizmet"}
                        </TableCell>
                        <TableCell>
                          {appointment.barbers?.name || "Bilinmeyen Berber"}
                        </TableCell>
                        <TableCell>
                          {formatStatus(appointment.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/dashboard/appointments/${appointment.id}`}>
                              Detaylar
                            </Link>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}

            {/* Sayfalama */}
            {filteredAppointments.length > 0 && (
              <div className="mt-4">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                        aria-disabled={currentPage === 1}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>

                    {renderPaginationItems()}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                        aria-disabled={currentPage === totalPages}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>

                <div className="text-center text-sm text-muted-foreground mt-2">
                  Toplam {filteredAppointments.length} randevu, {totalPages} sayfa
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
