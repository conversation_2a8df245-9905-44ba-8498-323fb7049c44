-- Custom Domains Table for SalonFlow
-- Date: 2024-07-17

-- Create custom_domains table
CREATE TABLE custom_domains (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  domain TEXT NOT NULL UNIQUE,
  is_verified BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on domain for faster lookups
CREATE INDEX idx_custom_domains_domain ON custom_domains(domain);

-- Create index on salon_id for faster lookups
CREATE INDEX idx_custom_domains_salon_id ON custom_domains(salon_id);

-- Enable Row Level Security
ALTER TABLE custom_domains ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Service role bypass policy
CREATE POLICY "Service role bypass" ON custom_domains
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Anyone can view verified custom domains (for middleware)
CREATE POLICY "Anyone can view verified custom domains" ON custom_domains
  FOR SELECT
  USING (is_verified = true);
