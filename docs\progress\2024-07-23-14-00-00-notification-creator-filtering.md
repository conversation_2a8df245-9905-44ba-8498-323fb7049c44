# Bildirim Oluşturucu Filtreleme - 2024-07-23

## <PERSON><PERSON> Bakış

Bu belge, berber/salon sahiplerinin kendi oluşturdukları randevuların bildirimlerini görmemelerini sağlamak için yapılan değişiklikleri belgelemektedir.

## Sorun

Mevcut sistemde, bir randevu oluşturulduğunda veya iptal edildiğinde, salon sahibine ve ilgili berbere bildirim gönderilmektedir. <PERSON><PERSON><PERSON>, eğer randevuyu oluşturan veya iptal eden kişi salon sahibi veya berberin kendisi ise, bu kişilerin kendi yaptıkları işlemler için bildirim almamaları gerekmektedir.

## Çözüm

1. Appointments tablosuna `created_by` sü<PERSON><PERSON> eklendi. Bu sütun, randevuyu oluşturan kullanıcının ID'sini saklamaktadır.
2. <PERSON><PERSON><PERSON> oluşturulduğunda `created_by` al<PERSON><PERSON><PERSON><PERSON> otomatik olarak dolduran bir trigger oluşturuldu.
3. Bildir<PERSON> trigger'ları güncellendi:
   - <PERSON><PERSON> randevu bildirimleri için, bildirim alacak kullanıcı ile randevuyu oluşturan kullanıcı aynı ise bildirim gönderilmemektedir.
   - İptal edilen randevu bildirimleri için, bildirim alacak kullanıcı ile randevuyu iptal eden kullanıcı aynı ise bildirim gönderilmemektedir.

## Teknik Detaylar

### 1. Appointments Tablosuna created_by Sütunu Ekleme

```sql
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
```

### 2. created_by Alanını Otomatik Dolduran Trigger

```sql
CREATE OR REPLACE FUNCTION set_appointment_created_by()
RETURNS TRIGGER AS $$
BEGIN
  -- Sadece kullanıcı oturum açmışsa created_by alanını doldur
  IF auth.uid() IS NOT NULL THEN
    NEW.created_by := auth.uid();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_appointment_created_by_trigger ON appointments;
CREATE TRIGGER set_appointment_created_by_trigger
BEFORE INSERT ON appointments
FOR EACH ROW
EXECUTE FUNCTION set_appointment_created_by();
```

### 3. Bildirim Trigger'larını Güncelleme

Yeni randevu bildirimleri için:
- Salon sahibine bildirim gönderirken `salon_owner_id != NEW.created_by` kontrolü eklendi.
- Berbere bildirim gönderirken `barber_user_id != NEW.created_by` kontrolü eklendi.

İptal edilen randevu bildirimleri için:
- Salon sahibine bildirim gönderirken `salon_owner_id != auth.uid()` kontrolü eklendi.
- Berbere bildirim gönderirken `barber_user_id != auth.uid()` kontrolü eklendi.

## Uygulama

Bu değişiklikler, `docs/sql/2024-07-23-14-00-00-appointment-creator-tracking.sql` dosyasında bulunmaktadır ve Supabase veritabanına uygulanmalıdır.

## Sonuç

Bu değişikliklerle birlikte, berber/salon sahipleri kendi oluşturdukları veya iptal ettikleri randevular için bildirim almayacaklardır. Bu, kullanıcı deneyimini iyileştirecek ve gereksiz bildirimleri önleyecektir.
