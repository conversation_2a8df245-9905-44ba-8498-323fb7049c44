-- Notifications RLS Politikalarını Düzeltme
-- Bu SQL dosyası, notifications tablosunun RLS politikalarını düzeltmek için gerekli değişiklikleri içerir.

-- Mevcut politikaları temizleyelim
DROP POLICY IF EXISTS "Salon owners can see their salon notifications" ON notifications;
DROP POLICY IF EXISTS "Salon owners can manage their salon notifications" ON notifications;
DROP POLICY IF EXISTS "Staff can see their own notifications" ON notifications;
DROP POLICY IF EXISTS "Staff can manage their own notifications" ON notifications;

-- RLS'yi geçici olarak devre dışı bırakalım (trigger'ların çalışabilmesi için)
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;

-- Servis rolü için bypass politikası ekleyelim
CREATE POLICY "Service role bypass for notifications" ON notifications
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Salon sahipleri için politika
CREATE POLICY "Salon owners can manage their salon notifications" ON notifications
  FOR ALL
  TO authenticated
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Berberler için politika
CREATE POLICY "Staff can manage their own notifications" ON notifications
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Herkes için ekleme politikası (trigger'lar için)
CREATE POLICY "Anyone can insert notifications" ON notifications
  FOR INSERT
  WITH CHECK (true);

-- RLS'yi tekrar etkinleştirelim
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
