// Test file for useSubscriptionFeatures hook
require('dotenv').config({ path: '.env.local' });
const { expect } = require('chai');
const sinon = require('sinon');
const { createClient } = require('@supabase/supabase-js');
const { JSDOM } = require('jsdom');
const React = require('react');
const { renderHook, act } = require('@testing-library/react-hooks');

// Mock React
global.React = React;

// Set up JSDOM
const dom = new JSDOM('<!doctype html><html><body></body></html>', {
  url: 'http://localhost',
});
global.window = dom.window;
global.document = dom.window.document;
global.navigator = dom.window.navigator;

// Mock the SubscriptionContext
const mockUseSubscription = sinon.stub();

// Mock the useSubscriptionFeatures hook
jest.mock('../../src/contexts/SubscriptionContext', () => ({
  useSubscription: () => mockUseSubscription(),
  SubscriptionFeatures: {}
}));

// Import the hook after mocking
const { useSubscriptionFeatures } = require('../../src/hooks/useSubscriptionFeatures');

describe('useSubscriptionFeatures Hook', () => {
  let sandbox;
  
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    
    // Default mock implementation
    mockUseSubscription.returns({
      features: {
        maxStaff: 1,
        hasAnalytics: false,
        hasFinance: false,
        hasCustomDomain: false
      },
      plan: null,
      isLoading: false,
      error: null,
      hasFeature: (feature) => false,
      canAddMoreStaff: (currentStaff) => false
    });
  });
  
  afterEach(() => {
    sandbox.restore();
    mockUseSubscription.reset();
  });
  
  it('should return loading state from context', () => {
    mockUseSubscription.returns({
      features: {
        maxStaff: 1,
        hasAnalytics: false,
        hasFinance: false,
        hasCustomDomain: false
      },
      plan: null,
      isLoading: true,
      error: null,
      hasFeature: () => false,
      canAddMoreStaff: () => false
    });
    
    const { result } = renderHook(() => useSubscriptionFeatures());
    
    expect(result.current.isLoading).to.be.true;
  });
  
  it('should return error state from context', () => {
    const testError = new Error('Test error');
    
    mockUseSubscription.returns({
      features: {
        maxStaff: 1,
        hasAnalytics: false,
        hasFinance: false,
        hasCustomDomain: false
      },
      plan: null,
      isLoading: false,
      error: testError,
      hasFeature: () => false,
      canAddMoreStaff: () => false
    });
    
    const { result } = renderHook(() => useSubscriptionFeatures());
    
    expect(result.current.error).to.equal(testError);
  });
  
  it('should return features from context', () => {
    const testFeatures = {
      maxStaff: 5,
      hasAnalytics: true,
      hasFinance: true,
      hasCustomDomain: false,
      customFeature: 'test'
    };
    
    mockUseSubscription.returns({
      features: testFeatures,
      plan: null,
      isLoading: false,
      error: null,
      hasFeature: () => false,
      canAddMoreStaff: () => false
    });
    
    const { result } = renderHook(() => useSubscriptionFeatures());
    
    expect(result.current.features).to.deep.equal(testFeatures);
  });
  
  it('should return plan from context', () => {
    const testPlan = {
      id: 'plan-123',
      name: 'Pro Plan',
      price_monthly: 5000,
      max_staff: 10,
      features: {
        analytics: true,
        finance: true,
        custom_domain: true
      }
    };
    
    mockUseSubscription.returns({
      features: {
        maxStaff: 10,
        hasAnalytics: true,
        hasFinance: true,
        hasCustomDomain: true
      },
      plan: testPlan,
      isLoading: false,
      error: null,
      hasFeature: () => false,
      canAddMoreStaff: () => false
    });
    
    const { result } = renderHook(() => useSubscriptionFeatures());
    
    expect(result.current.plan).to.deep.equal(testPlan);
  });
  
  it('should return hasFeature function from context', () => {
    const hasFeatureStub = sinon.stub();
    hasFeatureStub.withArgs('analytics').returns(true);
    hasFeatureStub.withArgs('finance').returns(false);
    
    mockUseSubscription.returns({
      features: {
        maxStaff: 5,
        hasAnalytics: true,
        hasFinance: false,
        hasCustomDomain: false
      },
      plan: null,
      isLoading: false,
      error: null,
      hasFeature: hasFeatureStub,
      canAddMoreStaff: () => false
    });
    
    const { result } = renderHook(() => useSubscriptionFeatures());
    
    expect(result.current.hasFeature('analytics')).to.be.true;
    expect(result.current.hasFeature('finance')).to.be.false;
    expect(hasFeatureStub.calledWith('analytics')).to.be.true;
    expect(hasFeatureStub.calledWith('finance')).to.be.true;
  });
  
  it('should return canAddMoreStaff function from context', () => {
    const canAddMoreStaffStub = sinon.stub();
    canAddMoreStaffStub.withArgs(3).returns(true);
    canAddMoreStaffStub.withArgs(5).returns(false);
    
    mockUseSubscription.returns({
      features: {
        maxStaff: 5,
        hasAnalytics: true,
        hasFinance: true,
        hasCustomDomain: false
      },
      plan: null,
      isLoading: false,
      error: null,
      hasFeature: () => false,
      canAddMoreStaff: canAddMoreStaffStub
    });
    
    const { result } = renderHook(() => useSubscriptionFeatures());
    
    expect(result.current.canAddMoreStaff(3)).to.be.true;
    expect(result.current.canAddMoreStaff(5)).to.be.false;
    expect(canAddMoreStaffStub.calledWith(3)).to.be.true;
    expect(canAddMoreStaffStub.calledWith(5)).to.be.true;
  });
});
