"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from "react"
import { toast } from "sonner"
import { getSupabaseBrowser } from "@/lib/supabase"

import { useUser } from "@/contexts/UserContext"

// Notification types
export type NotificationType = "new_booking" | "cancellation" | "update" | "subscription_reminder" | "payment_reminder" | "referral_used"

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  createdAt: Date
  data?: Record<string, unknown>
  createdBy?: string // Bildirim oluşturan kullanıcı ID'si
}

interface NotificationsContextType {
  notifications: Notification[]
  unreadCount: number
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  clearNotifications: () => void
  isLoading: boolean
}

const NotificationsContext = createContext<NotificationsContextType | undefined>(undefined)

export function NotificationsProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)

  // UserContext'ten kullanıcı bilgilerini al
  const userContext = useUser()

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length

  // Fetch notifications from database
  const fetchNotifications = useCallback(async () => {
    if (!userContext.user) return

    try {
      setIsLoading(true)
      const supabase = getSupabaseBrowser()

      // Fetch notifications for the current user
      // Kendi oluşturduğumuz bildirimleri filtrelemek için created_by != user.id koşulunu ekledik
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userContext.user.id)
        .or(`created_by.is.null,created_by.neq.${userContext.user.id}`) // Kendi oluşturduğumuz bildirimleri filtreleme
        .order('created_at', { ascending: false })
        .limit(50)

      if (error) {
        console.error('Error fetching notifications:', error)
        return
      }

      // Transform database notifications to our Notification type
      const transformedNotifications: Notification[] = data.map(item => ({
        id: item.id,
        type: item.type as NotificationType,
        title: item.title,
        message: item.message,
        read: item.read,
        createdAt: new Date(item.created_at),
        data: item.data,
        createdBy: item.created_by
      }))

      setNotifications(transformedNotifications)
    } catch (error) {
      console.error('Error in fetchNotifications:', error)
    } finally {
      setIsLoading(false)
    }
  }, [userContext.user])

  // Bu fonksiyon artık kullanılmıyor çünkü bildirimler database trigger'ları tarafından oluşturuluyor
  // Referans olarak bırakıldı

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      const supabase = getSupabaseBrowser()

      // Update notification in database
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id)

      if (error) {
        console.error('Error marking notification as read:', error)
        return
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id ? { ...notification, read: true } : notification
        )
      )
    } catch (error) {
      console.error('Error in markAsRead:', error)
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!userContext.user) return

    try {
      const supabase = getSupabaseBrowser()

      // Update all notifications in database
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userContext.user.id)
        .eq('read', false)

      if (error) {
        console.error('Error marking all notifications as read:', error)
        return
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      )
    } catch (error) {
      console.error('Error in markAllAsRead:', error)
    }
  }

  // Clear all notifications
  const clearNotifications = async () => {
    if (!userContext.user) return

    try {
      const supabase = getSupabaseBrowser()

      // Delete all notifications from database
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', userContext.user.id)

      if (error) {
        console.error('Error clearing notifications:', error)
        return
      }

      // Clear local state
      setNotifications([])
    } catch (error) {
      console.error('Error in clearNotifications:', error)
    }
  }

  // Fetch notifications when component mounts and periodically
  useEffect(() => {
    if (userContext.user) {
      // İlk yükleme
      fetchNotifications()

      // Periyodik yenileme (her 30 saniyede bir)
      const intervalId = setInterval(() => {
        fetchNotifications()
      }, 30000) // 30 saniye

      return () => {
        clearInterval(intervalId)
      }
    }
  }, [userContext.user, fetchNotifications])

  // Set up Supabase Realtime subscription
  useEffect(() => {
    if (!userContext.user) return

    const supabase = getSupabaseBrowser()

    // Subscribe to notifications table for real-time updates
    const notificationsSubscription = supabase
      .channel('notifications-updates')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userContext.user?.id}`,
        },
        (payload) => {
          // Yeni bildirim geldiğinde
          const newNotification = payload.new

          // Kendi oluşturduğumuz bildirimleri gösterme
          if (newNotification.created_by === userContext.user?.id) {
            return;
          }

          // Bildirimi state'e ekle
          const notification: Notification = {
            id: newNotification.id,
            type: newNotification.type as NotificationType,
            title: newNotification.title,
            message: newNotification.message,
            read: newNotification.read,
            createdAt: new Date(newNotification.created_at),
            data: newNotification.data,
            createdBy: newNotification.created_by
          }

          setNotifications(prev => [notification, ...prev])

          // Toast mesajı göster
          let toastType = 'success';

          // Bildirim tipine göre toast tipini belirle
          if (notification.type === 'cancellation') {
            toastType = 'error';
          } else if (notification.type === 'payment_reminder') {
            toastType = 'warning';
          } else if (notification.type === 'subscription_reminder') {
            toastType = 'info';
          } else if (notification.type === 'referral_used') {
            toastType = 'success';
          }
          const toastMessage = (
            <div className="space-y-1">
              <p className="font-medium">{notification.title}</p>
              <div className="text-sm text-muted-foreground">
                {notification.message}
              </div>
            </div>
          )

          // Show toast with detailed message
          toast[toastType](toastMessage, {
            action: {
              label: "Görüntüle",
              onClick: () => {
                if (notification.type === 'subscription_reminder' || notification.type === 'payment_reminder') {
                  window.location.href = `/dashboard/subscription`
                } else if (notification.type === 'referral_used') {
                  window.location.href = `/dashboard/subscription`
                } else if (notification.data?.id) {
                  window.location.href = `/dashboard/appointments/${notification.data.id}`
                }
              },
            },
            duration: 5000, // Show for 5 seconds
          })
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userContext.user?.id}`,
        },
        () => {
          // Bildirim güncellendiğinde bildirimleri yenile
          fetchNotifications()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userContext.user?.id}`,
        },
        () => {
          // Bildirim silindiğinde bildirimleri yenile
          fetchNotifications()
        }
      )
      .subscribe()

    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(notificationsSubscription)
    }
  }, [userContext.user, fetchNotifications])

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        clearNotifications,
        isLoading,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationsContext)
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationsProvider")
  }
  return context
}
