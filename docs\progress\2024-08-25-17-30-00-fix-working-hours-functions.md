# Çalışma Saatleri RPC Fonksiyonları Düzeltmesi

**Tarih:** 25 Ağustos 2024
**Saat:** 17:30

## Sorun

`get_public_working_hours_by_salon_id` R<PERSON> fonksiyonu, `working_hours` tablosundan veri çekerken `barber_id` sütununu seçmeye çalışıyordu, ancak bu sütun `working_hours` tablosunda bulunmuyor. Bu sütun sadece `barber_working_hours` tablosunda bulunuyor.

Hata mesajı:
```
{
    "code": "42703",
    "details": null,
    "hint": null,
    "message": "column w.barber_id does not exist"
}
```

## Çözüm

Sorunu çözmek için aşağıdaki değişiklikler yapıldı:

### 1. RPC Fonksiyonları Düzeltildi

`docs/sql/public_access_rpc/2024-08-25-17-15-00-fix-working-hours-functions.sql` dosyası oluşturuldu ve aşağıdaki değişiklikler yapıldı:

1. `get_public_working_hours_by_salon_id` fonksiyonu düzeltildi:
   - `barber_id` sütunu kaldırıldı
   - Sadece `working_hours` tablosundan veri çekiliyor

2. Berber çalışma saatleri için iki yeni fonksiyon eklendi:
   - `get_public_barber_working_hours_by_barber_id`: Berber ID'ye göre berber çalışma saatlerini getiriyor
   - `get_public_barber_working_hours_by_salon_id`: Salon ID'ye göre tüm berberlerin çalışma saatlerini getiriyor

### 2. Client-Side Wrapper Fonksiyonları Güncellendi

1. `src/lib/db/public/barber-working-hours.ts` dosyası oluşturuldu:
   - `getPublicBarberWorkingHoursByBarberId` fonksiyonu eklendi
   - `getPublicBarberWorkingHoursBySalonId` fonksiyonu eklendi

2. `src/lib/db/public/index.ts` dosyası güncellendi:
   - `barber-working-hours` modülü export edildi

### 3. Client-Side Kod Güncellendi

`src/components/client/client-booking-form.tsx` dosyası güncellendi:
- Hem salon çalışma saatlerini hem de berber çalışma saatlerini getiren kod eklendi
- Berber çalışma saatleri varsa onları, yoksa salon çalışma saatlerini kullanacak şekilde düzenlendi

## Sonuç

Bu değişiklikler sayesinde, client-side kod artık doğru RPC fonksiyonlarını kullanarak hem salon çalışma saatlerini hem de berber çalışma saatlerini getirebiliyor. Bu, uygulamanın doğru çalışmasını sağlıyor ve "column w.barber_id does not exist" hatasını çözüyor.
