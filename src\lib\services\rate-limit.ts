// Rate Limiting Service for SalonFlow
// Prevents abuse of Telegram notification API endpoint

interface RateLimit {
  count: number;
  resetTime: number;
}

interface NotificationAttempt {
  salonId: string;
  userId: string;
  notificationType: string;
  success: boolean;
  error?: string;
  timestamp: Date;
  ipAddress?: string;
}

class RateLimitService {
  private limits = new Map<string, RateLimit>();
  private auditLog: NotificationAttempt[] = [];

  // Different limits for authenticated vs unauthenticated users
  private readonly unauthenticatedMaxRequests = 5; // per salon per minute (stricter)
  private readonly authenticatedMaxRequests = 20; // per salon per minute (more generous)
  private readonly windowMs = 60 * 1000; // 1 minute
  private readonly maxAuditLogSize = 1000; // Keep last 1000 attempts

  constructor() {
    // Cleanup expired entries every 5 minutes
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Check if the salon has exceeded the rate limit
   * Key format: 'salon_id' for unauthenticated, 'auth_salon_id' for authenticated
   */
  checkLimit(key: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const isAuthenticated = key.startsWith('auth_');
    const maxRequests = isAuthenticated ? this.authenticatedMaxRequests : this.unauthenticatedMaxRequests;
    const limitKey = `limit:${key}`;
    const limit = this.limits.get(limitKey);

    if (!limit || now > limit.resetTime) {
      // No limit exists or window has expired, create new window
      const newLimit: RateLimit = {
        count: 0,
        resetTime: now + this.windowMs
      };
      this.limits.set(limitKey, newLimit);

      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: newLimit.resetTime
      };
    }

    if (limit.count >= maxRequests) {
      // Rate limit exceeded
      return {
        allowed: false,
        remaining: 0,
        resetTime: limit.resetTime
      };
    }

    // Within limits
    return {
      allowed: true,
      remaining: maxRequests - limit.count - 1,
      resetTime: limit.resetTime
    };
  }

  /**
   * Increment the request count for a salon
   * Key format: 'salon_id' for unauthenticated, 'auth_salon_id' for authenticated
   */
  incrementCount(key: string): void {
    const now = Date.now();
    const limitKey = `limit:${key}`;
    const limit = this.limits.get(limitKey);

    if (!limit || now > limit.resetTime) {
      // Create new window
      this.limits.set(limitKey, {
        count: 1,
        resetTime: now + this.windowMs
      });
    } else {
      // Increment existing count
      limit.count += 1;
      this.limits.set(limitKey, limit);
    }
  }

  /**
   * Log a notification attempt for audit purposes
   */
  logAttempt(attempt: NotificationAttempt): void {
    this.auditLog.push(attempt);

    // Keep only the last N attempts to prevent memory issues
    if (this.auditLog.length > this.maxAuditLogSize) {
      this.auditLog.splice(0, this.auditLog.length - this.maxAuditLogSize);
    }
  }

  /**
   * Get recent audit log entries for a salon
   */
  getAuditLog(salonId: string, limit: number = 50): NotificationAttempt[] {
    return this.auditLog
      .filter(attempt => attempt.salonId === salonId)
      .slice(-limit)
      .reverse(); // Most recent first
  }

  /**
   * Get rate limiting statistics
   */
  getStats(): {
    totalSalons: number;
    activeLimits: number;
    totalAttempts: number;
    recentAttempts: number;
  } {
    const now = Date.now();
    const activeLimits = Array.from(this.limits.values())
      .filter(limit => now <= limit.resetTime).length;

    const recentAttempts = this.auditLog
      .filter(attempt => now - attempt.timestamp.getTime() < this.windowMs).length;

    return {
      totalSalons: this.limits.size,
      activeLimits,
      totalAttempts: this.auditLog.length,
      recentAttempts
    };
  }

  /**
   * Clean up expired rate limit entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, limit] of this.limits.entries()) {
      if (now > limit.resetTime) {
        this.limits.delete(key);
        cleanedCount++;
      }
    }

    // Clean up old audit log entries (older than 24 hours)
    const cutoffTime = now - (24 * 60 * 60 * 1000);
    const originalLength = this.auditLog.length;
    this.auditLog = this.auditLog.filter(
      attempt => attempt.timestamp.getTime() > cutoffTime
    );

    const auditCleaned = originalLength - this.auditLog.length;

    if (cleanedCount > 0 || auditCleaned > 0) {
      console.log(`Rate limit cleanup: ${cleanedCount} limits, ${auditCleaned} audit entries`);
    }
  }

  /**
   * Reset rate limit for a specific salon (admin function)
   */
  resetSalonLimit(salonId: string): void {
    const key = `salon:${salonId}`;
    this.limits.delete(key);
  }

  /**
   * Get current rate limit status for a salon
   * Returns status for both authenticated and unauthenticated limits
   */
  getSalonStatus(salonId: string): {
    unauthenticated: {
      currentCount: number;
      maxRequests: number;
      resetTime: number;
      timeUntilReset: number;
    };
    authenticated: {
      currentCount: number;
      maxRequests: number;
      resetTime: number;
      timeUntilReset: number;
    };
  } {
    const now = Date.now();

    // Get unauthenticated status
    const unauthKey = `limit:${salonId}`;
    const unauthLimit = this.limits.get(unauthKey);

    // Get authenticated status
    const authKey = `limit:auth_${salonId}`;
    const authLimit = this.limits.get(authKey);

    const getStatus = (limit: RateLimit | undefined, maxRequests: number) => {
      if (!limit || now > limit.resetTime) {
        return {
          currentCount: 0,
          maxRequests,
          resetTime: now + this.windowMs,
          timeUntilReset: this.windowMs
        };
      }

      return {
        currentCount: limit.count,
        maxRequests,
        resetTime: limit.resetTime,
        timeUntilReset: limit.resetTime - now
      };
    };

    return {
      unauthenticated: getStatus(unauthLimit, this.unauthenticatedMaxRequests),
      authenticated: getStatus(authLimit, this.authenticatedMaxRequests)
    };
  }
}

// Singleton instance
const rateLimitService = new RateLimitService();

export { rateLimitService, type NotificationAttempt };
export default rateLimitService;
