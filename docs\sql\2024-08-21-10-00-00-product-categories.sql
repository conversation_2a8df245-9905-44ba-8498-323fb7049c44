-- SalonFlow Ürün <PERSON>ileri Tablosu
-- Oluşturulma Tarihi: 2024-08-21

-- 1. <PERSON><PERSON><PERSON><PERSON> kategorileri tablosu
CREATE TABLE IF NOT EXISTS product_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RLS Politikaları
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;

-- Salon sahipleri kendi salonlarının kategorilerini görebilir ve yönetebilir
CREATE POLICY "Salon owners can manage their salon's product categories" ON product_categories
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- <PERSON><PERSON> kendi salonlarının kategorilerini görebilir
CREATE POLICY "Staff can view their salon's product categories" ON product_categories
  FOR SELECT
  USING (salon_id IN (
    SELECT salon_id FROM barbers 
    WHERE user_id = auth.uid()
  ));

-- Admin tüm kategorileri görebilir ve yönetebilir
CREATE POLICY "Admins can manage all product categories" ON product_categories
  USING (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()))
  WITH CHECK (EXISTS (SELECT 1 FROM auth.users WHERE id = auth.uid() AND is_admin()));

-- Indexes
CREATE INDEX idx_product_categories_salon_id ON product_categories(salon_id);
