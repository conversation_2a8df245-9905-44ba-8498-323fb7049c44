"use client";
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export const AnimatedTestimonials = ({
  testimonials,
  className,
}: {
  testimonials: {
    image: string;
    name: string;
    title: string;
    quote: string;
  }[];
  className?: string;
}) => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {testimonials.map((testimonial, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, x: 100 }}
          animate={{
            opacity: currentTestimonial === index ? 1 : 0,
            x: currentTestimonial === index ? 0 : 100,
          }}
          exit={{ opacity: 0, x: -100 }}
          transition={{ duration: 0.5 }}
          className={cn(
            "absolute inset-0 flex flex-col items-center justify-center p-4 text-center",
            currentTestimonial === index ? "relative" : "hidden"
          )}
        >
          <div className="relative mx-auto mb-6 h-24 w-24 overflow-hidden rounded-full">
            <img
              src={testimonial.image}
              alt={testimonial.name}
              className="h-full w-full object-cover"
            />
          </div>
          <blockquote className="mb-4 max-w-2xl text-lg font-medium italic text-muted-foreground">
            &ldquo;{testimonial.quote}&rdquo;
          </blockquote>
          <div className="text-center">
            <div className="font-semibold">{testimonial.name}</div>
            <div className="text-sm text-muted-foreground">
              {testimonial.title}
            </div>
          </div>
        </motion.div>
      ))}
      <div className="mt-6 flex justify-center space-x-2">
        {testimonials.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentTestimonial(index)}
            className={`h-2 w-2 rounded-full ${
              currentTestimonial === index
                ? "bg-primary"
                : "bg-muted-foreground/30"
            }`}
            aria-label={`Go to testimonial ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};
