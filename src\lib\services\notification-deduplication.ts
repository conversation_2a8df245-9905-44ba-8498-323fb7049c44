// Notification Deduplication Service for SalonFlow
// Prevents duplicate Telegram notifications using database logging

import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { 
  generateNotificationHash, 
  generateTestNotificationHash,
  AppointmentNotificationData, 
  NotificationType,
  getNotificationContext
} from '@/lib/utils/notification-hash';

export interface DeduplicationResult {
  isDuplicate: boolean;
  hash: string;
  logId?: string;
  error?: string;
}

export interface NotificationLogEntry {
  id: string;
  salon_id: string;
  appointment_id: string;
  notification_type: string;
  notification_hash: string;
  sent_at: string;
  created_at: string;
}

class NotificationDeduplicationService {
  /**
   * Check if a notification was already sent and log it if not
   */
  async checkAndLogNotification(
    appointment: AppointmentNotificationData,
    notificationType: NotificationType
  ): Promise<DeduplicationResult> {
    try {
      // Generate notification hash
      const hash = generateNotificationHash(appointment, notificationType);

      // Check if notification already sent
      const isDuplicate = await this.checkNotificationSent(
        appointment.salon_id,
        appointment.id,
        notificationType,
        hash
      );

      if (isDuplicate) {
        console.log(`Duplicate notification prevented for appointment ${appointment.id}, type: ${notificationType}`);
        return {
          isDuplicate: true,
          hash
        };
      }

      // Log the notification attempt
      const logId = await this.logNotificationSent(
        appointment.salon_id,
        appointment.id,
        notificationType,
        hash
      );

      return {
        isDuplicate: false,
        hash,
        logId
      };

    } catch (error) {
      console.error('Error in notification deduplication:', error);
      return {
        isDuplicate: false,
        hash: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if a test notification should be sent (always allow test notifications)
   */
  async checkAndLogTestNotification(
    salonId: string,
    salonName: string
  ): Promise<DeduplicationResult> {
    try {
      // Generate unique hash for test notification (includes timestamp)
      const hash = generateTestNotificationHash(salonId, salonName);

      // Always log test notifications (they're not deduplicated)
      const logId = await this.logNotificationSent(
        salonId,
        'test-notification',
        'test_notification',
        hash
      );

      return {
        isDuplicate: false,
        hash,
        logId
      };

    } catch (error) {
      console.error('Error in test notification logging:', error);
      return {
        isDuplicate: false,
        hash: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Check if notification was already sent using database function
   */
  private async checkNotificationSent(
    salonId: string,
    appointmentId: string,
    notificationType: string,
    notificationHash: string
  ): Promise<boolean> {
    try {
      const supabase = createRouteHandlerClient({ cookies });
      
      const { data, error } = await supabase.rpc('check_notification_sent', {
        p_salon_id: salonId,
        p_appointment_id: appointmentId,
        p_notification_type: notificationType,
        p_notification_hash: notificationHash
      });

      if (error) {
        console.error('Error checking notification sent:', error);
        // In case of error, allow notification to prevent blocking
        return false;
      }

      return data || false;

    } catch (error) {
      console.error('Error in checkNotificationSent:', error);
      // In case of error, allow notification to prevent blocking
      return false;
    }
  }

  /**
   * Log successful notification using database function
   */
  private async logNotificationSent(
    salonId: string,
    appointmentId: string,
    notificationType: string,
    notificationHash: string
  ): Promise<string | null> {
    try {
      const supabase = createRouteHandlerClient({ cookies });
      
      const { data, error } = await supabase.rpc('log_notification_sent', {
        p_salon_id: salonId,
        p_appointment_id: appointmentId,
        p_notification_type: notificationType,
        p_notification_hash: notificationHash
      });

      if (error) {
        console.error('Error logging notification sent:', error);
        // Don't throw error, just log it
        return null;
      }

      return data;

    } catch (error) {
      console.error('Error in logNotificationSent:', error);
      return null;
    }
  }

  /**
   * Get notification history for a salon
   */
  async getNotificationHistory(
    salonId: string,
    limit: number = 50
  ): Promise<NotificationLogEntry[]> {
    try {
      const supabase = createRouteHandlerClient({ cookies });
      
      const { data, error } = await supabase
        .from('telegram_notification_log')
        .select('*')
        .eq('salon_id', salonId)
        .order('sent_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching notification history:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('Error in getNotificationHistory:', error);
      return [];
    }
  }

  /**
   * Get notification statistics for a salon
   */
  async getNotificationStats(
    salonId: string,
    days: number = 7
  ): Promise<{
    notification_type: string;
    total_count: number;
    unique_appointments: number;
    last_sent: string;
  }[]> {
    try {
      const supabase = createRouteHandlerClient({ cookies });
      
      const { data, error } = await supabase.rpc('get_notification_stats', {
        p_salon_id: salonId,
        p_days: days
      });

      if (error) {
        console.error('Error fetching notification stats:', error);
        return [];
      }

      return data || [];

    } catch (error) {
      console.error('Error in getNotificationStats:', error);
      return [];
    }
  }

  /**
   * Clean up old notification logs (admin function)
   */
  async cleanupOldLogs(): Promise<number> {
    try {
      const supabase = createRouteHandlerClient({ cookies });
      
      const { data, error } = await supabase.rpc('cleanup_old_notification_logs');

      if (error) {
        console.error('Error cleaning up old logs:', error);
        return 0;
      }

      return data || 0;

    } catch (error) {
      console.error('Error in cleanupOldLogs:', error);
      return 0;
    }
  }

  /**
   * Debug function to analyze notification patterns
   */
  async debugNotificationPattern(
    appointment: AppointmentNotificationData,
    notificationType: NotificationType
  ): Promise<{
    hash: string;
    context: any;
    existingLogs: NotificationLogEntry[];
    wouldBeDuplicate: boolean;
  }> {
    const hash = generateNotificationHash(appointment, notificationType);
    const context = getNotificationContext(appointment, notificationType);
    
    // Get existing logs for this appointment
    const supabase = createRouteHandlerClient({ cookies });
    const { data: existingLogs } = await supabase
      .from('telegram_notification_log')
      .select('*')
      .eq('salon_id', appointment.salon_id)
      .eq('appointment_id', appointment.id)
      .order('sent_at', { ascending: false });

    const wouldBeDuplicate = await this.checkNotificationSent(
      appointment.salon_id,
      appointment.id,
      notificationType,
      hash
    );

    return {
      hash,
      context,
      existingLogs: existingLogs || [],
      wouldBeDuplicate
    };
  }
}

// Singleton instance
const notificationDeduplicationService = new NotificationDeduplicationService();

export { notificationDeduplicationService };
export default notificationDeduplicationService;
