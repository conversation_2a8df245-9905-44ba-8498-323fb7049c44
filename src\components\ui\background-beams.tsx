"use client";
import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

export function BackgroundBeams({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });

  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (ref.current) {
        const rect = ref.current.getBoundingClientRect();
        setMousePosition({
          x: event.clientX - rect.left,
          y: event.clientY - rect.top,
        });
      }
    };

    const element = ref.current;
    if (element) {
      element.addEventListener("mousemove", handleMouseMove);
    }

    return () => {
      if (element) {
        element.removeEventListener("mousemove", handleMouseMove);
      }
    };
  }, []);

  return (
    <div
      ref={ref}
      className={cn(
        "h-full w-full bg-slate-950 [--x-px:0] [--y-px:0] [--size-px:0] [--opacity:0.0] [--beam-color:hsl(215,98%,61%)] [--beam-duration:0.5s] [--beam-size:150px] [--beam-easing:cubic-bezier(0.42,0,0.58,1)] [--beam-timing:0s] overflow-hidden",
        className
      )}
      {...props}
    >
      <svg className="hidden">
        <defs>
          <filter id="blurMe">
            <feGaussianBlur
              in="SourceGraphic"
              stdDeviation="10"
              result="blur"
            />
            <feColorMatrix
              in="blur"
              mode="matrix"
              values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"
              result="goo"
            />
            <feBlend in="SourceGraphic" in2="goo" />
          </filter>
        </defs>
      </svg>
      <div className="relative h-full w-full">
        <div
          className="pointer-events-none absolute inset-0 z-10 h-full w-full opacity-[var(--opacity)] blur-[calc(var(--size-px)/12)] transition-opacity duration-[var(--beam-duration)] ease-[var(--beam-easing)]"
          style={{
            "--x-px": `${mousePosition.x}px`,
            "--y-px": `${mousePosition.y}px`,
            "--opacity": "0.7",
            "--size-px": "400px",
          } as React.CSSProperties}
        >
          <div
            className="absolute left-[calc(var(--x-px)-var(--size-px)/2)] top-[calc(var(--y-px)-var(--size-px)/2)] h-[var(--size-px)] w-[var(--size-px)] rounded-full bg-[var(--beam-color)] opacity-[var(--opacity)] blur-[calc(var(--size-px)/6)] transition-[background-color] duration-[var(--beam-duration)] ease-[var(--beam-easing)]"
            style={{
              "--beam-color": "hsl(215, 98%, 61%)",
            } as React.CSSProperties}
          />
        </div>
        <div
          className="pointer-events-none absolute inset-0 z-10 h-full w-full opacity-[var(--opacity)] blur-[calc(var(--size-px)/12)] transition-opacity duration-[var(--beam-duration)] ease-[var(--beam-easing)]"
          style={{
            "--x-px": `${mousePosition.x}px`,
            "--y-px": `${mousePosition.y}px`,
            "--opacity": "0.4",
            "--size-px": "600px",
            "--beam-timing": "0.1s",
          } as React.CSSProperties}
        >
          <div
            className="absolute left-[calc(var(--x-px)-var(--size-px)/2)] top-[calc(var(--y-px)-var(--size-px)/2)] h-[var(--size-px)] w-[var(--size-px)] rounded-full bg-[var(--beam-color)] opacity-[var(--opacity)] blur-[calc(var(--size-px)/6)] transition-[background-color] duration-[var(--beam-duration)] ease-[var(--beam-easing)]"
            style={{
              "--beam-color": "hsl(260, 98%, 61%)",
            } as React.CSSProperties}
          />
        </div>
      </div>
    </div>
  );
}
