"use client"

import { useState, useRef } from "react"
import { useDraggable } from "@dnd-kit/core"
import { CSS } from "@dnd-kit/utilities"
import { Clock, User } from "lucide-react"

import { cn } from "@/lib/utils"
import { Appointment } from "@/lib/db/types"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { AppointmentActionMenu } from "@/components/calendar/AppointmentActionMenu"

interface DraggableAppointmentProps {
  appointment: Appointment & {
    customers?: {
      name: string;
      surname: string;
      phone: string;
    };
    barbers?: {
      name: string;
    };
    services?: {
      name: string;
      duration: number;
    };
  }
  isDraggingEnabled?: boolean
}

export function DraggableAppointment({ appointment, isDraggingEnabled = true, onAppointmentUpdated }: DraggableAppointmentProps & { onAppointmentUpdated?: () => void }) {
  const [isDragging, setIsDragging] = useState(false)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const dragHandleRef = useRef<HTMLDivElement>(null)
  const appointmentRef = useRef<HTMLDivElement>(null)

  // Sadece aktif randevular sürüklenebilir
  const isActive = appointment.status === 'booked'
  const isNotDraggable = !isActive

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: isDraggingKit
  } = useDraggable({
    id: appointment.id,
    data: {
      appointment,
      type: 'appointment'
    },
    disabled: !isDraggingEnabled || isMenuOpen || isNotDraggable
  })

  // Update local dragging state when dnd-kit dragging state changes
  if (isDragging !== isDraggingKit) {
    setIsDragging(isDraggingKit)
  }

  // Format appointment time
  const startTime = appointment.start_time.substring(0, 5)
  const endTime = appointment.end_time.substring(0, 5)
  const timeRange = `${startTime} - ${endTime}`

  // Get customer name
  const customerName = appointment.fullname ||
    (appointment.customers ? `${appointment.customers.name} ${appointment.customers.surname}` : "İsimsiz Müşteri")

  // Get service name
  const serviceName = appointment.services?.name || "Belirtilmemiş Hizmet"

  // Get barber name
  const barberName = appointment.barbers?.name || "Belirtilmemiş Berber"

  // Get status color
  const getStatusColor = () => {
    switch (appointment.status) {
      case 'completed':
        return "bg-green-100 border-green-300 dark:bg-green-900/30 dark:border-green-700"
      case 'cancelled':
        return "bg-red-100 border-red-300 dark:bg-red-900/30 dark:border-red-700"
      case 'no-show':
        return "bg-amber-100 border-amber-300 dark:bg-amber-900/30 dark:border-amber-700"
      default:
        return "bg-blue-100 border-blue-300 dark:bg-blue-900/30 dark:border-blue-700"
    }
  }

  // Get status text
  const getStatusText = () => {
    switch (appointment.status) {
      case 'completed':
        return "Tamamlandı"
      case 'cancelled':
        return "İptal Edildi"
      case 'no-show':
        return "Gelmedi"
      default:
        return "Aktif"
    }
  }

  // Handle appointment click
  const handleAppointmentClick = (e: React.MouseEvent) => {
    // Prevent click event from propagating to parent elements
    e.stopPropagation();

    // If we're dragging, don't do anything on click
    if (isDragging) return;

    // If the click is on the menu button or menu, don't do anything
    if (e.target instanceof Node &&
        (e.currentTarget.querySelector('.menu-button')?.contains(e.target) ||
         e.currentTarget.querySelector('.menu-content')?.contains(e.target))) {
      return;
    }

    // Otherwise, open the menu
    setIsMenuOpen(true);
  }

  const style = {
    transform: CSS.Translate.toString(transform),
    zIndex: isDragging ? 999 : (isMenuOpen ? 50 : 'auto'),
    opacity: isDragging ? 0.8 : (isNotDraggable ? 0.7 : 1),
    cursor: isDraggingEnabled && !isMenuOpen && isActive ? 'grab' : (isNotDraggable ? 'not-allowed' : 'default')
  }

  return (
    <div
      ref={(node) => {
        // Set both refs
        setNodeRef(node);
        if (appointmentRef) {
          appointmentRef.current = node;
        }
      }}
      style={style}
      className={cn(
        "p-2 rounded-md border text-sm mb-2 transition-colors relative draggable-appointment",
        getStatusColor(),
        isDragging && "shadow-lg",
        isDraggingEnabled && !isMenuOpen && "hover:brightness-95 dark:hover:brightness-125"
      )}
      onClick={handleAppointmentClick}
    >
      {/* Drag handle - only this area will start dragging for active appointments */}
      {isDraggingEnabled && !isMenuOpen && isActive && (
        <div
          ref={dragHandleRef}
          className="absolute inset-0 cursor-grab"
          {...attributes}
          {...listeners}
          onClick={(e) => e.stopPropagation()}
        />
      )}

      {/* Action Menu */}
      <div className="menu-content">
        <AppointmentActionMenu
          appointment={appointment}
          onAppointmentUpdated={onAppointmentUpdated}
          isOpen={isMenuOpen}
          onOpenChange={setIsMenuOpen}
        />
      </div>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="space-y-1">
              <div className="font-medium truncate pr-6">{customerName}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <Clock className="mr-1 h-3 w-3" />
                {timeRange}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <User className="mr-1 h-3 w-3" />
                {barberName}
              </div>
              <div className="text-xs truncate">{serviceName}</div>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p className="font-medium">{customerName}</p>
              <p className="text-xs">{serviceName}</p>
              <p className="text-xs">Berber: {barberName}</p>
              <p className="text-xs">Saat: {timeRange}</p>
              <p className="text-xs">Durum: {getStatusText()}</p>
              {appointment.notes && <p className="text-xs">Not: {appointment.notes}</p>}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}
