"use client"

import * as React from "react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { Calendar as CalendarIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { HolidayCalendar, HolidayDate } from "@/components/ui/holiday-calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  date: Date | undefined
  setDate: (date: Date | undefined) => void
  className?: string
  disabled?: boolean
  disabledDates?: Date[]
  fromDate?: Date
  holidayDates?: HolidayDate[]
  showHolidays?: boolean
  /** Kompakt görünüm için true, tam genişlik için false (varsayılan: true) */
  compact?: boolean
}

export function DatePicker({
  date,
  setDate,
  className,
  disabled = false,
  disabledDates,
  fromDate,
  holidayDates = [],
  showHolidays = false,
  compact = true
}: DatePickerProps) {
  // Varsayılan olarak kompakt görünüm, ancak className ile override edilebilir
  // veya compact prop ile kontrol edilebilir
  const isFullWidth = className?.includes("w-full") || !compact;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            // Eğer w-full sınıfı varsa veya compact=false ise, tam genişlik kullan, yoksa kompakt görünüm
            isFullWidth ? "w-full" : "w-full", // Tüm durumlarda tam genişlik kullan
            "justify-start text-left font-normal",
            !date && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "d MMM yyyy", { locale: tr }) : <span>Tarih seçin</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            initialFocus
            disabled={disabledDates}
            fromDate={fromDate}
          />
      </PopoverContent>
    </Popover>
  )
}
