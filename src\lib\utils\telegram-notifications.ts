/**
 * Telegram Notification Helper
 * Handles sending Telegram notifications from the application layer
 */

export interface AppointmentNotificationData {
  id: string;
  salon_id: string;
  salon_name: string;
  customer_name: string;
  customer_phone?: string;
  barber_name: string;
  service_name: string;
  date: string;
  start_time: string;
  end_time: string;
  status: string;
  notes?: string;
}

export type NotificationType = 'new_appointment' | 'cancelled_appointment' | 'updated_appointment';

/**
 * Send Telegram notification for appointment events
 * This function is non-blocking and won't affect the main operation if it fails
 */
export async function sendTelegramNotification(
  type: NotificationType,
  appointmentData: AppointmentNotificationData,
  previousData?: Partial<AppointmentNotificationData>
): Promise<{
  success: boolean;
  error?: string;
  isDuplicate?: boolean;
  hash?: string;
  processingTime?: number;
}> {
  try {
    // Prepare the notification payload
    const payload = {
      type: 'appointment_notification',
      appointment: appointmentData,
      notification_type: type,
      ...(previousData && { previous_data: previousData })
    };

    // Send the notification request
    const response = await fetch('/api/telegram/notify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    if (!response.ok || !result.success) {
      console.error('Telegram notification failed:', result.error);
      return {
        success: false,
        error: result.error || 'Failed to send notification'
      };
    }

    // Handle duplicate notification case
    if (result.isDuplicate) {
      console.log('Duplicate notification prevented:', result.hash);
      return {
        success: true,
        isDuplicate: true,
        hash: result.hash
      };
    }

    console.log('Telegram notification sent successfully');
    return {
      success: true,
      isDuplicate: false,
      hash: result.deduplication?.hash,
      processingTime: result.processingTime
    };

  } catch (error) {
    console.error('Error sending Telegram notification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Send Telegram notification for new appointment
 * Non-blocking wrapper function
 */
export async function notifyNewAppointment(appointmentData: AppointmentNotificationData): Promise<void> {
  // Fire and forget - don't wait for the result
  sendTelegramNotification('new_appointment', appointmentData).catch(error => {
    console.error('New appointment notification failed:', error);
  });
}

/**
 * Send Telegram notification for cancelled appointment
 * Non-blocking wrapper function
 */
export async function notifyCancelledAppointment(appointmentData: AppointmentNotificationData): Promise<void> {
  // Fire and forget - don't wait for the result
  sendTelegramNotification('cancelled_appointment', appointmentData).catch(error => {
    console.error('Cancelled appointment notification failed:', error);
  });
}

/**
 * Send Telegram notification for updated appointment
 * Non-blocking wrapper function
 */
export async function notifyUpdatedAppointment(
  appointmentData: AppointmentNotificationData,
  previousData: Partial<AppointmentNotificationData>
): Promise<void> {
  // Fire and forget - don't wait for the result
  sendTelegramNotification('updated_appointment', appointmentData, previousData).catch(error => {
    console.error('Updated appointment notification failed:', error);
  });
}

/**
 * Helper function to format appointment data from database record
 */
export function formatAppointmentForNotification(
  appointment: any,
  salonName: string,
  barberName: string,
  serviceName: string
): AppointmentNotificationData {
  // Get customer name with proper fallback logic
  const customerName = appointment.fullname ||
    (appointment.customers ? `${appointment.customers.name} ${appointment.customers.surname}` : 'İsimsiz Müşteri');

  // Get customer phone with proper fallback logic
  const customerPhone = appointment.phonenumber ||
    (appointment.customers ? appointment.customers.phone : '');

  return {
    id: appointment.id,
    salon_id: appointment.salon_id,
    salon_name: salonName,
    customer_name: customerName,
    customer_phone: customerPhone,
    barber_name: barberName,
    service_name: serviceName,
    date: appointment.date,
    start_time: appointment.start_time,
    end_time: appointment.end_time,
    status: appointment.status,
    notes: appointment.notes,
  };
}

/**
 * Send test Telegram notification
 */
export async function sendTestNotification(
  salonId: string,
  salonName: string
): Promise<{ success: boolean; error?: string; hash?: string; processingTime?: number }> {
  try {
    const payload = {
      type: 'test_notification',
      salon_id: salonId,
      salon_name: salonName
    };

    const response = await fetch('/api/telegram/notify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    const result = await response.json();

    if (!response.ok || !result.success) {
      console.error('Test notification failed:', result.error);
      return {
        success: false,
        error: result.error || 'Failed to send test notification'
      };
    }

    console.log('Test notification sent successfully');
    return {
      success: true,
      hash: result.deduplication?.hash,
      processingTime: result.processingTime
    };

  } catch (error) {
    console.error('Error sending test notification:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Utility function to safely call notification functions
 * Ensures that notification failures don't affect the main operation
 */
export async function safeNotify(notificationFn: () => Promise<void>): Promise<void> {
  try {
    await notificationFn();
  } catch (error) {
    // Log the error but don't throw it
    console.error('Notification error (safely handled):', error);
  }
}
