"use client"


import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { AppointmentForm } from "@/components/appointment-form-new"

interface AppointmentCreateModalProps {
  isOpen: boolean
  onClose: () => void
  salonId: string
  selectedDate: Date
  selectedTime?: string
  selectedBarber?: string
  selectedService?: string
  onSuccess?: () => void
}

export function AppointmentCreateModal({
  isOpen,
  onClose,
  salonId,
  selectedDate,
  selectedTime,
  selectedBarber,
  selectedService,
  onSuccess,
}: AppointmentCreateModalProps) {
  const formattedDate = format(selectedDate, "d MMMM yyyy", { locale: tr })

  const handleSuccess = () => {
    // Toast mesajını geri ekledik çünkü NotificationsContext'teki toast mesajları çalışmıyor olabilir
    toast.success("<PERSON>evu başarıyla oluşturuldu")
    if (onSuccess) {
      onSuccess()
    }
    onClose()
  }

  // İptal işlemi için ayrı bir fonksiyon
  const handleCancel = () => {
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl sm:max-w-2xl mx-auto">
        <DialogHeader className="mb-2">
          <DialogTitle>Yeni Randevu Oluştur</DialogTitle>
          <DialogDescription>
            {formattedDate}{selectedTime ? `, ${selectedTime}` : ''} için yeni bir randevu oluşturun.
          </DialogDescription>
        </DialogHeader>

        <div className="py-2 overflow-y-auto">
          <AppointmentForm
            salonId={salonId}
            initialDate={selectedDate}
            initialTime={selectedTime}
            initialBarberId={selectedBarber}
            initialServiceId={selectedService}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
