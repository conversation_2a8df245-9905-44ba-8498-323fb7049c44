-- SalonFlow CMS Migration Script
-- Date: 2025-05-23-23-54-49
-- Description: Safe migration script for CMS tables and policies
-- This script can be run multiple times safely (idempotent)

-- =====================================================
-- ENABLE EXTENSIONS
-- =====================================================
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- CREATE TABLES (IF NOT EXISTS)
-- =====================================================

-- Create salon_content table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.salon_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES public.salons(id) ON DELETE CASCADE,
    section TEXT NOT NULL,
    content_key TEXT NOT NULL,
    content_value TEXT,
    content_type TEXT DEFAULT 'text' CHECK (content_type IN ('text', 'number', 'boolean', 'json')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(salon_id, section, content_key)
);

-- Create salon_testimonials table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.salon_testimonials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    salon_id UUID NOT NULL REFERENCES public.salons(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    service_name TEXT,
    date_text TEXT,
    is_active BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CREATE INDEXES (IF NOT EXISTS)
-- =====================================================

-- Indexes for salon_content
CREATE INDEX IF NOT EXISTS idx_salon_content_salon_section 
ON public.salon_content(salon_id, section);

CREATE INDEX IF NOT EXISTS idx_salon_content_updated_at 
ON public.salon_content(updated_at);

-- Indexes for salon_testimonials
CREATE INDEX IF NOT EXISTS idx_salon_testimonials_salon_active 
ON public.salon_testimonials(salon_id, is_active);

CREATE INDEX IF NOT EXISTS idx_salon_testimonials_display_order 
ON public.salon_testimonials(display_order);

CREATE INDEX IF NOT EXISTS idx_salon_testimonials_updated_at 
ON public.salon_testimonials(updated_at);

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE public.salon_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.salon_testimonials ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- DROP EXISTING POLICIES (IF ANY)
-- =====================================================

DROP POLICY IF EXISTS "Users can view own salon content" ON public.salon_content;
DROP POLICY IF EXISTS "Users can modify own salon content" ON public.salon_content;
DROP POLICY IF EXISTS "Public can view active salon content" ON public.salon_content;

DROP POLICY IF EXISTS "Users can view own salon testimonials" ON public.salon_testimonials;
DROP POLICY IF EXISTS "Users can modify own salon testimonials" ON public.salon_testimonials;
DROP POLICY IF EXISTS "Public can view active testimonials" ON public.salon_testimonials;

-- =====================================================
-- CREATE RLS POLICIES
-- =====================================================

-- salon_content policies
CREATE POLICY "Users can view own salon content" ON public.salon_content
    FOR SELECT USING (
        salon_id IN (
            SELECT id FROM public.salons 
            WHERE owner_id = auth.uid()
        ) OR is_admin()
    );

CREATE POLICY "Users can modify own salon content" ON public.salon_content
    FOR ALL USING (
        salon_id IN (
            SELECT id FROM public.salons 
            WHERE owner_id = auth.uid()
        ) OR is_admin()
    );

CREATE POLICY "Public can view active salon content" ON public.salon_content
    FOR SELECT USING (true);

-- salon_testimonials policies
CREATE POLICY "Users can view own salon testimonials" ON public.salon_testimonials
    FOR SELECT USING (
        salon_id IN (
            SELECT id FROM public.salons 
            WHERE owner_id = auth.uid()
        ) OR is_admin()
    );

CREATE POLICY "Users can modify own salon testimonials" ON public.salon_testimonials
    FOR ALL USING (
        salon_id IN (
            SELECT id FROM public.salons 
            WHERE owner_id = auth.uid()
        ) OR is_admin()
    );

CREATE POLICY "Public can view active testimonials" ON public.salon_testimonials
    FOR SELECT USING (is_active = true);

-- =====================================================
-- CREATE OR REPLACE FUNCTIONS
-- =====================================================

-- Function to insert default content for new salons
CREATE OR REPLACE FUNCTION public.insert_default_salon_content(salon_uuid UUID)
RETURNS void AS $$
BEGIN
    -- Insert default hero content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (salon_uuid, 'hero', 'badge_text', 'Profesyonel Berber Hizmetleri', 'text'),
    (salon_uuid, 'hero', 'tagline', 'ile tarzını yansıt.', 'text'),
    (salon_uuid, 'hero', 'description', 'Uzman ekibimizle kaliteli hizmet garantisi. Modern teknikler ve kişisel yaklaşımla tarzınızı yenileyin.', 'text'),
    (salon_uuid, 'hero', 'cta_primary', 'Randevu Al', 'text'),
    (salon_uuid, 'hero', 'cta_secondary', 'Hizmetlerimizi Keşfet', 'text'),
    (salon_uuid, 'hero', 'stats_customers', '500+', 'text'),
    (salon_uuid, 'hero', 'stats_customers_label', 'Mutlu Müşteri', 'text'),
    (salon_uuid, 'hero', 'stats_experience', '5+', 'text'),
    (salon_uuid, 'hero', 'stats_experience_label', 'Yıl Deneyim', 'text'),
    (salon_uuid, 'hero', 'stats_rating', '4.9', 'text'),
    (salon_uuid, 'hero', 'stats_rating_label', 'Müşteri Puanı', 'text'),
    (salon_uuid, 'hero', 'stats_support', '24/7', 'text'),
    (salon_uuid, 'hero', 'stats_support_label', 'Online Randevu', 'text')
    ON CONFLICT (salon_id, section, content_key) DO NOTHING;

    -- Insert default about content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (salon_uuid, 'about', 'badge_text', 'Hakkımızda', 'text'),
    (salon_uuid, 'about', 'title', 'Profesyonel Berber Deneyimi', 'text'),
    (salon_uuid, 'about', 'description', 'Yılların deneyimi ve modern tekniklerin buluştuğu salonumuzda, her müşterimize özel yaklaşım sergiliyoruz.', 'text'),
    (salon_uuid, 'about', 'features', '[{"title":"Uzman Ekip","description":"Alanında uzman berberlerimizle kaliteli hizmet"},{"title":"Modern Teknikler","description":"En güncel kesim ve bakım teknikleri"},{"title":"Hijyen","description":"Yüksek hijyen standartları ve steril ekipmanlar"}]', 'json'),
    (salon_uuid, 'about', 'stats', '[{"number":"500+","label":"Mutlu Müşteri"},{"number":"5+","label":"Yıl Deneyim"},{"number":"100%","label":"Müşteri Memnuniyeti"}]', 'json')
    ON CONFLICT (salon_id, section, content_key) DO NOTHING;

    -- Insert default services content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (salon_uuid, 'services', 'badge_text', 'Hizmetlerimiz', 'text'),
    (salon_uuid, 'services', 'title_line1', 'Profesyonel', 'text'),
    (salon_uuid, 'services', 'title_line2', 'Berber Hizmetleri', 'text'),
    (salon_uuid, 'services', 'description', 'Saç kesimi, sakal tıraşı, cilt bakımı ve daha fazlası için uzman ekibimizle randevunuzu alın.', 'text'),
    (salon_uuid, 'services', 'cta_title', 'Randevunuzu Hemen Alın', 'text'),
    (salon_uuid, 'services', 'cta_description', 'Online randevu sistemi ile kolayca randevunuzu oluşturun.', 'text'),
    (salon_uuid, 'services', 'cta_button_text', 'İletişime Geç', 'text')
    ON CONFLICT (salon_id, section, content_key) DO NOTHING;

    -- Insert default contact content
    INSERT INTO public.salon_content (salon_id, section, content_key, content_value, content_type) VALUES
    (salon_uuid, 'contact', 'title', 'İletişime Geçin', 'text'),
    (salon_uuid, 'contact', 'description', 'Sorularınız için bizimle iletişime geçebilir, randevunuzu kolayca oluşturabilirsiniz.', 'text'),
    (salon_uuid, 'contact', 'cta_title', 'Hemen Randevu Alın', 'text'),
    (salon_uuid, 'contact', 'cta_description', 'Online sistemimiz ile 7/24 randevu oluşturabilirsiniz.', 'text')
    ON CONFLICT (salon_id, section, content_key) DO NOTHING;

    -- Insert sample testimonials
    INSERT INTO public.salon_testimonials (salon_id, customer_name, rating, comment, service_name, date_text, is_active, display_order) VALUES
    (salon_uuid, 'Ahmet Yılmaz', 5, 'Harika bir deneyimdi! Çok memnun kaldım, herkese tavsiye ederim.', 'Saç Kesimi', '1 hafta önce', true, 1),
    (salon_uuid, 'Mehmet Kaya', 5, 'Profesyonel hizmet ve güler yüzlü personel. Kesinlikle tekrar geleceğim.', 'Sakal Tıraşı', '2 hafta önce', true, 2),
    (salon_uuid, 'Ali Demir', 4, 'Temiz ve hijyenik ortam. Fiyatlar da uygun, tavsiye ederim.', 'Saç Kesimi', '3 hafta önce', true, 3)
    ON CONFLICT DO NOTHING;

END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGER FOR AUTO-UPDATING updated_at
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS update_salon_content_updated_at ON public.salon_content;
DROP TRIGGER IF EXISTS update_salon_testimonials_updated_at ON public.salon_testimonials;

-- Create triggers for auto-updating updated_at
CREATE TRIGGER update_salon_content_updated_at
    BEFORE UPDATE ON public.salon_content
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_salon_testimonials_updated_at
    BEFORE UPDATE ON public.salon_testimonials
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE public.salon_content IS 'Stores customizable content for salon landing pages using flexible key-value structure';
COMMENT ON TABLE public.salon_testimonials IS 'Stores customer testimonials for each salon with rating and display order';
COMMENT ON FUNCTION public.insert_default_salon_content(UUID) IS 'Inserts default content for a new salon including hero, about, services, contact sections and sample testimonials';

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'SalonFlow CMS migration completed successfully at %', NOW();
END $$;
