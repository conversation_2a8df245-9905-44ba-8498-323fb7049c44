-- Referral Benefits ve Salon Subscriptions Sorunlarını Düzeltme
-- Tarih: 2024-08-02

-- 1. referral_benefits tablosundaki benefit_type kısıtlamasını güncelleme
-- Mevcut kısıtlamayı kaldır
ALTER TABLE referral_benefits DROP CONSTRAINT IF EXISTS referral_benefits_benefit_type_check;

-- <PERSON><PERSON> k<PERSON> ekle (subscription_extension değerini de kabul edecek şekilde)
ALTER TABLE referral_benefits ADD CONSTRAINT referral_benefits_benefit_type_check 
  CHECK (benefit_type = ANY (ARRAY['discount', 'free_month', 'feature_unlock', 'subscription_extension']));

-- 2. salon_subscriptions tablosundaki plan sütununu nullable yap
-- Bu sütun eski bir sütun ve artık plan_id kullanılıyor
ALTER TABLE salon_subscriptions ALTER COLUMN plan DROP NOT NULL;

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON> değer ekle
-- <PERSON>ski kayıtlar için plan sütununa varsayılan değer ekle
UPDATE salon_subscriptions SET plan = 'basic' WHERE plan IS NULL;

-- 4. Referral benefits tablosunu kontrol et
-- Mevcut kayıtları kontrol et ve gerekirse güncelle
UPDATE referral_benefits 
SET benefit_type = 'subscription_extension' 
WHERE benefit_type NOT IN ('discount', 'free_month', 'feature_unlock', 'subscription_extension');

-- 5. Salon subscriptions tablosunu kontrol et
-- Mevcut kayıtları kontrol et ve gerekirse güncelle
UPDATE salon_subscriptions 
SET plan = 'basic' 
WHERE plan_id IS NOT NULL AND plan IS NULL;

-- 6. Referral benefits tablosunun yapısını kontrol et
-- Tablo yapısını göster
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'referral_benefits';

-- 7. Salon subscriptions tablosunun yapısını kontrol et
-- Tablo yapısını göster
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'salon_subscriptions';

-- 8. Referral benefits tablosundaki kısıtlamaları kontrol et
-- Kısıtlamaları göster
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'referral_benefits'::regclass;

-- 9. Salon subscriptions tablosundaki kısıtlamaları kontrol et
-- Kısıtlamaları göster
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'salon_subscriptions'::regclass;

-- 10. Referral benefits tablosuna yeni kayıt ekleme testi
-- Test kaydı ekle
DO $$
DECLARE
  test_referrer_id UUID := '00000000-0000-0000-0000-000000000001';
  test_referred_id UUID := '00000000-0000-0000-0000-000000000002';
  test_referral_code_id UUID := '00000000-0000-0000-0000-000000000003';
BEGIN
  -- Eğer test kaydı varsa sil
  DELETE FROM referral_benefits 
  WHERE referrer_salon_id = test_referrer_id AND referred_salon_id = test_referred_id;
  
  -- Test kaydı ekle
  INSERT INTO referral_benefits (
    referrer_salon_id, 
    referred_salon_id, 
    referral_code_id, 
    benefit_type, 
    benefit_value, 
    is_applied
  ) VALUES (
    test_referrer_id, 
    test_referred_id, 
    test_referral_code_id, 
    'subscription_extension', 
    '30', 
    false
  );
  
  -- Test kaydını sil
  DELETE FROM referral_benefits 
  WHERE referrer_salon_id = test_referrer_id AND referred_salon_id = test_referred_id;
  
  RAISE NOTICE 'Test başarılı: Referral benefits tablosuna kayıt eklenebiliyor.';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Test başarısız: %', SQLERRM;
END;
$$;

-- 11. Salon subscriptions tablosuna yeni kayıt ekleme testi
-- Test kaydı ekle
DO $$
DECLARE
  test_salon_id UUID := '00000000-0000-0000-0000-000000000001';
  test_plan_id UUID := '00000000-0000-0000-0000-000000000002';
BEGIN
  -- Eğer test kaydı varsa sil
  DELETE FROM salon_subscriptions 
  WHERE salon_id = test_salon_id;
  
  -- Test kaydı ekle
  INSERT INTO salon_subscriptions (
    salon_id, 
    plan_id, 
    status, 
    start_date, 
    is_active, 
    is_yearly
  ) VALUES (
    test_salon_id, 
    test_plan_id, 
    'trial', 
    CURRENT_DATE, 
    true, 
    false
  );
  
  -- Test kaydını sil
  DELETE FROM salon_subscriptions 
  WHERE salon_id = test_salon_id;
  
  RAISE NOTICE 'Test başarılı: Salon subscriptions tablosuna kayıt eklenebiliyor.';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Test başarısız: %', SQLERRM;
END;
$$;
