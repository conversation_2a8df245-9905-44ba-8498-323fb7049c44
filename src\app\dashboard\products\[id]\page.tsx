"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ProductForm } from "@/components/product-form"
import { useUser } from "@/contexts/UserContext"
import { useSubscription } from "@/contexts/SubscriptionContext"
import { products } from "@/lib/db"
import { Product } from "@/lib/db/types"

export default function EditProductPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { salonId } = useUser()
  const { hasFeature } = useSubscription()
  
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Ürün yönetimi özelliği kontrolü
  useEffect(() => {
    if (!hasFeature("product_management")) {
      router.push("/dashboard/products")
    }
  }, [hasFeature, router])

  // Ürün bilgilerini yükle
  useEffect(() => {
    async function loadProduct() {
      if (!salonId) return
      
      try {
        setIsLoading(true)
        const data = await products.getProductById(params.id)
        
        // Ürün bu salona ait değilse yönlendir
        if (data.salon_id !== salonId) {
          toast.error("Bu ürüne erişim izniniz yok")
          router.push("/dashboard/products")
          return
        }
        
        setProduct(data)
      } catch (error) {
        console.error("Ürün yüklenirken hata:", error)
        toast.error("Ürün yüklenirken bir hata oluştu")
        router.push("/dashboard/products")
      } finally {
        setIsLoading(false)
      }
    }
    
    loadProduct()
  }, [salonId, params.id, router])

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Ürün Düzenle</h1>
        </div>
      </header>

      <Card>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="space-y-6">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-32 w-full" />
              <div className="grid grid-cols-2 gap-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-20 w-full" />
            </div>
          ) : product ? (
            <ProductForm initialData={product} />
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">Ürün bulunamadı</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
