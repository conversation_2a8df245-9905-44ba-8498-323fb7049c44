"use client"

import { motion, useScroll, useTransform, useReducedMotion } from "framer-motion"
import { <PERSON>R<PERSON>, <PERSON>, Users, Clock } from "lucide-react"
import { BookingButton } from "./booking-button"
import { Spotlight } from "@/components/ui/spotlight"
import { useRef, useCallback } from "react"
import { getDefaultSalonContent } from "@/lib/db/public"
import { useSalon } from "@/contexts/SalonContext"
import { usePreview, useIsPreview } from "@/contexts/PreviewContext"

interface HeroSectionProps {
  salonName: string
  salonId: string
}

export function HeroSection({ salonName }: HeroSectionProps) {
  const containerRef = useRef<HTMLElement>(null)
  const shouldReduceMotion = useReducedMotion()

  const { salonContent, contentLoading } = useSalon()

  // Scroll transforms
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  })

  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "20%"])
  const textY = useTransform(scrollYProgress, [0, 1], ["0%", "15%"])
  const imageX = useTransform(scrollYProgress, [0, 1], [0, -10])

  // Memoize scroll handler
  const handleScrollToServices = useCallback(() => {
    document.getElementById('services')?.scrollIntoView({
      behavior: 'smooth'
    })
  }, [])


  // Preview context
  const isPreview = useIsPreview()
  const previewContext = isPreview ? usePreview() : null

  const heroContent = isPreview && previewContext
      ? previewContext.previewContent.hero
      : salonContent?.hero || getDefaultSalonContent().hero

  const isLoading = isPreview ? false : contentLoading

  // Optimized animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: shouldReduceMotion ? 0 : 0.1,
        delayChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: shouldReduceMotion ? 0 : 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: shouldReduceMotion ? 0 : 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
      <section
          ref={containerRef}
          className="relative min-h-[90vh] flex items-center overflow-hidden bg-gradient-to-br from-background via-background to-primary/5"
      >
        {/* Background Effects */}
        <motion.div
            style={{ y: shouldReduceMotion ? 0 : backgroundY }}
            className="absolute inset-0 will-change-transform"
        >
          <Spotlight
              className="-top-40 left-0 md:-top-20 md:left-60"
              fill="white"
          />
        </motion.div>

        {/* Animated Background Shapes */}
        {!shouldReduceMotion && (
            <div className="absolute inset-0 overflow-hidden">
              <motion.div
                  animate={{
                    rotate: [0, 360],
                  }}
                  transition={{
                    duration: 30,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  className="absolute -top-40 -right-40 w-60 h-60 md:w-80 md:h-80 bg-gradient-to-br from-primary/8 to-transparent rounded-full blur-2xl will-change-transform"
                  style={{ transform: "translateZ(0)" }}
              />
              <motion.div
                  animate={{
                    rotate: [360, 0],
                  }}
                  transition={{
                    duration: 40,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  className="absolute -bottom-40 -left-40 w-72 h-72 md:w-96 md:h-96 bg-gradient-to-tr from-secondary/8 to-transparent rounded-full blur-2xl will-change-transform"
                  style={{ transform: "translateZ(0)" }}
              />
            </div>
        )}

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8 md:gap-12 items-center">
              {/* Left Content */}
              <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  style={{ y: shouldReduceMotion ? 0 : textY }}
                  className="space-y-6 md:space-y-8 will-change-transform"
              >
                {/* Badge */}
                <motion.div
                    variants={itemVariants}
                    className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-3 py-1.5 md:px-4 md:py-2 rounded-full text-sm font-medium"
                >
                  <Star className="w-3 h-3 md:w-4 md:h-4 fill-current" />
                  <span>{heroContent.badgeText}</span>
                </motion.div>

                {/* Main Heading */}
                <div className="space-y-3 md:space-y-4">
                  <motion.h1
                      variants={itemVariants}
                      className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight"
                  >
                    <span className="block text-foreground">{salonName}</span>
                    <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    {heroContent.tagline}
                  </span>
                  </motion.h1>

                  <motion.p
                      variants={itemVariants}
                      className="text-lg sm:text-xl md:text-2xl text-muted-foreground max-w-2xl leading-relaxed"
                  >
                    {heroContent.description}
                  </motion.p>
                </div>

                {/* CTA Buttons */}
                <motion.div
                    variants={itemVariants}
                    className="flex flex-col sm:flex-row gap-3 md:gap-4"
                >
                  <BookingButton
                      buttonText={heroContent.ctaPrimary}
                      buttonSize="lg"
                      buttonClassName="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg hover:shadow-xl transition-all duration-300 text-base md:text-lg px-6 py-4 md:px-8 md:py-6 h-auto group"
                      triggerClassName="group-hover:translate-x-1 transition-transform duration-300"
                  />

                  <motion.button
                      whileHover={shouldReduceMotion ? {} : { scale: 1.02 }}
                      whileTap={shouldReduceMotion ? {} : { scale: 0.98 }}
                      className="inline-flex items-center justify-center px-6 py-4 md:px-8 md:py-6 text-base md:text-lg font-medium text-foreground bg-background border-2 border-border hover:border-primary/50 rounded-lg transition-all duration-300 group"
                      onClick={handleScrollToServices}
                  >
                    {heroContent.ctaSecondary}
                    <ArrowRight className="ml-2 w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </motion.button>
                </motion.div>

                {/* Stats */}
                <motion.div
                    variants={itemVariants}
                    className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6 md:space-x-8 pt-4"
                >
                  <div className="flex items-center space-x-2">
                    <div className="p-1.5 md:p-2 bg-primary/10 rounded-full">
                      <Users className="w-4 h-4 md:w-5 md:h-5 text-primary" />
                    </div>
                    <div>
                      <div className="text-xl md:text-2xl font-bold text-foreground">
                        {heroContent.stats.customers.value}
                      </div>
                      <div className="text-xs md:text-sm text-muted-foreground">
                        {heroContent.stats.customers.label}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <div className="p-1.5 md:p-2 bg-primary/10 rounded-full">
                      <Clock className="w-4 h-4 md:w-5 md:h-5 text-primary" />
                    </div>
                    <div>
                      <div className="text-xl md:text-2xl font-bold text-foreground">
                        {heroContent.stats.support.value}
                      </div>
                      <div className="text-xs md:text-sm text-muted-foreground">
                        {heroContent.stats.support.label}
                      </div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>

              {/* Right Content - Visual */}
              <motion.div
                  initial={{ opacity: 0, x: shouldReduceMotion ? 0 : 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: shouldReduceMotion ? 0 : 0.8, delay: 0.4 }}
                  style={{
                    y: shouldReduceMotion ? 0 : textY,
                    x: shouldReduceMotion ? 0 : imageX
                  }}
                  className="relative will-change-transform mt-8 lg:mt-0"
              >
                <div className="relative">
                  {/* Main Image Container */}
                  <div className="aspect-[4/5] bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl md:rounded-3xl overflow-hidden shadow-xl md:shadow-2xl">
                    <div className="w-full h-full bg-gradient-to-br from-muted/50 to-muted/30 flex items-center justify-center">
                      <div className="text-center space-y-3 md:space-y-4 p-4">
                        <div className="w-16 h-16 md:w-24 md:h-24 bg-primary/20 rounded-full mx-auto flex items-center justify-center">
                          <Users className="w-8 h-8 md:w-12 md:h-12 text-primary" />
                        </div>
                        <div className="space-y-1 md:space-y-2">
                          <h3 className="text-lg md:text-xl font-semibold text-foreground">Profesyonel Ekip</h3>
                          <p className="text-sm md:text-base text-muted-foreground">Uzman berberlerimizle</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Floating Elements */}
                  <motion.div
                      animate={shouldReduceMotion ? {} : {
                        y: [-5, 5, -5],
                      }}
                      transition={shouldReduceMotion ? {} : {
                        duration: 6,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="absolute -top-4 -right-4 md:-top-6 md:-right-6 bg-background shadow-lg rounded-xl md:rounded-2xl p-3 md:p-4 border will-change-transform"
                      style={{ transform: "translateZ(0)" }}
                  >
                    <div className="flex items-center space-x-2 md:space-x-3">
                      <div className="flex -space-x-1 md:-space-x-2">
                        <div className="w-6 h-6 md:w-8 md:h-8 bg-primary/20 rounded-full border-2 border-background"></div>
                        <div className="w-6 h-6 md:w-8 md:h-8 bg-secondary/20 rounded-full border-2 border-background"></div>
                        <div className="w-6 h-6 md:w-8 md:h-8 bg-accent/20 rounded-full border-2 border-background"></div>
                      </div>
                      <div>
                        <div className="text-sm md:text-base font-semibold">
                          {heroContent.stats.rating.value}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {heroContent.stats.rating.label}
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                      animate={shouldReduceMotion ? {} : {
                        y: [5, -5, 5],
                      }}
                      transition={shouldReduceMotion ? {} : {
                        duration: 8,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="absolute bottom-4 -left-4 md:bottom-4 md:-left-6 bg-background shadow-lg rounded-xl md:rounded-2xl p-3 md:p-4 border will-change-transform"
                      style={{ transform: "translateZ(0)" }}
                  >
                    <div className="flex items-center space-x-2 md:space-x-3">
                      <div className="p-1.5 md:p-2 bg-green-100 dark:bg-green-900/20 rounded-full">
                        <Clock className="w-4 h-4 md:w-5 md:h-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <div className="text-sm font-semibold">Hızlı Randevu</div>
                        <div className="text-xs text-muted-foreground">2 dakikada</div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
  )
}