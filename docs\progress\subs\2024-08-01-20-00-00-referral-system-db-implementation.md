# Referans Sistemi Veritabanı Tabanlı Uygulama (Düzeltilmiş)

**Tarih:** 1 Ağustos 2024
**Saat:** 20:00

## 1. Tespit Edilen Sorunlar

Referans sistemi uygulamasında aşağıdaki sorunlar tespit edildi:

1. **RLS Politikası Hatası**: `pending_referrals` tablosuna veri eklenirken RLS politikası hatası alınıyordu.
2. **Veri Bulunamama Hatası**: `pending_referrals` tablosunda veri aranırken PGRST116 hatası alınıyordu.

## 2. <PERSON><PERSON><PERSON> kaynağı, kayıt sırasında kullanıcı henüz oluşturulmadan referans kodunu veritabanına kaydetmeye çalışmamızdı. Supabase Auth ile kayıt işlemi şu şekilde çalışır:

1. Ku<PERSON><PERSON><PERSON><PERSON> kayıt formunu doldurur ve gönderir
2. <PERSON><PERSON><PERSON>, kullanıcıyı oluşturur ve e-posta doğrulama bağlantısı gönderir
3. Kullanıcı e-posta doğrulama bağlantısına tıklar
4. Supabase Auth, kullanıcıyı doğrular ve callback URL'ye yönlendirir

Kayıt işlemi sırasında, kullanıcı henüz tam olarak oluşturulmadan referans kodunu veritabanına kaydetmeye çalışıyorduk. Bu nedenle, RLS politikası hatası alıyorduk.

## 3. Çözüm

Sorunu çözmek için aşağıdaki değişiklikleri yaptık:

### 3.1. Kayıt İşlemini Güncelleme

Kayıt işlemi sırasında, kullanıcı oluşturulduktan hemen sonra referans kodunu veritabanına kaydetmeye çalışıyoruz:

```typescript
// Store the referral code in the database to use after email verification
if (values.referralCode && data.user) {
  try {
    // Referans kodunu veritabanında sakla
    await referrals.savePendingReferral(data.user.id, values.referralCode);
    console.log(`Referral code ${values.referralCode} saved for user ${data.user.id}`);
    
    toast.success("Kayıt başarılı! Hesabınızı doğruladıktan sonra referans kodunuz uygulanacaktır.");
  } catch (error) {
    console.error("Referans kodu kaydedilirken hata:", error);
    // Hata durumunda yedek olarak sessionStorage'a kaydet
    sessionStorage.setItem('referralCode', values.referralCode);
    console.log(`Referral code ${values.referralCode} saved in sessionStorage as fallback`);
    
    toast.success("Kayıt başarılı! Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.");
  }
} else {
  toast.success("Kayıt başarılı! Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.");
}
```

Burada önemli olan nokta, `data.user` kontrolü ile kullanıcının oluşturulduğundan emin olmamız. Ayrıca, hata durumunda yedek olarak sessionStorage'a kaydetme mekanizması da ekledik.

### 3.2. ReferralCodeChecker Komponenti Güncelleme

Dashboard'a giriş yapıldığında, veritabanında bekleyen referans kodu olup olmadığını kontrol eden ve yoksa sessionStorage'dan alıp veritabanına kaydeden bir komponent oluşturduk:

```typescript
useEffect(() => {
  const saveReferralCode = async () => {
    if (!checkReferral || !user) return

    try {
      // Önce veritabanında bekleyen referans kodu var mı kontrol et
      const pendingReferral = await referrals.getPendingReferral(user.id)
      if (pendingReferral) {
        console.log(`Pending referral already exists for user ${user.id}: ${pendingReferral.referral_code}`)
        return
      }

      // Veritabanında yoksa, sessionStorage'dan kontrol et (yedek mekanizma)
      const referralCode = sessionStorage.getItem('referralCode')
      if (!referralCode) return

      console.log(`Referral code ${referralCode} found in sessionStorage, saving to database...`)

      // Referans kodunu veritabanına kaydet
      await referrals.savePendingReferral(user.id, referralCode)
      
      // Referans kodunu sessionStorage'dan temizle
      sessionStorage.removeItem('referralCode')
      
      console.log(`Referral code ${referralCode} saved for user ${user.id}`)
      toast.success("Referans kodunuz kaydedildi. Salon oluşturduğunuzda otomatik olarak uygulanacaktır.")
    } catch (error) {
      console.error("Referans kodu kaydedilirken hata:", error)
    }
  }

  saveReferralCode()
}, [checkReferral, user])
```

Bu komponent, hem veritabanı tabanlı yaklaşımı hem de sessionStorage tabanlı yedek mekanizmayı destekler.

### 3.3. Salon Oluşturma Sayfasını Güncelleme

Salon oluşturma sayfası, hem veritabanından hem de sessionStorage'dan referans kodunu kontrol edecek şekilde güncellendi:

```typescript
// Veritabanından bekleyen referans kodunu kontrol et ve uygula
let isReferred = false
try {
  // Kullanıcının bekleyen referans kodunu al
  const pendingReferral = await referrals.getPendingReferral(user.id)
  
  if (pendingReferral) {
    try {
      // Referans kodunu uygula
      await referrals.applyReferralCode(pendingReferral.referral_code, data.id)
      
      // Bekleyen referans kodunu uygulandı olarak işaretle
      await referrals.markPendingReferralAsApplied(pendingReferral.id)
      
      isReferred = true
      console.log(`Referral code ${pendingReferral.referral_code} applied for salon ${data.id}`)
      toast.success("Referans kodu başarıyla uygulandı!")
    } catch (error) {
      console.error("Referans kodu uygulanırken hata:", error)
    }
  } else {
    // Yedek mekanizma olarak sessionStorage'dan kontrol et
    if (typeof window !== 'undefined') {
      const referralCode = sessionStorage.getItem('referralCode')
      if (referralCode) {
        try {
          // Referans kodunu uygula
          await referrals.applyReferralCode(referralCode, data.id)
          
          // Referans kodunu sessionStorage'dan temizle
          sessionStorage.removeItem('referralCode')
          
          isReferred = true
          console.log(`Referral code ${referralCode} from sessionStorage applied for salon ${data.id}`)
          toast.success("Referans kodu başarıyla uygulandı!")
        } catch (error) {
          console.error("Referans kodu uygulanırken hata:", error)
        }
      }
    }
  }
} catch (error) {
  console.error("Bekleyen referans kodu kontrol edilirken hata:", error)
}
```

Bu kod, önce veritabanından bekleyen referans kodunu kontrol eder, yoksa yedek mekanizma olarak sessionStorage'dan kontrol eder.

## 4. Sonuç

Bu değişikliklerle, referans sistemi artık veritabanı tabanlı bir yaklaşımla çalışmaktadır. Referans kodu ile kayıt olan kullanıcılar, kayıt sırasında referans kodları veritabanına kaydedilir. Salon oluşturduklarında ise, bu referans kodu otomatik olarak uygulanır ve referans faydaları etkinleştirilir.

Ayrıca, hata durumlarına karşı yedek mekanizma olarak sessionStorage kullanımı da desteklenmektedir. Bu sayede, veritabanına kaydetme işlemi başarısız olsa bile, referans kodu kaybedilmez ve kullanıcı deneyimi etkilenmez.
