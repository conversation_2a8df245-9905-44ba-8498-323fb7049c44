import { NextRequest, NextResponse } from 'next/server';
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';

export async function subscriptionMiddleware(req: NextRequest) {
  console.log("🔍 subscriptionMiddleware başladı: ", req.nextUrl.pathname);
  const res = NextResponse.next();
  const supabase = createMiddlewareClient({ req, res });

  // Kullanıcı oturumunu kontrol et
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    console.log("⚠️ Oturum bulunamadı, normal yanıt dönülüyor");
    return res;
  }
  console.log("✅ Oturum bulundu, kullanıcı:", session.user.email);

  // Salon ID'sini al
  const { data: salon, error: salonError } = await supabase
    .from('salons')
    .select('id, name')
    .eq('owner_id', session.user.id)
    .single();

  if (salonError) {
    console.log("❌ Salon sorgusu hatası:", salonError.message);
  }

  if (!salon) {
    console.log("⚠️ Salon bulunamadı, normal yanıt dönülüyor");
    return res;
  }
  console.log("✅ Salon bulundu:", salon.id, salon.name);

  // Abonelik durumunu kontrol et - Middleware içinde doğrudan sorgu yapıyoruz
  try {
    console.log("🔍 Abonelik durumu kontrol ediliyor...");

    // Middleware client ile doğrudan sorgu yapıyoruz, browser client kullanmıyoruz
    const { data: subscription, error: subscriptionError } = await supabase
      .from('salon_subscriptions')
      .select('*, plans:plan_id(*)')
      .eq('salon_id', salon.id)
      .eq('is_active', true)
      .single();

    if (subscriptionError && subscriptionError.code !== 'PGRST116') {
      console.error("❌ Abonelik sorgusunda hata:", subscriptionError);
      return res; // Hata durumunda normal yanıt dönüyoruz
    }

    console.log("📊 Abonelik bilgileri:", JSON.stringify(subscription, null, 2));

    // Abonelik yoksa veya askıya alınmışsa
    if (!subscription) {
      console.log("⚠️ Abonelik bulunamadı, abonelik sayfasına yönlendiriliyor");
      return NextResponse.redirect(new URL('/dashboard/subscription', req.url));
    }

    if (subscription.status === 'suspended') {
      console.log("⚠️ Abonelik askıya alınmış, abonelik sayfasına yönlendiriliyor");
      return NextResponse.redirect(new URL('/dashboard/subscription', req.url));
    }

    // Deneme süresi bitmişse
    if (subscription.status === 'trial') {
      // Deneme süresi kontrolünü de middleware içinde yapıyoruz
      if (!subscription.trial_end_date) {
        console.log("⚠️ Deneme bitiş tarihi bulunamadı, deneme süresi bitmiş kabul ediliyor");
        return NextResponse.redirect(new URL('/dashboard/subscription', req.url));
      }

      const today = new Date();
      const trialEndDate = new Date(subscription.trial_end_date);
      const isExpired = today > trialEndDate;

      console.log("🔍 Deneme süresi kontrolü:", isExpired ? "Süresi dolmuş" : "Aktif",
        "- Bitiş tarihi:", subscription.trial_end_date);

      if (isExpired) {
        console.log("⚠️ Deneme süresi bitmiş, abonelik sayfasına yönlendiriliyor");
        return NextResponse.redirect(new URL('/dashboard/subscription', req.url));
      }
    }

    // Ödeme gecikmiş durumda ise
    if (subscription.status === 'past_due') {
      const pastDueDate = new Date(subscription.updated_at);
      const today = new Date();
      const diffTime = Math.abs(today.getTime() - pastDueDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      console.log("🔍 Ödeme gecikmesi kontrolü:", diffDays, "gün geçmiş");

      if (diffDays > 7) {
        console.log("⚠️ Ödeme 7 günden fazla gecikmiş, abonelik sayfasına yönlendiriliyor");
        return NextResponse.redirect(new URL('/dashboard/subscription', req.url));
      }
    }

    console.log("✅ Abonelik kontrolü başarılı, normal yanıt dönülüyor");
  } catch (error) {
    console.error("❌ Abonelik kontrolü sırasında beklenmeyen hata:", error);
  }

  return res;
}
