-- Referans siste<PERSON> düzel<PERSON>i için SQL script
-- Tarih: 2024-08-05

-- 1. Referans fay<PERSON><PERSON> tipini güncelleme
-- Referans veren kişi (referrer) indirim kazan<PERSON>, referans alan kişi (referred) 1 aylık deneme süresi kazanacak
ALTER TABLE referral_benefits DROP CONSTRAINT IF EXISTS referral_benefits_benefit_type_check;
ALTER TABLE referral_benefits ADD CONSTRAINT referral_benefits_benefit_type_check 
CHECK (benefit_type IN ('discount', 'free_month', 'feature_unlock', 'subscription_extension', 'referred_discount', 'referrer_discount'));

-- 2. Mevcut kayıtları güncelleme
UPDATE referral_benefits
SET benefit_type = 'referrer_discount',
    benefit_value = (SELECT CAST(price_monthly AS TEXT) FROM subscription_plans ORDER BY price_monthly ASC LIMIT 1),
    discount_amount = (SELECT price_monthly FROM subscription_plans ORDER BY price_monthly ASC LIMIT 1)
WHERE benefit_type = 'referred_discount';

-- 3. Referans indirimi kontrolü için fonksiyonu güncelleme
CREATE OR REPLACE FUNCTION check_referral_discount(salon_id UUID)
RETURNS TABLE (
  id UUID,
  discount_amount NUMERIC,
  referred_salon_id UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    rb.id,
    rb.discount_amount,
    rb.referred_salon_id
  FROM
    referral_benefits rb
  WHERE
    rb.referrer_salon_id = salon_id
    AND rb.benefit_type = 'referrer_discount'
    AND rb.discount_applied = FALSE
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Referans indirimi uygulama fonksiyonunu güncelleme
CREATE OR REPLACE FUNCTION apply_referral_discount(benefit_id UUID, payment_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE referral_benefits
  SET
    discount_applied = TRUE,
    discount_applied_payment_id = payment_id,
    is_applied = TRUE,
    applied_date = CURRENT_DATE
  WHERE
    id = benefit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Fonksiyon açıklamasını güncelleme
COMMENT ON FUNCTION check_referral_discount(UUID) IS 'Bir salon için kullanılabilir referans indirimi olup olmadığını kontrol eder. Referans veren kişi (referrer) indirim kazanır.';
