# SalonFlow Abonelik Sistemi Geliştirme Süreci

**Tarih:** 30 Temmuz 2024
**Saat:** 12:00

## 1. Görev Durumu Takibi

### 1.1. Kanban Board Yapısı

SalonFlow abonelik sistemi geliştirme sürecinde görevlerin takibi için aşağıdaki Kanban board yapısı kullanılmaktadır:

#### Sütunlar

1. **Yapılacak (To Do)**
   - Hen<PERSON>z başlanmamış görevler
   - Öncelik ve tahmini süre bilgileri içerir

2. **Devam Ediyor (In Progress)**
   - Üzerinde çalışılan görevler
   - Atanan kişi ve başlangıç tarihi bilgileri içerir

3. **Test Ediliyor (Testing)**
   - Geliştirmesi tamamlanmış, test aşamasındaki görevler
   - Test senaryoları ve test eden kişi bilgileri içerir

4. **Tamamlandı (Done)**
   - Geliştirme ve test süreçleri tamamlanmış görevler
   - Tamamlanma tarihi ve PR/commit bilgileri içerir

#### Etiketler

- **Öncelik:** Yüksek, Orta, Düşük
- **Tür:** Veritabanı, Backend, Frontend, Test, Dokümantasyon
- **Zorluk:** Kolay, Orta, Zor
- **Sprint:** Sprint 1, Sprint 2, Sprint 3, Sprint 4

### 1.2. Görev Takip Dosyası

Görevlerin durumu, [Abonelik Sistemi Geliştirme Görevleri](docs/progress/subs/2024-07-25-16-45-00-subscription-development-tasks.md) dosyasında takip edilmektedir. Bu dosya:

- Tüm görevleri hiyerarşik bir yapıda listeler
- Tamamlanan görevleri ✅ işareti ile işaretler
- Görevlerin zorluk ve tahmini süre bilgilerini içerir
- Görevler arasındaki bağımlılıkları belirtir

### 1.3. Haftalık İlerleme Raporları

Her hafta sonunda, aşağıdaki bilgileri içeren bir ilerleme raporu oluşturulur:

- Tamamlanan görevler
- Devam eden görevler
- Karşılaşılan zorluklar ve çözümleri
- Bir sonraki hafta için planlanan görevler
- Genel ilerleme durumu ve zaman çizelgesi güncellemeleri

İlerleme raporları, `docs/progress/` klasöründe `YYYY-MM-DD-HH-MM-SS-subscription-implementation-progress.md` formatında saklanır.

## 2. Kod İnceleme Süreci

### 2.1. Kod İnceleme Kontrol Listesi

Her görev için kod incelemesi yapılırken aşağıdaki kontrol listesi kullanılır:

#### 2.1.1. Kod Kalitesi

- [ ] Kod, belirlenen kodlama standartlarına uygun mu?
- [ ] Fonksiyonlar ve değişkenler anlamlı isimlerle adlandırılmış mı?
- [ ] Kod tekrarı (DRY prensibi ihlali) var mı?
- [ ] Karmaşık kod blokları için yeterli açıklama eklenmiş mi?
- [ ] Hata yönetimi uygun şekilde yapılmış mı?

#### 2.1.2. Performans

- [ ] Veritabanı sorguları optimize edilmiş mi?
- [ ] Gereksiz API çağrıları var mı?
- [ ] React bileşenlerinde gereksiz render'lar önlenmiş mi?
- [ ] Büyük veri setleri için sayfalama veya sonsuz kaydırma kullanılmış mı?
- [ ] Memoization veya caching mekanizmaları gerekli yerlerde kullanılmış mı?

#### 2.1.3. Güvenlik

- [ ] RLS politikaları doğru şekilde uygulanmış mı?
- [ ] Kullanıcı girişleri doğrulanıyor ve temizleniyor mu?
- [ ] Hassas veriler güvenli şekilde işleniyor mu?
- [ ] Yetkilendirme kontrolleri hem frontend hem de backend'de yapılıyor mu?
- [ ] API endpoint'leri yetkilendirme gerektiriyor mu?

#### 2.1.4. Test Kapsamı

- [ ] Birim testleri yazılmış mı?
- [ ] Entegrasyon testleri yazılmış mı?
- [ ] Edge case'ler test edilmiş mi?
- [ ] Test kapsamı yeterli mi?
- [ ] Testler başarılı bir şekilde çalışıyor mu?

### 2.2. Kod İnceleme Süreci Adımları

1. Geliştirici, tamamlanan görevi bir pull request (PR) olarak gönderir.
2. PR, en az bir başka geliştirici tarafından incelenir.
3. İnceleyici, kod inceleme kontrol listesini kullanarak kodu değerlendirir.
4. Geri bildirimler PR üzerinde yorum olarak paylaşılır.
5. Geliştirici, geri bildirimlere göre gerekli düzeltmeleri yapar.
6. İnceleyici, düzeltmeleri kontrol eder ve PR'ı onaylar veya ek düzeltmeler ister.
7. PR onaylandıktan sonra, kod ana dala (main branch) birleştirilir.

## 3. Sürüm Yönetimi

### 3.1. Sürüm Numaralandırma Standardı

SalonFlow abonelik sistemi için Semantic Versioning (SemVer) standardı kullanılmaktadır:

**X.Y.Z** (Major.Minor.Patch)

- **X (Major):** Geriye dönük uyumlu olmayan değişiklikler
- **Y (Minor):** Geriye dönük uyumlu yeni özellikler
- **Z (Patch):** Geriye dönük uyumlu hata düzeltmeleri

Örnek: 1.2.3

### 3.2. Geliştirme Ortamları

#### 3.2.1. Geliştirme (Development)

- URL: dev.salonflow.com
- Veritabanı: salonflow_dev
- Otomatik dağıtım: Her PR birleştirildiğinde
- Kullanım: Aktif geliştirme ve dahili test

#### 3.2.2. Test (Staging)

- URL: staging.salonflow.com
- Veritabanı: salonflow_staging
- Otomatik dağıtım: Haftalık (veya manuel tetikleme)
- Kullanım: QA testi ve demo

#### 3.2.3. Üretim (Production)

- URL: salonflow.com
- Veritabanı: salonflow_prod
- Otomatik dağıtım: Manuel onay ile
- Kullanım: Son kullanıcılar

### 3.3. Sürüm Notları

Her sürüm için aşağıdaki bilgileri içeren sürüm notları oluşturulur:

- Sürüm numarası ve tarihi
- Yeni özellikler
- İyileştirmeler
- Hata düzeltmeleri
- Bilinen sorunlar
- Yükseltme notları

Sürüm notları, `docs/releases/` klasöründe `vX.Y.Z.md` formatında saklanır ve kullanıcılara duyurulur.
