"use client"

import { useState, useEffect, useCallback } from "react"
import { format, addDays, eachDayOfInterval, addYears, getMonth, getDate } from "date-fns"
import { toast } from "sonner"
import { CalendarDays, Plus, Trash2, Repeat } from "lucide-react"

import { useUser } from "@/contexts/UserContext"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { DatePicker } from "@/components/ui/date-picker"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import * as db from "@/lib/db"

// Tek tatil günü eklemek için form şeması
const singleHolidaySchema = z.object({
  date: z.date({
    required_error: "Lütfen bir tarih seçin",
  }),
  description: z.string().optional(),
  is_recurring: z.boolean().default(false),
})

// Tarih aralığında tatil günleri eklemek için form şeması
const dateRangeHolidaySchema = z.object({
  startDate: z.date({
    required_error: "Lütfen başlangıç tarihi seçin",
  }),
  endDate: z.date({
    required_error: "Lütfen bitiş tarihi seçin",
  }),
  description: z.string().optional(),
})
  .refine(data => data.endDate >= data.startDate, {
    message: "Bitiş tarihi başlangıç tarihinden sonra olmalıdır",
    path: ["endDate"],
  });

// Tekrarlayan tatil günleri eklemek için form şeması
const recurringHolidaySchema = z.object({
  date: z.date({
    required_error: "Lütfen bir tarih seçin",
  }),
  description: z.string().optional(),
  years: z.number().min(1, "En az 1 yıl seçmelisiniz").max(10, "En fazla 10 yıl seçebilirsiniz").default(5),
});

type SingleHolidayFormValues = z.infer<typeof singleHolidaySchema>
type DateRangeHolidayFormValues = z.infer<typeof dateRangeHolidaySchema>
type RecurringHolidayFormValues = z.infer<typeof recurringHolidaySchema>

export default function HolidaysPage() {
  const [loading, setLoading] = useState(false)
  const [holidays, setHolidays] = useState<db.Holiday[]>([])
  const [activeTab, setActiveTab] = useState<"single" | "range" | "recurring">("single")
  const { salon } = useUser()

  // Tek tatil günü formunu başlat
  const singleForm = useForm<SingleHolidayFormValues>({
    resolver: zodResolver(singleHolidaySchema),
    defaultValues: {
      description: "",
      is_recurring: false,
    },
  })

  // Tarih aralığı formunu başlat
  const rangeForm = useForm<DateRangeHolidayFormValues>({
    resolver: zodResolver(dateRangeHolidaySchema),
    defaultValues: {
      description: "",
    },
  })

  // Tekrarlayan tatil günü formunu başlat
  const recurringForm = useForm<RecurringHolidayFormValues>({
    resolver: zodResolver(recurringHolidaySchema),
    defaultValues: {
      description: "",
      years: 5,
    },
  })

  // Veritabanından tatil günlerini yükle
  const loadHolidays = useCallback(async () => {
    if (!salon?.id) return

    try {
      setLoading(true)
      const data = await db.holidays.getHolidays(salon.id)
      setHolidays(data)
    } catch (error) {
      console.error("Tatil günleri yüklenirken hata:", error)
      toast.error("Tatil günleri yüklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }, [salon?.id, setLoading, setHolidays])

  // Bileşen yüklendiğinde tatil günlerini yükle
  useEffect(() => {
    if (salon?.id) {
      loadHolidays()
    }
  }, [salon?.id, loadHolidays])

  // Tek tatil günü form gönderimini işle
  const onSubmitSingle = async (values: SingleHolidayFormValues) => {
    if (!salon?.id) {
      toast.error("Salon bilgisi bulunamadı")
      return
    }

    try {
      setLoading(true)

      // Tarihi YYYY-MM-DD formatına dönüştür
      const formattedDate = format(values.date, "yyyy-MM-dd")

      // Tarih zaten tatil günü olarak eklenmiş mi kontrol et
      const isExisting = holidays.some(h => h.date === formattedDate)
      if (isExisting) {
        toast.error("Bu tarih zaten tatil günü olarak eklenmiş")
        return
      }

      // Tatil günü oluştur
      await db.holidays.createHoliday({
        salon_id: salon.id,
        date: formattedDate,
        description: values.description || undefined,
        is_recurring: values.is_recurring,
      })

      // Formu sıfırla
      singleForm.reset({
        date: undefined,
        description: "",
        is_recurring: false,
      })

      // Tatil günlerini yeniden yükle
      await loadHolidays()

      toast.success("Tatil günü başarıyla eklendi")
    } catch (error) {
      console.error("Tatil günü eklenirken hata:", error)
      toast.error("Tatil günü eklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Tarih aralığı form gönderimini işle
  const onSubmitRange = async (values: DateRangeHolidayFormValues) => {
    if (!salon?.id) {
      toast.error("Salon bilgisi bulunamadı")
      return
    }

    try {
      setLoading(true)

      // Aralıktaki tüm tarihleri al
      const dateRange = eachDayOfInterval({
        start: values.startDate,
        end: values.endDate
      })

      // Tarihleri formatla ve tatil günü nesneleri oluştur
      const holidaysToCreate = dateRange.map(date => {
        const formattedDate = format(date, "yyyy-MM-dd")
        return {
          salon_id: salon.id,
          date: formattedDate,
          description: values.description || undefined,
        }
      })

      // Zaten tatil olan tarihleri filtrele
      const newHolidays = holidaysToCreate.filter(holiday =>
        !holidays.some(h => h.date === holiday.date)
      )

      if (newHolidays.length === 0) {
        toast.error("Seçilen tarih aralığındaki tüm günler zaten tatil olarak eklenmiş")
        return
      }

      // Tatil günlerini oluştur
      await db.holidays.createHolidays(newHolidays)

      // Formu sıfırla
      rangeForm.reset({
        startDate: undefined,
        endDate: undefined,
        description: "",
      })

      // Tatil günlerini yeniden yükle
      await loadHolidays()

      toast.success(`${newHolidays.length} tatil günü başarıyla eklendi`)
    } catch (error) {
      console.error("Tatil günleri eklenirken hata:", error)
      toast.error("Tatil günleri eklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Tekrarlayan tatil günü form gönderimini işle
  const onSubmitRecurring = async (values: RecurringHolidayFormValues) => {
    if (!salon?.id) {
      toast.error("Salon bilgisi bulunamadı")
      return
    }

    try {
      setLoading(true)

      const baseDate = values.date
      const month = getMonth(baseDate)
      const day = getDate(baseDate)
      const holidaysToCreate = []

      // Belirtilen yıl sayısı için tatil günleri oluştur
      for (let i = 0; i < values.years; i++) {
        const yearDate = addYears(baseDate, i)
        const formattedDate = format(yearDate, "yyyy-MM-dd")

        // Bu tarih zaten tatil günü mü kontrol et
        if (!holidays.some(h => h.date === formattedDate)) {
          holidaysToCreate.push({
            salon_id: salon.id,
            date: formattedDate,
            description: values.description || undefined,
            is_recurring: true,
          })
        }
      }

      if (holidaysToCreate.length === 0) {
        toast.error("Seçilen tarihler zaten tatil olarak eklenmiş")
        return
      }

      // Tatil günlerini oluştur
      await db.holidays.createHolidays(holidaysToCreate)

      // Formu sıfırla
      recurringForm.reset({
        date: undefined,
        description: "",
        years: 5,
      })

      // Tatil günlerini yeniden yükle
      await loadHolidays()

      toast.success(`${holidaysToCreate.length} tekrarlayan tatil günü başarıyla eklendi`)
    } catch (error) {
      console.error("Tekrarlayan tatil günleri eklenirken hata:", error)
      toast.error("Tekrarlayan tatil günleri eklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  // Tatil gününü sil
  const deleteHoliday = async (id: string) => {
    try {
      setLoading(true)
      await db.holidays.deleteHoliday(id)
      await loadHolidays()
      toast.success("Tatil günü başarıyla silindi")
    } catch (error) {
      console.error("Tatil günü silinirken hata:", error)
      toast.error("Tatil günü silinirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Tatil Günleri</h1>
        </div>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Add Holiday Form */}
        <Card>
          <CardHeader>
            <CardTitle>Tatil Günü Ekle</CardTitle>
            <CardDescription>
              Salonunuzun kapalı olacağı tatil günlerini ekleyin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="single" onValueChange={(value) => setActiveTab(value as "single" | "range" | "recurring")}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="single">Tek Gün</TabsTrigger>
                <TabsTrigger value="range">Tarih Aralığı</TabsTrigger>
                <TabsTrigger value="recurring">Tekrarlayan</TabsTrigger>
              </TabsList>

              <TabsContent value="single">
                <Form {...singleForm}>
                  <form onSubmit={singleForm.handleSubmit(onSubmitSingle)} className="space-y-4">
                    <FormField
                      control={singleForm.control}
                      name="date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Tarih</FormLabel>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                            fromDate={new Date()} // Sadece gelecek tarihlere izin ver
                            compact={true}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={singleForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Açıklama (İsteğe bağlı)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Örn: Bayram tatili"
                              {...field}
                              value={field.value || ""}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={singleForm.control}
                      name="is_recurring"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Tekrarlayan Tatil
                            </FormLabel>
                            <FormDescription>
                              Bu tatil her yıl aynı tarihte tekrarlanacak
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    <Button type="submit" disabled={loading} className="w-full">
                      {loading ? "Ekleniyor..." : "Tatil Günü Ekle"}
                    </Button>
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="range">
                <Form {...rangeForm}>
                  <form onSubmit={rangeForm.handleSubmit(onSubmitRange)} className="space-y-4">
                    <FormField
                      control={rangeForm.control}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Başlangıç Tarihi</FormLabel>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                            fromDate={new Date()} // Sadece gelecek tarihlere izin ver
                            compact={true}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={rangeForm.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Bitiş Tarihi</FormLabel>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                            fromDate={rangeForm.getValues("startDate") || new Date()} // Sadece başlangıç tarihinden sonraki tarihlere izin ver
                            compact={true}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={rangeForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Açıklama (İsteğe bağlı)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Örn: Yaz tatili"
                              {...field}
                              value={field.value || ""}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button type="submit" disabled={loading} className="w-full">
                      {loading ? "Ekleniyor..." : "Tatil Günleri Ekle"}
                    </Button>
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="recurring">
                <Form {...recurringForm}>
                  <form onSubmit={recurringForm.handleSubmit(onSubmitRecurring)} className="space-y-4">
                    <FormField
                      control={recurringForm.control}
                      name="date"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Tarih</FormLabel>
                          <DatePicker
                            date={field.value}
                            setDate={field.onChange}
                            fromDate={new Date()} // Sadece gelecek tarihlere izin ver
                            compact={true}
                          />
                          <FormDescription>
                            Bu tarih her yıl tekrarlanacak şekilde tatil günü olarak eklenecektir.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={recurringForm.control}
                      name="years"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Yıl Sayısı</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={10}
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                              value={field.value}
                            />
                          </FormControl>
                          <FormDescription>
                            Tatil gününün kaç yıl boyunca tekrarlanacağını belirtin (1-10).
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={recurringForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Açıklama (İsteğe bağlı)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Örn: Yılbaşı tatili"
                              {...field}
                              value={field.value || ""}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button type="submit" disabled={loading} className="w-full">
                      {loading ? "Ekleniyor..." : "Tekrarlayan Tatil Günü Ekle"}
                    </Button>
                  </form>
                </Form>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Holidays List */}
        <Card>
          <CardHeader>
            <CardTitle>Tatil Günleri</CardTitle>
            <CardDescription>
              Salonunuz için tanımlanmış tatil günleri
            </CardDescription>
          </CardHeader>
          <CardContent>
            {holidays.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <CalendarDays className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">Henüz tatil günü eklenmemiş</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tarih</TableHead>
                    <TableHead>Açıklama</TableHead>
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {holidays.map((holiday) => (
                    <TableRow key={holiday.id}>
                      <TableCell>
                        {format(new Date(holiday.date), "dd.MM.yyyy")}
                      </TableCell>
                      <TableCell>{holiday.description || "-"}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => deleteHoliday(holiday.id)}
                          disabled={loading}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Sil</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
