import { supabaseClient } from '../supabase-singleton';

// Define types
export interface CustomDomain {
  id: string;
  salon_id: string;
  domain: string;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

export interface CustomDomainInsert {
  salon_id: string;
  domain: string;
  is_verified?: boolean;
}

export interface CustomDomainUpdate {
  domain?: string;
  is_verified?: boolean;
}

const supabase = supabaseClient;

/**
 * Get all custom domains for a salon
 */
export async function getCustomDomainsBySalonId(salonId: string) {
  const { data, error } = await supabase
    .from('custom_domains')
    .select('*')
    .eq('salon_id', salonId);

  if (error) throw error;
  return data as CustomDomain[];
}

/**
 * Get a custom domain by domain name
 */
export async function getCustomDomainByDomain(domain: string) {
  const { data, error } = await supabase
    .from('custom_domains')
    .select('*, salons:salon_id(id)')
    .eq('domain', domain)
    .eq('is_verified', true)
    .single();

  if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "No rows returned" error
  return error && error.code === 'PGRST116' ? null : data;
}

/**
 * Create a new custom domain
 */
export async function createCustomDomain(customDomain: CustomDomainInsert) {
  const { data, error } = await supabase
    .from('custom_domains')
    .insert(customDomain)
    .select()
    .single();

  if (error) throw error;
  return data as CustomDomain;
}

/**
 * Update a custom domain
 */
export async function updateCustomDomain(id: string, updates: CustomDomainUpdate) {
  const { data, error } = await supabase
    .from('custom_domains')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as CustomDomain;
}

/**
 * Delete a custom domain
 */
export async function deleteCustomDomain(id: string) {
  const { error } = await supabase
    .from('custom_domains')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Verify a custom domain
 */
export async function verifyCustomDomain(id: string) {
  return updateCustomDomain(id, { is_verified: true });
}
