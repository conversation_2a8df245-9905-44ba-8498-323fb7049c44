'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  Users,
  MessageSquare,
  Shield,
  Zap,
  RefreshCw
} from 'lucide-react';

interface NotificationStats {
  notification_type: string;
  total_count: number;
  unique_appointments: number;
  last_sent: string;
}

interface RateLimitStats {
  totalSalons: number;
  activeLimits: number;
  totalAttempts: number;
  recentAttempts: number;
}

interface NotificationLogEntry {
  id: string;
  salon_id: string;
  appointment_id: string;
  notification_type: string;
  notification_hash: string;
  sent_at: string;
  created_at: string;
}

interface TelegramMonitoringDashboardProps {
  salonId: string;
  isAdmin?: boolean;
}

export function TelegramMonitoringDashboard({ salonId, isAdmin = false }: TelegramMonitoringDashboardProps) {
  const [stats, setStats] = useState<NotificationStats[]>([]);
  const [rateLimitStats, setRateLimitStats] = useState<RateLimitStats | null>(null);
  const [recentLogs, setRecentLogs] = useState<NotificationLogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Load monitoring data
  const loadMonitoringData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load notification statistics
      const statsResponse = await fetch(`/api/telegram/monitoring/stats?salon_id=${salonId}&days=7`);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.stats || []);
      }

      // Load recent notification logs
      const logsResponse = await fetch(`/api/telegram/monitoring/logs?salon_id=${salonId}&limit=20`);
      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        setRecentLogs(logsData.logs || []);
      }

      // Load rate limit stats (admin only)
      if (isAdmin) {
        const rateLimitResponse = await fetch('/api/telegram/monitoring/rate-limits');
        if (rateLimitResponse.ok) {
          const rateLimitData = await rateLimitResponse.json();
          setRateLimitStats(rateLimitData.stats);
        }
      }

      setLastRefresh(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Monitoring verisi yüklenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadMonitoringData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadMonitoringData, 30000);
    return () => clearInterval(interval);
  }, [salonId, isAdmin]);

  // Calculate summary metrics
  const totalNotifications = stats.reduce((sum, stat) => sum + stat.total_count, 0);
  const uniqueAppointments = stats.reduce((sum, stat) => sum + stat.unique_appointments, 0);
  const mostRecentNotification = stats.reduce((latest, stat) => {
    const statDate = new Date(stat.last_sent);
    const latestDate = latest ? new Date(latest) : new Date(0);
    return statDate > latestDate ? stat.last_sent : latest;
  }, '');

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Henüz yok';
    return new Date(dateString).toLocaleString('tr-TR');
  };

  const getNotificationTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'new_appointment': 'Yeni Randevu',
      'cancelled_appointment': 'İptal Edilen Randevu',
      'updated_appointment': 'Güncellenen Randevu',
      'test_notification': 'Test Bildirimi'
    };
    return labels[type] || type;
  };

  const getNotificationTypeBadge = (type: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      'new_appointment': 'default',
      'cancelled_appointment': 'destructive',
      'updated_appointment': 'secondary',
      'test_notification': 'outline'
    };
    return variants[type] || 'outline';
  };

  if (error) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Telegram Bildirim Monitoring</h2>
          <p className="text-muted-foreground">
            Son güncelleme: {formatDate(lastRefresh.toISOString())}
          </p>
        </div>
        <Button onClick={loadMonitoringData} disabled={isLoading} size="sm">
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Bildirim</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalNotifications}</div>
            <p className="text-xs text-muted-foreground">Son 7 gün</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Randevular</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{uniqueAppointments}</div>
            <p className="text-xs text-muted-foreground">Farklı randevu sayısı</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Son Bildirim</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-bold">
              {mostRecentNotification ? formatDate(mostRecentNotification) : 'Henüz yok'}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sistem Durumu</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-bold text-green-600">Aktif</div>
            <p className="text-xs text-muted-foreground">Tüm sistemler çalışıyor</p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue="statistics" className="space-y-4">
        <TabsList>
          <TabsTrigger value="statistics">İstatistikler</TabsTrigger>
          <TabsTrigger value="logs">Son Bildirimler</TabsTrigger>
          {isAdmin && <TabsTrigger value="system">Sistem Monitoring</TabsTrigger>}
        </TabsList>

        <TabsContent value="statistics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bildirim Türü Dağılımı</CardTitle>
              <CardDescription>Son 7 gündeki bildirim türleri ve sayıları</CardDescription>
            </CardHeader>
            <CardContent>
              {stats.length === 0 ? (
                <p className="text-muted-foreground">Henüz bildirim gönderilmemiş.</p>
              ) : (
                <div className="space-y-4">
                  {stats.map((stat) => (
                    <div key={stat.notification_type} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Badge variant={getNotificationTypeBadge(stat.notification_type)}>
                          {getNotificationTypeLabel(stat.notification_type)}
                        </Badge>
                        <div>
                          <p className="font-medium">{stat.total_count} bildirim</p>
                          <p className="text-sm text-muted-foreground">
                            {stat.unique_appointments} farklı randevu
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-muted-foreground">Son gönderim</p>
                        <p className="text-sm font-medium">{formatDate(stat.last_sent)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Son Bildirimler</CardTitle>
              <CardDescription>En son gönderilen 20 bildirim</CardDescription>
            </CardHeader>
            <CardContent>
              {recentLogs.length === 0 ? (
                <p className="text-muted-foreground">Henüz bildirim kaydı yok.</p>
              ) : (
                <div className="space-y-2">
                  {recentLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center space-x-3">
                        <Badge variant={getNotificationTypeBadge(log.notification_type)} size="sm">
                          {getNotificationTypeLabel(log.notification_type)}
                        </Badge>
                        <div>
                          <p className="text-sm font-medium">Randevu: {log.appointment_id}</p>
                          <p className="text-xs text-muted-foreground">
                            Hash: {log.notification_hash.substring(0, 8)}...
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">{formatDate(log.sent_at)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {isAdmin && (
          <TabsContent value="system" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Sistem Monitoring</CardTitle>
                <CardDescription>Rate limiting ve sistem performans metrikleri</CardDescription>
              </CardHeader>
              <CardContent>
                {rateLimitStats ? (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Shield className="h-4 w-4" />
                        <span className="font-medium">Rate Limiting</span>
                      </div>
                      <p className="text-sm">Aktif limitler: {rateLimitStats.activeLimits}</p>
                      <p className="text-sm">Toplam salon: {rateLimitStats.totalSalons}</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Zap className="h-4 w-4" />
                        <span className="font-medium">Aktivite</span>
                      </div>
                      <p className="text-sm">Son dakika: {rateLimitStats.recentAttempts}</p>
                      <p className="text-sm">Toplam: {rateLimitStats.totalAttempts}</p>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Sistem metrikleri yükleniyor...</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
