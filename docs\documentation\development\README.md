# SalonFlow Geliştirme Dokümantasyonu

Bu klasör, SalonFlow projesinin geliştirme dokümantasyonlarını içermektedir. Geliştirme doküman<PERSON>yonları, geliştiricilerin projeye katkıda bulunmalarına ve geliştirme süreçlerini anlamalarına yardımcı olmak için tasarlanmıştır.

## İçerik

Bu klasörde aşağıdaki türde dokümantasyonlar bulunmaktadır:

- **Geliştirme Süreçleri**: <PERSON><PERSON><PERSON><PERSON>rme, test ve dağıtım süreçleri
- **Kod Standartları**: Kod yazma ve düzenleme standartları
- **Test Prosedürleri**: Test yazma ve yürütme prosedürleri
- **Katkıda Bulunma Kılavuzu**: Projeye nasıl katkıda bulunulacağına dair talimatlar

## Geliştirme Alanları

Doküman<PERSON>yonlar, aşağı<PERSON>i geliştirme alanlarına göre kategorize edilebilir:

- **Frontend Geliştirme**: Next.js, React, Tailwind CSS, Shadcn UI
- **Backend Geliştirme**: Supabase, PostgreSQL, API'ler
- **Veritabanı Geliştirme**: Şema tasarımı, RLS politikaları, sorgular
- **DevOps**: CI/CD, dağıtım, izleme

## Dosya Adlandırma Kuralları

Tüm dokümantasyon dosyaları aşağıdaki formatta adlandırılmalıdır:

```
YYYY-MM-DD-HH-MM-SS-dosya-adi.md
```

Örnek: `2024-08-05-17-00-00-development-process.md`

## Dokümantasyon Listesi

Aşağıda bu klasördeki dokümantasyonların bir listesi bulunmaktadır:

1. [Geliştirme Süreci](./2024-08-05-17-00-00-development-process.md) - SalonFlow geliştirme süreci
2. [Kod Standartları](./2024-08-05-17-00-00-code-standards.md) - SalonFlow kod yazma standartları
3. [Test Prosedürleri](./2024-08-05-17-00-00-test-procedures.md) - SalonFlow test yazma ve yürütme prosedürleri
