# Appointment Drag Restrictions Implementation

## Overview
This update implements restrictions on dragging appointments in the calendar views based on their status. Only active appointments (status = 'booked') can be dragged, while completed, cancelled, or no-show appointments cannot be moved. Additionally, the date and time fields in the appointment edit form are disabled for non-active appointments.

## Changes Made

### 1. DraggableAppointment Component
- Modified the component to check for all non-active statuses (completed, cancelled, no-show) instead of just cancelled
- Updated the drag handle to only be visible for active appointments
- Updated the cursor and opacity styles to reflect the draggable state

### 2. Calendar View Components
- Updated all calendar view components (DailyCalendarView, WeeklyCalendarView, MonthlyCalendarView, CustomRangeCalendarView) to check for all non-active statuses
- Added appropriate error messages for each status type when a user attempts to drag a non-active appointment

### 3. Appointment Edit Form
- Added a state variable to track the appointment status
- Set the appointment status when loading an existing appointment
- Disabled date and time fields for non-active appointments
- Added informative messages explaining why fields are disabled
- Added a status indicator at the top of the form for non-active appointments
- Disabled barber and service selection fields for non-active appointments

## Testing
The changes have been implemented and should be tested with appointments in various statuses to ensure:
1. Only active appointments can be dragged in all calendar views
2. Appropriate error messages are shown when attempting to drag non-active appointments
3. Date and time fields are disabled in the edit form for non-active appointments
4. The status indicator is shown correctly in the edit form for non-active appointments

## Future Considerations
- Consider adding a visual indicator in the calendar views to make it more obvious which appointments are draggable
- Consider adding a confirmation dialog when changing appointment status to inform users that the appointment will no longer be editable
