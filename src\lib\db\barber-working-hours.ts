import { supabaseClient } from '../supabase-singleton';
import { BarberWorkingHours, BarberWorkingHoursInsert, BarberWorkingHoursUpdate } from './types';

const supabase = supabaseClient;

/**
 * Get working hours for a barber
 */
export async function getBarberWorkingHours(barberId: string) {
  const { data, error } = await supabase
    .from('barber_working_hours')
    .select('*')
    .eq('barber_id', barberId)
    .order('day_of_week');

  if (error) throw error;
  return data as BarberWorkingHours[];
}

/**
 * Get working hours for a specific day for a barber
 */
export async function getBarberWorkingHoursByDay(barberId: string, dayOfWeek: number) {
  const { data, error } = await supabase
    .from('barber_working_hours')
    .select('*')
    .eq('barber_id', barberId)
    .eq('day_of_week', dayOfWeek)
    .single();

  if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "No rows returned" error
  return data as BarberWorkingHours | null;
}

/**
 * Create working hours for a barber
 */
export async function createBarberWorkingHours(workingHours: BarberWorkingHoursInsert) {
  const { data, error } = await supabase
    .from('barber_working_hours')
    .insert(workingHours)
    .select()
    .single();

  if (error) throw error;
  return data as BarberWorkingHours;
}

/**
 * Update barber working hours
 */
export async function updateBarberWorkingHours({ id, ...workingHours }: BarberWorkingHoursUpdate) {
  const { data, error } = await supabase
    .from('barber_working_hours')
    .update(workingHours)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as BarberWorkingHours;
}

/**
 * Delete barber working hours
 */
export async function deleteBarberWorkingHours(id: string) {
  const { error } = await supabase
    .from('barber_working_hours')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Set up default working hours for a barber (copy from salon working hours)
 */
export async function setupDefaultBarberWorkingHours(barberId: string, salonId: string) {
  // First, get the salon's working hours
  const { data: salonWorkingHours, error: salonError } = await supabase
    .from('working_hours')
    .select('*')
    .eq('salon_id', salonId)
    .order('day_of_week');

  if (salonError) throw salonError;

  // Get existing barber working hours
  const { data: existingHours, error: existingError } = await supabase
    .from('barber_working_hours')
    .select('*')
    .eq('barber_id', barberId);

  if (existingError) throw existingError;

  // If there are existing hours, update them instead of inserting new ones
  if (existingHours && existingHours.length > 0) {
    // Create update promises for each day
    const updatePromises = salonWorkingHours.map(async (hours) => {
      const existingHour = existingHours.find(h => h.day_of_week === hours.day_of_week);

      if (existingHour) {
        // Update existing hour
        return updateBarberWorkingHours({
          id: existingHour.id,
          open_time: hours.open_time,
          close_time: hours.close_time,
          is_closed: hours.is_closed
        });
      } else {
        // Create new hour if it doesn't exist
        return createBarberWorkingHours({
          barber_id: barberId,
          day_of_week: hours.day_of_week,
          open_time: hours.open_time,
          close_time: hours.close_time,
          is_closed: hours.is_closed
        });
      }
    });

    // Wait for all updates to complete
    await Promise.all(updatePromises);

    // Get the updated hours
    const { data: updatedHours, error: updatedError } = await supabase
      .from('barber_working_hours')
      .select('*')
      .eq('barber_id', barberId)
      .order('day_of_week');

    if (updatedError) throw updatedError;
    return updatedHours as BarberWorkingHours[];
  } else {
    // If no existing hours, create new ones
    const barberWorkingHours = salonWorkingHours.map(hours => ({
      barber_id: barberId,
      day_of_week: hours.day_of_week,
      open_time: hours.open_time,
      close_time: hours.close_time,
      is_closed: hours.is_closed
    }));

    const { data, error } = await supabase
      .from('barber_working_hours')
      .insert(barberWorkingHours)
      .select();

    if (error) throw error;
    return data as BarberWorkingHours[];
  }
}

/**
 * Apply salon working hours to all barbers
 */
export async function applyWorkingHoursToAllBarbers(salonId: string) {
  // First, get all barbers for the salon
  const { data: barbers, error: barbersError } = await supabase
    .from('barbers')
    .select('id')
    .eq('salon_id', salonId);

  if (barbersError) throw barbersError;

  if (!barbers || barbers.length === 0) {
    return { message: "No barbers found for this salon" };
  }

  // Apply working hours to each barber
  const updatePromises = barbers.map(barber =>
    setupDefaultBarberWorkingHours(barber.id, salonId)
  );

  // Wait for all updates to complete
  await Promise.all(updatePromises);

  return {
    message: `Working hours applied to ${barbers.length} barbers`,
    barberCount: barbers.length
  };
}


