"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { toast } from "sonner"
import { Scissors, Clock, MapPin, Phone, Mail } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { publicAccess } from "@/lib/db"
import { ClientBookingModal } from "@/components/client/client-booking-modal"
import { Spotlight } from "@/components/ui/spotlight"
import { AnimatedTestimonials } from "@/components/ui/animated-testimonials"
import { CardSpotlight } from "@/components/ui/card-spotlight"
import { InfiniteMovingCards } from "@/components/ui/infinite-moving-cards"
import { getSalonSlugById } from "@/lib/utils/salon-resolver"
// TextGenerateEffect kaldırıldı
// ThemeToggle kaldırıldı

// Sample testimonials data - will be replaced with real data from CMS
const TESTIMONIALS = [
  {
    image: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80",
    name: "Ahmet Yılmaz",
    title: "Düzenli Müşteri",
    quote: "Bu berberde yıllardır tıraş oluyorum. Profesyonel hizmet ve samimi ortam için teşekkürler!"
  },
  {
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80",
    name: "Ayşe Kaya",
    title: "Yeni Müşteri",
    quote: "İlk ziyaretimde çok memnun kaldım. Saç kesimi tam istediğim gibi oldu, kesinlikle tekrar geleceğim."
  },
  {
    image: "https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80",
    name: "Mehmet Demir",
    title: "İş İnsanı",
    quote: "Yoğun iş tempomda bile kolayca randevu alabiliyorum. Online randevu sistemi hayatımı kolaylaştırdı."
  }
];

// Sample gallery images - will be replaced with real data from CMS
const GALLERY_IMAGES = [
  { image: "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80", alt: "Salon interior" },
  { image: "https://images.unsplash.com/photo-1599351431202-1e0f0137899a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80", alt: "Haircut" },
  { image: "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80", alt: "Salon interior" },
  { image: "https://images.unsplash.com/photo-1521590832167-7bcbfaa6381f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80", alt: "Barber tools" },
  { image: "https://images.unsplash.com/photo-1503951914875-452162b0f3f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1760&q=80", alt: "Salon interior" }
];

// Sample services - will be replaced with real data from CMS
const SERVICES = [
  {
    icon: <Scissors className="h-8 w-8 text-primary" />,
    title: "Saç Kesimi",
    description: "Profesyonel saç kesimi hizmeti, yüz şeklinize ve tarzınıza uygun kesimler."
  },
  {
    icon: <Scissors className="h-8 w-8 text-primary" />,
    title: "Sakal Tıraşı",
    description: "Geleneksel ustalıkla sakal şekillendirme ve bakım hizmeti."
  },
  {
    icon: <Scissors className="h-8 w-8 text-primary" />,
    title: "Saç Boyama",
    description: "Kaliteli ürünlerle profesyonel saç boyama hizmeti."
  },
  {
    icon: <Scissors className="h-8 w-8 text-primary" />,
    title: "Cilt Bakımı",
    description: "Yüz bakımı ve cilt temizliği ile kendinizi yenileyin."
  }
];

export default function SalonLandingPage() {
  const params = useParams()
  const router = useRouter()
  const salonId = params.salonId as string


  const [salon, setSalon] = useState<{
    id: string;
    name: string;
    address: string;
    phone?: string;
    email?: string;
    owner_id: string;
    slug?: string;
  } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [redirecting, setRedirecting] = useState(false)

  // Load salon details and redirect to slug-based URL
  useEffect(() => {
    async function loadSalonAndRedirect() {
      setIsLoading(true)
      try {
        const data = await publicAccess.getPublicSalonById(salonId)

        if (!data) throw new Error("Salon not found")
        setSalon(data)

        // If salon has a slug, redirect to the new URL structure
        if (data.slug) {
          setRedirecting(true)
          // Short delay to allow state to update before redirect
          setTimeout(() => {
            router.replace(`/${data.slug}`)
          }, 100)
        }
      } catch (error) {
        console.error("Error loading salon:", error)
        toast.error("Salon bilgileri yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    loadSalonAndRedirect()
  }, [salonId, router])

  if (isLoading || redirecting) {
    return (
      <div className="min-h-screen flex flex-col">
        <header className="border-b backdrop-blur-md bg-background/80">
          <div className="container mx-auto flex justify-between items-center py-4">
            <span className="text-2xl font-bold tracking-tight">
              SalonFlow
            </span>
          </div>
        </header>
        <main className="flex-1 container mx-auto py-12">
          <div className="flex justify-center items-center h-64">
            <div className="flex flex-col items-center gap-4">
              <div className="h-12 w-12 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
              <p className="text-lg font-medium">
                {redirecting ? "Yönlendiriliyor..." : "Yükleniyor..."}
              </p>
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (!salon) {
    return (
      <div className="min-h-screen flex flex-col">
        <header className="border-b backdrop-blur-md bg-background/80">
          <div className="container mx-auto flex justify-between items-center py-4">
            <span className="text-2xl font-bold tracking-tight">
              SalonFlow
            </span>
          </div>
        </header>
        <main className="flex-1 container mx-auto py-12">
          <div className="flex flex-col items-center justify-center h-64 space-y-6">
            <div className="flex flex-col items-center text-center">
              <div className="rounded-full bg-primary/10 p-4 mb-4">
                <MapPin className="h-8 w-8 text-primary" />
              </div>
              <h2 className="text-2xl font-bold mb-2">Salon Bulunamadı</h2>
              <p className="text-muted-foreground max-w-md mb-6">Aradığınız salon sistemimizde bulunamadı veya artık mevcut değil.</p>
            </div>
            <Button asChild size="lg">
              <Link href="/">Ana Sayfaya Dön</Link>
            </Button>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="border-b sticky top-0 z-40 backdrop-blur-md bg-background/80">
        <div className="container mx-auto flex justify-between items-center py-4">
          <span className="text-2xl font-bold tracking-tight">
            {salon.name}
          </span>
          <div className="flex items-center gap-4">
            <nav className="hidden md:flex gap-8">
              <a href="#services" className="relative font-medium text-sm tracking-wide uppercase transition-colors hover:text-primary group">
                Hizmetler
                <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a href="#about" className="relative font-medium text-sm tracking-wide uppercase transition-colors hover:text-primary group">
                Hakkımızda
                <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a href="#testimonials" className="relative font-medium text-sm tracking-wide uppercase transition-colors hover:text-primary group">
                Yorumlar
                <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </a>
              <a href="#contact" className="relative font-medium text-sm tracking-wide uppercase transition-colors hover:text-primary group">
                İletişim
                <span className="absolute -bottom-1 left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-[80vh] flex items-center overflow-hidden">
          <div className="container mx-auto px-4 relative">
            <Spotlight className="-top-40 left-0 md:-top-20 md:left-60" fill="white" />
            <div className="max-w-4xl mx-auto relative">
              <div className="text-center md:text-left">
                <h1 className="text-4xl md:text-7xl font-bold mb-6 tracking-tight leading-tight">
                  <span className="block">{salon.name}</span>
                  <span className="block text-primary mt-2">ile tarzını yansıt.</span>
                </h1>
                <p className="text-xl md:text-2xl mb-8 text-muted-foreground max-w-2xl mx-auto md:mx-0">
                  Profesyonel berber hizmetleri ile tarzınızı yenileyin.
                  Uzman ekibimizle kaliteli hizmet garantisi.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                  <ClientBookingModal
                    salonId={salonId}
                    buttonText="Hemen Randevu Al"
                    buttonSize="lg"
                    buttonClassName="text-lg px-8"
                  />
                  <Button size="lg" variant="outline" className="text-lg px-8" asChild>
                    <a href="#services">Hizmetlerimiz</a>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Gallery Section */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4">
            <InfiniteMovingCards items={GALLERY_IMAGES} speed="slow" />
          </div>
        </section>

        {/* Services Section */}
        <section id="services" className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Hizmetlerimiz</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Profesyonel ekibimizle sunduğumuz kaliteli hizmetlerimiz
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {SERVICES.map((service, index) => (
                <CardSpotlight key={index} className="h-full">
                  <div className="flex flex-col items-center text-center p-4">
                    <div className="mb-4">{service.icon}</div>
                    <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                    <p className="text-muted-foreground">{service.description}</p>
                  </div>
                </CardSpotlight>
              ))}
            </div>
            <div className="mt-12 text-center">
              <ClientBookingModal
                salonId={salonId}
                buttonText="Randevu Al"
              />
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold mb-6">Hakkımızda</h2>
                <p className="text-muted-foreground mb-4">
                  {salon.name} olarak, müşterilerimize en kaliteli hizmeti sunmak için buradayız.
                  Uzman ekibimiz ve modern ekipmanlarımızla, size özel çözümler sunuyoruz.
                </p>
                <p className="text-muted-foreground mb-6">
                  Amacımız sadece saç kesmek değil, aynı zamanda size keyifli bir deneyim yaşatmak.
                  Rahat atmosferimizde kendinizi evinizde hissedeceğinize eminiz.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button variant="outline" asChild>
                    <a href="#contact">Bize Ulaşın</a>
                  </Button>
                  <ClientBookingModal salonId={salonId} />
                </div>
              </div>
              <div className="rounded-xl overflow-hidden h-[400px]">
                <img
                  src="https://images.unsplash.com/photo-1585747860715-2ba37e788b70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
                  alt="Salon interior"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Müşteri Yorumları</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Müşterilerimizin deneyimleri hakkında ne söylediklerini okuyun
              </p>
            </div>
            <div className="max-w-3xl mx-auto">
              <AnimatedTestimonials testimonials={TESTIMONIALS} />
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-3xl font-bold mb-6">İletişim</h2>
                <p className="text-muted-foreground mb-8">
                  Sorularınız için bize ulaşın veya doğrudan randevu alın.
                </p>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-primary mt-0.5" />
                    <div>
                      <h3 className="font-semibold">Adres</h3>
                      <p className="text-muted-foreground">{salon.address}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-primary mt-0.5" />
                    <div>
                      <h3 className="font-semibold">Telefon</h3>
                      <p className="text-muted-foreground">{salon.phone || "+90 ************"}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-primary mt-0.5" />
                    <div>
                      <h3 className="font-semibold">E-posta</h3>
                      <p className="text-muted-foreground">{salon.email || "<EMAIL>"}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-primary mt-0.5" />
                    <div>
                      <h3 className="font-semibold">Çalışma Saatleri</h3>
                      <p className="text-muted-foreground">Pazartesi - Cumartesi: 09:00 - 19:00</p>
                      <p className="text-muted-foreground">Pazar: Kapalı</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col justify-center">
                <Card>
                  <CardHeader>
                    <CardTitle>Randevu Alın</CardTitle>
                    <CardDescription>
                      Hemen online randevu alarak sıra beklemeden hizmet alın.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground">
                      Online randevu sistemi ile istediğiniz gün ve saatte,
                      tercih ettiğiniz berberimizden hizmet alabilirsiniz.
                    </p>
                    <div className="flex justify-center">
                      <ClientBookingModal
                        salonId={salonId}
                        buttonText="Randevu Al"
                        buttonClassName="w-full"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">{salon.name}</h3>
              <p className="text-muted-foreground">
                Profesyonel berber hizmetleri ile tarzınızı yenileyin.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Hızlı Bağlantılar</h3>
              <ul className="space-y-3">
                <li>
                  <a href="#services" className="text-muted-foreground transition-colors duration-200 hover:text-primary flex items-center gap-2">
                    <span className="h-1 w-1 rounded-full bg-primary"></span>
                    <span>Hizmetler</span>
                  </a>
                </li>
                <li>
                  <a href="#about" className="text-muted-foreground transition-colors duration-200 hover:text-primary flex items-center gap-2">
                    <span className="h-1 w-1 rounded-full bg-primary"></span>
                    <span>Hakkımızda</span>
                  </a>
                </li>
                <li>
                  <a href="#testimonials" className="text-muted-foreground transition-colors duration-200 hover:text-primary flex items-center gap-2">
                    <span className="h-1 w-1 rounded-full bg-primary"></span>
                    <span>Yorumlar</span>
                  </a>
                </li>
                <li>
                  <a href="#contact" className="text-muted-foreground transition-colors duration-200 hover:text-primary flex items-center gap-2">
                    <span className="h-1 w-1 rounded-full bg-primary"></span>
                    <span>İletişim</span>
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">İletişim</h3>
              <address className="not-italic text-muted-foreground">
                <p>{salon.address}</p>
                <p>{salon.phone || "+90 ************"}</p>
                <p>{salon.email || "<EMAIL>"}</p>
              </address>
            </div>
          </div>
          <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>&copy; {new Date().getFullYear()} {salon.name}. Tüm hakları saklıdır.</p>
            <p className="mt-1">
              <span className="text-xs">Powered by </span>
              <Link href="/" className="text-primary hover:underline">
                SalonFlow
              </Link>
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
