<!DOCTYPE html>
<html lang="tr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sayfa Bulunamadı - SalonFlow</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #000;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }
    p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      max-width: 600px;
    }
    .buttons {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      justify-content: center;
    }
    .button {
      background-color: #3b82f6;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      font-weight: 500;
      cursor: pointer;
      text-decoration: none;
      font-size: 1rem;
    }
    .button.outline {
      background-color: transparent;
      border: 1px solid #3b82f6;
    }
  </style>
  <script>
    // This script attempts to handle potential salon slugs
    document.addEventListener('DOMContentLoaded', function() {
      const path = window.location.pathname;
      
      // If the path looks like a potential salon slug (e.g., /mustafabaytan)
      if (path && path.startsWith('/') && path.split('/').length === 2) {
        const potentialSlug = path.substring(1);
        console.log('Potential salon slug detected:', potentialSlug);
        
        // Add a message to the page
        const messageEl = document.createElement('p');
        messageEl.textContent = 'Yönlendiriliyor...';
        document.querySelector('.message-container').appendChild(messageEl);
        
        // Redirect to the home page after a delay
        setTimeout(function() {
          window.location.href = '/';
        }, 3000);
      }
    });
  </script>
</head>
<body>
  <h1>Sayfa Bulunamadı</h1>
  <p>Aradığınız sayfa mevcut değil veya taşınmış olabilir.</p>
  <div class="message-container"></div>
  <div class="buttons">
    <a href="javascript:history.back()" class="button">Geri Dön</a>
    <a href="/" class="button outline">Ana Sayfaya Git</a>
  </div>
</body>
</html>
