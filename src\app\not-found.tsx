"use client"

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export default function NotFound() {
  const router = useRouter()
  const pathname = usePathname()

  // Try to handle potential salon slugs that might be causing 404s
  useEffect(() => {
    // If the path looks like a potential salon slug (e.g., /mustaf<PERSON>ytan)
    if (pathname && pathname.startsWith('/') && pathname.split('/').length === 2) {
      const potentialSlug = pathname.substring(1)
      
      // Log for debugging
      console.log(`404 page handling potential slug: ${potentialSlug}`)
      
      // We could try to fetch from API to check if this is a valid slug
      // For now, we'll just redirect to the home page after a delay
      const timer = setTimeout(() => {
        router.push('/')
      }, 5000)
      
      return () => clearTimeout(timer)
    }
  }, [pathname, router])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
      <h1 className="text-4xl font-bold mb-4">Sayfa Bulunamadı</h1>
      <p className="text-xl mb-8">
        Aradığınız sayfa mevcut değil veya taşınmış olabilir.
      </p>
      <div className="flex gap-4">
        <Button onClick={() => router.back()}>
          Geri Dön
        </Button>
        <Link href="/">
          <Button variant="outline">
            Ana Sayfaya Git
          </Button>
        </Link>
      </div>
    </div>
  )
}
