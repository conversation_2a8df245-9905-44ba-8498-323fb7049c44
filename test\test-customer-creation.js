// Test script to check customer creation with service role
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Error: Supabase credentials not found in environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test customer creation
async function testCustomerCreation() {
  try {
    console.log('Testing customer creation...');
    
    // Get a salon ID for testing
    const { data: salons, error: salonsError } = await supabase
      .from('salons')
      .select('id')
      .limit(1);
    
    if (salonsError) {
      console.error('Error getting salon:', salonsError);
      return false;
    }
    
    if (!salons || salons.length === 0) {
      console.error('No salons found for testing');
      return false;
    }
    
    const salonId = salons[0].id;
    console.log('Using salon ID:', salonId);
    
    // Create a test customer
    const testCustomer = {
      salon_id: salonId,
      name: 'Test',
      surname: 'Customer',
      phone: '+905551234567',
      email: '<EMAIL>'
    };
    
    console.log('Creating test customer:', testCustomer);
    
    const { data: newCustomer, error: createError } = await supabase
      .from('customers')
      .insert(testCustomer)
      .select()
      .single();
    
    if (createError) {
      console.error('Error creating test customer:', createError);
      return false;
    }
    
    console.log('Test customer created successfully:', newCustomer);
    
    // Clean up - delete the test customer
    const { error: deleteError } = await supabase
      .from('customers')
      .delete()
      .eq('id', newCustomer.id);
    
    if (deleteError) {
      console.error('Error deleting test customer:', deleteError);
    } else {
      console.log('Test customer deleted successfully.');
    }
    
    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

// Run the test
testCustomerCreation()
  .then(success => {
    if (success) {
      console.log('Customer creation test passed!');
    } else {
      console.error('Customer creation test failed!');
    }
  })
  .catch(error => {
    console.error('Error running test:', error);
  });
