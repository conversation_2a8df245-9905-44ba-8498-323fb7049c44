"use client"

import { useState, useEffect } from "react"
import { format, addDays, startOfWeek } from "date-fns"
import { tr } from "date-fns/locale"
import { Clock } from "lucide-react"

import { cn } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useIsMobile } from "@/hooks/use-mobile"

interface WorkingHoursCalendarProps {
  workingHours: {
    day_of_week: number
    open_time: string
    close_time: string
    is_closed: boolean
  }[]
}

export function WorkingHoursCalendar({ workingHours }: WorkingHoursCalendarProps) {
  const [weekDays, setWeekDays] = useState<Date[]>([])
  const isMobile = useIsMobile()

  // Generate week days starting from Monday
  useEffect(() => {
    const today = new Date()
    const monday = startOfWeek(today, { weekStartsOn: 1 })
    const days = Array.from({ length: 7 }, (_, i) => addDays(monday, i))
    setWeekDays(days)
  }, [])

  // Get working hours for a specific day
  const getWorkingHoursForDay = (dayOfWeek: number) => {
    return workingHours.find(wh => wh.day_of_week === dayOfWeek)
  }

  // Format time for display
  const formatTime = (time: string) => {
    return time.substring(0, 5)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">Haftalık Çalışma Saatleri</CardTitle>
      </CardHeader>
      <CardContent>
        <div className={cn(
          "grid gap-2",
          isMobile ? "grid-cols-1" : "grid-cols-7"
        )}>
          {weekDays.map((day, index) => {
            const dayOfWeek = (index + 1) % 7 // Convert to 0-6 format (0 = Sunday)
            const workingHour = getWorkingHoursForDay(dayOfWeek)
            const isClosed = workingHour?.is_closed || false

            return (
              <div key={index} className={cn(
                "flex items-center",
                isMobile ? "flex-row space-x-3" : "flex-col"
              )}>
                <div className={cn(
                  "font-medium",
                  isMobile ? "w-16 text-left" : "text-sm mb-1 text-center w-full"
                )}>
                  {format(day, "EEE", { locale: tr })}
                  {isMobile ? null : (
                    <div className="text-xs text-muted-foreground mt-1">
                      {format(day, "d", { locale: tr })}
                    </div>
                  )}
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          "rounded-md border p-2 flex items-center justify-center",
                          isMobile
                            ? "flex-1 h-12"
                            : "w-full h-20 flex-col",
                          isClosed
                            ? "bg-red-100 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                            : "bg-green-100 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                        )}
                      >
                        {isClosed ? (
                          <span className="text-xs font-medium">Kapalı</span>
                        ) : (
                          <>
                            {!isMobile && <Clock className="h-3 w-3 mb-1" />}
                            <span className="text-xs">
                              {formatTime(workingHour?.open_time || "09:00")} - {formatTime(workingHour?.close_time || "18:00")}
                            </span>
                          </>
                        )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      {isClosed
                        ? "Bu gün kapalı"
                        : `Çalışma Saatleri: ${formatTime(workingHour?.open_time || "09:00")} - ${formatTime(workingHour?.close_time || "18:00")}`
                      }
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
