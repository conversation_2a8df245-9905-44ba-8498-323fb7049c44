"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { MapPin, Phone, Mail, Save, RotateCcw } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

import { useContent } from "@/contexts/ContentContext"

interface ContactContentEditorProps {
  onContentChange?: () => void
}

export function ContactContentEditor({ onContentChange }: ContactContentEditorProps) {
  const { getContentValue, updateContentValue, saveContent, resetChanges, hasChanges, isLoading } = useContent()
  
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    ctaTitle: '',
    ctaDescription: ''
  })

  const [isSaving, setIsSaving] = useState(false)

  // Load initial data
  useEffect(() => {
    setFormData({
      title: getContentValue('contact', 'title', 'Bizimle İletişime Geçin'),
      description: getContentValue('contact', 'description', 'Sorularınız için bize ulaşın. Size en kısa sürede dönüş yapacağız.'),
      ctaTitle: getContentValue('contact', 'cta_title', 'Hemen Randevu Alın'),
      ctaDescription: getContentValue('contact', 'cta_description', 'Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz.')
    })
  }, [getContentValue])

  // Handle input changes
  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }))
    
    // Map form keys to content keys
    const contentKeyMap: Record<string, string> = {
      title: 'title',
      description: 'description',
      ctaTitle: 'cta_title',
      ctaDescription: 'cta_description'
    }

    const contentKey = contentKeyMap[key]
    if (contentKey) {
      updateContentValue('contact', contentKey, value)
      onContentChange?.()
    }
  }

  // Handle save
  const handleSave = async () => {
    if (!hasChanges) return

    setIsSaving(true)
    try {
      await saveContent()
    } catch (error) {
      console.error('Save error:', error)
    } finally {
      setIsSaving(false)
    }
  }

  // Handle reset
  const handleReset = () => {
    resetChanges()
    // Reload initial data
    setFormData({
      title: getContentValue('contact', 'title', 'Bizimle İletişime Geçin'),
      description: getContentValue('contact', 'description', 'Sorularınız için bize ulaşın. Size en kısa sürede dönüş yapacağız.'),
      ctaTitle: getContentValue('contact', 'cta_title', 'Hemen Randevu Alın'),
      ctaDescription: getContentValue('contact', 'cta_description', 'Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz.')
    })
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-semibold">İletişim Bölümü</h3>
          <p className="text-sm text-muted-foreground">
            İletişim bölümünün başlık, açıklama ve CTA metinlerini düzenleyin
          </p>
        </div>
        
        {hasChanges && (
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">Kaydedilmemiş değişiklikler</Badge>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="mr-2 h-4 w-4" />
              Sıfırla
            </Button>
            <Button size="sm" onClick={handleSave} disabled={isSaving}>
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Kaydet
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      <Separator />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Bölüm Başlığı ve Açıklaması</CardTitle>
          <CardDescription>
            İletişim bölümünün ana başlık ve açıklama metinleri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Bölüm Başlığı</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Bizimle İletişime Geçin"
              />
              <p className="text-xs text-muted-foreground">
                İletişim bölümünün ana başlığı
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Bölüm Açıklaması</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Sorularınız için bize ulaşın. Size en kısa sürede dönüş yapacağız."
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                İletişim bölümünün açıklama metni
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* CTA Content */}
      <Card>
        <CardHeader>
          <CardTitle>Randevu CTA Kartı</CardTitle>
          <CardDescription>
            İletişim bölümündeki randevu alma çağrısı kartının içeriği
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label htmlFor="ctaTitle">CTA Başlığı</Label>
              <Input
                id="ctaTitle"
                value={formData.ctaTitle}
                onChange={(e) => handleInputChange('ctaTitle', e.target.value)}
                placeholder="Hemen Randevu Alın"
              />
              <p className="text-xs text-muted-foreground">
                Randevu alma kartının başlığı
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="ctaDescription">CTA Açıklaması</Label>
              <Textarea
                id="ctaDescription"
                value={formData.ctaDescription}
                onChange={(e) => handleInputChange('ctaDescription', e.target.value)}
                placeholder="Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz."
                rows={4}
              />
              <p className="text-xs text-muted-foreground">
                Randevu alma kartının açıklama metni
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preview Section */}
      <Card>
        <CardHeader>
          <CardTitle>Önizleme</CardTitle>
          <CardDescription>
            İletişim bölümünün müşteri sayfasında nasıl görüneceğinin önizlemesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/20 rounded-lg p-6 space-y-6">
            {/* Section Header Preview */}
            <div className="text-center space-y-4">
              <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
                <MapPin className="w-4 h-4" />
                <span>İletişim</span>
              </div>

              <h2 className="text-3xl md:text-4xl font-bold text-foreground">
                {formData.title || 'Bizimle İletişime Geçin'}
              </h2>

              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                {formData.description || 'Sorularınız için bize ulaşın. Size en kısa sürede dönüş yapacağız.'}
              </p>
            </div>

            <Separator />

            {/* CTA Card Preview */}
            <div className="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-xl p-6 border border-primary/20 max-w-md mx-auto">
              <div className="text-center space-y-4">
                <h3 className="text-xl font-bold text-foreground">
                  {formData.ctaTitle || 'Hemen Randevu Alın'}
                </h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                  {formData.ctaDescription || 'Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz.'}
                </p>
                <Button className="w-full bg-gradient-to-r from-primary to-primary/80 text-white">
                  Randevu Al
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tips */}
      <Card>
        <CardHeader>
          <CardTitle>💡 İpuçları</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <span>
                <strong>Başlık:</strong> Kısa ve net olsun. "İletişim", "Bize Ulaşın" gibi basit başlıklar etkilidir.
              </span>
            </li>
            <li className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <span>
                <strong>Açıklama:</strong> Müşterilerin size nasıl ulaşabileceğini ve ne kadar sürede dönüş yapacağınızı belirtin.
              </span>
            </li>
            <li className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <span>
                <strong>CTA Başlığı:</strong> Eylem odaklı olsun. "Hemen Randevu Al", "Şimdi Rezervasyon Yap" gibi.
              </span>
            </li>
            <li className="flex items-start space-x-2">
              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
              <span>
                <strong>CTA Açıklaması:</strong> Online randevu sisteminin avantajlarını vurgulayın.
              </span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  )
}
