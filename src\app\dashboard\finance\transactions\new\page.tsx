"use client"

import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useUser } from "@/contexts/UserContext"
import { TransactionForm } from "@/components/transaction-form"

export default function NewTransactionPage() {
  const { salon } = useUser()
  const salonId = salon?.id

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/finance/transactions">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Ye<PERSON></h1>
        </div>
      </header>

      <Card>
        <CardContent className="pt-6">
          <TransactionForm />
        </CardContent>
      </Card>
    </div>
  )
}
