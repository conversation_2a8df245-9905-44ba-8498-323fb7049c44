import { supabaseClient } from '../supabase-singleton';
import { <PERSON>, <PERSON>, BarberUpdate } from './types';
import { generateToken } from '../utils';

const supabase = supabaseClient;

/**
 * Get all barbers for a salon
 */
export async function getBarbers(salonId: string) {
  const { data, error } = await supabase
    .from('barbers')
    .select('*')
    .eq('salon_id', salonId)
    .order('name');

  if (error) throw error;
  return data as Barber[];
}

/**
 * Get a barber by ID
 */
export async function getBarberById(id: string) {
  const { data, error } = await supabase
    .from('barbers')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as Barber;
}

/**
 * Get a barber by user ID
 */
export async function getBarberByUserId(userId: string) {
  const { data, error } = await supabase
    .from('barbers')
    .select('*')
    .eq('user_id', userId)
    .maybeSingle();

  if (error) throw error;
  return data as Barber | null;
}

/**
 * Get a barber by invitation token
 */
export async function getBarberByInvitationToken(token: string) {
  const { data, error } = await supabase
    .from('barbers')
    .select('*')
    .eq('invitation_token', token)
    .is('invitation_accepted_at', null) // Only return if not already accepted
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data as Barber | null;
}

/**
 * Create a new barber
 */
export async function createBarber(barber: BarberInsert) {
  const { data, error } = await supabase
    .from('barbers')
    .insert(barber)
    .select()
    .single();

  if (error) throw error;
  return data as Barber;
}

/**
 * Update a barber
 */
export async function updateBarber({ id, ...barber }: BarberUpdate) {
  const { data, error } = await supabase
    .from('barbers')
    .update(barber)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Barber;
}

/**
 * Delete a barber
 */
export async function deleteBarber(id: string) {
  const { error } = await supabase
    .from('barbers')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Generate an invitation token for a barber and update the record
 */
export async function generateInvitationToken(barberId: string) {
  const token = generateToken();
  const now = new Date().toISOString();

  const { data, error } = await supabase
    .from('barbers')
    .update({
      invitation_token: token,
      invitation_sent_at: now,
      invitation_accepted_at: null
    })
    .eq('id', barberId)
    .select()
    .single();

  if (error) throw error;
  return data as Barber;
}

/**
 * Accept an invitation and link a barber to a user account
 */
export async function acceptInvitation(token: string, userId: string) {
  const now = new Date().toISOString();

  const { data, error } = await supabase
    .from('barbers')
    .update({
      user_id: userId,
      invitation_accepted_at: now
    })
    .eq('invitation_token', token)
    .is('invitation_accepted_at', null) // Only update if not already accepted
    .select()
    .single();

  if (error) throw error;
  return data as Barber;
}

/**
 * Get barbers who can perform a specific service
 */
export async function getBarbersForService(salonId: string, serviceId: string) {
  const { data, error } = await supabase
    .from('barbers')
    .select(`
      *,
      barber_services!inner(service_id)
    `)
    .eq('salon_id', salonId)
    .eq('barber_services.service_id', serviceId)
    .order('name');

  if (error) throw error;
  return data as Barber[];
}
