// Test script to check customer access
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Error: Supabase credentials not found in environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test customer access
async function testCustomerAccess() {
  try {
    console.log('Testing customer access...');
    
    // Try to get all customers
    const { data: customers, error: customersError } = await supabase
      .from('customers')
      .select('*')
      .limit(5);
    
    if (customersError) {
      console.error('Error querying customers table:', customersError);
      console.log('This is expected for unauthenticated users due to RLS policies.');
    } else {
      console.log('Customers found:', customers.length);
      console.log('Customers:', customers);
    }
    
    // Try to create a test customer
    const testCustomer = {
      salon_id: '6d6c0462-21fd-410e-97fd-86b14ca0d9cb', // Use a valid salon ID from your database
      name: 'Test',
      surname: 'Customer',
      phone: '+905551234567',
      email: '<EMAIL>'
    };
    
    const { data: newCustomer, error: createError } = await supabase
      .from('customers')
      .insert(testCustomer)
      .select()
      .single();
    
    if (createError) {
      console.error('Error creating test customer:', createError);
      console.log('This might be due to RLS policies or missing salon_id.');
    } else {
      console.log('Test customer created successfully:', newCustomer);
      
      // Clean up - delete the test customer
      const { error: deleteError } = await supabase
        .from('customers')
        .delete()
        .eq('id', newCustomer.id);
      
      if (deleteError) {
        console.error('Error deleting test customer:', deleteError);
      } else {
        console.log('Test customer deleted successfully.');
      }
    }
    
    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

// Run the test
testCustomerAccess()
  .then(success => {
    if (success) {
      console.log('Customer access test completed!');
    } else {
      console.error('Customer access test failed!');
    }
  })
  .catch(error => {
    console.error('Error running test:', error);
  });
