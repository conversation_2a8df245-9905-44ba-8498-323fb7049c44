-- Migration: Move customer data to appointments table

-- Step 1: Add new columns to appointments table
ALTER TABLE appointments ADD COLUMN fullname TEXT;
ALTER TABLE appointments ADD COLUMN phonenumber TEXT;
ALTER TABLE appointments ADD COLUMN email TEXT;

-- Step 2: Add indexes to the new columns
CREATE INDEX idx_appointments_fullname ON appointments (fullname);
CREATE INDEX idx_appointments_phonenumber ON appointments (phonenumber);

-- Step 3: Make customer_id column nullable
ALTER TABLE appointments ALTER COLUMN customer_id DROP NOT NULL;
ALTER TABLE appointments DROP CONSTRAINT appointments_customer_id_fkey;
ALTER TABLE appointments ADD CONSTRAINT appointments_customer_id_fkey 
  FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL;

-- Step 4: Copy data from customers table to appointments table
UPDATE appointments a
SET 
  fullname = c.name || ' ' || c.surname,
  phonenumber = c.phone,
  email = c.email
FROM customers c
WHERE a.customer_id = c.id;
