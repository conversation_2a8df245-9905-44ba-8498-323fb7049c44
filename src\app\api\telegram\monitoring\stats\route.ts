// Telegram Notification Statistics API
// Provides monitoring data for notification analytics

import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { authenticateUser, checkSalonAccess } from '@/lib/middleware/auth';

// GET /api/telegram/monitoring/stats
export async function GET(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. EXTRACT QUERY PARAMETERS
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');
    const days = parseInt(searchParams.get('days') || '7');

    if (!salonId) {
      return NextResponse.json(
        { success: false, error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Validate days parameter
    if (days < 1 || days > 365) {
      return NextResponse.json(
        { success: false, error: 'Days must be between 1 and 365' },
        { status: 400 }
      );
    }

    // 3. SALON ACCESS CONTROL
    const accessResult = await checkSalonAccess(authResult.user, salonId);
    if (!accessResult.success) {
      return NextResponse.json(
        { success: false, error: accessResult.error || 'Failed to verify salon access' },
        { status: accessResult.statusCode || 500 }
      );
    }

    if (!accessResult.hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Access denied to salon' },
        { status: 403 }
      );
    }

    // 4. FETCH NOTIFICATION STATISTICS
    const supabase = createRouteHandlerClient({ cookies });

    const { data: stats, error: statsError } = await supabase.rpc('get_notification_stats', {
      p_salon_id: salonId,
      p_days: days
    });

    if (statsError) {
      console.error('Error fetching notification stats:', statsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch notification statistics' },
        { status: 500 }
      );
    }

    // 5. FETCH ADDITIONAL METRICS
    // Get total notification count for the salon
    const { data: totalCount, error: countError } = await supabase
      .from('telegram_notification_log')
      .select('id', { count: 'exact' })
      .eq('salon_id', salonId)
      .gte('sent_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString());

    if (countError) {
      console.error('Error fetching total count:', countError);
    }

    // Get recent activity (last 24 hours)
    const { data: recentActivity, error: recentError } = await supabase
      .from('telegram_notification_log')
      .select('notification_type', { count: 'exact' })
      .eq('salon_id', salonId)
      .gte('sent_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    if (recentError) {
      console.error('Error fetching recent activity:', recentError);
    }

    // 6. CALCULATE SUMMARY METRICS
    const summary = {
      totalNotifications: totalCount?.length || 0,
      recentActivity: recentActivity?.length || 0,
      timeRange: days,
      generatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      stats: stats || [],
      summary,
      message: 'Notification statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Error in GET /api/telegram/monitoring/stats:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/telegram/monitoring/stats (for custom date ranges)
export async function POST(request: NextRequest) {
  try {
    // 1. AUTHENTICATION
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: authResult.error || 'Authentication failed' },
        { status: authResult.statusCode || 401 }
      );
    }

    // 2. PARSE REQUEST BODY
    const body = await request.json();
    const { salon_id, start_date, end_date, group_by } = body;

    if (!salon_id) {
      return NextResponse.json(
        { success: false, error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // 3. SALON ACCESS CONTROL
    const accessResult = await checkSalonAccess(authResult.user, salon_id);
    if (!accessResult.success || !accessResult.hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Access denied to salon' },
        { status: 403 }
      );
    }

    // 4. VALIDATE DATE RANGE
    const startDate = start_date ? new Date(start_date) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const endDate = end_date ? new Date(end_date) : new Date();

    if (startDate >= endDate) {
      return NextResponse.json(
        { success: false, error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // 5. FETCH CUSTOM STATISTICS
    const supabase = createRouteHandlerClient({ cookies });

    let query = supabase
      .from('telegram_notification_log')
      .select('notification_type, sent_at, appointment_id')
      .eq('salon_id', salon_id)
      .gte('sent_at', startDate.toISOString())
      .lte('sent_at', endDate.toISOString())
      .order('sent_at', { ascending: false });

    const { data: logs, error: logsError } = await query;

    if (logsError) {
      console.error('Error fetching custom stats:', logsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch custom statistics' },
        { status: 500 }
      );
    }

    // 6. GROUP AND AGGREGATE DATA
    const groupedStats: Record<string, any> = {};

    if (group_by === 'day') {
      // Group by day
      logs?.forEach(log => {
        const day = new Date(log.sent_at).toISOString().split('T')[0];
        if (!groupedStats[day]) {
          groupedStats[day] = { date: day, count: 0, types: {} };
        }
        groupedStats[day].count++;
        groupedStats[day].types[log.notification_type] = (groupedStats[day].types[log.notification_type] || 0) + 1;
      });
    } else if (group_by === 'type') {
      // Group by notification type
      logs?.forEach(log => {
        if (!groupedStats[log.notification_type]) {
          groupedStats[log.notification_type] = { 
            type: log.notification_type, 
            count: 0, 
            unique_appointments: new Set() 
          };
        }
        groupedStats[log.notification_type].count++;
        groupedStats[log.notification_type].unique_appointments.add(log.appointment_id);
      });

      // Convert Sets to counts
      Object.values(groupedStats).forEach((stat: any) => {
        stat.unique_appointments = stat.unique_appointments.size;
      });
    }

    return NextResponse.json({
      success: true,
      stats: Object.values(groupedStats),
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      },
      totalRecords: logs?.length || 0,
      message: 'Custom statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Error in POST /api/telegram/monitoring/stats:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
