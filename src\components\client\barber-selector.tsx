"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>, ArrowRight, ArrowLeft } from "lucide-react";
import { <PERSON> } from "@/lib/db/types";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface BarberSelectorProps {
  barbers: Barber[];
  selectedBarberId: string | undefined;
  onSelect: (barberId: string) => void;
  onBack: () => void;
}

export function BarberSelector({
  barbers,
  selectedBarberId,
  onSelect,
  onBack,
}: BarberSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");

  // Filter barbers based on search query
  const filteredBarbers = barbers.filter((barber) =>
    barber.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <div className="text-center space-y-3">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
        >
          <h2 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Berber Seçin
          </h2>
        </motion.div>
        <p className="text-muted-foreground text-lg">
          Size hizmet vermesini istediğiniz berberi seçin
        </p>
      </div>

      <motion.div
        className="relative max-w-md mx-auto"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Input
          placeholder="Berber ara..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-12 h-12 text-base rounded-xl border-2 focus:border-primary/50 transition-all"
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-h-[55vh] overflow-y-auto pr-3 pl-2 pt-1"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        {filteredBarbers.length > 0 ? (
          filteredBarbers.map((barber, index) => (
            <motion.div
              key={barber.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div
                onClick={() => onSelect(barber.id)}
                className={cn(
                  "border rounded-xl p-4 cursor-pointer transition-all",
                  selectedBarberId === barber.id
                    ? "border-primary bg-primary/5 shadow-sm"
                    : "hover:border-primary/50 hover:bg-muted/50"
                )}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={cn(
                      "w-12 h-12 rounded-full flex items-center justify-center",
                      selectedBarberId === barber.id
                        ? "bg-primary/10 text-primary"
                        : "bg-muted text-muted-foreground"
                    )}
                  >
                    {barber.profile_image_url ? (
                      <img
                        src={barber.profile_image_url}
                        alt={barber.name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <User className="h-6 w-6" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium">{barber.name}</h3>
                    <div className="flex items-center mt-1 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Star className="h-3.5 w-3.5 mr-1 fill-yellow-500 text-yellow-500" />
                        <Star className="h-3.5 w-3.5 mr-1 fill-yellow-500 text-yellow-500" />
                        <Star className="h-3.5 w-3.5 mr-1 fill-yellow-500 text-yellow-500" />
                        <Star className="h-3.5 w-3.5 mr-1 fill-yellow-500 text-yellow-500" />
                        <Star className="h-3.5 w-3.5 mr-1 fill-yellow-500 text-yellow-500" />
                      </div>
                    </div>
                  </div>
                  {selectedBarberId === barber.id && (
                    <ArrowRight className="ml-auto h-4 w-4 text-primary" />
                  )}
                </div>
              </div>
            </motion.div>
          ))
        ) : (
          <motion.div
            className="text-center py-12 col-span-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.3, type: "spring" }}
            >
              <User className="h-16 w-16 mx-auto mb-4 text-muted-foreground/30" />
            </motion.div>
            <p className="text-muted-foreground text-lg">Aradığınız berber bulunamadı.</p>
            <p className="text-muted-foreground/70 text-sm mt-2">Farklı bir arama terimi deneyin.</p>
          </motion.div>
        )}
      </motion.div>

      <motion.div
        className="pt-6 flex flex-col sm:flex-row gap-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full sm:w-1/2 h-12 text-base font-semibold border-2 hover:border-primary/50 transition-all duration-300"
          size="lg"
        >
          <ArrowLeft className="mr-2 h-5 w-5" />
          Geri
        </Button>

        {selectedBarberId && (
          <motion.div
            className="w-full sm:w-1/2"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Button
              onClick={() => onSelect(selectedBarberId)}
              className="w-full h-12 text-base font-semibold bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all duration-300"
              size="lg"
            >
              Devam Et
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  );
}
