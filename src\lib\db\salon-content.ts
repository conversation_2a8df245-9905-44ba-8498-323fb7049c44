// Salon Content Database Operations
// Manages customizable content for salon landing pages

import { supabaseClient } from '../supabase-singleton'
import { SalonContent, SalonContentInsert, SalonContentUpdate } from './types'

const supabase = supabaseClient

// =====================================================
// SALON CONTENT OPERATIONS
// =====================================================

/**
 * Get all content for a specific salon
 */
export async function getSalonContent(salonId: string): Promise<SalonContent[]> {
  const { data, error } = await supabase
    .from('salon_content')
    .select('*')
    .eq('salon_id', salonId)
    .order('section', { ascending: true })
    .order('content_key', { ascending: true })

  if (error) {
    console.error('Error fetching salon content:', error)
    throw error
  }

  return data || []
}

/**
 * Get content for a specific section of a salon
 */
export async function getSalonContentBySection(
  salonId: string,
  section: 'hero' | 'about' | 'services' | 'contact'
): Promise<SalonContent[]> {
  const { data, error } = await supabase
    .from('salon_content')
    .select('*')
    .eq('salon_id', salonId)
    .eq('section', section)
    .order('content_key', { ascending: true })

  if (error) {
    console.error('Error fetching salon content by section:', error)
    throw error
  }

  return data || []
}

/**
 * Get a specific content item by key
 */
export async function getSalonContentByKey(
  salonId: string,
  section: string,
  contentKey: string
): Promise<SalonContent | null> {
  const { data, error } = await supabase
    .from('salon_content')
    .select('*')
    .eq('salon_id', salonId)
    .eq('section', section)
    .eq('content_key', contentKey)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      // No rows returned
      return null
    }
    console.error('Error fetching salon content by key:', error)
    throw error
  }

  return data
}

/**
 * Create or update salon content (upsert)
 */
export async function upsertSalonContent(
  salonId: string,
  section: string,
  contentKey: string,
  contentValue: string,
  contentType: 'text' | 'number' | 'boolean' | 'json' = 'text'
): Promise<SalonContent> {
  const { data, error } = await supabase
    .from('salon_content')
    .upsert({
      salon_id: salonId,
      section,
      content_key: contentKey,
      content_value: contentValue,
      content_type: contentType
    }, {
      onConflict: 'salon_id,section,content_key'
    })
    .select()
    .single()

  if (error) {
    console.error('Error upserting salon content:', error)
    throw error
  }

  return data
}

/**
 * Bulk upsert multiple content items
 */
export async function bulkUpsertSalonContent(
  salonId: string,
  contentItems: Array<{
    section: string
    content_key: string
    content_value: string
    content_type?: 'text' | 'number' | 'boolean' | 'json'
  }>
): Promise<SalonContent[]> {
  const itemsToUpsert = contentItems.map(item => ({
    salon_id: salonId,
    section: item.section,
    content_key: item.content_key,
    content_value: item.content_value,
    content_type: item.content_type || 'text'
  }))

  const { data, error } = await supabase
    .from('salon_content')
    .upsert(itemsToUpsert, {
      onConflict: 'salon_id,section,content_key'
    })
    .select()

  if (error) {
    console.error('Error bulk upserting salon content:', error)
    throw error
  }

  return data || []
}

/**
 * Create new salon content
 */
export async function createSalonContent(content: SalonContentInsert): Promise<SalonContent> {
  const { data, error } = await supabase
    .from('salon_content')
    .insert(content)
    .select()
    .single()

  if (error) {
    console.error('Error creating salon content:', error)
    throw error
  }

  return data
}

/**
 * Update existing salon content
 */
export async function updateSalonContent(
  id: string,
  updates: Partial<SalonContentUpdate>
): Promise<SalonContent> {
  const { data, error } = await supabase
    .from('salon_content')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating salon content:', error)
    throw error
  }

  return data
}

/**
 * Delete salon content
 */
export async function deleteSalonContent(id: string): Promise<void> {
  const { error } = await supabase
    .from('salon_content')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting salon content:', error)
    throw error
  }
}

/**
 * Delete all content for a specific section
 */
export async function deleteSalonContentBySection(
  salonId: string,
  section: string
): Promise<void> {
  const { error } = await supabase
    .from('salon_content')
    .delete()
    .eq('salon_id', salonId)
    .eq('section', section)

  if (error) {
    console.error('Error deleting salon content by section:', error)
    throw error
  }
}

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Convert content array to key-value object for easier access
 */
export function contentArrayToObject(content: SalonContent[]): Record<string, string> {
  return content.reduce((acc, item) => {
    acc[item.content_key] = item.content_value || ''
    return acc
  }, {} as Record<string, string>)
}

/**
 * Get content value with fallback
 */
export function getContentValue(
  content: SalonContent[],
  key: string,
  fallback: string = ''
): string {
  const item = content.find(c => c.content_key === key)
  return item?.content_value || fallback
}

/**
 * Parse JSON content safely
 */
export function parseJsonContent<T>(
  content: SalonContent[],
  key: string,
  fallback: T
): T {
  const item = content.find(c => c.content_key === key && c.content_type === 'json')
  if (!item?.content_value) return fallback

  try {
    return JSON.parse(item.content_value) as T
  } catch (error) {
    console.error('Error parsing JSON content:', error)
    return fallback
  }
}

/**
 * Initialize default content for a new salon
 */
export async function initializeDefaultSalonContent(salonId: string): Promise<void> {
  // Call the database function to insert default content
  const { error } = await supabase.rpc('insert_default_salon_content', {
    p_salon_id: salonId
  })

  if (error) {
    console.error('Error initializing default salon content:', error)
    throw error
  }
}
