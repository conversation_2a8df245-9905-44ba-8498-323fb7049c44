# Referans Sistemi Kullanıcı Yolculuğu

**Tarih:** 1 Ağustos 2024
**Saat:** 15:00

B<PERSON> belge, SalonFlow referans sistemi için tam bir kullanıcı yolculuğunu açıklamaktadır. Referans veren ve referans alan kullanıcılar için adım adım süreçleri içerir.

## 1. Referans Veren Kullanıcı Yolculuğu

### 1.1. Referans Kodu Oluşturma

1. Kullanıcı SalonFlow'a giriş yapar
2. Dashboard'da sol menüden "Referans Programı" seçeneğine tıklar
3. <PERSON>ferans kodu yoksa "Referans Kodu Oluştur" butonuna tıklar
4. Sistem otomatik olarak benzersiz bir referans kodu oluşturur
5. Kullanıcı referans kodunu veya referans URL'sini kopyalayabilir

### 1.2. Referans Kodunu Paylaşma

1. Kullanıcı referans kodunu veya URL'sini kopyalar
2. <PERSON><PERSON> e-post<PERSON>, mesaj veya sosyal medya aracılığıyla potansiyel kullanıcılarla paylaşır
3. Paylaşılan URL, alıcıyı doğrudan kayıt sayfasına yönlendirir ve referans kodu otomatik olarak doldurulur

### 1.3. Referans Faydalarını Görüntüleme

1. Kullanıcı "Referans Programı" sayfasında referans kodunun kullanım istatistiklerini görebilir
2. Referans kodunu kullanan kullanıcıların listesini ve durumlarını görüntüleyebilir
3. Kazanılan faydaları (ücretsiz abonelik süreleri) takip edebilir

## 2. Referans Alan Kullanıcı Yolculuğu

### 2.1. Referans Kodu ile Kayıt

1. Kullanıcı referans URL'sine tıklar veya SalonFlow kayıt sayfasını ziyaret eder
2. Kayıt formunda e-posta, şifre ve diğer bilgileri doldurur
3. Referans URL'sinden geldiyse, referans kodu alanı otomatik olarak doldurulur
4. Referans URL'sinden gelmediyse, referans kodunu manuel olarak girebilir
5. "Kayıt Ol" butonuna tıklar
6. Sistem referans kodunu sessionStorage'a kaydeder
7. Kullanıcıya e-posta doğrulaması için bir e-posta gönderilir

### 2.2. E-posta Doğrulama ve Giriş

1. Kullanıcı e-postasındaki doğrulama bağlantısına tıklar
2. Sistem kullanıcı hesabını doğrular ve oturum açar
3. Kullanıcı SalonFlow dashboard'a yönlendirilir

### 2.3. Salon Oluşturma ve Referans Faydalarının Uygulanması

1. Yeni kullanıcı dashboard'da salon oluşturma sayfasına yönlendirilir
2. Salon bilgilerini (ad, adres, telefon, e-posta vb.) doldurur
3. "Değişiklikleri Kaydet" butonuna tıklar
4. Sistem yeni bir salon oluşturur
5. Sistem sessionStorage'dan referans kodunu alır
6. Referans kodu bulunursa:
   - Referans kodunu doğrular
   - Referans veren salon ID'sini alır
   - Referans kodu kullanım sayısını artırır
   - Referans faydası kaydı oluşturur
   - Referans faydasını "applied" durumuna günceller
   - Referans kodunu sessionStorage'dan temizler
7. Sistem yeni salon için uzatılmış deneme süreli (30 gün) bir abonelik oluşturur
8. Kullanıcı normal dashboard'a yönlendirilir ve tüm özelliklere erişebilir

## 3. Referans Faydaları

### 3.1. Referans Veren Kullanıcı İçin Faydalar

- Her başarılı referans için 1 ay ücretsiz abonelik
- Referans sayısına göre özel indirimler veya ek özellikler
- Referans programı istatistikleri ve takip paneli

### 3.2. Referans Alan Kullanıcı İçin Faydalar

- Uzatılmış deneme süresi (14 gün yerine 30 gün)
- İlk abonelikte %10 indirim
- Tüm özelliklere deneme süresince erişim

## 4. Teknik Uygulama

### 4.1. Referans Kodu Saklama

Referans kodu, kayıt sırasında sessionStorage'a kaydedilir:

```javascript
if (values.referralCode) {
  sessionStorage.setItem('referralCode', values.referralCode);
}
```

### 4.2. Salon Oluşturma Sırasında Referans Kodunu Uygulama

Salon oluşturulduktan sonra, referans kodu kontrol edilir ve uygulanır:

```javascript
// Referans kodunu kontrol et ve uygula
if (typeof window !== 'undefined') {
  const referralCode = sessionStorage.getItem('referralCode');
  if (referralCode) {
    try {
      // Referans kodunu uygula
      await referrals.applyReferralCode(referralCode, data.id);
      
      // Referans kodunu sessionStorage'dan temizle
      sessionStorage.removeItem('referralCode');
      
      isReferred = true;
      toast.success("Referans kodu başarıyla uygulandı!");
    } catch (error) {
      console.error("Referans kodu uygulanırken hata:", error);
    }
  }
}
```

### 4.3. Deneme Aboneliği Oluşturma

Referans kodu uygulandıysa, uzatılmış deneme süreli bir abonelik oluşturulur:

```javascript
// Deneme aboneliği oluştur
const trialDays = isReferred ? 30 : 14;
trialEndDate.setDate(today.getDate() + trialDays);

const subscription = {
  salon_id: salonId,
  plan_id: planId,
  status: 'trial',
  start_date: today.toISOString().split('T')[0],
  trial_end_date: trialEndDate.toISOString().split('T')[0],
  payment_method: 'manual',
  is_yearly: false,
  is_active: true
};

await createSalonSubscription(subscription);
```

## 5. Hata Durumları ve Çözümleri

### 5.1. Geçersiz Referans Kodu

- Kullanıcı geçersiz bir referans kodu girerse, sistem bir hata mesajı gösterir
- Salon yine de oluşturulur, ancak standart deneme süresi (14 gün) uygulanır

### 5.2. Referans Kodu Uygulanamadı

- Referans kodu uygulanırken bir hata oluşursa, hata konsola kaydedilir
- Kullanıcıya bildirim gösterilmez, salon standart deneme süresiyle oluşturulur

### 5.3. Aynı Referans Kodunu Birden Fazla Kullanma Girişimi

- Bir kullanıcı aynı referans kodunu birden fazla salon için kullanamaz
- Sistem, referans kodunun daha önce kullanılıp kullanılmadığını kontrol eder

## 6. Gelecek Geliştirmeler

1. **Sosyal Medya Entegrasyonu**: Referans kodlarını doğrudan sosyal medyada paylaşma özelliği
2. **Referans Kampanyaları**: Sınırlı süreli özel referans kampanyaları oluşturma
3. **Kademeli Referans Faydaları**: Referans sayısına göre artan faydalar
4. **Referans Bildirimleri**: Referans kodu kullanıldığında gerçek zamanlı bildirimler
5. **Referans Analitikleri**: Detaylı referans performans analizi ve raporlama
