"use client"

import * as React from "react"
import { format, parse } from "date-fns"
import { Clock } from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { FormControl } from "@/components/ui/form"

interface TimeInputProps {
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
  disabled?: boolean
  className?: string
  placeholder?: string
  name?: string
}

export function TimeInput({
  value,
  onChange,
  onBlur,
  disabled = false,
  className,
  placeholder = "00:00",
  name,
}: TimeInputProps) {
  // Parse the initial value
  const [hour, setHour] = React.useState<string>(() => {
    if (!value) return "09"
    try {
      const date = parse(value, "HH:mm", new Date())
      return format(date, "HH")
    } catch (e) {
      return "09"
    }
  })

  const [minute, setMinute] = React.useState<string>(() => {
    if (!value) return "00"
    try {
      const date = parse(value, "HH:mm", new Date())
      return format(date, "mm")
    } catch (e) {
      return "00"
    }
  })

  // Update the value when hour or minute changes
  React.useEffect(() => {
    onChange(`${hour}:${minute}`)
  }, [hour, minute, onChange])

  // Generate hours and minutes options
  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, "0"))
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, "0"))

  return (
    <Popover>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant="outline"
            role="combobox"
            className={cn(
              "w-full justify-start text-left font-normal",
              !value && "text-muted-foreground",
              className
            )}
            disabled={disabled}
            name={name}
            onBlur={onBlur}
          >
            <Clock className="mr-2 h-4 w-4" />
            {value || <span>{placeholder}</span>}
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-4" align="start">
        <div className="flex items-center space-x-2">
          <div className="grid gap-1">
            <div className="text-xs font-medium">Saat</div>
            <Select
              value={hour}
              onValueChange={setHour}
              disabled={disabled}
            >
              <SelectTrigger className="w-[70px]">
                <SelectValue placeholder="Saat" />
              </SelectTrigger>
              <SelectContent>
                {hours.map((h) => (
                  <SelectItem key={h} value={h}>
                    {h}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-1">
            <div className="text-xs font-medium">Dakika</div>
            <Select
              value={minute}
              onValueChange={setMinute}
              disabled={disabled}
            >
              <SelectTrigger className="w-[70px]">
                <SelectValue placeholder="Dakika" />
              </SelectTrigger>
              <SelectContent>
                {minutes.map((m) => (
                  <SelectItem key={m} value={m}>
                    {m}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
