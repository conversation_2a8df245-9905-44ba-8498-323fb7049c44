import { supabaseClient } from '../supabase-singleton';
import { Holiday, HolidayInsert, HolidayUpdate } from './types';

const supabase = supabaseClient;


/**
 * Get holidays for a salon
 */
export async function getHolidays(salonId: string) {
  const { data, error } = await supabase
    .from('holidays')
    .select('*')
    .eq('salon_id', salonId)
    .order('date');

  if (error) throw error;
  return data as Holiday[];
}

/**
 * Get holidays for a salon within a date range
 */
export async function getHolidaysInRange(salonId: string, startDate: string, endDate: string) {
  const { data, error } = await supabase
    .from('holidays')
    .select('*')
    .eq('salon_id', salonId)
    .gte('date', startDate)
    .lte('date', endDate)
    .order('date');

  if (error) throw error;
  return data as Holiday[];
}

/**
 * Check if a date is a holiday for a salon
 */
export async function isHoliday(salonId: string, date: string) {
  const { data, error } = await supabase
    .from('holidays')
    .select('*')
    .eq('salon_id', salonId)
    .eq('date', date);

  if (error) throw error;
  return data.length > 0;
}

/**
 * Create a holiday for a salon
 */
export async function createHoliday(holiday: HolidayInsert) {
  const { data, error } = await supabase
    .from('holidays')
    .insert(holiday)
    .select()
    .single();

  if (error) throw error;
  return data as Holiday;
}

/**
 * Create multiple holidays for a salon
 */
export async function createHolidays(holidays: HolidayInsert[]) {
  const { data, error } = await supabase
    .from('holidays')
    .insert(holidays)
    .select();

  if (error) throw error;
  return data as Holiday[];
}

/**
 * Update a holiday
 */
export async function updateHoliday({ id, ...holiday }: HolidayUpdate) {
  const { data, error } = await supabase
    .from('holidays')
    .update(holiday)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Holiday;
}

/**
 * Delete a holiday
 */
export async function deleteHoliday(id: string) {
  const { error } = await supabase
    .from('holidays')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}
