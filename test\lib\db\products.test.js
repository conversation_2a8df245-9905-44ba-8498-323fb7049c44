import { expect } from 'chai';
import sinon from 'sinon';
import * as productsModule from '../../../src/lib/db/products';
import { getSupabaseBrowser } from '../../../src/lib/supabase';

// Mock Supabase client
const mockSupabase = {
  from: sinon.stub(),
  storage: {
    from: sinon.stub(),
    getPublicUrl: sinon.stub()
  }
};

// Mock select, insert, update, delete methods
const mockSelect = sinon.stub();
const mockInsert = sinon.stub();
const mockUpdate = sinon.stub();
const mockDelete = sinon.stub();
const mockEq = sinon.stub();
const mockOrder = sinon.stub();
const mockSingle = sinon.stub();
const mockIlike = sinon.stub();
const mockNot = sinon.stub();

describe('Products Database Module', () => {
  let sandbox;
  
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    
    // Reset stubs
    mockSelect.reset();
    mockInsert.reset();
    mockUpdate.reset();
    mockDelete.reset();
    mockEq.reset();
    mockOrder.reset();
    mockSingle.reset();
    mockIlike.reset();
    mockNot.reset();
    
    // Setup method chaining
    mockSelect.returns({ eq: mockEq });
    mockEq.returns({ order: mockOrder });
    mockOrder.returns({ single: mockSingle });
    mockSingle.returns({ data: null, error: null });
    mockOrder.returns({ data: [], error: null });
    mockEq.returns({ data: null, error: null });
    mockInsert.returns({ select: mockSelect });
    mockUpdate.returns({ eq: mockEq });
    mockDelete.returns({ eq: mockEq });
    mockIlike.returns({ order: mockOrder });
    mockNot.returns({ order: mockOrder });
    
    // Setup from stub
    mockSupabase.from.returns({
      select: mockSelect,
      insert: mockInsert,
      update: mockUpdate,
      delete: mockDelete
    });
    
    // Mock getSupabaseBrowser
    sandbox.stub(getSupabaseBrowser, 'getSupabaseBrowser').returns(mockSupabase);
  });
  
  afterEach(() => {
    sandbox.restore();
  });
  
  describe('getProducts', () => {
    it('should fetch products for a salon', async () => {
      const salonId = 'test-salon-id';
      const mockProducts = [
        { id: '1', name: 'Product 1' },
        { id: '2', name: 'Product 2' }
      ];
      
      mockOrder.returns({ data: mockProducts, error: null });
      
      const result = await productsModule.getProducts(salonId);
      
      expect(mockSupabase.from.calledWith('products')).to.be.true;
      expect(mockSelect.calledWith('*')).to.be.true;
      expect(mockEq.calledWith('salon_id', salonId)).to.be.true;
      expect(mockOrder.calledWith('name')).to.be.true;
      expect(result).to.deep.equal(mockProducts);
    });
    
    it('should throw an error if the query fails', async () => {
      const salonId = 'test-salon-id';
      const mockError = new Error('Database error');
      
      mockOrder.returns({ data: null, error: mockError });
      
      try {
        await productsModule.getProducts(salonId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.equal(mockError);
      }
    });
  });
  
  describe('getActiveProducts', () => {
    it('should fetch active products for a salon', async () => {
      const salonId = 'test-salon-id';
      const mockProducts = [
        { id: '1', name: 'Product 1', is_active: true },
        { id: '2', name: 'Product 2', is_active: true }
      ];
      
      mockOrder.returns({ data: mockProducts, error: null });
      
      const result = await productsModule.getActiveProducts(salonId);
      
      expect(mockSupabase.from.calledWith('products')).to.be.true;
      expect(mockSelect.calledWith('*')).to.be.true;
      expect(mockEq.calledWith('salon_id', salonId)).to.be.true;
      expect(mockEq.calledWith('is_active', true)).to.be.true;
      expect(mockOrder.calledWith('name')).to.be.true;
      expect(result).to.deep.equal(mockProducts);
    });
  });
  
  describe('createProduct', () => {
    it('should create a new product', async () => {
      const productData = {
        salon_id: 'test-salon-id',
        name: 'New Product',
        price: 100,
        is_active: true
      };
      
      const mockCreatedProduct = {
        id: 'new-product-id',
        ...productData,
        created_at: '2024-08-20T12:00:00Z',
        updated_at: '2024-08-20T12:00:00Z'
      };
      
      mockSingle.returns({ data: mockCreatedProduct, error: null });
      
      const result = await productsModule.createProduct(productData);
      
      expect(mockSupabase.from.calledWith('products')).to.be.true;
      expect(mockInsert.calledWith(productData)).to.be.true;
      expect(mockSelect.called).to.be.true;
      expect(mockSingle.called).to.be.true;
      expect(result).to.deep.equal(mockCreatedProduct);
    });
  });
  
  describe('updateProduct', () => {
    it('should update an existing product', async () => {
      const productData = {
        id: 'test-product-id',
        name: 'Updated Product',
        price: 150
      };
      
      const mockUpdatedProduct = {
        ...productData,
        salon_id: 'test-salon-id',
        created_at: '2024-08-20T12:00:00Z',
        updated_at: '2024-08-20T13:00:00Z'
      };
      
      mockSingle.returns({ data: mockUpdatedProduct, error: null });
      
      const result = await productsModule.updateProduct(productData);
      
      expect(mockSupabase.from.calledWith('products')).to.be.true;
      expect(mockUpdate.calledWith(productData)).to.be.true;
      expect(mockEq.calledWith('id', productData.id)).to.be.true;
      expect(mockSelect.called).to.be.true;
      expect(mockSingle.called).to.be.true;
      expect(result).to.deep.equal(mockUpdatedProduct);
    });
  });
  
  describe('deleteProduct', () => {
    it('should delete a product', async () => {
      const productId = 'test-product-id';
      
      mockEq.returns({ error: null });
      
      const result = await productsModule.deleteProduct(productId);
      
      expect(mockSupabase.from.calledWith('products')).to.be.true;
      expect(mockDelete.called).to.be.true;
      expect(mockEq.calledWith('id', productId)).to.be.true;
      expect(result).to.be.true;
    });
  });
});
