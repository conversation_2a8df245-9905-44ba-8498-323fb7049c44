import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import * as db from '@/lib/db';

// Define the request schema for creating/updating a transaction
const transactionSchema = z.object({
  salon_id: z.string().uuid(),
  category_id: z.string().uuid(),
  amount: z.number().positive('Tutar pozitif olmalıdır'),
  transaction_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Geç<PERSON><PERSON> bir tarih giriniz (YYYY-MM-DD)'),
  description: z.string().optional(),
  appointment_id: z.string().uuid().optional(),
});

// GET /api/finance/transactions
export async function GET(request: NextRequest) {
  try {
    // Get the query parameters
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const categoryId = searchParams.get('category_id');
    const type = searchParams.get('type') as 'income' | 'expense' | null;

    if (!salonId) {
      return NextResponse.json(
        { error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the transactions based on the query parameters
    let transactions;
    if (startDate && endDate) {
      transactions = await db.financeTransactions.getFinanceTransactionsInRange(salonId, startDate, endDate);
    } else if (categoryId) {
      transactions = await db.financeTransactions.getFinanceTransactionsByCategory(salonId, categoryId);
    } else if (type) {
      transactions = await db.financeTransactions.getFinanceTransactionsByType(salonId, type);
    } else {
      transactions = await db.financeTransactions.getFinanceTransactions(salonId);
    }

    return NextResponse.json(transactions);
  } catch (error) {
    console.error('Error fetching finance transactions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/finance/transactions
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const validation = transactionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }

    const transactionData = validation.data;

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Add the created_by field
    const transactionWithUser = {
      ...transactionData,
      created_by: user.id,
    };

    // Create the transaction
    const transaction = await db.financeTransactions.createFinanceTransaction(transactionWithUser);

    return NextResponse.json(transaction);
  } catch (error) {
    console.error('Error creating finance transaction:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
