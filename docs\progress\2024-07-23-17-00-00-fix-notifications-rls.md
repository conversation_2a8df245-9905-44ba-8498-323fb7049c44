# Bildirimler RLS Politikası Düzeltmesi - 2024-07-23

## <PERSON>l Bakış

Bu belge, notifications tablosunun Row Level Security (RLS) politikalarını düzeltmek için yapılan değişiklikleri belgelemektedir.

## <PERSON><PERSON> (an<PERSON><PERSON>) rande<PERSON> oluşturduğunda, trigger çalışıyor ve bildirim oluşturmaya çalışıyor, ancak RLS politikası buna izin vermiyordu. <PERSON><PERSON> ne<PERSON>, aşağ<PERSON>daki hata alınıyordu:

```
{
    "code": "42501",
    "details": null,
    "hint": null,
    "message": "new row violates row-level security policy for table \"notifications\""
}
```

## Çözüm

Notifications tablosunun RLS politikalarını düzelttik:

1. Mevcut politikaları temizledik
2. RLS'yi geçici olarak devre dışı bıraktık (trigger'ların çalışabilmesi için)
3. Servis rolü için bypass politikası ekledik
4. Salon sahipleri için politika ekledik
5. Berberler için politika ekledik
6. Herkes için ekleme politikası ekledik (trigger'lar için)
7. RLS'yi tekrar etkinleştirdik

## Teknik Detaylar

```sql
-- Mevcut politikaları temizleyelim
DROP POLICY IF EXISTS "Salon owners can see their salon notifications" ON notifications;
DROP POLICY IF EXISTS "Salon owners can manage their salon notifications" ON notifications;
DROP POLICY IF EXISTS "Staff can see their own notifications" ON notifications;
DROP POLICY IF EXISTS "Staff can manage their own notifications" ON notifications;

-- RLS'yi geçici olarak devre dışı bırakalım (trigger'ların çalışabilmesi için)
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;

-- Servis rolü için bypass politikası ekleyelim
CREATE POLICY "Service role bypass for notifications" ON notifications
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Salon sahipleri için politika
CREATE POLICY "Salon owners can manage their salon notifications" ON notifications
  FOR ALL
  TO authenticated
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Berberler için politika
CREATE POLICY "Staff can manage their own notifications" ON notifications
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Herkes için ekleme politikası (trigger'lar için)
CREATE POLICY "Anyone can insert notifications" ON notifications
  FOR INSERT
  WITH CHECK (true);

-- RLS'yi tekrar etkinleştirelim
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
```

## Sonuç

Bu değişikliklerle birlikte, müşteriler (anonim kullanıcılar) randevu oluşturduğunda, trigger çalışacak ve bildirim oluşturabilecektir. Ayrıca, salon sahipleri ve berberler kendi bildirimlerini yönetebileceklerdir.
