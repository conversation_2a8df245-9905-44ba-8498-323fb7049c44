import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';
import * as db from '@/lib/db';

// Define the request schema for creating/updating a category
const categorySchema = z.object({
  salon_id: z.string().uuid(),
  name: z.string().min(1, 'Kategori adı gereklidir'),
  type: z.enum(['income', 'expense']),
  description: z.string().optional(),
  color: z.string().optional(),
  is_system_default: z.boolean().optional().default(false),
});

// GET /api/finance/categories
export async function GET(request: NextRequest) {
  try {
    // Get the salon_id from the query parameters
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');
    const type = searchParams.get('type') as 'income' | 'expense' | null;

    if (!salonId) {
      return NextResponse.json(
        { error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the categories
    let categories;
    if (type) {
      categories = await db.financeCategories.getFinanceCategoriesByType(salonId, type);
    } else {
      categories = await db.financeCategories.getFinanceCategories(salonId);
    }

    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching finance categories:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/finance/categories
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const validation = categorySchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validation.error.format() },
        { status: 400 }
      );
    }

    const categoryData = validation.data;

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Create the category
    const category = await db.financeCategories.createFinanceCategory(categoryData);

    return NextResponse.json(category);
  } catch (error) {
    console.error('Error creating finance category:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
