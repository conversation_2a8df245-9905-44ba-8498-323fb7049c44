-- SalonFlow Admin RLS Politikalarını Düzeltme
-- Tarih: 2024-08-05
-- Açıklama: Email kontrolü yapan tüm RLS politikalarını is_admin() fonksiyonunu kullanacak şekilde değiştirme

-- 1. salon_subscriptions tablosu için RLS politikalarını düzeltme
DROP POLICY IF EXISTS "Admin tüm abonelikleri görebilir" ON salon_subscriptions;
DROP POLICY IF EXISTS "Admin abonelikleri ekleyebilir" ON salon_subscriptions;
DROP POLICY IF EXISTS "Admin abonelikleri güncelleyebilir" ON salon_subscriptions;
DROP POLICY IF EXISTS "Admin abonelikleri silebilir" ON salon_subscriptions;

-- Yeni politikalar oluştur
CREATE POLICY "Admin tüm abonelikleri görebilir" ON salon_subscriptions
  FOR SELECT USING (is_admin());

CREATE POLICY "Admin abonelikleri ekleyebilir" ON salon_subscriptions
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admin abonelikleri güncelleyebilir" ON salon_subscriptions
  FOR UPDATE USING (is_admin());

CREATE POLICY "Admin abonelikleri silebilir" ON salon_subscriptions
  FOR DELETE USING (is_admin());

-- 2. referral_codes tablosu için RLS politikalarını düzeltme
DROP POLICY IF EXISTS "Admin tüm referans kodlarını görebilir" ON referral_codes;
DROP POLICY IF EXISTS "Admin referans kodlarını ekleyebilir" ON referral_codes;
DROP POLICY IF EXISTS "Admin referans kodlarını güncelleyebilir" ON referral_codes;
DROP POLICY IF EXISTS "Admin referans kodlarını silebilir" ON referral_codes;

-- Yeni politikalar oluştur
CREATE POLICY "Admin tüm referans kodlarını görebilir" ON referral_codes
  FOR SELECT USING (is_admin());

CREATE POLICY "Admin referans kodlarını ekleyebilir" ON referral_codes
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admin referans kodlarını güncelleyebilir" ON referral_codes
  FOR UPDATE USING (is_admin());

CREATE POLICY "Admin referans kodlarını silebilir" ON referral_codes
  FOR DELETE USING (is_admin());

-- 3. referral_benefits tablosu için RLS politikalarını düzeltme
DROP POLICY IF EXISTS "Admin tüm referans faydalarını görebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin referans faydalarını ekleyebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin referans faydalarını güncelleyebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin referans faydalarını silebilir" ON referral_benefits;

-- Yeni politikalar oluştur
CREATE POLICY "Admin tüm referans faydalarını görebilir" ON referral_benefits
  FOR SELECT USING (is_admin());

CREATE POLICY "Admin referans faydalarını ekleyebilir" ON referral_benefits
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY "Admin referans faydalarını güncelleyebilir" ON referral_benefits
  FOR UPDATE USING (is_admin());

CREATE POLICY "Admin referans faydalarını silebilir" ON referral_benefits
  FOR DELETE USING (is_admin());

-- 4. pending_referrals tablosu için RLS politikalarını düzeltme
DROP POLICY IF EXISTS "Admin tüm bekleyen referans kodlarını görebilir" ON pending_referrals;
DROP POLICY IF EXISTS "Admin tüm bekleyen referans kodlarını güncelleyebilir" ON pending_referrals;
DROP POLICY IF EXISTS "Admin tüm bekleyen referans kodlarını silebilir" ON pending_referrals;

-- Yeni politikalar oluştur
CREATE POLICY "Admin tüm bekleyen referans kodlarını görebilir" ON pending_referrals
  FOR SELECT USING (is_admin());

CREATE POLICY "Admin tüm bekleyen referans kodlarını güncelleyebilir" ON pending_referrals
  FOR UPDATE USING (is_admin());

CREATE POLICY "Admin tüm bekleyen referans kodlarını silebilir" ON pending_referrals
  FOR DELETE USING (is_admin());
