"use client"

import { useState, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import * as z from "zod"
import { X, Upload, Loader2, HardDrive, Plus } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { useUser } from "@/contexts/UserContext"
import { useSubscription } from "@/contexts/SubscriptionContext"
import { products, productCategories } from "@/lib/db"
import { Product, ProductInsert, ProductUpdate, ProductCategory, ProductCategoryInsert } from "@/lib/db/types"
import { uploadProductImage, processImage, hasReachedStorageLimit, getSalonStorageUsage } from "@/lib/storage"

// Form şeması
const formSchema = z.object({
  name: z.string().min(2, "Ürün adı en az 2 karakter olmalıdır"),
  description: z.string().optional(),
  price: z.string().optional(),
  category: z.string().optional(),
  is_active: z.boolean().default(true),
})

interface ProductFormProps {
  initialData?: Product
  onSuccess?: () => void
}

export function ProductForm({ initialData, onSuccess }: ProductFormProps) {
  const router = useRouter()
  const { salonId } = useUser()
  const { features } = useSubscription()

  const [isLoading, setIsLoading] = useState(false)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(initialData?.image_url || null)
  const [isUploading, setIsUploading] = useState(false)
  const [categories, setCategories] = useState<ProductCategory[]>([])
  const [newCategoryName, setNewCategoryName] = useState("")
  const [isAddingCategory, setIsAddingCategory] = useState(false)
  const [storageUsage, setStorageUsage] = useState(0)
  const [storagePercentage, setStoragePercentage] = useState(0)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // Kategorileri yükle
  const loadCategories = useCallback(async () => {
    if (!salonId) return

    try {
      const data = await productCategories.getProductCategories(salonId)
      setCategories(data)
    } catch (error) {
      console.error("Kategoriler yüklenirken hata:", error)
      toast.error("Kategoriler yüklenirken bir hata oluştu")
    }
  }, [salonId])

  // Depolama kullanımını yükle
  const loadStorageUsage = useCallback(async () => {
    if (!salonId) return

    try {
      const usage = await getSalonStorageUsage(salonId)
      const usageMB = usage / (1024 * 1024)
      setStorageUsage(usageMB)

      // Yüzde hesapla
      const percentage = features.storageLimitMB > 0
        ? Math.min(100, (usageMB / features.storageLimitMB) * 100)
        : 100
      setStoragePercentage(percentage)
    } catch (error) {
      console.error("Depolama kullanımı yüklenirken hata:", error)
    }
  }, [salonId, features.storageLimitMB])

  // Yeni kategori ekle
  const handleAddCategory = async () => {
    if (!salonId || !newCategoryName.trim()) return

    setIsAddingCategory(true)

    try {
      const categoryData: ProductCategoryInsert = {
        salon_id: salonId,
        name: newCategoryName.trim()
      }

      await productCategories.createProductCategory(categoryData)
      toast.success("Kategori başarıyla eklendi")
      setNewCategoryName("")
      loadCategories()
      setIsDialogOpen(false)
    } catch (error) {
      console.error("Kategori eklenirken hata:", error)
      toast.error("Kategori eklenirken bir hata oluştu")
    } finally {
      setIsAddingCategory(false)
    }
  }

  // Sayfa yüklendiğinde kategorileri ve depolama kullanımını getir
  useEffect(() => {
    loadCategories()
    loadStorageUsage()
  }, [loadCategories, loadStorageUsage])

  // Form oluştur
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      price: initialData?.price ? String(initialData.price) : "",
      category: initialData?.category || "no_category",
      is_active: initialData?.is_active ?? true,
    },
  })

  // Görsel seçildiğinde
  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    try {
      // Depolama limiti kontrolü
      if (await hasReachedStorageLimit(salonId!, features.storageLimitMB)) {
        toast.error(`Depolama limitiniz doldu. Aboneliğinizi yükseltin veya bazı görselleri silin.`)
        return
      }

      // Görsel işleme
      const processedFile = await processImage(file)
      setImageFile(processedFile)

      // Önizleme URL'i oluştur
      const previewUrl = URL.createObjectURL(processedFile)
      setImagePreview(previewUrl)
    } catch (error) {
      console.error("Görsel işlenirken hata:", error)
      toast.error("Görsel işlenirken bir hata oluştu")
    }
  }

  // Görseli kaldır
  const handleRemoveImage = () => {
    setImageFile(null)
    setImagePreview(initialData?.image_url || null)

    // Input değerini sıfırla
    const fileInput = document.getElementById("product-image") as HTMLInputElement
    if (fileInput) fileInput.value = ""
  }

  // Form gönderildiğinde
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!salonId) {
      toast.error("Salon bilgisi bulunamadı")
      return
    }

    setIsLoading(true)

    try {
      // Fiyatı sayıya çevir
      const price = values.price ? parseFloat(values.price) : null

      // Kategori değerini kontrol et
      const category = values.category === "no_category" ? null : values.category

      // Görsel yükleme
      let imageUrl = initialData?.image_url || null

      if (imageFile) {
        setIsUploading(true)
        // Yeni ürün oluşturuluyorsa geçici bir ID kullan
        const productId = initialData?.id || "temp-" + Date.now()
        imageUrl = await uploadProductImage(salonId, productId, imageFile)
        setIsUploading(false)
      }

      if (initialData) {
        // Mevcut ürünü güncelle
        const productData: ProductUpdate = {
          id: initialData.id,
          name: values.name,
          description: values.description || null,
          price: price,
          category: category,
          is_active: values.is_active,
        }

        if (imageUrl) {
          productData.image_url = imageUrl
        }

        await products.updateProduct(productData)
        toast.success("Ürün başarıyla güncellendi")
      } else {
        // Yeni ürün oluştur
        const productData: ProductInsert = {
          salon_id: salonId,
          name: values.name,
          description: values.description || null,
          price: price,
          category: category,
          is_active: values.is_active,
          image_url: imageUrl,
        }

        await products.createProduct(productData)
        toast.success("Ürün başarıyla oluşturuldu")
      }

      // Başarılı olduğunda
      if (onSuccess) {
        onSuccess()
      } else {
        router.push("/dashboard/products")
        router.refresh()
      }
    } catch (error) {
      console.error("Ürün kaydedilirken hata:", error)
      toast.error("Ürün kaydedilirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ürün Adı</FormLabel>
                  <FormControl>
                    <Input placeholder="Ürün adı" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Ürün açıklaması"
                      className="min-h-32"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fiyat (₺)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Kategori</FormLabel>
                    <div className="flex gap-2">
                      <FormControl>
                        <Select
                          value={field.value}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Kategori seçin" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="no_category">Kategori Yok</SelectItem>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.name}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                          <Button type="button" variant="outline" size="icon">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Yeni Kategori Ekle</DialogTitle>
                            <DialogDescription>
                              Ürünleriniz için yeni bir kategori oluşturun.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="grid grid-cols-4 items-center gap-4">
                              <FormLabel className="text-right">Kategori Adı</FormLabel>
                              <Input
                                id="category-name"
                                value={newCategoryName}
                                onChange={(e) => setNewCategoryName(e.target.value)}
                                className="col-span-3"
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              type="button"
                              onClick={handleAddCategory}
                              disabled={isAddingCategory || !newCategoryName.trim()}
                            >
                              {isAddingCategory && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                              Ekle
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Aktif</FormLabel>
                    <FormDescription>
                      Ürün müşteriler tarafından görüntülenebilir
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <FormLabel>Depolama Kullanımı</FormLabel>
                <span className="text-sm text-muted-foreground">
                  {storageUsage.toFixed(2)} MB / {features.storageLimitMB} MB
                </span>
              </div>
              <Progress value={storagePercentage} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {features.storageLimitMB === 0 ? (
                  "Aboneliğinizde depolama alanı bulunmuyor. Görsel yüklemek için aboneliğinizi yükseltin."
                ) : storagePercentage >= 90 ? (
                  "Depolama alanınız neredeyse doldu. Daha fazla alan için aboneliğinizi yükseltin."
                ) : (
                  `Kalan depolama alanı: ${(features.storageLimitMB - storageUsage).toFixed(2)} MB`
                )}
              </p>
            </div>

            <FormItem>
              <FormLabel>Ürün Görseli</FormLabel>
              <FormControl>
                <div className="flex flex-col items-center justify-center border-2 border-dashed border-border rounded-lg p-6 h-64">
                  {imagePreview ? (
                    <div className="relative w-full h-full">
                      <Image
                        src={imagePreview}
                        alt="Ürün görseli"
                        fill
                        className="object-contain"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2"
                        onClick={handleRemoveImage}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center text-center">
                      <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                      <p className="text-sm text-muted-foreground mb-2">
                        Görsel yüklemek için tıklayın veya sürükleyin
                      </p>
                      <p className="text-xs text-muted-foreground">
                        PNG, JPG veya WebP (max. 2MB)
                      </p>
                      <Input
                        id="product-image"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageChange}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="mt-4"
                        onClick={() => document.getElementById("product-image")?.click()}
                      >
                        Görsel Seç
                      </Button>
                    </div>
                  )}
                </div>
              </FormControl>
              <FormDescription>
                Önerilen boyut: 800x800 piksel. Maksimum dosya boyutu: 2MB.
              </FormDescription>
            </FormItem>
          </div>
        </div>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/dashboard/products")}
            disabled={isLoading || isUploading}
          >
            İptal
          </Button>
          <Button type="submit" disabled={isLoading || isUploading}>
            {(isLoading || isUploading) && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {initialData ? "Güncelle" : "Oluştur"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
