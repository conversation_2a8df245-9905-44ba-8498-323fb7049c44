// Public access functions for salon content
// These functions are used by customer-facing pages without authentication

import { supabaseClient } from '@/lib/supabase-singleton'
import { SalonContent, SalonTestimonial } from '../types'

const supabase = supabaseClient

// =====================================================
// PUBLIC SALON CONTENT ACCESS
// =====================================================

/**
 * Get all content for a specific salon (public access)
 */
export async function getPublicSalonContent(salonId: string): Promise<SalonContent[]> {
  const { data, error } = await supabase
    .from('salon_content')
    .select('*')
    .eq('salon_id', salonId)
    .order('section', { ascending: true })
    .order('content_key', { ascending: true })

  if (error) {
    console.error('Error fetching public salon content:', error)
    throw error
  }

  return data || []
}

/**
 * Get content for a specific section (public access)
 */
export async function getPublicSalonContentBySection(
  salonId: string,
  section: 'hero' | 'about' | 'services' | 'contact'
): Promise<SalonContent[]> {
  const { data, error } = await supabase
    .from('salon_content')
    .select('*')
    .eq('salon_id', salonId)
    .eq('section', section)
    .order('content_key', { ascending: true })

  if (error) {
    console.error('Error fetching public salon content by section:', error)
    throw error
  }

  return data || []
}

/**
 * Get active testimonials for public display
 */
export async function getPublicSalonTestimonials(salonId: string): Promise<SalonTestimonial[]> {
  const { data, error } = await supabase
    .from('salon_testimonials')
    .select('*')
    .eq('salon_id', salonId)
    .eq('is_active', true)
    .order('display_order', { ascending: true })
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching public salon testimonials:', error)
    throw error
  }

  return data || []
}

// =====================================================
// CONTENT PARSING HELPERS
// =====================================================

/**
 * Parse salon content into structured data for landing page
 */
export interface ParsedSalonContent {
  hero: {
    badgeText: string
    tagline: string
    description: string
    ctaPrimary: string
    ctaSecondary: string
    stats: {
      customers: { value: string; label: string }
      experience: { value: string; label: string }
      rating: { value: string; label: string }
      support: { value: string; label: string }
    }
  }
  about: {
    badgeText: string
    title: string
    description: string
    ctaButtonText: string
    trustTitle: string
    features: Array<{ icon: string; title: string; description: string }>
    stats: Array<{ number: string; label: string }>
    trustItems: Array<{ text: string }>
  }
  services: {
    badgeText: string
    titleLine1: string
    titleLine2: string
    title: string
    description: string
    cta: {
      title: string
      description: string
      button: string
    }
  }
  contact: {
    badgeText: string
    title: string
    description: string
    cta: {
      title: string
      description: string
      button: string
    }
  }
}

/**
 * Parse raw content array into structured format
 */
export function parseSalonContent(content: SalonContent[]): ParsedSalonContent {
  const getContentValue = (section: string, key: string, fallback: string = '') => {
    const item = content.find(c => c.section === section && c.content_key === key)
    return item?.content_value || fallback
  }

  const parseJsonContent = <T>(section: string, key: string, fallback: T): T => {
    const item = content.find(c => c.section === section && c.content_key === key)
    if (!item?.content_value) return fallback

    try {
      return JSON.parse(item.content_value) as T
    } catch (error) {
      console.error('Error parsing JSON content:', error)
      return fallback
    }
  }

  return {
    hero: {
      badgeText: getContentValue('hero', 'badge_text', 'Profesyonel Berber Hizmetleri'),
      tagline: getContentValue('hero', 'tagline', 'ile tarzını yansıt.'),
      description: getContentValue('hero', 'description', 'Uzman ekibimizle kaliteli hizmet garantisi. Modern teknikler ve kişisel yaklaşımla tarzınızı yenileyin.'),
      ctaPrimary: getContentValue('hero', 'cta_primary', 'Randevu Al'),
      ctaSecondary: getContentValue('hero', 'cta_secondary', 'Hizmetlerimizi Keşfet'),
      stats: {
        customers: {
          value: getContentValue('hero', 'stats_customers', '500+'),
          label: getContentValue('hero', 'stats_customers_label', 'Mutlu Müşteri')
        },
        experience: {
          value: getContentValue('hero', 'stats_experience', '5+'),
          label: getContentValue('hero', 'stats_experience_label', 'Yıl Deneyim')
        },
        rating: {
          value: getContentValue('hero', 'stats_rating', '4.9'),
          label: getContentValue('hero', 'stats_rating_label', 'Müşteri Puanı')
        },
        support: {
          value: getContentValue('hero', 'stats_support', '24/7'),
          label: getContentValue('hero', 'stats_support_label', 'Online Randevu')
        }
      }
    },
    about: {
      badgeText: getContentValue('about', 'badge_text', 'Hakkımızda'),
      title: getContentValue('about', 'title', 'Farkını Yaşayın'),
      description: getContentValue('about', 'description', 'Yılların deneyimi ve modern yaklaşımımızla, her müşterimize özel hizmet sunuyoruz. Kalite, güven ve memnuniyet bizim önceliğimiz.'),
      ctaButtonText: getContentValue('about', 'cta_button_text', 'Hemen Randevu Al'),
      trustTitle: getContentValue('about', 'trust_title', 'Güvenilir Hizmet'),
      features: parseJsonContent('about', 'features', [
        { icon: 'Award', title: 'Uzman Ekip', description: 'Alanında deneyimli ve sertifikalı berberlerimiz' },
        { icon: 'Clock', title: 'Esnek Saatler', description: 'Size uygun saatlerde randevu imkanı' },
        { icon: 'Heart', title: 'Müşteri Memnuniyeti', description: 'Her müşterimize özel ilgi ve kaliteli hizmet' },
        { icon: 'Users', title: 'Modern Yaklaşım', description: 'Güncel trendler ve tekniklerle hizmet' }
      ]),
      stats: parseJsonContent('about', 'stats', [
        { number: '500+', label: 'Mutlu Müşteri' },
        { number: '5+', label: 'Yıl Deneyim' },
        { number: '4.9', label: 'Müşteri Puanı' },
        { number: '24/7', label: 'Online Destek' }
      ]),
      trustItems: parseJsonContent('about', 'trust_items', [
        { text: 'Hijyen ve temizlik standartları' },
        { text: 'Kaliteli ürün ve ekipman kullanımı' },
        { text: 'Müşteri memnuniyeti garantisi' }
      ])
    },
    services: {
      badgeText: getContentValue('services', 'badge_text', 'Hizmetlerimiz'),
      titleLine1: getContentValue('services', 'title_line1', 'Profesyonel'),
      titleLine2: getContentValue('services', 'title_line2', 'Berber Hizmetleri'),
      title: getContentValue('services', 'title', 'Profesyonel Berber Hizmetleri'),
      description: getContentValue('services', 'description', 'Uzman ekibimizle size özel tasarlanmış hizmetler. Modern teknikler ve kaliteli ürünlerle tarzınızı yansıtın.'),
      cta: {
        title: getContentValue('services', 'cta_title', 'Özel İhtiyaçlarınız mı Var?'),
        description: getContentValue('services', 'cta_description', 'Listelenen hizmetler dışında özel talepleriniz için bizimle iletişime geçin. Size özel çözümler sunmaktan mutluluk duyarız.'),
        button: getContentValue('services', 'cta_button_text', 'İletişime Geç')
      }
    },
    contact: {
      badgeText: getContentValue('contact', 'badge_text', 'İletişim'),
      title: getContentValue('contact', 'title', 'Bizimle İletişime Geçin'),
      description: getContentValue('contact', 'description', 'Sorularınız için bizimle iletişime geçebilirsiniz'),
      cta: {
        title: getContentValue('contact', 'cta_title', 'Hemen Randevu Alın'),
        description: getContentValue('contact', 'cta_description', 'Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz.'),
        button: getContentValue('contact', 'cta_button', 'Randevu Al')
      }
    }
  }
}

/**
 * Get complete salon landing page data
 */
export async function getCompleteSalonLandingData(salonId: string): Promise<{
  content: ParsedSalonContent
  testimonials: SalonTestimonial[]
}> {
  try {
    const [contentData, testimonialsData] = await Promise.all([
      getPublicSalonContent(salonId),
      getPublicSalonTestimonials(salonId)
    ])

    return {
      content: parseSalonContent(contentData),
      testimonials: testimonialsData
    }
  } catch (error) {
    console.error('Error fetching complete salon landing data:', error)
    throw error
  }
}

// =====================================================
// FALLBACK DATA
// =====================================================

/**
 * Get default content when no custom content exists
 */
export function getDefaultSalonContent(): ParsedSalonContent {
  return {
    hero: {
      badgeText: 'Profesyonel Berber Hizmetleri',
      tagline: 'ile tarzını yansıt.',
      description: 'Uzman ekibimizle kaliteli hizmet garantisi. Modern teknikler ve kişisel yaklaşımla tarzınızı yenileyin.',
      ctaPrimary: 'Randevu Al',
      ctaSecondary: 'Hizmetlerimizi Keşfet',
      stats: {
        customers: { value: '500+', label: 'Mutlu Müşteri' },
        experience: { value: '5+', label: 'Yıl Deneyim' },
        rating: { value: '4.9', label: 'Müşteri Puanı' },
        support: { value: '24/7', label: 'Online Randevu' }
      }
    },
    about: {
      badgeText: 'Hakkımızda',
      title: 'Farkını Yaşayın',
      description: 'Yılların deneyimi ve modern yaklaşımımızla, her müşterimize özel hizmet sunuyoruz. Kalite, güven ve memnuniyet bizim önceliğimiz.',
      ctaButtonText: 'Hemen Randevu Al',
      trustTitle: 'Güvenilir Hizmet',
      features: [
        { icon: 'Award', title: 'Uzman Ekip', description: 'Alanında deneyimli ve sertifikalı berberlerimiz' },
        { icon: 'Clock', title: 'Esnek Saatler', description: 'Size uygun saatlerde randevu imkanı' },
        { icon: 'Heart', title: 'Müşteri Memnuniyeti', description: 'Her müşterimize özel ilgi ve kaliteli hizmet' },
        { icon: 'Users', title: 'Modern Yaklaşım', description: 'Güncel trendler ve tekniklerle hizmet' }
      ],
      stats: [
        { number: '500+', label: 'Mutlu Müşteri' },
        { number: '5+', label: 'Yıl Deneyim' },
        { number: '4.9', label: 'Müşteri Puanı' },
        { number: '24/7', label: 'Online Destek' }
      ],
      trustItems: [
        { text: 'Hijyen ve temizlik standartları' },
        { text: 'Kaliteli ürün ve ekipman kullanımı' },
        { text: 'Müşteri memnuniyeti garantisi' }
      ]
    },
    services: {
      badgeText: 'Hizmetlerimiz',
      titleLine1: 'Profesyonel',
      titleLine2: 'Berber Hizmetleri',
      title: 'Profesyonel Berber Hizmetleri',
      description: 'Uzman ekibimizle size özel tasarlanmış hizmetler. Modern teknikler ve kaliteli ürünlerle tarzınızı yansıtın.',
      cta: {
        title: 'Özel İhtiyaçlarınız mı Var?',
        description: 'Listelenen hizmetler dışında özel talepleriniz için bizimle iletişime geçin. Size özel çözümler sunmaktan mutluluk duyarız.',
        button: 'İletişime Geç'
      }
    },
    contact: {
      badgeText: 'İletişim',
      title: 'Bizimle İletişime Geçin',
      description: 'Sorularınız için bizimle iletişime geçebilirsiniz',
      cta: {
        title: 'Hemen Randevu Alın',
        description: 'Online randevu sistemimiz ile hızlıca randevunuzu oluşturabilir, müsait saatleri görebilir ve tercih ettiğiniz berberi seçebilirsiniz.',
        button: 'Randevu Al'
      }
    }
  }
}
