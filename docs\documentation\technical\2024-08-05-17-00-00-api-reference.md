# SalonFlow API Referansı

**Tarih:** 5 Ağustos 2024
**Saat:** 17:00

## <PERSON>l Bakış

Bu dokümantasyon, SalonFlow API'lerinin detaylı bir referansını sağlamaktadır. SalonFlow, Supabase üzerine kurulu bir backend kullanmaktadır ve API'ler doğrudan Supabase client üzerinden erişilebilir.

## Kimlik Doğrulama

Tüm API istekleri, Supabase Auth tarafından sağlanan JWT token ile kimlik doğrulaması gerektirir. <PERSON>lik doğrulaması, aşağıdaki yöntemlerle yapılabilir:

### Email/Şifre ile G<PERSON>ş

```typescript
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})
```

### Magic Link ile Giriş

```typescript
const { data, error } = await supabase.auth.signInWithOtp({
  email: '<EMAIL>'
})
```

## Salon API'leri

### Salon Bilgilerini Getir

```typescript
const { data, error } = await supabase
  .from('salons')
  .select('*')
  .eq('id', salonId)
  .single()
```

### Salon Oluştur

```typescript
const { data, error } = await supabase
  .from('salons')
  .insert([
    {
      name: 'Salon Adı',
      address: 'Salon Adresi',
      phone: 'Telefon Numarası',
      owner_id: userId
    }
  ])
  .select()
```

### Salon Güncelle

```typescript
const { data, error } = await supabase
  .from('salons')
  .update({
    name: 'Yeni Salon Adı',
    address: 'Yeni Salon Adresi',
    phone: 'Yeni Telefon Numarası'
  })
  .eq('id', salonId)
  .select()
```

## Randevu API'leri

### Randevuları Getir

```typescript
const { data, error } = await supabase
  .from('appointments')
  .select('*, barbers(*), services(*)')
  .eq('salon_id', salonId)
```

### Randevu Oluştur

```typescript
const { data, error } = await supabase
  .from('appointments')
  .insert([
    {
      salon_id: salonId,
      barber_id: barberId,
      service_id: serviceId,
      start_time: startTime,
      end_time: endTime,
      fullname: customerName,
      phonenumber: customerPhone,
      email: customerEmail
    }
  ])
  .select()
```

### Randevu Güncelle

```typescript
const { data, error } = await supabase
  .from('appointments')
  .update({
    barber_id: newBarberId,
    service_id: newServiceId,
    start_time: newStartTime,
    end_time: newEndTime
  })
  .eq('id', appointmentId)
  .select()
```

### Randevu İptal Et

```typescript
const { data, error } = await supabase
  .from('appointments')
  .update({
    status: 'cancelled'
  })
  .eq('id', appointmentId)
  .select()
```

## Abonelik API'leri

### Salon Aboneliğini Getir

```typescript
const { data, error } = await supabase
  .from('salon_subscriptions')
  .select('*, subscription_plans(*)')
  .eq('salon_id', salonId)
  .single()
```

### Abonelik Ödemesi Oluştur

```typescript
const { data, error } = await supabase
  .from('subscription_payments')
  .insert([
    {
      subscription_id: subscriptionId,
      amount: amount,
      payment_date: paymentDate,
      payment_method: paymentMethod,
      status: 'completed'
    }
  ])
  .select()
```

## Hata Kodları

| Kod | Açıklama |
|-----|----------|
| 401 | Kimlik doğrulama hatası |
| 403 | Yetkilendirme hatası |
| 404 | Kaynak bulunamadı |
| 422 | Geçersiz istek |
| 500 | Sunucu hatası |

## Sürüm Geçmişi

| Sürüm | Tarih | Açıklama |
|-------|-------|----------|
| 1.0.0 | 2024-08-01 | İlk sürüm |
| 1.1.0 | 2024-08-05 | Abonelik API'leri eklendi |
