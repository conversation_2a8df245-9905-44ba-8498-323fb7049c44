import { supabaseClient } from '../supabase-singleton';
import { Product, ProductInsert, ProductUpdate } from './types';

const supabase = supabaseClient;

/**
 * Get all products for a salon
 */
export async function getProducts(salonId: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('salon_id', salonId)
    .order('name');

  if (error) throw error;
  return data as Product[];
}

/**
 * Get active products for a salon (for public display)
 */
export async function getActiveProducts(salonId: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('salon_id', salonId)
    .eq('is_active', true)
    .order('name');

  if (error) throw error;
  return data as Product[];
}

/**
 * Get a product by ID
 */
export async function getProductById(id: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as Product;
}

/**
 * Create a new product
 */
export async function createProduct(product: ProductInsert) {
  const { data, error } = await supabase
    .from('products')
    .insert(product)
    .select()
    .single();

  if (error) throw error;
  return data as Product;
}

/**
 * Update a product
 */
export async function updateProduct(product: ProductUpdate) {
  const { data, error } = await supabase
    .from('products')
    .update(product)
    .eq('id', product.id)
    .select()
    .single();

  if (error) throw error;
  return data as Product;
}

/**
 * Delete a product
 */
export async function deleteProduct(id: string) {
  const { error } = await supabase
    .from('products')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Get products by category
 */
export async function getProductsByCategory(salonId: string, category: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('salon_id', salonId)
    .eq('category', category)
    .eq('is_active', true)
    .order('name');

  if (error) throw error;
  return data as Product[];
}

/**
 * Get all product categories for a salon
 */
export async function getProductCategories(salonId: string) {
  const { data, error } = await supabase
    .from('products')
    .select('category')
    .eq('salon_id', salonId)
    .not('category', 'is', null)
    .order('category');

  if (error) throw error;
  
  // Extract unique categories
  const categories = [...new Set(data.map(item => item.category))];
  return categories;
}

/**
 * Toggle product active status
 */
export async function toggleProductStatus(id: string, isActive: boolean) {
  const { data, error } = await supabase
    .from('products')
    .update({ is_active: isActive })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Product;
}

/**
 * Search products by name
 */
export async function searchProducts(salonId: string, query: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('salon_id', salonId)
    .ilike('name', `%${query}%`)
    .order('name');

  if (error) throw error;
  return data as Product[];
}
