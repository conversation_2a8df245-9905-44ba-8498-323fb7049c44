"use client";

import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

export const InfiniteMovingCards = ({
  items,
  direction = "left",
  speed = "fast",
  pauseOnHover = true,
  className,
}: {
  items: {
    image: string;
    alt: string;
  }[];
  direction?: "left" | "right";
  speed?: "fast" | "normal" | "slow";
  pauseOnHover?: boolean;
  className?: string;
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const scrollerRef = React.useRef<HTMLUListElement>(null);

  const [start, setStart] = useState(false);

  useEffect(() => {
    setStart(true);
  }, []);

  const getSpeed = () => {
    switch (speed) {
      case "fast":
        return "30s";
      case "normal":
        return "45s";
      case "slow":
        return "60s";
      default:
        return "45s";
    }
  };

  const getDirection = () => {
    if (direction === "left") {
      return "animate-marquee";
    } else {
      return "animate-marquee-reverse";
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative flex max-w-full overflow-hidden [mask-image:linear-gradient(to_right,transparent,white_20%,white_80%,transparent)]",
        className
      )}
    >
      <ul
        ref={scrollerRef}
        className={cn(
          "flex min-w-full shrink-0 gap-4 py-4",
          start && getDirection(),
          pauseOnHover && "hover:[animation-play-state:paused]"
        )}
        style={{
          animationDuration: getSpeed(),
        }}
      >
        {items.map((item, idx) => (
          <li
            className="relative h-40 w-60 max-w-full flex-shrink-0 rounded-xl"
            key={idx}
          >
            <img
              src={item.image}
              alt={item.alt}
              className="h-full w-full rounded-xl object-cover"
            />
          </li>
        ))}
      </ul>
      <ul
        className={cn(
          "flex min-w-full shrink-0 gap-4 py-4",
          start && getDirection(),
          pauseOnHover && "hover:[animation-play-state:paused]"
        )}
        style={{
          animationDuration: getSpeed(),
        }}
      >
        {items.map((item, idx) => (
          <li
            className="relative h-40 w-60 max-w-full flex-shrink-0 rounded-xl"
            key={idx}
          >
            <img
              src={item.image}
              alt={item.alt}
              className="h-full w-full rounded-xl object-cover"
            />
          </li>
        ))}
      </ul>
    </div>
  );
};
