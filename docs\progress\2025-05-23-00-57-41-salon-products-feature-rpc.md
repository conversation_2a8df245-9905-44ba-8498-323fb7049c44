# Salon Ürün Özelliği RPC Fonksiyonu Ekleme

## Tarih: 2025-05-23

## <PERSON><PERSON><PERSON><PERSON>iklikler

### 1. RPC Fonksiyonu Oluşturma

Supabase'de `check_salon_has_products_feature` adında bir RPC fonksiyonu oluşturuldu. Bu fonksiyon:

- Bir salon ID'si alıyor
- Salonun ürün yönetimi özelliğine sahip bir abonelik planında olup olmadığını kontrol ediyor
- Salonun en az bir ürüne sahip olup olmadığını kontrol ediyor
- Her iki koşul da sağlanıyorsa true, aksi takdirde false döndürüyor

```sql
CREATE OR REPLACE FUNCTION check_salon_has_products_feature(p_salon_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_has_product_feature BOOLEAN;
  v_has_products BOOLEAN;
BEGIN
  -- 1. <PERSON><PERSON> ürün yönetimi özelliğine sahip bir abonelik planında olup olmadığını kontrol et
  SELECT EXISTS (
    SELECT 1
    FROM salon_subscriptions ss
    JOIN subscription_plans sp ON ss.plan_id = sp.id
    WHERE ss.salon_id = p_salon_id
      AND ss.status = 'active'
      AND sp.features->>'product_management' = 'true'
  ) INTO v_has_product_feature;

  -- 2. Salonun en az bir ürüne sahip olup olmadığını kontrol et
  SELECT EXISTS (
    SELECT 1
    FROM products
    WHERE salon_id = p_salon_id
      AND is_active = TRUE
  ) INTO v_has_products;

  -- Her iki koşul da sağlanıyorsa true, aksi takdirde false döndür
  RETURN v_has_product_feature AND v_has_products;
END;
$$ LANGUAGE plpgsql;
```

### 2. Public Access Fonksiyonu Ekleme

`src/lib/db/public/salon-features.ts` dosyası oluşturuldu:

```typescript
import { supabaseClient } from '@/lib/supabase-singleton';

/**
 * Bir salonun ürün yönetimi özelliğine sahip olup olmadığını ve en az bir ürüne sahip olup olmadığını kontrol eder
 * Her iki koşul da sağlanıyorsa true, aksi takdirde false döndürür
 */
export async function checkSalonHasProductsFeature(salonId: string) {
  const { data, error } = await supabaseClient
    .rpc('check_salon_has_products_feature', { p_salon_id: salonId });

  if (error) throw error;
  return data;
}
```

### 3. Public Access Index Güncelleme

`src/lib/db/public/index.ts` dosyası güncellendi:

```typescript
// Export all public access functions
export * from './salons';
export * from './barbers';
export * from './services';
export * from './working-hours';
export * from './barber-working-hours';
export * from './holidays';
export * from './products';
export * from './appointments';
export * from './salon-features';
```

### 4. SalonContext Güncelleme

`src/contexts/SalonContext.tsx` dosyası güncellendi:

- İki ayrı kontrol (hasProducts ve hasProductFeature) yerine tek bir RPC fonksiyonu kullanıldı
- Gereksiz API çağrıları kaldırıldı
- showProductsMenu değeri RPC fonksiyonunun sonucuna göre belirlendi

```typescript
// Önceki kod
import { products } from "@/lib/db"
import { subscriptions } from "@/lib/db"
import { planHasFeature } from "@/lib/db/subscription-plans"

// Yeni kod
import { publicAccess } from "@/lib/db"

// Önceki kod
// Salon'un ürünlerini kontrol et
const productList = await products.getActiveProducts(salonInfo.salonId)
setHasProducts(productList.length > 0)

// Salon'un abonelik planını kontrol et
const plan = await subscriptions.getSalonSubscriptionPlan(salonInfo.salonId)
setHasProductFeature(plan ? planHasFeature(plan, "product_management") : false)

// Yeni kod
// Salon'un ürün özelliğini ve ürünlerini kontrol et
const hasProductsFeature = await publicAccess.checkSalonHasProductsFeature(salonInfo.salonId)
setHasProducts(hasProductsFeature)
setHasProductFeature(hasProductsFeature)
```

## Faydalar

1. **Performans İyileştirmesi**: İki ayrı API çağrısı yerine tek bir RPC fonksiyonu kullanılarak performans iyileştirildi.
2. **Güvenlik**: Tüm kontroller veritabanı tarafında yapılarak güvenlik artırıldı.
3. **Kod Temizliği**: Client tarafındaki kod daha temiz ve anlaşılır hale getirildi.
4. **Bakım Kolaylığı**: Gelecekte yapılacak değişiklikler için tek bir noktada güncelleme yapılması yeterli olacak.
