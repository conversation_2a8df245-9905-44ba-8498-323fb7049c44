// Authentication Middleware for SalonFlow API Routes
// Validates JWT tokens and salon ownership permissions

import { NextRequest } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export interface AuthenticatedUser {
  id: string;
  email: string;
  role?: string;
  salonId?: string;
  isAdmin: boolean;
}

export interface AuthResult {
  success: boolean;
  user?: AuthenticatedUser;
  error?: string;
  statusCode?: number;
}

export interface SalonAccessResult {
  success: boolean;
  hasAccess: boolean;
  error?: string;
  statusCode?: number;
}

/**
 * Authenticate user from JWT token in request
 */
export async function authenticateUser(request: NextRequest): Promise<AuthResult> {
  try {
    // Check for Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // Try to get user from cookies (for same-origin requests)
      const supabase = createRouteHandlerClient({ cookies });
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        return {
          success: false,
          error: 'Authentication required',
          statusCode: 401
        };
      }

      // Get user profile with role information
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('role, salon_id')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching user profile:', profileError);
        return {
          success: false,
          error: 'Failed to fetch user profile',
          statusCode: 500
        };
      }

      // Check if user is admin
      const { data: isAdminResult } = await supabase.rpc('is_admin');
      const isAdmin = isAdminResult || false;

      return {
        success: true,
        user: {
          id: user.id,
          email: user.email || '',
          role: profile?.role,
          salonId: profile?.salon_id,
          isAdmin
        }
      };
    }

    // Extract token from Authorization header
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Validate token with Supabase
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return {
        success: false,
        error: 'Invalid or expired token',
        statusCode: 401
      };
    }

    // Get user profile with role information
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('role, salon_id')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return {
        success: false,
        error: 'Failed to fetch user profile',
        statusCode: 500
      };
    }

    // Check if user is admin
    const { data: isAdminResult } = await supabase.rpc('is_admin');
    const isAdmin = isAdminResult || false;

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email || '',
        role: profile?.role,
        salonId: profile?.salon_id,
        isAdmin
      }
    };

  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: 'Authentication failed',
      statusCode: 500
    };
  }
}

/**
 * Check if user has access to a specific salon
 */
export async function checkSalonAccess(
  user: AuthenticatedUser,
  salonId: string
): Promise<SalonAccessResult> {
  try {
    // Admins have access to all salons
    if (user.isAdmin) {
      return {
        success: true,
        hasAccess: true
      };
    }

    // Check if user is the salon owner
    if (user.role === 'owner' && user.salonId === salonId) {
      return {
        success: true,
        hasAccess: true
      };
    }

    // Check if user is staff member of the salon
    if (user.role === 'staff') {
      const supabase = createRouteHandlerClient({ cookies });
      const { data: barber, error } = await supabase
        .from('barbers')
        .select('salon_id')
        .eq('user_id', user.id)
        .eq('salon_id', salonId)
        .single();

      if (error) {
        console.error('Error checking staff access:', error);
        return {
          success: false,
          hasAccess: false,
          error: 'Failed to verify salon access',
          statusCode: 500
        };
      }

      return {
        success: true,
        hasAccess: !!barber
      };
    }

    // No access for other roles
    return {
      success: true,
      hasAccess: false
    };

  } catch (error) {
    console.error('Salon access check error:', error);
    return {
      success: false,
      hasAccess: false,
      error: 'Failed to check salon access',
      statusCode: 500
    };
  }
}

/**
 * Validate that appointment belongs to the salon
 */
export async function validateAppointmentAccess(
  salonId: string,
  appointmentId: string
): Promise<{ success: boolean; error?: string; statusCode?: number }> {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Check if appointment exists and belongs to the salon
    const { data: appointment, error } = await supabase
      .from('appointments')
      .select('salon_id')
      .eq('id', appointmentId)
      .eq('salon_id', salonId)
      .single();

    if (error) {
      console.error('Error validating appointment access:', error);
      return {
        success: false,
        error: 'Appointment not found or access denied',
        statusCode: 404
      };
    }

    if (!appointment) {
      return {
        success: false,
        error: 'Appointment not found or access denied',
        statusCode: 404
      };
    }

    return { success: true };

  } catch (error) {
    console.error('Appointment validation error:', error);
    return {
      success: false,
      error: 'Failed to validate appointment access',
      statusCode: 500
    };
  }
}

/**
 * Extract client IP address from request
 */
export function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to connection remote address
  return 'unknown';
}

/**
 * Sanitize and validate input data
 */
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }

  if (typeof input === 'object' && input !== null) {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }

  return input;
}
