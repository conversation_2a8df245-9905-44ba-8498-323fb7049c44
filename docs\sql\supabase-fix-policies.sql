-- Önce sorunlu politikaları kaldıralım
DROP POLICY IF EXISTS "Staff can view their salon" ON salons;
DROP POLICY IF EXISTS "Staff can view salon barbers" ON barbers;
DROP POLICY IF EXISTS "Staff can update their own profile" ON barbers;
DROP POLICY IF EXISTS "Staff can view all barber working hours" ON barber_working_hours;
DROP POLICY IF EXISTS "Staff can manage their own working hours" ON barber_working_hours;

-- Anoni<PERSON> erişim için politikalar ekleyelim
-- Salons: Anoni<PERSON> kullanıcılar salonları görebilir
CREATE POLICY "Anyone can view salons" ON salons
  FOR SELECT
  USING (true);

-- Barbers: Anoni<PERSON> kullanıcılar berberleri görebilir
CREATE POLICY "Anyone can view barbers" ON barbers
  FOR SELECT
  USING (true);

-- Services: Anonim kullanıcılar hizmetleri görebilir
CREATE POLICY "Anyone can view services" ON services
  FOR SELECT
  USING (true);

-- Barber services: <PERSON><PERSON><PERSON> kullanıcılar berber hizmetlerini görebilir
CREATE POLICY "Anyone can view barber services" ON barber_services
  FOR SELECT
  USING (true);

-- Working hours: Anonim kullanıcılar çalışma saatlerini görebilir
CREATE POLICY "Anyone can view working hours" ON working_hours
  FOR SELECT
  USING (true);

-- Barber working hours: Anonim kullanıcılar berber çalışma saatlerini görebilir
CREATE POLICY "Anyone can view barber working hours" ON barber_working_hours
  FOR SELECT
  USING (true);

-- Holidays: Anonim kullanıcılar tatil günlerini görebilir
CREATE POLICY "Anyone can view holidays" ON holidays
  FOR SELECT
  USING (true);

-- Şimdi staff politikalarını düzeltelim (özyineleme olmadan)
-- Önce tüm salon politikalarını kaldıralım
DROP POLICY IF EXISTS "Salon owners can manage their own salons" ON salons;
DROP POLICY IF EXISTS "Staff can view their salon" ON salons;
DROP POLICY IF EXISTS "Anyone can view salons" ON salons;

-- Salon politikalarını yeniden oluşturalım
-- Salon sahipleri kendi salonlarını yönetebilir
CREATE POLICY "Salon owners can manage their own salons" ON salons
  USING (owner_id = auth.uid())
  WITH CHECK (owner_id = auth.uid());

-- Herkes salonları görüntüleyebilir (anonim erişim için)
CREATE POLICY "Anyone can view salons" ON salons
  FOR SELECT
  USING (true);

-- Staff kendi profilini güncelleme
CREATE POLICY "Staff can update their own profile" ON barbers
  FOR UPDATE
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Staff kendi çalışma saatlerini yönetme
CREATE POLICY "Staff can manage their own working hours" ON barber_working_hours
  USING (EXISTS (
    SELECT 1 FROM barbers
    WHERE barbers.id = barber_working_hours.barber_id
    AND barbers.user_id = auth.uid()
  ))
  WITH CHECK (EXISTS (
    SELECT 1 FROM barbers
    WHERE barbers.id = barber_working_hours.barber_id
    AND barbers.user_id = auth.uid()
  ));
