"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { toast } from "sonner"
import { supabase } from "@/lib/supabase"
import { getSalonInfoFromSlug } from "@/lib/utils/salon-resolver"
import { publicAccess } from "@/lib/db"
import { getPublicSalonContent, getPublicSalonTestimonials, parseSalonContent } from "@/lib/db/public"
import { SalonContent, SalonTestimonial } from "@/lib/db/types"

// Salon tipi
type Salon = {
  id: string
  name: string
  address: string
  phone?: string
  email?: string
  owner_id: string
  slug?: string
}

// Context tipi
type SalonContextType = {
  salon: Salon | null
  salonId: string | null
  slug: string | null
  isLoading: boolean
  hasProducts: boolean
  hasProductFeature: boolean
  showProductsMenu: boolean
  error: Error | null
  dataLoaded: boolean
  // Content data
  salonContent: ReturnType<typeof parseSalonContent> | null
  testimonials: SalonTestimonial[]
  contentLoading: boolean
}

// Default context değerleri
const defaultContext: SalonContextType = {
  salon: null,
  salonId: null,
  slug: null,
  isLoading: true,
  hasProducts: false,
  hasProductFeature: false,
  showProductsMenu: false,
  error: null,
  dataLoaded: false,
  salonContent: null,
  testimonials: [],
  contentLoading: true
}

// Context oluşturma
const SalonContext = createContext<SalonContextType>(defaultContext)

// Provider bileşeni
export function SalonProvider({
  children,
  slug
}: {
  children: ReactNode
  slug: string
}) {
  const [salon, setSalon] = useState<Salon | null>(null)
  const [salonId, setSalonId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasProducts, setHasProducts] = useState(false)
  const [hasProductFeature, setHasProductFeature] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [dataLoaded, setDataLoaded] = useState(false)
  // Content states
  const [salonContent, setSalonContent] = useState<ReturnType<typeof parseSalonContent> | null>(null)
  const [testimonials, setTestimonials] = useState<SalonTestimonial[]>([])
  const [contentLoading, setContentLoading] = useState(true)

  // Load salon content data
  const loadSalonContent = async (salonId: string) => {
    try {
      setContentLoading(true)
      console.log("Salon content yükleniyor...", salonId)

      const [contentData, testimonialsData] = await Promise.all([
        getPublicSalonContent(salonId),
        getPublicSalonTestimonials(salonId)
      ])

      const parsedContent = parseSalonContent(contentData)
      setSalonContent(parsedContent)
      setTestimonials(testimonialsData)

      console.log("Salon content yüklendi")
    } catch (error) {
      console.error("Salon content yüklenirken hata:", error)
      // Use default content on error
      setSalonContent(null)
      setTestimonials([])
    } finally {
      setContentLoading(false)
    }
  }

  // Salon bilgilerini yükle
  useEffect(() => {
    // Slug değiştiğinde verileri yeniden yükle
    async function loadSalonInfo() {
      // Eğer aynı slug için veriler zaten yüklenmişse, tekrar yükleme
      if (dataLoaded && salon && salonId && slug === salon.slug) {
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      try {
        console.log("Salon bilgileri yükleniyor...", slug)
        // Slug'dan salon ID'sini al
        const salonInfo = await getSalonInfoFromSlug(slug, supabase)

        if (!salonInfo) {
          toast.error("Salon bulunamadı.")
          setIsLoading(false)
          return
        }

        setSalonId(salonInfo.salonId)

        // Salon detaylarını yükle
        const { data, error } = await supabase
          .from('salons')
          .select('*')
          .eq('id', salonInfo.salonId)
          .single()

        if (error) throw error
        setSalon(data)

        // Salon'un ürün özelliğini ve ürünlerini kontrol et
        const hasProductsFeature = await publicAccess.checkSalonHasProductsFeature(salonInfo.salonId)
        setHasProducts(hasProductsFeature)
        setHasProductFeature(hasProductsFeature)

        // Verilerin yüklendiğini işaretle
        setDataLoaded(true)
        console.log("Salon bilgileri yüklendi:", data.name)

        // Load content data after salon is loaded
        loadSalonContent(salonInfo.salonId)
      } catch (error) {
        console.error("Salon bilgileri yüklenirken hata:", error)
        setError(error as Error)
        toast.error("Salon bilgileri yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    if (slug) {
      loadSalonInfo()
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [slug])

  // Ürünler menü öğesini göster/gizle
  const showProductsMenu = hasProducts && !isLoading

  // Context değeri
  const contextValue = {
    salon,
    salonId,
    slug,
    isLoading,
    hasProducts,
    hasProductFeature,
    showProductsMenu,
    error,
    dataLoaded,
    salonContent,
    testimonials,
    contentLoading
  }

  return (
    <SalonContext.Provider value={contextValue}>
      {children}
    </SalonContext.Provider>
  )
}

// Hook
export function useSalon() {
  const context = useContext(SalonContext)
  if (context === undefined) {
    throw new Error("useSalon must be used within a SalonProvider")
  }
  return context
}
