import { Service, ServiceInsert, ServiceUpdate } from './types';
import { supabaseClient } from '../supabase-singleton';

const supabase = supabaseClient;

/**
 * Get all services for a salon
 */
export async function getServices(salonId: string) {
  const { data, error } = await supabase
    .from('services')
    .select('*')
    .eq('salon_id', salonId)
    .order('name');
  
  if (error) throw error;
  return data as Service[];
}

/**
 * Get a service by ID
 */
export async function getServiceById(id: string) {
  const { data, error } = await supabase
    .from('services')
    .select('*')
    .eq('id', id)
    .single();
  
  if (error) throw error;
  return data as Service;
}

/**
 * Create a new service
 */
export async function createService(service: ServiceInsert) {
  const { data, error } = await supabase
    .from('services')
    .insert(service)
    .select()
    .single();
  
  if (error) throw error;
  return data as Service;
}

/**
 * Update a service
 */
export async function updateService({ id, ...service }: ServiceUpdate) {
  const { data, error } = await supabase
    .from('services')
    .update(service)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data as Service;
}

/**
 * Delete a service
 */
export async function deleteService(id: string) {
  const { error } = await supabase
    .from('services')
    .delete()
    .eq('id', id);
  
  if (error) throw error;
  return true;
}

/**
 * Get services that a specific barber can perform
 */
export async function getServicesForBarber(salonId: string, barberId: string) {
  const { data, error } = await supabase
    .from('services')
    .select(`
      *,
      barber_services!inner(barber_id)
    `)
    .eq('salon_id', salonId)
    .eq('barber_services.barber_id', barberId)
    .order('name');
  
  if (error) throw error;
  return data as Service[];
}
