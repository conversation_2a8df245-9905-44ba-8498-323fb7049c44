"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ArrowLeft, Loader2 } from "lucide-react"
import { toast } from "sonner"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useUser } from "@/contexts/UserContext"
import { TransactionForm } from "@/components/transaction-form"
import { FinanceTransaction } from "@/lib/db/types"
import { financeTransactions } from "@/lib/db"

interface EditTransactionPageProps {
  params: {
    id: string
  }
}

export default function EditTransactionPage({ params }: EditTransactionPageProps) {
  const id = params.id as string

  const router = useRouter()
  const { salon } = useUser()
  const salonId = salon?.id

  const [transaction, setTransaction] = useState<FinanceTransaction | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch transaction data
  useEffect(() => {
    if (salonId && id) {
      fetchTransaction()
    }
  }, [salonId, id])

  const fetchTransaction = async () => {
    try {
      setIsLoading(true)
      const data = await financeTransactions.getFinanceTransactionById(id);

      // Check if the transaction belongs to the current salon
      if (data.salon_id !== salonId) {
        throw new Error("Bu işleme erişim izniniz yok")
      }

      setTransaction(data)
    } catch (error) {
      console.error("Error fetching transaction:", error)
      setError(error instanceof Error ? error.message : "İşlem yüklenirken bir hata oluştu")
      toast.error(error instanceof Error ? error.message : "İşlem yüklenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/finance/transactions">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">İşlemi Düzenle</h1>
        </div>
      </header>

      <Card>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>İşlem yükleniyor...</span>
            </div>
          ) : error ? (
            <div className="py-8 text-center">
              <p className="text-destructive">{error}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => router.push("/dashboard/finance/transactions")}
              >
                İşlemler Sayfasına Dön
              </Button>
            </div>
          ) : transaction ? (
            <TransactionForm
              transaction={transaction}
              onSuccess={() => {
                router.push("/dashboard/finance/transactions")
                toast.success("İşlem başarıyla güncellendi")
              }}
            />
          ) : (
            <div className="py-8 text-center">
              <p className="text-destructive">İşlem bulunamadı</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => router.push("/dashboard/finance/transactions")}
              >
                İşlemler Sayfasına Dön
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
