"use client"

import { Calendar } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useBookingModalSafe } from "@/contexts/BookingModalContext"
import { useIsPreview } from "@/contexts/PreviewContext"
import { cn } from "@/lib/utils"

interface BookingButtonProps {
  buttonText?: string
  buttonSize?: "default" | "sm" | "lg" | "icon"
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  buttonClassName?: string
  triggerClassName?: string
  showIcon?: boolean
}

export function BookingButton({
  buttonText = "Randevu Al",
  buttonSize = "default",
  buttonVariant = "default",
  buttonClassName,
  triggerClassName,
  showIcon = true
}: BookingButtonProps) {
  const isPreview = useIsPreview()
  const bookingModal = useBookingModalSafe()

  const handleClick = () => {
    if (isPreview) {
      // In preview mode, show a message instead of opening modal
      alert("Bu bir önizleme modudur. Gerçek randevu almak için sayfayı ziyaret edin.")
      return
    }

    if (bookingModal) {
      bookingModal.openModal()
    }
  }

  return (
    <Button
      size={buttonSize}
      variant={buttonVariant}
      className={cn(
        buttonClassName,
        isPreview && "opacity-75 cursor-default"
      )}
      onClick={handleClick}
    >
      {showIcon && <Calendar className={cn("w-4 h-4 mr-2", triggerClassName)} />}
      {buttonText}
      {isPreview && <span className="ml-2 text-xs">(Önizleme)</span>}
    </Button>
  )
}
