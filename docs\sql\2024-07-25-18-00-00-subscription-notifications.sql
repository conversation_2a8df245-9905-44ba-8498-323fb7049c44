-- SalonFlow Abonelik Bildirimleri Trigger'ı
-- Oluşturulma Tarihi: 2024-07-25

-- Abonelik bildirimleri için trigger
CREATE OR REPLACE FUNCTION create_subscription_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  notification_title TEXT;
  notification_message TEXT;
  notification_type TEXT := 'subscription_update';
BEGIN
  -- Salon sahibi ID'sini al
  SELECT owner_id INTO salon_owner_id FROM salons WHERE id = NEW.salon_id;

  -- <PERSON><PERSON><PERSON><PERSON> içeriğini belirle
  IF NEW.status = 'trial' AND (OLD IS NULL OR OLD.status != 'trial') THEN
    notification_title := 'Deneme Süresi Başladı';
    notification_message := 'Abonelik deneme süreniz başladı. 14 gün boyunca tüm özellikleri ücretsiz kullanabilirsiniz.';
  ELSIF NEW.status = 'active' AND OLD.status = 'trial' THEN
    notification_title := 'Aboneliğiniz Aktifleştirildi';
    notification_message := 'Deneme süreniz sona erdi ve aboneliğiniz aktifleştirildi.';
  ELSIF NEW.status = 'past_due' AND OLD.status != 'past_due' THEN
    notification_title := 'Ödeme Hatırlatması';
    notification_message := 'Abonelik ödemesi gecikti. Lütfen en kısa sürede ödemenizi yapın.';
  ELSIF NEW.status = 'suspended' AND OLD.status != 'suspended' THEN
    notification_title := 'Abonelik Askıya Alındı';
    notification_message := 'Ödeme yapılmadığı için aboneliğiniz askıya alındı.';
  ELSIF NEW.status = 'cancelled' AND OLD.status != 'cancelled' THEN
    notification_title := 'Abonelik İptal Edildi';
    notification_message := 'Aboneliğiniz iptal edildi.';
  ELSIF NEW.plan_id != OLD.plan_id THEN
    notification_title := 'Abonelik Planı Değişti';
    notification_message := 'Abonelik planınız değiştirildi.';
  END IF;

  -- Bildirim oluştur
  IF notification_title IS NOT NULL THEN
    INSERT INTO notifications (
      salon_id,
      user_id,
      type,
      title,
      message,
      read,
      data
    ) VALUES (
      NEW.salon_id,
      salon_owner_id,
      notification_type,
      notification_title,
      notification_message,
      FALSE,
      jsonb_build_object('subscription_id', NEW.id, 'status', NEW.status)
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ı salon_subscriptions tablosuna bağla
DROP TRIGGER IF EXISTS subscription_notification_trigger ON salon_subscriptions;

CREATE TRIGGER subscription_notification_trigger
AFTER INSERT OR UPDATE OF status, plan_id
ON salon_subscriptions
FOR EACH ROW
EXECUTE FUNCTION create_subscription_notification();

-- Ödeme bildirimleri için trigger
CREATE OR REPLACE FUNCTION create_payment_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_id UUID;
  salon_owner_id UUID;
  notification_title TEXT;
  notification_message TEXT;
  notification_type TEXT := 'payment_update';
BEGIN
  -- Salon ID'sini al
  SELECT salon_id INTO salon_id FROM salon_subscriptions WHERE id = NEW.subscription_id;
  
  -- Salon sahibi ID'sini al
  SELECT owner_id INTO salon_owner_id FROM salons WHERE id = salon_id;

  -- Bildirim içeriğini belirle
  IF NEW.status = 'completed' AND (OLD IS NULL OR OLD.status != 'completed') THEN
    notification_title := 'Ödeme Tamamlandı';
    notification_message := format('%.2f TL tutarındaki ödemeniz başarıyla tamamlandı.', NEW.amount);
  ELSIF NEW.status = 'pending' AND (OLD IS NULL OR OLD.status != 'pending') THEN
    notification_title := 'Ödeme Bekleniyor';
    notification_message := format('%.2f TL tutarındaki ödemeniz bekleniyor.', NEW.amount);
  ELSIF NEW.status = 'failed' AND (OLD IS NULL OR OLD.status != 'failed') THEN
    notification_title := 'Ödeme Başarısız';
    notification_message := format('%.2f TL tutarındaki ödemeniz başarısız oldu.', NEW.amount);
  END IF;

  -- Bildirim oluştur
  IF notification_title IS NOT NULL THEN
    INSERT INTO notifications (
      salon_id,
      user_id,
      type,
      title,
      message,
      read,
      data
    ) VALUES (
      salon_id,
      salon_owner_id,
      notification_type,
      notification_title,
      notification_message,
      FALSE,
      jsonb_build_object('payment_id', NEW.id, 'status', NEW.status, 'amount', NEW.amount)
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger'ı subscription_payments tablosuna bağla
DROP TRIGGER IF EXISTS payment_notification_trigger ON subscription_payments;

CREATE TRIGGER payment_notification_trigger
AFTER INSERT OR UPDATE OF status
ON subscription_payments
FOR EACH ROW
EXECUTE FUNCTION create_payment_notification();
