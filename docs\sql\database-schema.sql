-- SalonFlow Database Schema

-- Salons table
CREATE TABLE salons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  address TEXT,
  phone TEXT,
  email TEXT,
  website TEXT,
  description TEXT,
  logo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  owner_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Working hours table
CREATE TABLE working_hours (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday, 6 = Saturday
  open_time TIME NOT NULL,
  close_time TIME NOT NULL,
  is_closed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (salon_id, day_of_week)
);

-- Holidays/Days off table
CREATE TABLE holidays (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (salon_id, date)
);

-- Barbers/Staff table
CREATE TABLE barbers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  profile_image_url TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  invitation_token TEXT,
  invitation_sent_at TIMESTAMP WITH TIME ZONE,
  invitation_accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Barber working hours (for individual barber schedules)
CREATE TABLE barber_working_hours (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  barber_id UUID REFERENCES barbers(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6),
  open_time TIME NOT NULL,
  close_time TIME NOT NULL,
  is_closed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (barber_id, day_of_week)
);

-- Services table
CREATE TABLE services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  duration INTEGER NOT NULL, -- Duration in minutes
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Barber services (which barber can perform which services)
CREATE TABLE barber_services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  barber_id UUID REFERENCES barbers(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (barber_id, service_id)
);

-- Customers table
CREATE TABLE customers (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  surname TEXT NOT NULL,
  email TEXT,
  phone TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Appointments table
CREATE TABLE appointments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  barber_id UUID REFERENCES barbers(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('booked', 'completed', 'cancelled', 'no-show')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Salon subscriptions table (for tiered pricing)
CREATE TABLE salon_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  salon_id UUID REFERENCES salons(id) ON DELETE CASCADE,
  plan TEXT NOT NULL CHECK (plan IN ('basic', 'standard', 'premium')),
  start_date DATE NOT NULL,
  end_date DATE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security Policies

-- Salons: Owners can only see and manage their own salons, staff can view their salon
ALTER TABLE salons ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage their own salons" ON salons
  USING (owner_id = auth.uid())
  WITH CHECK (owner_id = auth.uid());
CREATE POLICY "Staff can view their salon" ON salons
  FOR SELECT
  USING (id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Working hours: Salon owners can manage their salon's working hours, staff can view
ALTER TABLE working_hours ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage their salon's working hours" ON working_hours
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Staff can view salon working hours" ON working_hours
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Holidays: Salon owners can manage their salon's holidays, staff can view
ALTER TABLE holidays ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage their salon's holidays" ON holidays
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Staff can view salon holidays" ON holidays
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Barbers: Salon owners can manage their salon's barbers, staff can view and update themselves
ALTER TABLE barbers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage their salon's barbers" ON barbers
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Staff can view salon barbers" ON barbers
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));
CREATE POLICY "Staff can update their own profile" ON barbers
  FOR UPDATE
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid() AND salon_id = (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Barber working hours: Salon owners can manage all barbers' working hours, staff can manage their own
ALTER TABLE barber_working_hours ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage their barbers' working hours" ON barber_working_hours
  USING (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())))
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())));
CREATE POLICY "Staff can view all barber working hours" ON barber_working_hours
  FOR SELECT
  USING (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid())));
CREATE POLICY "Staff can manage their own working hours" ON barber_working_hours
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

-- Services: Salon owners can manage their salon's services, staff can view
ALTER TABLE services ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage their salon's services" ON services
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Staff can view salon services" ON services
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Barber services: Salon owners can manage their barbers' services, staff can view
ALTER TABLE barber_services ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage their barbers' services" ON barber_services
  USING (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())))
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())));
CREATE POLICY "Staff can view barber services" ON barber_services
  FOR SELECT
  USING (barber_id IN (SELECT id FROM barbers WHERE salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid())));

-- Customers: Salon owners and staff can see customers of their salon
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can see salon customers" ON customers
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Staff can view salon customers" ON customers
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Appointments: Salon owners can manage all appointments, staff can manage their own appointments
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can manage appointments at their salon" ON appointments
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
CREATE POLICY "Staff can view all salon appointments" ON appointments
  FOR SELECT
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));
CREATE POLICY "Staff can manage their own appointments" ON appointments
  FOR INSERT
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));
CREATE POLICY "Staff can update their own appointments" ON appointments
  FOR UPDATE
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()))
  WITH CHECK (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));
CREATE POLICY "Staff can delete their own appointments" ON appointments
  FOR DELETE
  USING (barber_id IN (SELECT id FROM barbers WHERE user_id = auth.uid()));

-- Salon subscriptions: Only salon owners can see their own subscriptions
ALTER TABLE salon_subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Salon owners can see their own subscriptions" ON salon_subscriptions
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));
