"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ClientBookingForm } from "@/components/client/client-booking-form"
import { motion, AnimatePresence } from "framer-motion"

interface ClientBookingModalProps {
  salonId: string
  buttonText?: string
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  buttonSize?: "default" | "sm" | "lg" | "icon"
  buttonClassName?: string
  dialogTitle?: string
  dialogDescription?: string
}

export function ClientBookingModal({
  salonId,
  buttonText = "Randevu Al",
  buttonVariant = "default",
  buttonSize = "default",
  buttonClassName,
  dialogTitle = "Randevu Oluştur",
  dialogDescription
}: ClientBookingModalProps) {
  const [open, setOpen] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)
  const [formKey, setFormKey] = useState(0) // Form'u yeniden oluşturmak için key

  const handleBookingSuccess = () => {
    setBookingSuccess(true)
  }

  const handleOpenChange = (open: boolean) => {
    // Modalın açık/kapalı durumunu güncelle
    setOpen(open)

    // Eğer modal kapatılıyorsa
    if (!open) {
      // Başarı durumunu sıfırla
      setBookingSuccess(false)

      // Modal kapanma animasyonu tamamlandıktan sonra formu sıfırla
      setTimeout(() => {
        // Form bileşenini tamamen yeniden oluşturmak için key değerini artır
        setFormKey(prevKey => prevKey + 1)
      }, 300)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          className={buttonClassName}
        >
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto p-0 gap-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-xl font-bold">{dialogTitle}</DialogTitle>
        </DialogHeader>

        <AnimatePresence mode="wait">
          {bookingSuccess ? (
            <motion.div
              key="success"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="p-6 space-y-6"
            >
              <div className="flex flex-col items-center justify-center text-center space-y-4">
                <div className="rounded-full bg-primary/10 p-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-primary"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold">Randevunuz Oluşturuldu!</h3>
                <p className="text-muted-foreground">
                  Randevunuz başarıyla oluşturuldu. Teşekkür ederiz!
                </p>
                <p className="text-sm text-muted-foreground/70 italic mt-2">
                  Randevunuza gelemeyecekseniz lütfen önceden haber veriniz.
                </p>
              </div>
              <div className="flex justify-center pt-4">
                <Button
                  onClick={() => handleOpenChange(false)}
                  size="lg"
                  className="w-full sm:w-auto px-8"
                >
                  Kapat
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="form"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <ClientBookingForm
                key={formKey}
                salonId={salonId}
                onSuccess={handleBookingSuccess}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  )
}
