"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { Plus, Search, ShoppingBag, HardDrive } from "lucide-react"
import { toast } from "sonner"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Skeleton } from "@/components/ui/skeleton"
import { useUser } from "@/contexts/UserContext"
import { useSubscription } from "@/contexts/SubscriptionContext"
import { products } from "@/lib/db"
import { Product } from "@/lib/db/types"
import { getSalonStorageUsage } from "@/lib/storage"

export default function ProductsPage() {
  const { salonId } = useUser()
  const { features, hasFeature } = useSubscription()

  const [productList, setProductList] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [storageUsage, setStorageUsage] = useState(0)
  const [storagePercentage, setStoragePercentage] = useState(0)

  // Ürünleri yükle
  const loadProducts = useCallback(async () => {
    if (!salonId) return

    try {
      setIsLoading(true)
      const data = await products.getProducts(salonId)
      setProductList(data)
      setFilteredProducts(data)
    } catch (error) {
      console.error("Ürünler yüklenirken hata:", error)
      toast.error("Ürünler yüklenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }, [salonId])

  // Depolama kullanımını yükle
  const loadStorageUsage = useCallback(async () => {
    if (!salonId) return

    try {
      const usage = await getSalonStorageUsage(salonId)
      const usageMB = usage / (1024 * 1024)
      setStorageUsage(usageMB)

      // Yüzde hesapla
      const percentage = features.storageLimitMB > 0
        ? Math.min(100, (usageMB / features.storageLimitMB) * 100)
        : 100
      setStoragePercentage(percentage)
    } catch (error) {
      console.error("Depolama kullanımı yüklenirken hata:", error)
    }
  }, [salonId, features.storageLimitMB])

  // Sayfa yüklendiğinde ürünleri ve depolama kullanımını getir
  useEffect(() => {
    loadProducts()
    loadStorageUsage()
  }, [loadProducts, loadStorageUsage])

  // Arama işlevi
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredProducts(productList)
    } else {
      const filtered = productList.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.category && product.category.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      setFilteredProducts(filtered)
    }
  }, [searchQuery, productList])

  // Ürün durumunu değiştir
  const handleToggleStatus = async (id: string, currentStatus: boolean) => {
    try {
      await products.toggleProductStatus(id, !currentStatus)
      toast.success(`Ürün ${!currentStatus ? "aktif" : "pasif"} duruma getirildi`)
      loadProducts()
    } catch (error) {
      console.error("Ürün durumu değiştirilirken hata:", error)
      toast.error("Ürün durumu değiştirilirken bir hata oluştu")
    }
  }

  // Ürün sil
  const handleDeleteProduct = async (id: string) => {
    if (!confirm("Bu ürünü silmek istediğinizden emin misiniz?")) return

    try {
      await products.deleteProduct(id)
      toast.success("Ürün başarıyla silindi")
      loadProducts()
    } catch (error) {
      console.error("Ürün silinirken hata:", error)
      toast.error("Ürün silinirken bir hata oluştu")
    }
  }

  // Ürün yönetimi özelliği kontrolü
  if (!hasFeature("product_management")) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Ürün Yönetimi</h1>
          </div>
        </header>

        <div className="flex flex-col items-center justify-center h-64 space-y-6">
          <div className="text-center space-y-2">
            <h2 className="text-2xl font-bold">Bu özellik aboneliğinizde bulunmuyor</h2>
            <p className="text-muted-foreground">Ürün yönetimi özelliğini kullanmak için aboneliğinizi yükseltmeniz gerekiyor.</p>
          </div>
          <div className="flex flex-col items-center space-y-4">
            <Button size="lg" asChild className="w-64">
              <Link href="/dashboard/subscription/upgrade">
                <ShoppingBag className="mr-2 h-5 w-5" />
                Aboneliği Yükselt
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Ürün Yönetimi</h1>
        </div>
      </header>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center gap-2">
              <HardDrive className="h-5 w-5 text-muted-foreground" />
              <h3 className="font-medium">Depolama Kullanımı</h3>
            </div>
            <span className="text-sm text-muted-foreground">
              {storageUsage.toFixed(2)} MB / {features.storageLimitMB} MB
            </span>
          </div>
          <Progress value={storagePercentage} className="h-2 mb-2" />
          <p className="text-sm text-muted-foreground">
            {features.storageLimitMB === 0 ? (
              "Aboneliğinizde depolama alanı bulunmuyor. Görsel yüklemek için aboneliğinizi yükseltin."
            ) : storagePercentage >= 90 ? (
              "Depolama alanınız neredeyse doldu. Daha fazla alan için aboneliğinizi yükseltin."
            ) : (
              `Kalan depolama alanı: ${(features.storageLimitMB - storageUsage).toFixed(2)} MB`
            )}
          </p>
        </CardContent>
      </Card>

      <div className="flex justify-between items-center mb-6">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Ürün ara..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button asChild>
          <Link href="/dashboard/products/new">
            <Plus className="mr-2 h-4 w-4" />
            Yeni Ürün
          </Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-3">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </div>
      ) : (
        <Table>
          <TableCaption>
            {filteredProducts.length === 0
              ? "Henüz ürün bulunmuyor"
              : `Toplam ${filteredProducts.length} ürün`}
          </TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Ürün Adı</TableHead>
              <TableHead>Kategori</TableHead>
              <TableHead className="text-right">Fiyat</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProducts.map((product) => (
              <TableRow key={product.id}>
                <TableCell className="font-medium">{product.name}</TableCell>
                <TableCell>{product.category || "-"}</TableCell>
                <TableCell className="text-right">
                  {product.price ? `${product.price} ₺` : "-"}
                </TableCell>
                <TableCell>
                  <Badge variant={product.is_active ? "default" : "secondary"}>
                    {product.is_active ? "Aktif" : "Pasif"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        İşlemler
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ürün İşlemleri</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/products/${product.id}`}>
                          Düzenle
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleToggleStatus(product.id, product.is_active)}>
                        {product.is_active ? "Pasif Yap" : "Aktif Yap"}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive"
                        onClick={() => handleDeleteProduct(product.id)}
                      >
                        Sil
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  )
}
