-- Remove Telegram HTTP Triggers for SalonFlow
-- Date: 2025-05-25-06-45-00
-- Description: Remove HTTP-based database triggers and move notification logic to application layer

-- Drop existing Telegram notification triggers
DROP TRIGGER IF EXISTS trigger_telegram_new_appointment ON appointments;
DROP TRIGGER IF EXISTS trigger_telegram_cancelled_appointment ON appointments;
DROP TRIGGER IF EXISTS trigger_telegram_updated_appointment ON appointments;

-- Drop the trigger functions
DROP FUNCTION IF EXISTS send_telegram_new_appointment_notification();
DROP FUNCTION IF EXISTS send_telegram_cancelled_appointment_notification();
DROP FUNCTION IF EXISTS send_telegram_updated_appointment_notification();
DROP FUNCTION IF EXISTS process_telegram_notifications();

-- Note: We're keeping the salon_telegram_settings table and related functions
-- as they are still needed for the application layer notification system

-- The following functions are still needed and should remain:
-- - is_telegram_enabled(UUID) - for checking if Telegram is enabled
-- - get_salon_telegram_settings(UUID) - for retrieving settings

-- HTTP extension can be removed if not used elsewhere
-- DROP EXTENSION IF EXISTS http;

COMMENT ON DATABASE postgres IS 'SalonFlow database - Telegram notifications moved to application layer';
