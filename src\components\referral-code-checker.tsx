"use client"

import { useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { useUser } from "@/contexts/UserContext"
import { referrals } from "@/lib/db"
import { toast } from "sonner"

export function ReferralCodeChecker() {
  const searchParams = useSearchParams()
  const { user } = useUser()
  const checkReferral = searchParams.get('check_referral')

  useEffect(() => {
    const saveReferralCode = async () => {
      if (!checkReferral || !user) return

      try {
        // Önce veritabanında bekleyen referans kodu var mı kontrol et
        const pendingReferral = await referrals.getPendingReferral(user.id)
        if (pendingReferral) {
          console.log(`Pending referral already exists for user ${user.id}: ${pendingReferral.referral_code}`)
          return
        }

        // Veritabanında yoksa, sessionStorage'dan kontrol et (ye<PERSON> me<PERSON>)
        const referralCode = sessionStorage.getItem('referralCode')
        if (!referralCode) return

        console.log(`Referral code ${referralCode} found in sessionStorage, saving to database...`)

        // Referans kodunu veritabanına kaydet
        await referrals.savePendingReferral(user.id, referralCode)

        // Referans kodunu sessionStorage'dan temizle
        sessionStorage.removeItem('referralCode')

        console.log(`Referral code ${referralCode} saved for user ${user.id}`)
        toast.success("Referans kodunuz kaydedildi. Salon oluşturduğunuzda otomatik olarak uygulanacaktır.")
      } catch (error) {
        console.error("Referans kodu kaydedilirken hata:", error)
      }
    }

    saveReferralCode()
  }, [checkReferral, user])

  // Bu komponent görünür bir şey render etmez
  return null
}
