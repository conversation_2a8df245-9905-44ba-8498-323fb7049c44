"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Plus, Mail, Phone, UserPlus, RefreshCw, CheckCircle2, Clock, AlertCircle } from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { barbers, barberWorkingHours } from "@/lib/db"
import type { Barber as BarberType } from "@/lib/db/types"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { formatDate } from "@/lib/utils"
import { useUser } from "@/contexts/UserContext"
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Define the form schema
const formSchema = z.object({
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }),
  email: z.string().email({
    message: "Lütfen geçerli bir e-posta adresi girin.",
  }),
  phone: z.string().optional(),
})

export default function StaffPage() {
  const [staffList, setStaffList] = useState<BarberType[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  // Artık salonId UserContext'ten geliyor
  const [editingBarber, setEditingBarber] = useState<BarberType | null>(null)
  const [sendingInvitation, setSendingInvitation] = useState<string | null>(null)

  // UserContext'ten salon bilgilerini al
  const { salonId, salonLoading } = useUser()

  // Abonelik özelliklerini al
  const { features, plan, canAddMoreStaff, isLoading: featuresLoading } = useSubscriptionFeatures()

  // Load barbers
  useEffect(() => {
    async function loadStaff() {
      if (!salonId || salonLoading) return

      setLoading(true)
      try {
        // Load barbers for this salon
        await loadBarbers(salonId)
      } catch (error) {
        console.error("Error:", error)
        toast.error("Bir hata oluştu.")
        setLoading(false)
      }
    }

    loadStaff()
  }, [salonId, salonLoading])

  // Load barbers
  async function loadBarbers(salonId: string) {
    try {
      const data = await barbers.getBarbers(salonId)
      setStaffList(data)
    } catch (error) {
      console.error("Error loading barbers:", error)
      toast.error("Berberler yüklenirken bir hata oluştu.")
    } finally {
      setLoading(false)
    }
  }

  // Initialize the form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
    },
  })

  // Set form values when editing
  useEffect(() => {
    if (editingBarber) {
      form.reset({
        name: editingBarber.name,
        email: editingBarber.email || "",
        phone: editingBarber.phone || "",
      })
      setShowForm(true)
    }
  }, [editingBarber, form])

  // Handle form submission
  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!salonId) {
      toast.error("Salon bilgisi bulunamadı.")
      return
    }

    try {
      if (editingBarber) {
        // Update existing barber
        await barbers.updateBarber({
          id: editingBarber.id,
          name: values.name,
          email: values.email,
          phone: values.phone || undefined,
        })

        toast.success("Berber başarıyla güncellendi!")
      } else {
        // Personel sayısı kısıtlamasını kontrol et
        if (!canAddMoreStaff(staffList.length)) {
          toast.error(`Abonelik planınız maksimum ${features.maxStaff} personel eklemenize izin veriyor. Daha fazla personel eklemek için planınızı yükseltin.`)
          return
        }

        // Create new barber
        const newBarber = await barbers.createBarber({
          salon_id: salonId,
          name: values.name,
          email: values.email,
          phone: values.phone || undefined,
        })

        // Set up default working hours for the new barber based on salon's working hours
        try {
          await barberWorkingHours.setupDefaultBarberWorkingHours(newBarber.id, salonId)
        } catch (error) {
          console.error("Error setting up default working hours:", error)
          // Don't show an error toast here, as the barber was created successfully
        }

        toast.success("Berber başarıyla eklendi!")

        // Ask if they want to send an invitation
        if (confirm("Bu personele davet e-postası göndermek ister misiniz?")) {
          await sendInvitation(newBarber.id, values.email)
        }
      }

      // Reload barbers
      await loadBarbers(salonId)

      // Reset form and state
      form.reset()
      setShowForm(false)
      setEditingBarber(null)
    } catch (error) {
      console.error("Error saving barber:", error)
      toast.error("Berber kaydedilirken bir hata oluştu.")
    }
  }

  // Send invitation email
  async function sendInvitation(barberId: string, email: string) {
    setSendingInvitation(barberId)
    try {
      const response = await fetch('/api/staff/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          barberId,
          email,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Davet gönderme başarısız oldu')
      }

      toast.success("Davet başarıyla gönderildi!")
      await loadBarbers(salonId!)
    } catch (error) {
      console.error("Error sending invitation:", error)
      toast.error("Davet gönderme başarısız oldu. Lütfen tekrar deneyin.")
    } finally {
      setSendingInvitation(null)
    }
  }

  // Handle barber deletion
  async function handleDelete(id: string) {
    if (!confirm("Bu berberi silmek istediğinizden emin misiniz?")) {
      return
    }

    try {
      await barbers.deleteBarber(id)
      toast.success("Berber başarıyla silindi!")

      // Reload barbers
      if (salonId) {
        await loadBarbers(salonId)
      }
    } catch (error) {
      console.error("Error deleting barber:", error)
      toast.error("Berber silinirken bir hata oluştu.")
    }
  }

  // Handle edit button click
  function handleEdit(barber: BarberType) {
    setEditingBarber(barber)
  }

  // Handle cancel button click
  function handleCancel() {
    setShowForm(false)
    setEditingBarber(null)
    form.reset()
  }

  // Get invitation status badge
  function getInvitationStatus(barber: BarberType) {
    if (barber.user_id) {
      return (
        <Badge className="bg-green-500">
          <CheckCircle2 className="h-3 w-3 mr-1" />
          Aktif
        </Badge>
      )
    } else if (barber.invitation_sent_at) {
      return (
        <Badge variant="outline" className="border-amber-500 text-amber-500">
          <Clock className="h-3 w-3 mr-1" />
          Beklemede
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline" className="border-muted-foreground text-muted-foreground">
          Davet Edilmedi
        </Badge>
      )
    }
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Personel</h1>
        </div>
        <Button
          onClick={() => {
            setEditingBarber(null)
            form.reset()
            setShowForm(!showForm)
          }}
          disabled={loading || (staffList.length >= features.maxStaff)}
        >
          <Plus className="mr-2 h-4 w-4" />
          Personel Ekle
        </Button>
      </header>

      {/* Abonelik Bilgisi Kartı */}
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-base">Abonelik Planı</CardTitle>
              <CardDescription>
                {plan ? plan.name : 'Yükleniyor...'}
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/subscription">
                Abonelik Detayları
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="text-sm">
                <span className="text-muted-foreground">Personel Kullanımı:</span>{' '}
                <span className="font-medium">{staffList.length} / {features.maxStaff}</span>
              </div>
              <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className={`h-full ${staffList.length >= features.maxStaff ? 'bg-red-500' : 'bg-primary'}`}
                  style={{ width: `${Math.min(100, (staffList.length / features.maxStaff) * 100)}%` }}
                />
              </div>
            </div>
            {staffList.length >= features.maxStaff && (
              <Button size="sm" asChild>
                <Link href="/dashboard/subscription/upgrade">
                  <Plus className="mr-1 h-3 w-3" />
                  Planınızı Yükseltin
                </Link>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Personel sayısı uyarısı */}
      {staffList.length >= features.maxStaff && (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Personel Sayısı Sınırı</AlertTitle>
          <AlertDescription>
            Abonelik planınız maksimum {features.maxStaff} personel eklemenize izin veriyor.
            Daha fazla personel eklemek için <Link href="/dashboard/subscription/upgrade" className="font-medium underline">planınızı yükseltin</Link>.
          </AlertDescription>
        </Alert>
      )}

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingBarber ? "Personeli Düzenle" : "Yeni Personel Ekle"}</CardTitle>
            <CardDescription>
              {editingBarber ? "Mevcut bir berberi veya personeli düzenleyin." : "Salonunuza yeni bir berber veya personel ekleyin."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ad Soyad</FormLabel>
                      <FormControl>
                        <Input placeholder="Ahmet Yılmaz" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>E-posta</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefon (İsteğe bağlı)</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="+90 (555) 123 45 67"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                  >
                    İptal
                  </Button>
                  <Button type="submit">
                    {editingBarber ? "Personeli Güncelle" : "Personeli Kaydet"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p>Yükleniyor...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {staffList.length > 0 ? (
            staffList.map((barber) => (
              <Card key={barber.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary font-semibold text-lg">
                        {barber.name.charAt(0)}
                      </div>
                      <div>
                        <CardTitle>{barber.name}</CardTitle>
                        <CardDescription className="flex items-center gap-2 mt-1">
                          {getInvitationStatus(barber)}
                        </CardDescription>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{barber.email || "E-posta yok"}</span>
                    </div>
                    {barber.phone && (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{barber.phone}</span>
                      </div>
                    )}
                    {barber.invitation_sent_at && !barber.user_id && (
                      <div className="text-xs text-muted-foreground mt-2">
                        Davet gönderildi: {formatDate(barber.invitation_sent_at)}
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(barber)}
                    >
                      Düzenle
                    </Button>
                    {!barber.user_id && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => sendInvitation(barber.id, barber.email || '')}
                              disabled={!barber.email || sendingInvitation === barber.id}
                            >
                              {sendingInvitation === barber.id ? (
                                <RefreshCw className="h-4 w-4 animate-spin" />
                              ) : barber.invitation_sent_at ? (
                                <RefreshCw className="h-4 w-4 mr-1" />
                              ) : (
                                <UserPlus className="h-4 w-4 mr-1" />
                              )}
                              {barber.invitation_sent_at ? "Yeniden Gönder" : "Davet Et"}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {!barber.email
                              ? "Davet göndermek için e-posta adresi gereklidir"
                              : barber.invitation_sent_at
                                ? "Davet e-postasını yeniden gönder"
                                : "Davet e-postası gönder"}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(barber.id)}
                  >
                    Sil
                  </Button>
                </CardFooter>
              </Card>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-muted-foreground">
                Henüz personel eklenmemiş. İlk personelinizi eklemek için "Personel Ekle" düğmesine tıklayın.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
