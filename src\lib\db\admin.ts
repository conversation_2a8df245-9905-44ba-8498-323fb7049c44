import { supabaseClient } from '../supabase-singleton';
import { SalonSubscription, SalonSubscriptionUpdate } from './types';

const supabase = supabaseClient;

/**
 * Kullanıcının admin olup olmadığını kontrol eder
 */
export async function isAdmin() {
  const { data, error } = await supabase.rpc('is_admin');

  if (error) {
    console.error('Admin kontrolü hatası:', error);
    return false;
  }

  return data;
}

/**
 * Tüm salon aboneliklerini getirir
 * @param page Sayfa numarası (1'den başlar)
 * @param limit Sayfa başına kayıt sayısı
 * @param search Arama metni (salon adı)
 * @param statusFilter Durum filtresi
 */
export async function getAllSalonSubscriptions(
  page = 1,
  limit = 10,
  search = '',
  statusFilter = 'all'
) {
  // Sayfalama için offset hesapla
  const offset = (page - 1) * limit;

  // Query oluştur
  let query = supabase
    .from('salon_subscriptions')
    .select(`
      *,
      plans:plan_id(*),
      salons:salon_id(id, name, slug)
    `, { count: 'exact' });

  // Arama filtresi
  if (search) {
    query = query.textSearch('salons.name', search);
  }

  // Durum filtresi
  if (statusFilter && statusFilter !== 'all') {
    query = query.eq('status', statusFilter);
  }

  // Sayfalama ve sıralama
  query = query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  const { data, error, count } = await query;

  if (error) throw error;

  // Toplam sayfa sayısını hesapla
  const totalPages = count ? Math.ceil(count / limit) : 0;

  return {
    data: data as (SalonSubscription & {
      salons: { id: string; name: string; slug: string };
    })[],
    totalPages,
    totalCount: count || 0
  };
}

/**
 * Abonelik ID'sine göre detaylı abonelik bilgilerini getirir
 * @param id Abonelik ID
 */
export async function getSalonSubscriptionById(id: string) {
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .select(`
      *,
      plans:plan_id(*),
      salons:salon_id(*)
    `)
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as (SalonSubscription & {
    salons: { id: string; name: string; slug: string };
    plans: any;
  });
}

/**
 * Abonelik bilgilerini günceller
 * @param id Abonelik ID
 * @param data Güncellenecek veriler
 */
export async function updateSalonSubscription(
  id: string,
  data: SalonSubscriptionUpdate
) {
  const { data: updatedData, error } = await supabase
    .from('salon_subscriptions')
    .update(data)
    .eq('id', id)
    .select(`
      *,
      plans:plan_id(*),
      salons:salon_id(*)
    `)
    .single();

  if (error) throw error;
  return updatedData as (SalonSubscription & {
    salons: { id: string; name: string; slug: string };
    plans: any;
  });
}

/**
 * Manuel ödeme kaydı oluşturur
 * @param subscriptionId Abonelik ID
 * @param amount Ödeme tutarı
 * @param paymentDate Ödeme tarihi
 * @param invoiceNumber Fatura numarası
 */
export async function createManualPayment(
  subscriptionId: string,
  amount: number,
  paymentDate: string,
  invoiceNumber?: string
) {
  const { data, error } = await supabase
    .from('subscription_payments')
    .insert({
      subscription_id: subscriptionId,
      amount,
      status: 'completed',
      payment_date: paymentDate,
      payment_method: 'manual',
      invoice_number: invoiceNumber
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}
