import { supabaseClient } from '../supabase-singleton';
import { Salon, SalonInsert, SalonUpdate } from './types';

const supabase = supabaseClient;

/**
 * Get all salons for the current user
 */
export async function getSalons() {
  const { data, error } = await supabase
    .from('salons')
    .select('*')
    .order('name');

  if (error) throw error;
  return data as Salon[];
}

/**
 * Get a salon by ID
 */
export async function getSalonById(id: string) {
  const { data, error } = await supabase
    .from('salons')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as Salon;
}

/**
 * Create a new salon
 */
export async function createSalon(salon: SalonInsert) {
  const { data, error } = await supabase
    .from('salons')
    .insert(salon)
    .select()
    .single();

  if (error) throw error;
  return data as Salon;
}

/**
 * Update a salon
 */
export async function updateSalon({ id, ...salon }: SalonUpdate) {
  const { data, error } = await supabase
    .from('salons')
    .update(salon)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as Salon;
}

/**
 * Delete a salon
 */
export async function deleteSalon(id: string) {
  const { error } = await supabase
    .from('salons')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}

/**
 * Get a salon by owner ID
 */
export async function getSalonByOwnerId(ownerId: string) {
  try {
    const { data, error } = await supabase
      .from('salons')
      .select('*')
      .eq('owner_id', ownerId)
      .maybeSingle();

    return { data, error };
  } catch (error) {
    console.error("Error in getSalonByOwnerId:", error);
    return { data: null, error };
  }
}
