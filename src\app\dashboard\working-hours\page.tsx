"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON>back } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useUser } from "@/contexts/UserContext"
import { BarberWorkingHoursForm } from "@/components/barber-working-hours-form"
import { TimeInput } from "@/components/ui/time-input"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip"
import { CopyToAllDaysDialog } from "@/components/ui/copy-to-all-days-dialog"
import { WorkingHoursCalendar } from "@/components/ui/working-hours-calendar"
import { useIsMobile } from "@/hooks/use-mobile"
import * as db from "@/lib/db"

const DAYS_OF_WEEK = [
  "Pazartesi",
  "Salı",
  "Çarşamba",
  "Perşembe",
  "Cuma",
  "Cumartesi",
  "Pazar",
]

// Define the form schema
const formSchema = z.object({
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_open`,
      z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
        message: "Lütfen geçerli bir saat girin (HH:MM)",
      }),
    ])
  ),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_close`,
      z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
        message: "Lütfen geçerli bir saat girin (HH:MM)",
      }),
    ])
  ),
  ...Object.fromEntries(
    DAYS_OF_WEEK.map((day) => [
      `${day.toLowerCase()}_closed`,
      z.boolean().default(false),
    ])
  ),
})

// Define the type for the form values
type FormValues = z.infer<typeof formSchema>

export default function WorkingHoursPage() {
  const [loading, setLoading] = useState(false)
  const [workingHours, setWorkingHours] = useState<db.WorkingHours[]>([])
  const { salon } = useUser()
  const isMobile = useIsMobile()

  // Initialize the form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      monday_open: "09:00",
      monday_close: "18:00",
      monday_closed: false,
      tuesday_open: "09:00",
      tuesday_close: "18:00",
      tuesday_closed: false,
      wednesday_open: "09:00",
      wednesday_close: "18:00",
      wednesday_closed: false,
      thursday_open: "09:00",
      thursday_close: "18:00",
      thursday_closed: false,
      friday_open: "09:00",
      friday_close: "18:00",
      friday_closed: false,
      saturday_open: "10:00",
      saturday_close: "16:00",
      saturday_closed: false,
      sunday_open: "10:00",
      sunday_close: "16:00",
      sunday_closed: true,
    },
  })

  // Load working hours from the database
  const loadWorkingHours = useCallback(async () => {
    if (!salon?.id) return

    try {
      setLoading(true)
      const data = await db.workingHours.getWorkingHours(salon.id)
      setWorkingHours(data)

      // Convert the data to form values
      const formValues: Record<string, string | boolean> = {}

      // Initialize with default values
      DAYS_OF_WEEK.forEach((day, index) => {
        const dayOfWeek = (index + 1) % 7 // Convert to 0-6 format (0 = Sunday)
        const dayData = data.find(d => d.day_of_week === dayOfWeek)

        formValues[`${day.toLowerCase()}_open`] = dayData ? dayData.open_time.substring(0, 5) : "09:00"
        formValues[`${day.toLowerCase()}_close`] = dayData ? dayData.close_time.substring(0, 5) : "18:00"
        formValues[`${day.toLowerCase()}_closed`] = dayData ? dayData.is_closed : (dayOfWeek === 0) // Sunday is closed by default
      })

      // Update the form
      form.reset(formValues as FormValues)
    } catch (error) {
      console.error("Error loading working hours:", error)
      toast.error("Çalışma saatleri yüklenirken bir hata oluştu")
    } finally {
      setLoading(false)
    }
  }, [salon?.id, form])

  // Load working hours when the component mounts
  useEffect(() => {
    if (salon?.id) {
      loadWorkingHours()
    }
  }, [salon?.id, loadWorkingHours])

  // Handle form submission
  async function onSubmit(values: FormValues) {
    if (!salon?.id) {
      toast.error("Salon bilgisi bulunamadı")
      return
    }

    setLoading(true)
    try {
      // Convert form values to working hours
      const workingHoursPromises = DAYS_OF_WEEK.map(async (day, index) => {
        const dayOfWeek = (index + 1) % 7 // Convert to 0-6 format (0 = Sunday)

        // Get existing working hours for this day
        const existingHours = await db.workingHours.getWorkingHoursByDay(salon.id, dayOfWeek)

        if (existingHours) {
          // Update existing hours
          return db.workingHours.updateWorkingHours({
            id: existingHours.id,
            is_closed: values[`${day.toLowerCase()}_closed` as keyof typeof values] as boolean,
            open_time: values[`${day.toLowerCase()}_open` as keyof typeof values] as string,
            close_time: values[`${day.toLowerCase()}_close` as keyof typeof values] as string,
          })
        } else {
          // Create new hours
          return db.workingHours.createWorkingHours({
            salon_id: salon.id,
            day_of_week: dayOfWeek,
            is_closed: values[`${day.toLowerCase()}_closed` as keyof typeof values] as boolean,
            open_time: values[`${day.toLowerCase()}_open` as keyof typeof values] as string,
            close_time: values[`${day.toLowerCase()}_close` as keyof typeof values] as string,
          })
        }
      })

      await Promise.all(workingHoursPromises)

      // Reload working hours
      await loadWorkingHours()

      toast.success("Salon çalışma saatleri başarıyla güncellendi")
    } catch (error) {
      toast.error("Çalışma saatleri güncellenirken bir hata oluştu")
      console.error(error)
    } finally {
      setLoading(false)
    }
  }

  // Handle copying working hours from one day to all days
  const handleCopyToAllDays = (sourceDay: string) => {
    const sourceDayLower = sourceDay.toLowerCase()
    const isClosed = form.getValues(`${sourceDayLower}_closed`)
    const openTime = form.getValues(`${sourceDayLower}_open`)
    const closeTime = form.getValues(`${sourceDayLower}_close`)

    // Create a new form values object with the copied values
    const newValues: Record<string, string | boolean> = {}

    DAYS_OF_WEEK.forEach(day => {
      const dayLower = day.toLowerCase()
      if (dayLower !== sourceDayLower) {
        newValues[`${dayLower}_closed`] = isClosed
        newValues[`${dayLower}_open`] = openTime
        newValues[`${dayLower}_close`] = closeTime
      }
    })

    // Update the form with the new values
    form.setValue("monday_closed", isClosed as boolean)
    form.setValue("tuesday_closed", isClosed as boolean)
    form.setValue("wednesday_closed", isClosed as boolean)
    form.setValue("thursday_closed", isClosed as boolean)
    form.setValue("friday_closed", isClosed as boolean)
    form.setValue("saturday_closed", isClosed as boolean)
    form.setValue("sunday_closed", isClosed as boolean)

    form.setValue("monday_open", openTime as string)
    form.setValue("tuesday_open", openTime as string)
    form.setValue("wednesday_open", openTime as string)
    form.setValue("thursday_open", openTime as string)
    form.setValue("friday_open", openTime as string)
    form.setValue("saturday_open", openTime as string)
    form.setValue("sunday_open", openTime as string)

    form.setValue("monday_close", closeTime as string)
    form.setValue("tuesday_close", closeTime as string)
    form.setValue("wednesday_close", closeTime as string)
    form.setValue("thursday_close", closeTime as string)
    form.setValue("friday_close", closeTime as string)
    form.setValue("saturday_close", closeTime as string)
    form.setValue("sunday_close", closeTime as string)

    toast.success(`${sourceDay} çalışma saatleri tüm günlere kopyalandı`)
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Çalışma Saatleri</h1>
        </div>
      </header>

      <Tabs defaultValue="salon" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="salon">Salon</TabsTrigger>
          <TabsTrigger value="barber">Personel</TabsTrigger>
        </TabsList>

        <TabsContent value="salon" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Salon Çalışma Saatleri</CardTitle>
              <CardDescription>
                Salonunuzun düzenli çalışma saatlerini ayarlayın. Bu saatler, randevu slotlarını belirlemek için kullanılacaktır.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-[2fr_1fr] gap-6">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-sm font-medium">Günlük Çalışma Saatleri</h3>
                      <CopyToAllDaysDialog days={DAYS_OF_WEEK} onCopy={handleCopyToAllDays} />
                    </div>
                    {DAYS_OF_WEEK.map((day) => (
                      <div key={day} className="grid grid-cols-1 md:grid-cols-[200px_1fr] gap-4 items-center p-3 rounded-lg hover:bg-muted/30 transition-colors">
                        <div className="font-medium flex items-center">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className={`w-3 h-3 rounded-full mr-2 ${form.watch(`${day.toLowerCase()}_closed`) ? 'bg-red-500' : 'bg-green-500'}`} />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{form.watch(`${day.toLowerCase()}_closed`) ? 'Kapalı' : 'Açık'}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {day}
                        </div>
                        <div className={`grid gap-4 items-start ${isMobile ? 'grid-cols-1' : 'sm:grid-cols-[auto_1fr_1fr] sm:items-center'}`}>
                          <FormField
                            control={form.control}
                            name={`${day.toLowerCase()}_closed`}
                            render={({ field }) => (
                              <FormItem className="flex items-center space-x-2 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value as boolean}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal cursor-pointer">
                                  Kapalı
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name={`${day.toLowerCase()}_open`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-xs">Açılış</FormLabel>
                                <FormControl>
                                  <TimeInput
                                    value={field.value as string}
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    disabled={Boolean(form.watch(`${day.toLowerCase()}_closed`))}
                                    placeholder="09:00"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name={`${day.toLowerCase()}_close`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-xs">Kapanış</FormLabel>
                                <FormControl>
                                  <TimeInput
                                    value={field.value as string}
                                    onChange={field.onChange}
                                    onBlur={field.onBlur}
                                    name={field.name}
                                    disabled={Boolean(form.watch(`${day.toLowerCase()}_closed`))}
                                    placeholder="18:00"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    ))}
                    <div className="flex justify-end">
                      <Button type="submit" disabled={loading}>
                        {loading ? "Kaydediliyor..." : "Kaydet"}
                      </Button>
                    </div>
                  </form>
                </Form>

                <div className="space-y-6">
                  <WorkingHoursCalendar workingHours={workingHours} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="barber" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Personel Çalışma Saatleri</CardTitle>
              <CardDescription>
                Personelin çalışma saatlerini ayarlayın. Bu saatler, personelin randevu alabileceği zaman dilimlerini belirler.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {salon?.id && <BarberWorkingHoursForm salonId={salon.id} salonWorkingHours={workingHours} />}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
