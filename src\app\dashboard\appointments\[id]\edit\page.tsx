"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { ArrowLeft } from "lucide-react"

import { Button } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { AppointmentForm } from "@/components/appointment-form-new"
import { useUser } from "@/contexts/UserContext"

export default function EditAppointmentPage() {
  const params = useParams()
  const appointmentId = params.id as string


  // UserContext'ten salon bilgilerini al
  const { salonId, salonLoading } = useUser()
  const [isLoading, setIsLoading] = useState(true)

  // Update loading state based on salon loading
  useEffect(() => {
    if (!salonLoading) {
      setIsLoading(false)
    }
  }, [salonLoading])

  if (isLoading) {
    return (
      <div className="p-4">
        <div className="flex justify-center items-center h-64">
          <p>Yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (!salonId) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Randevu Düzenle</h1>
          </div>
        </header>

        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <p>Salon bulunamadı. Lütfen önce bir salon oluşturun.</p>
          <Button asChild>
            <Link href="/dashboard/settings">Salon Oluştur</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/dashboard/appointments">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Randevu Düzenle</h1>
        </div>
      </header>

      <div className="bg-card rounded-lg border p-6">
        <AppointmentForm salonId={salonId} appointmentId={appointmentId} />
      </div>
    </div>
  )
}
