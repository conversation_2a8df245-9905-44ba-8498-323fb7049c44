import { supabaseClient } from '../supabase-singleton';
import { WorkingHours, WorkingHoursInsert, WorkingHoursUpdate } from './types';

const supabase = supabaseClient;

/**
 * Get working hours for a salon
 */
export async function getWorkingHours(salonId: string) {
  const { data, error } = await supabase
    .from('working_hours')
    .select('*')
    .eq('salon_id', salonId)
    .order('day_of_week');
  
  if (error) throw error;
  return data as WorkingHours[];
}

/**
 * Get working hours for a specific day
 */
export async function getWorkingHoursByDay(salonId: string, dayOfWeek: number) {
  const { data, error } = await supabase
    .from('working_hours')
    .select('*')
    .eq('salon_id', salonId)
    .eq('day_of_week', dayOfWeek)
    .single();
  
  if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "No rows returned" error
  return data as WorkingHours | null;
}

/**
 * Create working hours for a salon
 */
export async function createWorkingHours(workingHours: WorkingHoursInsert) {
  const { data, error } = await supabase
    .from('working_hours')
    .insert(workingHours)
    .select()
    .single();
  
  if (error) throw error;
  return data as WorkingHours;
}

/**
 * Update working hours
 */
export async function updateWorkingHours({ id, ...workingHours }: WorkingHoursUpdate) {
  const { data, error } = await supabase
    .from('working_hours')
    .update(workingHours)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data as WorkingHours;
}

/**
 * Delete working hours
 */
export async function deleteWorkingHours(id: string) {
  const { error } = await supabase
    .from('working_hours')
    .delete()
    .eq('id', id);
  
  if (error) throw error;
  return true;
}

/**
 * Set up default working hours for a salon (Monday to Saturday, 9 AM to 6 PM, Sunday closed)
 */
export async function setupDefaultWorkingHours(salonId: string) {
  const defaultHours: WorkingHoursInsert[] = [
    { salon_id: salonId, day_of_week: 0, open_time: '09:00', close_time: '18:00', is_closed: true }, // Sunday
    { salon_id: salonId, day_of_week: 1, open_time: '09:00', close_time: '18:00', is_closed: false }, // Monday
    { salon_id: salonId, day_of_week: 2, open_time: '09:00', close_time: '18:00', is_closed: false }, // Tuesday
    { salon_id: salonId, day_of_week: 3, open_time: '09:00', close_time: '18:00', is_closed: false }, // Wednesday
    { salon_id: salonId, day_of_week: 4, open_time: '09:00', close_time: '18:00', is_closed: false }, // Thursday
    { salon_id: salonId, day_of_week: 5, open_time: '09:00', close_time: '18:00', is_closed: false }, // Friday
    { salon_id: salonId, day_of_week: 6, open_time: '09:00', close_time: '18:00', is_closed: false }, // Saturday
  ];

  const { data, error } = await supabase
    .from('working_hours')
    .insert(defaultHours)
    .select();
  
  if (error) throw error;
  return data as WorkingHours[];
}
