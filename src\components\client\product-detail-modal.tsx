"use client"

import { useState } from "react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { X, ShoppingBag } from "lucide-react"
import { Product } from "@/lib/db/types"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

interface ProductDetailModalProps {
  product: Product | null
  open: boolean
  onOpenChange: (open: boolean) => void
  salonId: string
}

export function ProductDetailModal({
  product,
  open,
  onOpenChange,
  salonId
}: ProductDetailModalProps) {
  const [imageLoaded, setImageLoaded] = useState(false)

  if (!product) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-auto p-0">
        <DialogHeader className="sticky top-0 z-10 bg-background/80 p-4 backdrop-blur-md">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold">{product.name}</DialogTitle>
            <DialogClose asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                <X className="h-4 w-4" />
              </Button>
            </DialogClose>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 gap-6 p-6 md:grid-cols-2">
          {/* Product image */}
          <div className="relative aspect-square overflow-hidden rounded-lg bg-muted">
            {product.image_url ? (
              <>
                {/* Skeleton while image loads */}
                <AnimatePresence>
                  {!imageLoaded && (
                    <motion.div
                      className="absolute inset-0 bg-muted"
                      initial={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3 }}
                    />
                  )}
                </AnimatePresence>

                <Image
                  src={product.image_url}
                  alt={product.name}
                  fill
                  sizes="(max-width: 768px) 100vw, 50vw"
                  className="h-full w-full object-cover"
                  onLoad={() => setImageLoaded(true)}
                />
              </>
            ) : (
              <div className="flex h-full w-full items-center justify-center">
                <ShoppingBag className="h-20 w-20 text-muted-foreground" />
              </div>
            )}
          </div>

          {/* Product details */}
          <div className="flex flex-col space-y-6">
            <div>
              <h3 className="text-2xl font-bold">{product.name}</h3>
              {product.category && (
                <p className="text-sm text-muted-foreground">{product.category}</p>
              )}
              {product.price && (
                <p className="mt-2 text-xl font-medium">{product.price} ₺</p>
              )}
            </div>

            {product.description && (
              <div className="space-y-2">
                <h4 className="font-medium">Ürün Açıklaması</h4>
                <p className="text-muted-foreground">{product.description}</p>
              </div>
            )}

            <div className="mt-auto pt-6">
              {/* Ürün detayları burada bitiyor */}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
