/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
  // Add trailingSlash: true for better compatibility with Netlify
  trailingSlash: true,
  // Configure images for Netlify
  images: {
    domains: ['kuafor.netlify.app'],
    unoptimized: true,
  },
}

module.exports = nextConfig
