"use client"

import { motion } from "framer-motion"
import { Sciss<PERSON>, Sparkles } from "lucide-react"
import { ServiceCard } from "./service-card"
import { useStaggeredAnimation } from "@/hooks/use-scroll-animation"
import {getDefaultSalonContent} from "@/lib/db/public"
import { usePreview, useIsPreview } from "@/contexts/PreviewContext"
import { useSalon } from "@/contexts/SalonContext"

interface Service {
  id: string
  name: string
  description?: string
  duration: number
  price: number
}

interface ServicesSectionProps {
  services: Service[]
  salonId: string
}

export function ServicesSection({ services, salonId }: ServicesSectionProps) {
  const { ref: servicesRef, visibleItems } = useStaggeredAnimation(services.length, 0.15)

  // Preview context
  const isPreview = useIsPreview()
  const previewContext = isPreview ? usePreview() : null

  // Salon context for content
  const { salonContent, contentLoading } = useSalon()

  // Get services content based on context
  const content = isPreview && previewContext
    ? previewContext.previewContent.services
    : salonContent?.services || getDefaultSalonContent().services


  return (
    <section id="services" className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto" ref={servicesRef}>
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-6 mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
              <Scissors className="w-4 h-4" />
              <span>{content.badgeText}</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-foreground">
              {content.titleLine1 || content.title?.split(' ')[0] || 'Profesyonel'}
              <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                {content.titleLine2 || content.title?.split(' ').slice(1).join(' ') || 'Berber Hizmetleri'}
              </span>
            </h2>

            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {content.description}
            </p>
          </motion.div>

          {/* Services Grid */}
          {services.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 50, scale: 0.9 }}
                  animate={visibleItems.includes(index) ? {
                    opacity: 1,
                    y: 0,
                    scale: 1
                  } : {}}
                  transition={{
                    duration: 0.6,
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 100
                  }}
                  className="h-full"
                >
                  <ServiceCard
                    service={service}
                    salonId={salonId}
                    index={index}
                  />
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center py-16"
            >
              <div className="max-w-md mx-auto space-y-6">
                <div className="p-4 bg-muted/50 rounded-full w-fit mx-auto">
                  <Sparkles className="w-12 h-12 text-muted-foreground" />
                </div>
                <div className="space-y-3">
                  <h3 className="text-2xl font-bold text-foreground">
                    Hizmetler Yakında
                  </h3>
                  <p className="text-muted-foreground">
                    Hizmet listesi güncelleniyor. Randevu almak için bizimle iletişime geçin.
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Bottom CTA */}
          {services.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center mt-16"
            >
              <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-8 border border-primary/20">
                <h3 className="text-2xl font-bold text-foreground mb-4">
                  {content.cta?.title || 'Özel İhtiyaçlarınız mı Var?'}
                </h3>
                <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                  {content.cta?.description || 'Listelenen hizmetler dışında özel talepleriniz için bizimle iletişime geçin. Size özel çözümler sunmaktan mutluluk duyarız.'}
                </p>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="inline-flex items-center px-6 py-3 bg-background border border-border hover:border-primary/50 rounded-lg font-medium transition-all duration-300"
                  onClick={() => {
                    document.getElementById('contact')?.scrollIntoView({
                      behavior: 'smooth'
                    })
                  }}
                >
                  {content.cta?.button || 'İletişime Geç'}
                </motion.button>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </section>
  )
}
