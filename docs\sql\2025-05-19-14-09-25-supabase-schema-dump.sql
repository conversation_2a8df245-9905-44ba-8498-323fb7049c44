-- SalonFlow Database Schema Dump
-- Date: 2025-05-19-14-09-25

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table definitions
CREATE TABLE public.salons (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    address text,
    phone text,
    email text,
    website text,
    description text,
    logo_url text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    owner_id uuid,
    slug text NOT NULL
);

CREATE TABLE public.barbers (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    name text NOT NULL,
    email text,
    phone text,
    profile_image_url text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    user_id uuid,
    invitation_token text,
    invitation_sent_at timestamp with time zone,
    invitation_accepted_at timestamp with time zone
);

CREATE TABLE public.services (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    name text NOT NULL,
    description text,
    duration integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    price numeric NOT NULL
);

CREATE TABLE public.customers (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    surname text NOT NULL,
    email text,
    phone text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    salon_id uuid NOT NULL,
    notes text
);

CREATE TABLE public.appointments (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    barber_id uuid,
    customer_id uuid,
    service_id uuid,
    date date NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    status text NOT NULL,
    notes text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    fullname text,
    phonenumber text,
    email text,
    cancellation_token text,
    cancellation_token_expires_at timestamp with time zone,
    created_by uuid
);

CREATE TABLE public.working_hours (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    day_of_week integer NOT NULL,
    open_time time without time zone NOT NULL,
    close_time time without time zone NOT NULL,
    is_closed boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.barber_working_hours (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    barber_id uuid,
    day_of_week integer NOT NULL,
    open_time time without time zone NOT NULL,
    close_time time without time zone NOT NULL,
    is_closed boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    lunch_start_time time without time zone,
    lunch_end_time time without time zone,
    has_lunch_break boolean DEFAULT false
);

CREATE TABLE public.barber_services (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    barber_id uuid,
    service_id uuid,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.holidays (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    date date NOT NULL,
    description text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    is_recurring boolean DEFAULT false
);

CREATE TABLE public.notifications (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    user_id uuid,
    type text NOT NULL,
    title text NOT NULL,
    message text NOT NULL,
    read boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    data jsonb,
    created_by uuid
);

CREATE TABLE public.custom_domains (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    domain text NOT NULL,
    is_verified boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.subscription_plans (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    name text NOT NULL,
    price_monthly numeric NOT NULL,
    price_yearly numeric NOT NULL,
    max_staff integer NOT NULL,
    features jsonb,
    created_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.salon_subscriptions (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    plan text NOT NULL,
    start_date date NOT NULL,
    end_date date,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    plan_id uuid,
    status text DEFAULT 'active'::text NOT NULL,
    trial_end_date date,
    payment_method text,
    is_yearly boolean DEFAULT false
);

CREATE TABLE public.subscription_payments (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    subscription_id uuid NOT NULL,
    amount numeric NOT NULL,
    payment_date date NOT NULL,
    payment_method text,
    status text NOT NULL,
    invoice_number text,
    notes text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.referral_codes (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid NOT NULL,
    code text NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.referral_benefits (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    referrer_salon_id uuid NOT NULL,
    referred_salon_id uuid NOT NULL,
    referral_code_id uuid NOT NULL,
    benefit_type text NOT NULL,
    benefit_value text,
    is_applied boolean DEFAULT false,
    applied_date date,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.finance_categories (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    name text NOT NULL,
    type text NOT NULL,
    description text,
    color text,
    is_system_default boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

CREATE TABLE public.finance_transactions (
    id uuid DEFAULT uuid_generate_v4() NOT NULL,
    salon_id uuid,
    category_id uuid,
    amount numeric NOT NULL,
    transaction_date date NOT NULL,
    description text,
    appointment_id uuid,
    created_by uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    service_id uuid
);

-- Primary Keys
ALTER TABLE ONLY public.salons ADD CONSTRAINT salons_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.barbers ADD CONSTRAINT barbers_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.services ADD CONSTRAINT services_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.customers ADD CONSTRAINT customers_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.appointments ADD CONSTRAINT appointments_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.working_hours ADD CONSTRAINT working_hours_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.barber_working_hours ADD CONSTRAINT barber_working_hours_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.barber_services ADD CONSTRAINT barber_services_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.holidays ADD CONSTRAINT holidays_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.notifications ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.custom_domains ADD CONSTRAINT custom_domains_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.subscription_plans ADD CONSTRAINT subscription_plans_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.salon_subscriptions ADD CONSTRAINT salon_subscriptions_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.subscription_payments ADD CONSTRAINT subscription_payments_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.referral_codes ADD CONSTRAINT referral_codes_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.referral_benefits ADD CONSTRAINT referral_benefits_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.finance_categories ADD CONSTRAINT finance_categories_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.finance_transactions ADD CONSTRAINT finance_transactions_pkey PRIMARY KEY (id);

-- Foreign Keys
ALTER TABLE ONLY public.barbers ADD CONSTRAINT barbers_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.services ADD CONSTRAINT services_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.customers ADD CONSTRAINT customers_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.appointments ADD CONSTRAINT appointments_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.appointments ADD CONSTRAINT appointments_barber_id_fkey FOREIGN KEY (barber_id) REFERENCES public.barbers(id);
ALTER TABLE ONLY public.appointments ADD CONSTRAINT appointments_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customers(id);
ALTER TABLE ONLY public.appointments ADD CONSTRAINT appointments_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services(id);
ALTER TABLE ONLY public.working_hours ADD CONSTRAINT working_hours_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.barber_working_hours ADD CONSTRAINT barber_working_hours_barber_id_fkey FOREIGN KEY (barber_id) REFERENCES public.barbers(id);
ALTER TABLE ONLY public.barber_services ADD CONSTRAINT barber_services_barber_id_fkey FOREIGN KEY (barber_id) REFERENCES public.barbers(id);
ALTER TABLE ONLY public.barber_services ADD CONSTRAINT barber_services_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services(id);
ALTER TABLE ONLY public.holidays ADD CONSTRAINT holidays_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.notifications ADD CONSTRAINT notifications_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.custom_domains ADD CONSTRAINT custom_domains_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.salon_subscriptions ADD CONSTRAINT salon_subscriptions_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.salon_subscriptions ADD CONSTRAINT salon_subscriptions_plan_id_fkey FOREIGN KEY (plan_id) REFERENCES public.subscription_plans(id);
ALTER TABLE ONLY public.subscription_payments ADD CONSTRAINT subscription_payments_subscription_id_fkey FOREIGN KEY (subscription_id) REFERENCES public.salon_subscriptions(id);
ALTER TABLE ONLY public.referral_codes ADD CONSTRAINT referral_codes_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.referral_benefits ADD CONSTRAINT referral_benefits_referrer_salon_id_fkey FOREIGN KEY (referrer_salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.referral_benefits ADD CONSTRAINT referral_benefits_referred_salon_id_fkey FOREIGN KEY (referred_salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.referral_benefits ADD CONSTRAINT referral_benefits_referral_code_id_fkey FOREIGN KEY (referral_code_id) REFERENCES public.referral_codes(id);
ALTER TABLE ONLY public.finance_categories ADD CONSTRAINT finance_categories_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.finance_transactions ADD CONSTRAINT finance_transactions_salon_id_fkey FOREIGN KEY (salon_id) REFERENCES public.salons(id);
ALTER TABLE ONLY public.finance_transactions ADD CONSTRAINT finance_transactions_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.finance_categories(id);
ALTER TABLE ONLY public.finance_transactions ADD CONSTRAINT finance_transactions_appointment_id_fkey FOREIGN KEY (appointment_id) REFERENCES public.appointments(id);
ALTER TABLE ONLY public.finance_transactions ADD CONSTRAINT finance_transactions_service_id_fkey FOREIGN KEY (service_id) REFERENCES public.services(id);

-- Indexes
CREATE INDEX idx_appointments_salon_id ON public.appointments(salon_id);
CREATE INDEX idx_appointments_barber_id ON public.appointments(barber_id);
CREATE INDEX idx_appointments_customer_id ON public.appointments(customer_id);
CREATE INDEX idx_appointments_date ON public.appointments(date);
CREATE INDEX idx_appointments_status ON public.appointments(status);
CREATE INDEX idx_appointments_phonenumber ON public.appointments(phonenumber);
CREATE INDEX idx_appointments_email ON public.appointments(email);
CREATE INDEX idx_appointments_fullname ON public.appointments(fullname);

CREATE INDEX idx_barbers_salon_id ON public.barbers(salon_id);
CREATE INDEX idx_barbers_user_id ON public.barbers(user_id);

CREATE INDEX idx_customers_salon_id ON public.customers(salon_id);
CREATE INDEX idx_customers_phone ON public.customers(phone);

CREATE INDEX idx_services_salon_id ON public.services(salon_id);

CREATE INDEX idx_salons_owner_id ON public.salons(owner_id);
CREATE INDEX idx_salons_slug ON public.salons(slug);

CREATE INDEX idx_custom_domains_domain ON public.custom_domains(domain);
CREATE INDEX idx_custom_domains_salon_id ON public.custom_domains(salon_id);

CREATE INDEX idx_salon_subscriptions_salon_id ON public.salon_subscriptions(salon_id);
CREATE INDEX idx_salon_subscriptions_status ON public.salon_subscriptions(status);

-- Functions
CREATE OR REPLACE FUNCTION public.is_admin()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid() AND email = '<EMAIL>'
  );
END;
$function$;

CREATE OR REPLACE FUNCTION public.generate_salon_slug()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  base_slug TEXT;
  new_slug TEXT;
  counter INTEGER := 0;
BEGIN
  -- Salon adından slug oluştur
  base_slug := LOWER(REPLACE(NEW.name, ' ', '-'));

  -- Özel karakterleri kaldır (sadece harfler, rakamlar ve tire kalacak)
  base_slug := REGEXP_REPLACE(base_slug, '[^a-z0-9\-]', '', 'g');

  -- Başlangıç slug'ı
  new_slug := base_slug;

  -- Slug benzersiz olana kadar sayı ekle
  WHILE EXISTS (SELECT 1 FROM salons WHERE slug = new_slug) LOOP
    counter := counter + 1;
    new_slug := base_slug || '-' || counter;
  END LOOP;

  NEW.slug := new_slug;
  RETURN NEW;
END;
$function$;

-- Triggers
CREATE TRIGGER salon_slug_trigger BEFORE INSERT ON public.salons FOR EACH ROW EXECUTE FUNCTION public.generate_salon_slug();
CREATE TRIGGER create_default_finance_categories_trigger AFTER INSERT ON public.salons FOR EACH ROW EXECUTE FUNCTION public.create_default_finance_categories();

CREATE TRIGGER appointment_cancellation_token_trigger BEFORE INSERT ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.generate_cancellation_token();
CREATE TRIGGER appointment_customer_insert_trigger BEFORE INSERT ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.manage_customer_for_appointment();
CREATE TRIGGER appointment_customer_update_trigger BEFORE UPDATE ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.manage_customer_for_appointment();
CREATE TRIGGER on_appointment_cancelled AFTER UPDATE ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.create_cancelled_appointment_notification();
CREATE TRIGGER on_appointment_created AFTER INSERT ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.create_new_appointment_notification();
CREATE TRIGGER record_appointment_income_trigger AFTER UPDATE ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.record_appointment_income();
CREATE TRIGGER set_appointment_created_by_trigger BEFORE INSERT ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.set_appointment_created_by();

-- RLS Policies
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.barber_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.barber_working_hours ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.barbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.custom_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.finance_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.finance_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.holidays ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referral_benefits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referral_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.salon_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.salons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.working_hours ENABLE ROW LEVEL SECURITY;

-- Appointments policies
CREATE POLICY "Anyone can insert appointments" ON public.appointments FOR INSERT TO public WITH CHECK (true);
CREATE POLICY "Anyone can view appointments" ON public.appointments FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can manage appointments at their salon" ON public.appointments FOR ALL TO public USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Staff can delete their own appointments" ON public.appointments FOR DELETE TO public USING ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.user_id = auth.uid()))));
CREATE POLICY "Staff can manage their own appointments" ON public.appointments FOR INSERT TO public WITH CHECK ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.user_id = auth.uid()))));
CREATE POLICY "Staff can update their own appointments" ON public.appointments FOR UPDATE TO public USING ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.user_id = auth.uid())))) WITH CHECK ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.user_id = auth.uid()))));
CREATE POLICY "Staff can view all salon appointments" ON public.appointments FOR SELECT TO public USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()))));

-- Barber services policies
CREATE POLICY "Anyone can view barber services" ON public.barber_services FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can manage their barbers' services" ON public.barber_services FOR ALL TO public USING ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))))) WITH CHECK ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))))));
CREATE POLICY "Staff can view barber services" ON public.barber_services FOR SELECT TO public USING ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.salon_id IN ( SELECT barbers_1.salon_id FROM barbers barbers_1 WHERE (barbers_1.user_id = auth.uid()))))));

-- Barber working hours policies
CREATE POLICY "Anyone can view barber working hours" ON public.barber_working_hours FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can manage their barbers' working hours" ON public.barber_working_hours FOR ALL TO public USING ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))))) WITH CHECK ((barber_id IN ( SELECT barbers.id FROM barbers WHERE (barbers.salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))))));
CREATE POLICY "Staff can manage their own working hours" ON public.barber_working_hours FOR ALL TO public USING ((EXISTS ( SELECT 1 FROM barbers WHERE ((barbers.id = barber_working_hours.barber_id) AND (barbers.user_id = auth.uid()))))) WITH CHECK ((EXISTS ( SELECT 1 FROM barbers WHERE ((barbers.id = barber_working_hours.barber_id) AND (barbers.user_id = auth.uid())))));

-- Barbers policies
CREATE POLICY "Anyone can view barbers" ON public.barbers FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can manage barbers" ON public.barbers FOR ALL TO authenticated USING ((EXISTS ( SELECT 1 FROM salons WHERE ((salons.id = barbers.salon_id) AND (salons.owner_id = auth.uid()))))) WITH CHECK ((EXISTS ( SELECT 1 FROM salons WHERE ((salons.id = barbers.salon_id) AND (salons.owner_id = auth.uid())))));
CREATE POLICY "Service role bypass" ON public.barbers FOR ALL TO service_role USING (true) WITH CHECK (true);
CREATE POLICY "Staff can update their own profile" ON public.barbers FOR UPDATE TO authenticated USING ((user_id = auth.uid())) WITH CHECK ((user_id = auth.uid()));

-- Custom domains policies
CREATE POLICY "Anyone can view verified custom domains" ON public.custom_domains FOR SELECT TO public USING ((is_verified = true));
CREATE POLICY "Service role bypass" ON public.custom_domains FOR ALL TO service_role USING (true) WITH CHECK (true);

-- Customers policies
CREATE POLICY "Salon owners can manage their salon's customers" ON public.customers FOR ALL TO authenticated USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Staff can insert customers for their salon" ON public.customers FOR INSERT TO authenticated WITH CHECK ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()))));
CREATE POLICY "Staff can update customers for their salon" ON public.customers FOR UPDATE TO authenticated USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()))));
CREATE POLICY "Staff can view their salon's customers" ON public.customers FOR SELECT TO authenticated USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()))));

-- Finance categories policies
CREATE POLICY "Salon owners can manage their own finance categories" ON public.finance_categories FOR ALL TO authenticated USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Service role bypass for finance categories" ON public.finance_categories FOR ALL TO service_role USING (true) WITH CHECK (true);
CREATE POLICY "Staff can view finance categories of their salon" ON public.finance_categories FOR SELECT TO authenticated USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()) UNION SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));

-- Finance transactions policies
CREATE POLICY "Salon owners can manage their own finance transactions" ON public.finance_transactions FOR ALL TO authenticated USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Service role bypass for finance transactions" ON public.finance_transactions FOR ALL TO service_role USING (true) WITH CHECK (true);
CREATE POLICY "Staff can create finance transactions for their salon" ON public.finance_transactions FOR INSERT TO authenticated WITH CHECK ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()) UNION SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Staff can update finance transactions they created" ON public.finance_transactions FOR UPDATE TO authenticated USING ((created_by = auth.uid())) WITH CHECK ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()) UNION SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Staff can view finance transactions of their salon" ON public.finance_transactions FOR SELECT TO authenticated USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()) UNION SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));

-- Holidays policies
CREATE POLICY "Anyone can view holidays" ON public.holidays FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can manage their salon's holidays" ON public.holidays FOR ALL TO public USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Staff can view salon holidays" ON public.holidays FOR SELECT TO public USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()))));

-- Notifications policies
CREATE POLICY "Anyone can insert notifications" ON public.notifications FOR INSERT TO public WITH CHECK (true);
CREATE POLICY "Salon owners can manage their salon notifications" ON public.notifications FOR ALL TO authenticated USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Service role bypass for notifications" ON public.notifications FOR ALL TO service_role USING (true) WITH CHECK (true);
CREATE POLICY "Staff can manage their own notifications" ON public.notifications FOR ALL TO authenticated USING ((user_id = auth.uid())) WITH CHECK ((user_id = auth.uid()));

-- Referral benefits policies
CREATE POLICY "Admin referans faydalarını ekleyebilir" ON public.referral_benefits FOR INSERT TO public WITH CHECK (is_admin());
CREATE POLICY "Admin referans faydalarını güncelleyebilir" ON public.referral_benefits FOR UPDATE TO public USING (is_admin());
CREATE POLICY "Admin referans faydalarını silebilir" ON public.referral_benefits FOR DELETE TO public USING (is_admin());
CREATE POLICY "Admin tüm referans faydalarını görebilir" ON public.referral_benefits FOR SELECT TO public USING (is_admin());
CREATE POLICY "Salon sahipleri kendi referans faydalarını görebilir" ON public.referral_benefits FOR SELECT TO public USING (((referrer_salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))) OR (referred_salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))));

-- Referral codes policies
CREATE POLICY "Admin referans kodlarını ekleyebilir" ON public.referral_codes FOR INSERT TO public WITH CHECK (is_admin());
CREATE POLICY "Admin referans kodlarını güncelleyebilir" ON public.referral_codes FOR UPDATE TO public USING (is_admin());
CREATE POLICY "Admin referans kodlarını silebilir" ON public.referral_codes FOR DELETE TO public USING (is_admin());
CREATE POLICY "Admin tüm referans kodlarını görebilir" ON public.referral_codes FOR SELECT TO public USING (is_admin());
CREATE POLICY "Salon sahipleri kendi referans kodlarını görebilir" ON public.referral_codes FOR SELECT TO public USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Salon sahipleri referans kodu oluşturabilir" ON public.referral_codes FOR INSERT TO public WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));

-- Salon subscriptions policies
CREATE POLICY "Admin abonelikleri ekleyebilir" ON public.salon_subscriptions FOR INSERT TO public WITH CHECK (is_admin());
CREATE POLICY "Admin abonelikleri güncelleyebilir" ON public.salon_subscriptions FOR UPDATE TO public USING (is_admin());
CREATE POLICY "Admin abonelikleri silebilir" ON public.salon_subscriptions FOR DELETE TO public USING (is_admin());
CREATE POLICY "Admin tüm abonelikleri görebilir" ON public.salon_subscriptions FOR SELECT TO public USING (is_admin());
CREATE POLICY "Salon sahipleri kendi aboneliklerini görebilir" ON public.salon_subscriptions FOR SELECT TO public USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));

-- Salons policies
CREATE POLICY "Anyone can select salons" ON public.salons FOR SELECT TO public USING (true);
CREATE POLICY "Anyone can view salons" ON public.salons FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can delete their salons" ON public.salons FOR DELETE TO authenticated USING ((owner_id = auth.uid()));
CREATE POLICY "Salon owners can insert salons" ON public.salons FOR INSERT TO authenticated WITH CHECK ((owner_id = auth.uid()));
CREATE POLICY "Salon owners can manage their own salons" ON public.salons FOR ALL TO public USING ((owner_id = auth.uid())) WITH CHECK ((owner_id = auth.uid()));
CREATE POLICY "Salon owners can update their salons" ON public.salons FOR UPDATE TO authenticated USING ((owner_id = auth.uid())) WITH CHECK ((owner_id = auth.uid()));

-- Services policies
CREATE POLICY "Anyone can view services" ON public.services FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can manage their salon's services" ON public.services FOR ALL TO public USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Staff can view salon services" ON public.services FOR SELECT TO public USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()))));

-- Subscription payments policies
CREATE POLICY "Admin tüm ödemeleri görebilir" ON public.subscription_payments FOR SELECT TO public USING (is_admin());
CREATE POLICY "Admin ödemeleri ekleyebilir" ON public.subscription_payments FOR INSERT TO public WITH CHECK (is_admin());
CREATE POLICY "Admin ödemeleri güncelleyebilir" ON public.subscription_payments FOR UPDATE TO public USING (is_admin());
CREATE POLICY "Admin ödemeleri silebilir" ON public.subscription_payments FOR DELETE TO public USING (is_admin());
CREATE POLICY "Salon sahipleri kendi ödemelerini görebilir" ON public.subscription_payments FOR SELECT TO public USING ((subscription_id IN ( SELECT salon_subscriptions.id FROM salon_subscriptions WHERE (salon_subscriptions.salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))))));

-- Subscription plans policies
CREATE POLICY "Admin subscription_plans ekleyebilir" ON public.subscription_plans FOR INSERT TO public WITH CHECK (is_admin());
CREATE POLICY "Admin subscription_plans güncelleyebilir" ON public.subscription_plans FOR UPDATE TO public USING (is_admin());
CREATE POLICY "Admin subscription_plans silebilir" ON public.subscription_plans FOR DELETE TO public USING (is_admin());
CREATE POLICY "Herkes subscription_plans görebilir" ON public.subscription_plans FOR SELECT TO public USING (true);

-- Working hours policies
CREATE POLICY "Anyone can view working hours" ON public.working_hours FOR SELECT TO public USING (true);
CREATE POLICY "Salon owners can manage their salon's working hours" ON public.working_hours FOR ALL TO public USING ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid())))) WITH CHECK ((salon_id IN ( SELECT salons.id FROM salons WHERE (salons.owner_id = auth.uid()))));
CREATE POLICY "Staff can view salon working hours" ON public.working_hours FOR SELECT TO public USING ((salon_id IN ( SELECT barbers.salon_id FROM barbers WHERE (barbers.user_id = auth.uid()))));
