import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import * as db from '@/lib/db';

// GET /api/appointments
export async function GET(request: NextRequest) {
  try {
    // Get the query parameters
    const searchParams = request.nextUrl.searchParams;
    const salonId = searchParams.get('salon_id');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const barberId = searchParams.get('barber_id');
    const date = searchParams.get('date');

    if (!salonId) {
      return NextResponse.json(
        { error: 'Salon ID is required' },
        { status: 400 }
      );
    }

    // Create a Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the appointments based on the query parameters
    let appointments;
    if (startDate && endDate) {
      appointments = await db.appointments.getAppointmentsInRange(salonId, startDate, endDate);
    } else if (date) {
      appointments = await db.appointments.getAppointmentsByDate(salonId, date);
    } else if (barberId) {
      appointments = await db.appointments.getAppointmentsForBarber(barberId);
    } else {
      appointments = await db.appointments.getAppointments(salonId);
    }

    return NextResponse.json(appointments);
  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
