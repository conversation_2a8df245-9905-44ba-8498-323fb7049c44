"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { toast } from "sonner"
import { ArrowLeft, Edit, Calendar, CreditCard } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { useUser } from "@/contexts/UserContext"
import { getSalonSubscriptionById } from "@/lib/db/admin"
import { getSubscriptionPayments } from "@/lib/db/subscription-payments"

export default function SubscriptionDetailPage({ params }: { params: { id: string } }) {
    const id = params.id as string
  
  const router = useRouter()
  const [subscription, setSubscription] = useState<any>(null)
  const [payments, setPayments] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // UserContext'ten isAdminUser değişkenini al
  const { isAdminUser } = useUser()

  useEffect(() => {
    if (!isAdminUser) {
      router.push("/dashboard")
      toast.error("Bu sayfaya erişim yetkiniz yok.")
    }
  }, [isAdminUser, router])

  useEffect(() => {
    async function loadSubscriptionDetails() {
      if (!isAdminUser) return

      try {
        setIsLoading(true)

        // Abonelik detaylarını yükle
        const subscriptionData = await getSalonSubscriptionById(id)
        setSubscription(subscriptionData)

        // Ödeme geçmişini yükle
        const paymentsData = await getSubscriptionPayments(id)
        setPayments(paymentsData)
      } catch (error) {
        console.error("Abonelik detayları yüklenirken hata:", error)
        toast.error("Abonelik detayları yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    if (id) {
      loadSubscriptionDetails()
    }
  }, [isAdminUser, id])

  // Abonelik durumunu Türkçe olarak göster
  const getStatusText = (status: string) => {
    switch (status) {
      case 'trial': return 'Deneme Süresi'
      case 'active': return 'Aktif'
      case 'past_due': return 'Ödeme Gecikti'
      case 'canceled': return 'İptal Edildi'
      case 'suspended': return 'Askıya Alındı'
      default: return status
    }
  }

  // Abonelik durumuna göre renk belirle
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'trial': return 'bg-blue-500'
      case 'active': return 'bg-green-500'
      case 'past_due': return 'bg-yellow-500'
      case 'canceled': return 'bg-red-500'
      case 'suspended': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  // Ödeme durumunu Türkçe olarak göster
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Bekliyor'
      case 'completed': return 'Tamamlandı'
      case 'failed': return 'Başarısız'
      default: return status
    }
  }

  // Ödeme durumuna göre renk belirle
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500'
      case 'completed': return 'bg-green-500'
      case 'failed': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  if (!isAdminUser) {
    return null
  }

  if (isLoading) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Button variant="outline" size="icon" asChild className="mr-2">
              <Link href="/admin/subscriptions">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold">Abonelik Detayları</h1>
          </div>
        </header>
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-24 w-full" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-2">
            <Link href="/admin/subscriptions">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Abonelik Detayları</h1>
        </div>
        <div className="ml-auto">
          <Button asChild>
            <Link href={`/admin/subscriptions/${id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Düzenle
            </Link>
          </Button>
        </div>
      </header>

      <div className="space-y-6">
        {/* Abonelik Detayları */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>{subscription?.salons?.name || 'Bilinmeyen Salon'}</CardTitle>
                <CardDescription>Abonelik ID: {subscription?.id}</CardDescription>
              </div>
              <Badge className={getStatusColor(subscription?.status)}>
                {getStatusText(subscription?.status)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Plan</h3>
                  <p className="text-lg font-semibold">{subscription?.plans?.name || 'Bilinmeyen Plan'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Ödeme Döngüsü</h3>
                  <p className="text-lg font-semibold">{subscription?.is_yearly ? 'Yıllık' : 'Aylık'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Ödeme Yöntemi</h3>
                  <p className="text-lg font-semibold">
                    {subscription?.payment_method === 'manual' ? 'Manuel (Havale/EFT)' :
                     subscription?.payment_method === 'iyzico' ? 'iyzico' :
                     subscription?.payment_method === 'paytr' ? 'PayTR' :
                     subscription?.payment_method}
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Başlangıç Tarihi</h3>
                  <p className="text-lg font-semibold">
                    {subscription?.start_date ?
                      format(new Date(subscription.start_date), 'd MMMM yyyy', { locale: tr }) :
                      '-'}
                  </p>
                </div>
                {subscription?.status === 'trial' && subscription?.trial_end_date && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Deneme Süresi Bitiş</h3>
                    <p className="text-lg font-semibold">
                      {format(new Date(subscription.trial_end_date), 'd MMMM yyyy', { locale: tr })}
                    </p>
                  </div>
                )}
                {subscription?.end_date && (
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Bitiş Tarihi</h3>
                    <p className="text-lg font-semibold">
                      {format(new Date(subscription.end_date), 'd MMMM yyyy', { locale: tr })}
                    </p>
                  </div>
                )}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Oluşturulma Tarihi</h3>
                  <p className="text-lg font-semibold">
                    {format(new Date(subscription?.created_at), 'd MMMM yyyy', { locale: tr })}
                  </p>
                </div>
              </div>
            </div>

            <Separator className="my-6" />

            <div>
              <h3 className="text-lg font-semibold mb-4">Plan Özellikleri</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center p-4 border rounded-md">
                  <div className="mr-4">
                    <Calendar className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Maksimum Personel</p>
                    <p className="text-2xl font-bold">{subscription?.plans?.max_staff || 0}</p>
                  </div>
                </div>
                <div className="flex items-center p-4 border rounded-md">
                  <div className="mr-4">
                    <CreditCard className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Aylık Ücret</p>
                    <p className="text-2xl font-bold">{subscription?.plans?.price_monthly || 0} TL</p>
                  </div>
                </div>
                <div className="flex items-center p-4 border rounded-md">
                  <div className="mr-4">
                    <CreditCard className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Yıllık Ücret</p>
                    <p className="text-2xl font-bold">{subscription?.plans?.price_yearly || 0} TL</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ödeme Geçmişi */}
        <Card>
          <CardHeader>
            <CardTitle>Ödeme Geçmişi</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Ödeme Tarihi</TableHead>
                    <TableHead>Ödeme Dönemi</TableHead>
                    <TableHead>Tutar</TableHead>
                    <TableHead>İndirim</TableHead>
                    <TableHead>Ödeme Yöntemi</TableHead>
                    <TableHead>Fatura No</TableHead>
                    <TableHead>Durum</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.length > 0 ? (
                    payments.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell>
                          {payment.payment_date ?
                            format(new Date(payment.payment_date), 'd MMM yyyy', { locale: tr }) :
                            format(new Date(payment.created_at), 'd MMM yyyy', { locale: tr })}
                        </TableCell>
                        <TableCell>
                          {payment.period_start_date && payment.period_end_date ? (
                            <>
                              {format(new Date(payment.period_start_date), 'd MMM yyyy', { locale: tr })} - {format(new Date(payment.period_end_date), 'd MMM yyyy', { locale: tr })}
                            </>
                          ) : '-'}
                        </TableCell>
                        <TableCell>
                          {payment.amount} TL
                          {payment.original_amount && payment.original_amount > payment.amount && (
                            <div className="text-xs text-muted-foreground">
                              Orijinal: {payment.original_amount} TL
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          {payment.discount_amount && payment.discount_amount > 0 ? (
                            <div className="text-green-500">
                              {payment.discount_amount} TL
                              {payment.discount_type && (
                                <div className="text-xs text-muted-foreground">
                                  {payment.discount_type === 'referral' ? 'Referans' : payment.discount_type}
                                </div>
                              )}
                            </div>
                          ) : '-'}
                        </TableCell>
                        <TableCell>
                          {payment.payment_method === 'manual' ? 'Havale/EFT' :
                           payment.payment_method === 'iyzico' ? 'iyzico' :
                           payment.payment_method === 'paytr' ? 'PayTR' :
                           payment.payment_method}
                        </TableCell>
                        <TableCell>{payment.invoice_number || '-'}</TableCell>
                        <TableCell>
                          <Badge className={getPaymentStatusColor(payment.status)}>
                            {getPaymentStatusText(payment.status)}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        Henüz ödeme kaydı bulunmuyor.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href={`/admin/subscriptions/${id}/payment`}>
                <CreditCard className="mr-2 h-4 w-4" />
                Ödeme Ekle
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
