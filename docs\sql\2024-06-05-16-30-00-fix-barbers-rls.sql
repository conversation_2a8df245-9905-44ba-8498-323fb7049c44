-- Fix RLS policies for barbers table

-- First, drop the existing policies
DROP POLICY IF EXISTS "Salon owners can manage their salon's barbers" ON barbers;
DROP POLICY IF EXISTS "Staff can update their own profile" ON barbers;
DROP POLICY IF EXISTS "Anyone can view barbers" ON barbers;

-- Make sure RLS is enabled
ALTER TABLE barbers ENABLE ROW LEVEL SECURITY;

-- Create a new policy for salon owners to manage their barbers
CREATE POLICY "Salon owners can manage their barbers" ON barbers
  FOR ALL
  TO authenticated
  USING (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()))
  WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- Create a policy for staff to view all barbers in their salon
CREATE POLICY "Staff can view salon barbers" ON barbers
  FOR SELECT
  TO authenticated
  USING (salon_id IN (SELECT salon_id FROM barbers WHERE user_id = auth.uid()));

-- Create a policy for staff to update their own profile
CREATE POLICY "Staff can update their own profile" ON barbers
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Create a policy for anonymous users to view barbers (for booking page)
CREATE POLICY "Anyone can view barbers" ON barbers
  FOR SELECT
  USING (true);

-- Create a service role bypass policy
CREATE POLICY "Service role bypass" ON barbers
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);
