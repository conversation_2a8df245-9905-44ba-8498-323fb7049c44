"use client"

import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { ProductForm } from "@/components/product-form"
import { useSubscription } from "@/contexts/SubscriptionContext"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function NewProductPage() {
  const router = useRouter()
  const { hasFeature } = useSubscription()

  // Ürün yönetimi özelliği kontrolü
  useEffect(() => {
    if (!hasFeature("product_management")) {
      router.push("/dashboard/products")
    }
  }, [hasFeature, router])

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold"><PERSON><PERSON></h1>
        </div>
      </header>

      <Card>
        <CardContent className="pt-6">
          <ProductForm />
        </CardContent>
      </Card>
    </div>
  )
}
