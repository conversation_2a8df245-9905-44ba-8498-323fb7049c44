# isAdminUser Değişkeninin UserContext'e Taşınması

**Tarih:** 20 Mayıs 2025
**Saat:** 22:06

## Ya<PERSON><PERSON><PERSON>ğişiklikler

### 1. UserContext'e isAdminUser Değişkeninin Eklenmesi

UserContext'e `isAdminUser` değiş<PERSON>i eklenerek, admin kullan<PERSON> kontrolünün merkezi bir yerden yapılması sağlandı:

1. **UserContextType'a Ekleme:**
   - `isAdminUser: boolean` alanı eklendi

2. **defaultContext'e Ekleme:**
   - `isAdminUser: false` varsayılan değeri eklendi

3. **UserProvider İçinde State Oluşturma:**
   - `const [isAdminUser, setIsAdminUser] = useState(false)` state'i eklendi

4. **determineUserRole Fonksiyonunda Admin Kontrolü:**
   - Ku<PERSON><PERSON><PERSON>ı e-postası '<EMAIL>' ise `isAdminUser` değişkeni `true` olarak ayarlandı
   - Di<PERSON>er du<PERSON> `isAdminUser` değişkeni `false` olarak ayarlandı

5. **contextValue'ya Ekleme:**
   - `isAdminUser` değişkeni context değerlerine eklendi

### 2. Diğer Dosyalardaki Kullanımların Güncellenmesi

Aşağıdaki dosyalarda `isAdminUser` değişkeninin kullanımı güncellendi:

1. **src/app/dashboard/page.tsx:**
   - `isAdminUser` değişkeni artık UserContext'ten alınıyor
   - Yerel `isAdminUser` tanımı kaldırıldı

2. **src/components/custom-app-sidebar.tsx:**
   - `isAdminUser` değişkeni artık UserContext'ten alınıyor
   - Yerel `isAdminUser` tanımı kaldırıldı

3. **src/app/admin/subscriptions/[id]/page.tsx:**
   - `isAdminUser` değişkeni artık UserContext'ten alınıyor
   - Admin kontrolü için `isAdmin()` fonksiyonu yerine UserContext'teki `isAdminUser` kullanılıyor

## Teknik Detaylar

### UserContext'e isAdminUser Ekleme

```tsx
// UserContextType'a ekleme
type UserContextType = {
  // ... diğer alanlar
  isAdminUser: boolean
  // ... diğer alanlar
}

// defaultContext'e ekleme
const defaultContext: UserContextType = {
  // ... diğer alanlar
  isAdminUser: false,
  // ... diğer alanlar
}

// UserProvider içinde state oluşturma
const [isAdminUser, setIsAdminUser] = useState(false)

// determineUserRole fonksiyonunda admin kontrolü
const determineUserRole = useCallback(async (user: User) => {
  if (!user) return;

  try {
    // Admin kontrolü
    if (user.email === '<EMAIL>') {
      setIsAdminUser(true)
      setUserRole('owner') // Admin için varsayılan rol
      return
    } else {
      setIsAdminUser(false)
    }
    
    // ... diğer rol kontrolleri
  } catch (err) {
    // ... hata yönetimi
  }
}, [])

// contextValue'ya ekleme
const contextValue = useMemo(() => ({
  // ... diğer değerler
  isAdminUser,
  // ... diğer değerler
}), [
  // ... diğer bağımlılıklar
  isAdminUser,
  // ... diğer bağımlılıklar
]);
```

### Diğer Dosyalardaki Kullanımların Güncellenmesi

```tsx
// src/app/dashboard/page.tsx
// Eski kod
const isAdminUser = user?.email === '<EMAIL>';

// Yeni kod
const { salonId, salonLoading, userRole, isAdminUser } = useUser();
```

```tsx
// src/components/custom-app-sidebar.tsx
// Eski kod
const isAdminUser = React.useMemo(() => {
  return user?.email === '<EMAIL>'
}, [user?.email])

// Yeni kod
const { user, userRole, signOut, salon, isAdminUser } = useUser();
```

```tsx
// src/app/admin/subscriptions/[id]/page.tsx
// Eski kod
const [isAdminUser, setIsAdminUser] = useState(false)

useEffect(() => {
  async function checkAdmin() {
    try {
      const admin = await isAdmin()
      setIsAdminUser(admin)
      // ... diğer işlemler
    } catch (error) {
      // ... hata yönetimi
    }
  }

  checkAdmin()
}, [router])

// Yeni kod
const { isAdminUser } = useUser()

useEffect(() => {
  if (!isAdminUser) {
    router.push("/dashboard")
    toast.error("Bu sayfaya erişim yetkiniz yok.")
  }
}, [isAdminUser, router])
```

## Faydaları

1. **Kod Tekrarının Azaltılması:** Admin kontrolü artık merkezi bir yerden yapılıyor, her dosyada ayrı ayrı kontrol etmeye gerek kalmadı.
2. **Tutarlılık:** Tüm uygulamada admin kontrolü aynı şekilde yapılıyor, bu da tutarlılığı artırıyor.
3. **Bakım Kolaylığı:** Admin kontrolü değiştiğinde sadece UserContext'i güncellemek yeterli olacak, tüm dosyaları tek tek değiştirmeye gerek kalmayacak.
4. **Performans İyileştirmesi:** Gereksiz API çağrıları (isAdmin) ortadan kaldırıldı.

## Sonraki Adımlar

- Diğer admin sayfalarında da `isAdminUser` değişkeninin kullanımının güncellenmesi
- Admin rolü için daha kapsamlı bir yetkilendirme sistemi oluşturulması
- UserRole tipine 'admin' değerinin eklenmesi düşünülebilir
