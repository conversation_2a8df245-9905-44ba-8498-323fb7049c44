"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useUser } from "@/contexts/UserContext"
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"
import { toast } from "sonner"
import { PieChart as PieChartIcon, Bar<PERSON>hart as BarChartIcon, Line<PERSON>hart as LineChartIcon, Loader2 } from "lucide-react"
import { format, subMonths } from "date-fns"
import { tr } from "date-fns/locale"
import {
  PieChart, Pie, Cell,
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  LineChart, Line
} from "recharts"
import { analytics } from "@/lib/db"
import {
  BarberDistribution,
  ServicePopularity,
  AppointmentTrend,
  BarberPerformance
} from "@/lib/db/analytics"

// Custom hook to detect screen size
function useMediaQuery(query: string) {
  const [matches, setMatches] = React.useState(false)

  React.useEffect(() => {
    const media = window.matchMedia(query)
    if (media.matches !== matches) {
      setMatches(media.matches)
    }

    const listener = () => setMatches(media.matches)
    media.addEventListener("change", listener)
    return () => media.removeEventListener("change", listener)
  }, [matches, query])

  return matches
}

// Generate random colors for charts
const COLORS = [
  '#8884d8', '#83a6ed', '#8dd1e1', '#82ca9d', '#a4de6c',
  '#d0ed57', '#ffc658', '#ff8042', '#ff6361', '#bc5090'
];

export default function AnalyticsPage() {
  const router = useRouter()
  const { salon } = useUser()
  const salonId = salon?.id

  // Check if screen is mobile
  const isMobile = useMediaQuery("(max-width: 640px)")

  // Abonelik özelliklerini kontrol et
  const { features, hasFeature } = useSubscriptionFeatures()

  // Default date range (last 6 months)
  const [dateRange, setDateRange] = useState({
    from: subMonths(new Date(), 6),
    to: new Date(),
  })

  // State for analytics data
  const [barberDistribution, setBarberDistribution] = useState<BarberDistribution[]>([])
  const [servicePopularity, setServicePopularity] = useState<ServicePopularity[]>([])
  const [appointmentTrends, setAppointmentTrends] = useState<AppointmentTrend[]>([])
  const [barberPerformance, setBarberPerformance] = useState<BarberPerformance[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Analitik özelliğine erişim kontrolü
  useEffect(() => {
    if (!hasFeature('analytics')) {
      router.push('/dashboard/subscription/upgrade')
      toast.error("Bu özelliği kullanmak için abonelik planınızı yükseltmeniz gerekiyor.")
    }
  }, [hasFeature, router])

  // Fetch data when component mounts or date range changes
  useEffect(() => {
    if (salonId && dateRange.from && dateRange.to && hasFeature('analytics')) {
      fetchAnalyticsData()
    }
  }, [salonId, dateRange, hasFeature])

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    if (!salonId || !dateRange.from || !dateRange.to) return

    setIsLoading(true)
    try {
      // Format dates for API requests
      const startDate = format(dateRange.from, "yyyy-MM-dd")
      const endDate = format(dateRange.to, "yyyy-MM-dd")

      console.log(`Fetching analytics data for salon: ${salonId} from ${startDate} to ${endDate}`)

      // Fetch barber distribution
      try {
        const distributionData = await analytics.getAppointmentDistributionByBarber(salonId, startDate, endDate)
        console.log('Barber distribution data:', distributionData)
        setBarberDistribution(distributionData)
      } catch (error) {
        console.error("Error fetching barber distribution:", error)
        toast.error("Personel dağılımı verileri yüklenirken bir hata oluştu")
        setBarberDistribution([])
      }

      // Fetch service popularity
      try {
        const popularityData = await analytics.getServicePopularity(salonId, startDate, endDate)
        console.log('Service popularity data:', popularityData)
        setServicePopularity(popularityData)
      } catch (error) {
        console.error("Error fetching service popularity:", error)
        toast.error("Hizmet popülerliği verileri yüklenirken bir hata oluştu")
        setServicePopularity([])
      }

      // Fetch appointment trends
      try {
        const trendsData = await analytics.getAppointmentTrends(salonId, startDate, endDate)
        console.log('Appointment trends data:', trendsData)
        setAppointmentTrends(trendsData)
      } catch (error) {
        console.error("Error fetching appointment trends:", error)
        toast.error("Randevu trendi verileri yüklenirken bir hata oluştu")
        setAppointmentTrends([])
      }

      // Fetch barber performance
      try {
        const performanceData = await analytics.getBarberPerformance(salonId, startDate, endDate)
        console.log('Barber performance data:', performanceData)
        setBarberPerformance(performanceData)
      } catch (error) {
        console.error("Error fetching barber performance:", error)
        toast.error("Personel performansı verileri yüklenirken bir hata oluştu")
        setBarberPerformance([])
      }
    } catch (error) {
      console.error("Error fetching analytics data:", error)
      toast.error("Analitik verileri yüklenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Analitik</h1>
        </div>
      </header>

      {/* Analitik özelliği erişim kontrolü */}
      {!hasFeature('analytics') && (
        <Alert className="mb-4">
          <AlertTitle>Abonelik Planı Gerekli</AlertTitle>
          <AlertDescription>
            Analitik özelliğini kullanmak için abonelik planınızı yükseltmeniz gerekiyor.
            <Link href="/dashboard/subscription/upgrade" className="ml-1 font-medium underline">
              Planınızı yükseltin
            </Link>
          </AlertDescription>
        </Alert>
      )}

      {hasFeature('analytics') && (
        <div className="mb-4">
          <DatePickerWithRange
            dateRange={dateRange}
            setDateRange={(range) => {
              if (range?.from && range?.to) {
                setDateRange({ from: range.from, to: range.to });
              }
            }}
          />
        </div>
      )}

      {isLoading && hasFeature('analytics') ? (
        <div className="flex justify-center items-center py-8 sm:py-12">
          <Loader2 className="h-6 w-6 sm:h-8 sm:w-8 animate-spin mr-2" />
          <span className="text-sm sm:text-base">Analitik verileri yükleniyor...</span>
        </div>
      ) : (
        <>
          {hasFeature('analytics') && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChartIcon className="h-5 w-5 text-primary" />
                    Randevu Dağılımı
                  </CardTitle>
                  <CardDescription>
                    Personel bazında randevu dağılımı
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {barberDistribution.length === 0 ? (
                    <div className="h-48 flex items-center justify-center bg-muted/20 rounded-md">
                      <p className="text-muted-foreground text-sm">Bu tarih aralığında veri bulunmamaktadır</p>
                    </div>
                  ) : (
                    <ResponsiveContainer width="100%" height={200}>
                      <PieChart>
                        <Pie
                          data={barberDistribution}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={isMobile ? 60 : 80}
                          fill="#8884d8"
                          dataKey="appointment_count"
                          nameKey="barber_name"
                          label={isMobile ? undefined : ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {barberDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value) => [
                            `${value} randevu`,
                            "Randevu Sayısı"
                          ]}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChartIcon className="h-5 w-5 text-primary" />
                    Hizmet Popülerliği
                  </CardTitle>
                  <CardDescription>
                    En çok tercih edilen hizmetler
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {servicePopularity.length === 0 ? (
                    <div className="h-48 flex items-center justify-center bg-muted/20 rounded-md">
                      <p className="text-muted-foreground text-sm">Bu tarih aralığında veri bulunmamaktadır</p>
                    </div>
                  ) : (
                    <ResponsiveContainer width="100%" height={200}>
                      <BarChart
                        data={servicePopularity.slice(0, 5)} // Show top 5 services
                        layout="vertical"
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis
                          type="category"
                          dataKey="service_name"
                          width={100}
                          tick={{ fontSize: isMobile ? 10 : 12 }}
                        />
                        <Tooltip
                          formatter={(value) => [
                            `${value} randevu (${servicePopularity.find(s => s.appointment_count === value)?.percentage}%)`,
                            "Randevu Sayısı"
                          ]}
                        />
                        <Bar dataKey="appointment_count" fill="#8884d8">
                          {servicePopularity.slice(0, 5).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <LineChartIcon className="h-5 w-5 text-primary" />
                    Randevu Trendi
                  </CardTitle>
                  <CardDescription>
                    Aylık randevu sayısı trendi
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {appointmentTrends.length === 0 ? (
                    <div className="h-48 flex items-center justify-center bg-muted/20 rounded-md">
                      <p className="text-muted-foreground text-sm">Bu tarih aralığında veri bulunmamaktadır</p>
                    </div>
                  ) : (
                    <ResponsiveContainer width="100%" height={200}>
                      <LineChart
                        data={appointmentTrends}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey="month"
                          tick={{ fontSize: isMobile ? 10 : 12 }}
                        />
                        <YAxis tick={{ fontSize: isMobile ? 10 : 12 }} />
                        <Tooltip
                          formatter={(value) => [
                            `${value} randevu`,
                            "Randevu Sayısı"
                          ]}
                          labelFormatter={(label) => {
                            const item = appointmentTrends.find(t => t.month === label);
                            return item ? `${item.month} ${item.year}` : label;
                          }}
                        />
                        <Line
                          type="monotone"
                          dataKey="appointment_count"
                          stroke="#8884d8"
                          activeDot={{ r: 8 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {hasFeature('analytics') && (
            <div className="mt-8">
              <Card>
                <CardHeader>
                  <CardTitle>Personel Performansı</CardTitle>
                  <CardDescription>
                    Personel bazında randevu ve müşteri istatistikleri
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {barberPerformance.length === 0 ? (
                    <div className="h-64 flex items-center justify-center bg-muted/20 rounded-md">
                      <p className="text-muted-foreground text-sm">Bu tarih aralığında veri bulunmamaktadır</p>
                    </div>
                  ) : (
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Personel</TableHead>
                            <TableHead className="text-right">Toplam Randevu</TableHead>
                            <TableHead className="text-right">Tamamlanan</TableHead>
                            <TableHead className="text-right">İptal Edilen</TableHead>
                            <TableHead className="text-right">Gelmeyenler</TableHead>
                            <TableHead className="text-right">Tamamlanma Oranı</TableHead>
                            <TableHead className="text-right">Benzersiz Müşteri</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {barberPerformance.map((barber) => (
                            <TableRow key={barber.barber_id}>
                              <TableCell className="font-medium">{barber.barber_name}</TableCell>
                              <TableCell className="text-right">{barber.total_appointments}</TableCell>
                              <TableCell className="text-right">{barber.completed_appointments}</TableCell>
                              <TableCell className="text-right">{barber.cancelled_appointments}</TableCell>
                              <TableCell className="text-right">{barber.no_show_appointments}</TableCell>
                              <TableCell className="text-right">{barber.completion_rate}%</TableCell>
                              <TableCell className="text-right">{barber.unique_customers}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </>
      )}
    </div>
  )
}
