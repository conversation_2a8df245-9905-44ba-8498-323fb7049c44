"use client"

import { motion } from "framer-motion"
import { Star, Quote } from "lucide-react"

interface TestimonialCardProps {
  testimonial: {
    id: string
    name: string
    rating: number
    comment: string
    service?: string
    date?: string
  }
  index: number
}

export function TestimonialCard({ testimonial, index }: TestimonialCardProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating 
            ? "text-yellow-400 fill-current" 
            : "text-gray-300 dark:text-gray-600"
        }`}
      />
    ))
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        ease: "easeOut"
      }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}
      className="group relative"
    >
      <div className="relative bg-background border border-border rounded-2xl p-6 shadow-sm hover:shadow-xl transition-all duration-500 overflow-hidden h-full">
        {/* Background Gradient on Hover */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        
        {/* Quote Icon */}
        <div className="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
          <Quote className="w-8 h-8 text-primary" />
        </div>
        
        {/* Content */}
        <div className="relative space-y-4 h-full flex flex-col">
          {/* Header */}
          <div className="flex items-start space-x-4">
            {/* Avatar */}
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center border-2 border-primary/10">
                <span className="text-sm font-semibold text-primary">
                  {getInitials(testimonial.name)}
                </span>
              </div>
            </div>
            
            {/* User Info */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-foreground truncate">
                {testimonial.name}
              </h3>
              {testimonial.service && (
                <p className="text-sm text-muted-foreground truncate">
                  {testimonial.service}
                </p>
              )}
              {testimonial.date && (
                <p className="text-xs text-muted-foreground/70">
                  {testimonial.date}
                </p>
              )}
            </div>
          </div>

          {/* Rating */}
          <div className="flex items-center space-x-1">
            {renderStars(testimonial.rating)}
            <span className="ml-2 text-sm font-medium text-foreground">
              {testimonial.rating}/5
            </span>
          </div>

          {/* Comment */}
          <div className="flex-1">
            <p className="text-muted-foreground leading-relaxed">
              "{testimonial.comment}"
            </p>
          </div>

          {/* Bottom Decoration */}
          <div className="pt-2">
            <div className="h-1 w-12 bg-gradient-to-r from-primary to-primary/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-tl from-primary/5 to-transparent rounded-tl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      </div>
    </motion.div>
  )
}
