"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

/**
 * Skeleton loader for the appointments page header
 */
export function AppointmentHeaderSkeleton() {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-32" />
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <Skeleton className="h-8 w-32 rounded-md" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
          <Skeleton className="h-7 w-7 rounded-md" />
        </div>
      </div>
    </div>
  )
}

/**
 * Skeleton loader for the weekly calendar view
 */
export function WeeklyCalendarSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
      {Array.from({ length: 7 }).map((_, index) => (
        <Card key={index} className="h-full border">
          <CardHeader className="p-3">
            <div className="flex justify-between items-center">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
            <Skeleton className="h-4 w-24 mt-1" />
          </CardHeader>
          <CardContent className="p-3 space-y-2">
            <Skeleton className="h-10 w-full" />
            {Array.from({ length: [1, 3, 2, 2, 1, 2, 1][index] }).map((_, idx) => (
              <Skeleton key={idx} className="h-20 w-full" />
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * Skeleton loader for the daily calendar view
 */
export function DailyCalendarSkeleton() {
  return (
    <Card>
      <CardHeader className="py-3">
        <div className="flex justify-between items-center">
          <Skeleton className="h-6 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {Array.from({ length: 20 }).map((_, index) => (
          <div key={index} className="flex items-center justify-between p-2 border rounded-md">
            <Skeleton className="h-5 w-12" />
            <Skeleton className="h-4 w-48" />
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

/**
 * Skeleton loader for the monthly calendar view
 */
export function MonthlyCalendarSkeleton() {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-7 gap-1 text-center">
        {Array.from({ length: 7 }).map((_, index) => (
          <Skeleton key={index} className="h-6 w-full" />
        ))}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: 35 }).map((_, index) => (
          <Card key={index} className="min-h-24 p-1">
            <div className="flex justify-between items-start">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-4 w-8" />
            </div>
            <div className="mt-2 space-y-1">
              {Array.from({ length: (index % 7 === 1 || index % 7 === 2) ? 1 : 0 }).map((_, idx) => (
                <Skeleton key={idx} className="h-20 w-full" />
              ))}
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}

/**
 * Main appointments page skeleton that adapts to the current view
 */
export function AppointmentsPageSkeleton({
  viewType = "weekly",
  className
}: {
  viewType?: "daily" | "weekly" | "monthly" | "custom",
  className?: string
}) {
  return (
    <div className={cn("space-y-4", className)}>
      <AppointmentHeaderSkeleton />

      {viewType === "weekly" && <WeeklyCalendarSkeleton />}
      {viewType === "daily" && <DailyCalendarSkeleton />}
      {viewType === "monthly" && <MonthlyCalendarSkeleton />}
      {viewType === "custom" && <WeeklyCalendarSkeleton />}
    </div>
  )
}

/**
 * Calendar-only skeleton loader that only shows skeleton for the calendar part
 */
export function CalendarOnlySkeleton({
  viewType = "weekly",
  className
}: {
  viewType?: "daily" | "weekly" | "monthly" | "custom",
  className?: string
}) {
  return (
    <div className={cn("space-y-4", className)}>
      {viewType === "weekly" && <WeeklyCalendarSkeleton />}
      {viewType === "daily" && <DailyCalendarSkeleton />}
      {viewType === "monthly" && <MonthlyCalendarSkeleton />}
      {viewType === "custom" && <WeeklyCalendarSkeleton />}
    </div>
  )
}

/**
 * Weekly calendar skeleton that only shows skeleton for appointment cards
 */
export function WeeklyCalendarAppointmentsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
      {Array.from({ length: 7 }).map((_, index) => (
        <Card key={index} className="h-full border">
          <CardHeader className="p-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-sm font-medium">
                {["Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi", "Pazar"][index]}
              </CardTitle>
            </div>
            <p className="text-xs text-muted-foreground">{`${15 + index} Mayıs`}</p>
          </CardHeader>
          <CardContent className="p-3 space-y-2">
            <div className="text-xs text-muted-foreground p-2 rounded-md hover:bg-muted cursor-pointer">
              Yeni randevu eklemek için tıklayın
            </div>
            {Array.from({ length: [1, 3, 2, 2, 1, 2, 1][index] }).map((_, idx) => (
              <Skeleton key={idx} className="h-20 w-full" />
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * Daily calendar skeleton that only shows skeleton for appointment cards
 */
export function DailyCalendarAppointmentsSkeleton() {
  return (
    <Card>
      <CardHeader className="py-3">
        <div className="flex justify-between items-center">
          <CardTitle>Perşembe, 18 Mayıs 2025</CardTitle>
          <div className="flex gap-2">
            <Badge variant="outline">Çalışma Saatleri: 09:00-19:00</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {["09:00", "09:30", "10:00", "10:30", "11:00", "11:30", "12:00", "12:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00", "17:30", "18:00", "18:30"].map((time, index) => (
          <div key={time} className="flex items-center p-2 border rounded-md">
            <div className="font-medium">{time}</div>
            {index % 5 === 1 ? (
              <div className="flex-1 ml-4">
                <Skeleton className="h-20 w-full" />
              </div>
            ) : (
              <div className="text-muted-foreground text-sm ml-4">
                Müsait - Randevu eklemek için tıklayın
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

/**
 * Monthly calendar skeleton that only shows skeleton for appointment indicators
 */
export function MonthlyCalendarAppointmentsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-7 gap-1 text-center mb-2">
        {["Pazartesi", "Salı", "Çarşamba", "Perşembe", "Cuma", "Cumartesi", "Pazar"].map((day) => (
          <div key={day} className="font-medium text-sm py-2">
            {day}
          </div>
        ))}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: 35 }).map((_, index) => {
          const isCurrentMonth = index >= 7 && index < 35;
          const displayDay = (index - 7 + 1);

          return (
            <div
              key={index}
              className={`min-h-24 p-2 border rounded-md ${!isCurrentMonth ? 'opacity-40' : ''} hover:bg-muted/50 cursor-pointer`}
            >
              <div className="flex justify-between items-start">
                {index === 12 && <div className="bg-red-100 text-red-800 text-xs px-1 rounded-sm">Tatil</div>}
                <span className={`inline-block rounded-full w-7 h-7 text-center leading-7 ${index === 15 ? 'bg-primary text-primary-foreground' : ''}`}>
                  {isCurrentMonth ? displayDay : ''}
                </span>
              </div>
              {isCurrentMonth && index % 7 !== 0 && index % 7 !== 6 && (
                <div className="space-y-1 mt-2">
                  {index % 5 === 2 && (
                    <>
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-20 w-full" />
                      <Skeleton className="h-4 w-20 mx-auto" />
                    </>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  )
}

/**
 * Appointments-only skeleton loader that only shows skeleton for appointment cards
 */
export function AppointmentsOnlySkeleton({
  viewType = "weekly",
  className
}: {
  viewType?: "daily" | "weekly" | "monthly" | "custom",
  className?: string
}) {
  return (
    <div className={cn("space-y-4", className)}>
      {viewType === "weekly" && <WeeklyCalendarAppointmentsSkeleton />}
      {viewType === "daily" && <DailyCalendarAppointmentsSkeleton />}
      {viewType === "monthly" && <MonthlyCalendarAppointmentsSkeleton />}
      {viewType === "custom" && <WeeklyCalendarAppointmentsSkeleton />}
    </div>
  )
}
