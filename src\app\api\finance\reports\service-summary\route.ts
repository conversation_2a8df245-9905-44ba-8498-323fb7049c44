import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { z } from 'zod';

// Define the request schema
const requestSchema = z.object({
  salon_id: z.string().uuid(),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
});

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const salon_id = url.searchParams.get('salon_id');
    const start_date = url.searchParams.get('start_date');
    const end_date = url.searchParams.get('end_date');

    // Validate parameters
    const params = requestSchema.parse({
      salon_id,
      start_date,
      end_date,
    });

    // Initialize Supabase client
    const supabase = createRouteHandlerClient({ cookies: () => cookies() });

    // Call the stored function
    const { data, error } = await supabase.rpc('get_financial_summary_by_service', {
      p_salon_id: params.salon_id,
      p_start_date: params.start_date,
      p_end_date: params.end_date,
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}