import { supabaseClient } from '../supabase-singleton';
import { ProductCategory, ProductCategoryInsert, ProductCategoryUpdate } from './types';

const supabase = supabaseClient;

/**
 * Get all product categories for a salon
 */
export async function getProductCategories(salonId: string) {
  const { data, error } = await supabase
    .from('product_categories')
    .select('*')
    .eq('salon_id', salonId)
    .order('name');

  if (error) throw error;
  return data as ProductCategory[];
}

/**
 * Get a product category by ID
 */
export async function getProductCategoryById(id: string) {
  const { data, error } = await supabase
    .from('product_categories')
    .select('*')
    .eq('id', id)
    .single();

  if (error) throw error;
  return data as ProductCategory;
}

/**
 * Create a new product category
 */
export async function createProductCategory(category: ProductCategoryInsert) {
  const { data, error } = await supabase
    .from('product_categories')
    .insert(category)
    .select()
    .single();

  if (error) throw error;
  return data as ProductCategory;
}

/**
 * Update a product category
 */
export async function updateProductCategory(category: ProductCategoryUpdate) {
  const { data, error } = await supabase
    .from('product_categories')
    .update(category)
    .eq('id', category.id)
    .select()
    .single();

  if (error) throw error;
  return data as ProductCategory;
}

/**
 * Delete a product category
 */
export async function deleteProductCategory(id: string) {
  const { error } = await supabase
    .from('product_categories')
    .delete()
    .eq('id', id);

  if (error) throw error;
  return true;
}
