"use client"

import { useState, useEffect, useMemo } from "react"
import { motion } from "framer-motion"
import { useMediaQuery } from 'react-responsive'
import {
  Monitor,
  ExternalLink,
  Eye
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"

import { useContent } from "@/contexts/ContentContext"
import { useUser } from "@/contexts/UserContext"
import { PreviewProvider } from "@/contexts/PreviewContext"
import { HeroSection } from "@/components/customer/hero-section"
import { ServicesSection } from "@/components/customer/services-section"
import { AboutSection } from "@/components/customer/about-section"
import { TestimonialsSection } from "@/components/customer/testimonials-section"
import { ContactSection } from "@/components/customer/contact-section"
import { parseSalon<PERSON>ontent, getDefaultSalonContent } from "@/lib/db/public"

interface ContentPreviewProps {
  isOpen: boolean
  onClose: () => void
}

type DeviceType = 'desktop' | 'tablet' | 'mobile'

const deviceSizes = {
  desktop: { width: '100%', maxWidth: '1200px', height: '100%' },
  tablet: { width: '768px', maxWidth: '768px', height: '100%' },
  mobile: { width: '375px', maxWidth: '375px', height: '100%' }
}

const deviceIcons = {
  desktop: Monitor,
}

export function ContentPreview({ isOpen, onClose }: ContentPreviewProps) {
  const { content, testimonials, hasChanges } = useContent()
  const { salon } = useUser()
  const [selectedDevice, setSelectedDevice] = useState<DeviceType>('desktop')
  const [isLoading, setIsLoading] = useState(false)

  // Parse content for preview
  const parsedContent = useMemo(() => {
    if (!content || content.length === 0) {
      return getDefaultSalonContent()
    }
    return parseSalonContent(content)
  }, [content])

  // Mock services data for preview (since we don't have services in CMS yet)
  const mockServices = [
    {
      id: '1',
      name: 'Saç Kesimi',
      description: 'Profesyonel saç kesimi ve şekillendirme',
      price: 150,
      duration: 45,
      is_active: true
    },
    {
      id: '2',
      name: 'Sakal Tıraşı',
      description: 'Geleneksel ustura ile sakal tıraşı',
      price: 100,
      duration: 30,
      is_active: true
    }
  ]

  const isMobile = useMediaQuery({ query: '(max-width: 767px)' });

  // Handle external preview (open in new tab)
  const handleExternalPreview = () => {
    if (!salon?.slug || isMobile) return
    const previewUrl = `/${salon.slug}`
    window.open(previewUrl, '_blank')
  }

  // Reset to desktop view when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedDevice('desktop')
    }
  }, [isOpen])

  // @ts-ignore
  // @ts-ignore
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Eye className="h-5 w-5 text-primary" />
              <DialogTitle>İçerik Önizleme</DialogTitle>
              {hasChanges && (
                <Badge variant="secondary" className="text-xs">
                  Kaydedilmemiş Değişiklikler
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* Device Size Switcher */}
              <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
                <Button
                  key="desktop"
                  variant={selectedDevice === "desktop" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setSelectedDevice("desktop")}
                  className="h-8 w-8 p-0"
                >
                  <Monitor className="h-4 w-4" />
                </Button>
              </div>

              <Separator orientation="vertical" className="h-6" />

              {/* External Preview */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleExternalPreview}
                disabled={!salon?.slug}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Yeni Sekmede Aç
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Preview Content */}
        <div className="flex-1 bg-muted/30 p-6 overflow-hidden">
          <div className="h-full flex items-center justify-center">
            <motion.div
              key={selectedDevice}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2 }}
              style={deviceSizes[selectedDevice]}
              className="bg-background rounded-lg border shadow-lg overflow-hidden"
            >
              <ScrollArea className="h-full">
                <div className="min-h-full">
                  {/* Preview Content with PreviewProvider */}
                  <PreviewProvider
                    isPreviewMode={true}
                    content={content}
                    testimonials={testimonials}
                  >
                    <div className="space-y-0">
                      {/* Hero Section */}
                      <div className="relative">
                        <HeroSection
                          salonName={salon?.name || 'Salon Adı'}
                          salonId={salon?.id || 'preview'}
                        />
                      </div>

                      {/* Services Section */}
                      <div className="relative">
                        <ServicesSection
                          services={mockServices}
                          salonId={salon?.id || 'preview'}
                        />
                      </div>

                      {/* About Section */}
                      <div className="relative">
                        <AboutSection
                          salonName={salon?.name || 'Salon Adı'}
                          salonId={salon?.id || 'preview'}
                        />
                      </div>

                      {/* Testimonials Section */}
                      <div className="relative">
                        <TestimonialsSection
                          salonName={salon?.name || 'Salon Adı'}
                          salonId={salon?.id || 'preview'}
                        />
                      </div>

                      {/* Contact Section */}
                      <div className="relative">
                        <ContactSection
                          salon={salon || {
                            id: 'preview',
                            name: 'Salon Adı',
                            slug: 'salon-adi',
                            phone: '+90 555 123 45 67',
                            email: '<EMAIL>',
                            address: 'Salon Adresi',
                            city: 'İstanbul',
                            district: 'Kadıköy'
                          }}
                          salonId={salon?.id || 'preview'}
                        />
                      </div>
                    </div>
                  </PreviewProvider>
                </div>
              </ScrollArea>
            </motion.div>
          </div>
        </div>

        {/* Footer Info */}
        <div className="p-4 border-t bg-muted/50">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <span>Cihaz: {selectedDevice === 'desktop' ? 'Masaüstü' : selectedDevice === 'tablet' ? 'Tablet' : 'Mobil'}</span>
              <span>Boyut: {deviceSizes[selectedDevice].width}</span>
            </div>
            {hasChanges && (
              <span className="text-amber-600">
                Değişikliklerinizi kaydetmeyi unutmayın
              </span>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
