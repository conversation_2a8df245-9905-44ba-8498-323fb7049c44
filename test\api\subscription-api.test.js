// Test file for subscription API functions
require('dotenv').config({ path: '.env.local' });
const { expect } = require('chai');
const sinon = require('sinon');
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create a real Supabase client for integration tests
const realSupabase = createClient(supabaseUrl, supabaseAnonKey);

// Mock Supabase client for unit tests
const mockSupabase = {
  from: sinon.stub(),
  auth: {
    getUser: sinon.stub()
  }
};

// Mock the Supabase client in the modules
jest.mock('@/lib/supabase', () => ({
  getSupabaseBrowser: () => mockSupabase
}));

// Import the API functions after mocking
const subscriptionPlans = require('../../src/lib/db/subscription-plans');
const subscriptions = require('../../src/lib/db/subscriptions');
const subscriptionPayments = require('../../src/lib/db/subscription-payments');
const referrals = require('../../src/lib/db/referrals');

describe('Subscription API Functions', () => {
  let sandbox;
  
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    
    // Reset all stubs
    mockSupabase.from.reset();
    mockSupabase.auth.getUser.reset();
    
    // Default mock implementation for from()
    const mockSelect = sinon.stub();
    const mockEq = sinon.stub();
    const mockSingle = sinon.stub();
    const mockInsert = sinon.stub();
    const mockUpdate = sinon.stub();
    const mockOrder = sinon.stub();
    
    mockSelect.returns({ eq: mockEq });
    mockEq.returns({ single: mockSingle });
    mockSingle.resolves({ data: null, error: null });
    
    mockInsert.returns({ select: mockSelect });
    mockUpdate.returns({ eq: mockEq });
    mockOrder.returns({ data: [], error: null });
    
    mockSupabase.from.returns({
      select: mockSelect,
      insert: mockInsert,
      update: mockUpdate,
      order: mockOrder
    });
    
    // Default mock implementation for auth.getUser()
    mockSupabase.auth.getUser.resolves({
      data: { user: { id: 'test-user-id', email: '<EMAIL>' } },
      error: null
    });
  });
  
  afterEach(() => {
    sandbox.restore();
  });
  
  describe('Subscription Plans API', () => {
    it('should get all subscription plans', async () => {
      const mockPlans = [
        { id: 'plan-1', name: 'Solo', price_monthly: 750 },
        { id: 'plan-2', name: 'Small Team', price_monthly: 1500 },
        { id: 'plan-3', name: 'Pro Salon', price_monthly: 5000 }
      ];
      
      const mockSelect = sinon.stub();
      const mockOrder = sinon.stub();
      
      mockSelect.returns({ order: mockOrder });
      mockOrder.resolves({ data: mockPlans, error: null });
      
      mockSupabase.from.withArgs('subscription_plans').returns({
        select: mockSelect
      });
      
      const result = await subscriptionPlans.getSubscriptionPlans();
      
      expect(result).to.deep.equal(mockPlans);
      expect(mockSupabase.from.calledWith('subscription_plans')).to.be.true;
      expect(mockSelect.called).to.be.true;
      expect(mockOrder.called).to.be.true;
    });
    
    it('should get a subscription plan by ID', async () => {
      const mockPlan = {
        id: 'plan-1',
        name: 'Solo',
        price_monthly: 750,
        max_staff: 1,
        features: {
          analytics: false,
          finance: false,
          custom_domain: false
        }
      };
      
      const mockSelect = sinon.stub();
      const mockEq = sinon.stub();
      const mockSingle = sinon.stub();
      
      mockSelect.returns({ eq: mockEq });
      mockEq.returns({ single: mockSingle });
      mockSingle.resolves({ data: mockPlan, error: null });
      
      mockSupabase.from.withArgs('subscription_plans').returns({
        select: mockSelect
      });
      
      const result = await subscriptionPlans.getSubscriptionPlanById('plan-1');
      
      expect(result).to.deep.equal(mockPlan);
      expect(mockSupabase.from.calledWith('subscription_plans')).to.be.true;
      expect(mockSelect.called).to.be.true;
      expect(mockEq.calledWith('id', 'plan-1')).to.be.true;
      expect(mockSingle.called).to.be.true;
    });
    
    it('should check if a plan has a feature', () => {
      const plan = {
        id: 'plan-1',
        name: 'Pro Salon',
        features: {
          analytics: true,
          finance: true,
          custom_domain: false
        }
      };
      
      expect(subscriptionPlans.planHasFeature(plan, 'analytics')).to.be.true;
      expect(subscriptionPlans.planHasFeature(plan, 'finance')).to.be.true;
      expect(subscriptionPlans.planHasFeature(plan, 'custom_domain')).to.be.false;
      expect(subscriptionPlans.planHasFeature(plan, 'nonexistent')).to.be.false;
      expect(subscriptionPlans.planHasFeature(null, 'analytics')).to.be.false;
    });
    
    it('should get maximum staff count for a plan', () => {
      const plan = {
        id: 'plan-1',
        name: 'Small Team',
        max_staff: 5
      };
      
      expect(subscriptionPlans.getPlanMaxStaff(plan)).to.equal(5);
      expect(subscriptionPlans.getPlanMaxStaff(null)).to.equal(0);
      expect(subscriptionPlans.getPlanMaxStaff({})).to.equal(0);
    });
    
    it('should calculate yearly price with discount', () => {
      expect(subscriptionPlans.calculateYearlyPrice(1000)).to.equal(10800); // 1000 * 12 * 0.9
      expect(subscriptionPlans.calculateYearlyPrice(750)).to.equal(8100);   // 750 * 12 * 0.9
    });
  });
  
  // Additional tests for other API functions can be added here
});
