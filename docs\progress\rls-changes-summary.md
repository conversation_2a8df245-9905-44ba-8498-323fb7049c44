# RLS Politikaları Değişiklik Özeti

## Ya<PERSON><PERSON><PERSON> Değişiklikler

Anonim kullanıcıların landing page üzerinden randevu oluşturabilmesi için aşağıdaki RLS politikaları eklenmiştir:

### 1. <PERSON><PERSON><PERSON> (barbers) Tablosu
- **Eklenen Politika**: "Anyone can view barbers"
  - Her<PERSON>in berberleri görüntülemesine izin verir (SELECT)
  - Salon sahipleri hala kendi salonlarındaki berberleri yönetebilir (INSERT, UPDATE, DELETE)

### 2. <PERSON><PERSON><PERSON><PERSON> (services) Tablosu
- **Eklenen Politika**: "Anyone can view services"
  - Herkesin hizmetleri görüntülemesine izin verir (SELECT)
  - Salon sahipleri hala kendi salonlarındaki hizmetleri yönetebilir (INSERT, UPDATE, DELETE)

### 3. <PERSON><PERSON><PERSON> (appointments) Tablosu
- **Düzenlenen Politikalar**:
  - "Salon owners can manage appointments at their salon" - Salon sahiplerinin kendi salonlarındaki randevuları yönetmesine izin verir
  - **Eklenen Politika**: "Anyone can insert appointments" - <PERSON><PERSON><PERSON> randevu oluşturmasına izin verir
  - **Eklenen Politika**: "Anyone can view appointments" - Herkesin randevuları görüntülemesine izin verir (müsait saatleri kontrol etmek için)

### 4. Müşteriler (customers) Tablosu
- **Kaldırılan Politikalar**:
  - "Anyone can do anything with customers"
  - "Salon owners can manage their salon's customers"
- **Eklenen Politikalar**:
  - "Anyone can insert customers" - Herkesin müşteri eklemesine izin verir
  - "Salon owners can view their salon's customers" - Salon sahiplerinin kendi müşterilerini görüntülemesine izin verir
  - "Salon owners can update their salon's customers" - Salon sahiplerinin kendi müşterilerini güncellemesine izin verir
  - "Salon owners can delete their salon's customers" - Salon sahiplerinin kendi müşterilerini silmesine izin verir

## Sonuç

Bu değişikliklerle:
1. Anonim kullanıcılar berberleri ve hizmetleri görüntüleyebilir
2. Anonim kullanıcılar randevu oluşturabilir ve müsait saatleri kontrol edebilir
3. Anonim kullanıcılar müşteri bilgisi ekleyebilir
4. Salon sahipleri kendi salonlarına ait verileri yönetebilir

Bu sayede, kullanıcılar üye olmadan sadece kişisel bilgileriyle randevu oluşturabilir hale gelmiştir.
