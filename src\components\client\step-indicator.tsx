"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import {
  Scissors,
  User,
  Calendar,
  Clock,
  UserCircle,
  CheckCircle,
  Check
} from "lucide-react"
import React from "react"

type Step = "service" | "barber" | "date" | "time" | "info" | "summary"

interface StepIndicatorProps {
  currentStep: Step
  steps: Step[]
}

export function StepIndicator({ currentStep, steps }: StepIndicatorProps) {
  const stepIcons = {
    service: Scissors,
    barber: User,
    date: Calendar,
    time: Clock,
    info: UserCircle,
    summary: CheckCircle
  }

  const stepLabels = {
    service: "Hizmet",
    barber: "Berber",
    date: "Tarih",
    time: "Saat",
    info: "Bilgiler",
    summary: "Onay"
  }

  const stepDescriptions = {
    service: "Almak istediğiniz hizmeti seçin",
    barber: "Tercih ettiğiniz berberi seçin",
    date: "Randevu tarihinizi belirleyin",
    time: "Uygun saati seçin",
    info: "İletişim bilgilerinizi girin",
    summary: "<PERSON>ev<PERSON> detaylarını onaylayın"
  }

  const currentStepIndex = steps.indexOf(currentStep)
  const progressPercentage = ((currentStepIndex + 1) / steps.length) * 100

  return (
    <div className="w-full bg-gradient-to-r from-muted/20 to-muted/30 px-6 py-5 border-b border-border/50">
      {/* Progress Bar */}
      <div className="w-full bg-muted/50 rounded-full h-2 mb-4 overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
        />
      </div>

      {/* Desktop Step Indicator */}
      <div className="hidden md:flex items-start justify-between max-w-4xl mx-auto">
        {steps.map((step, index) => {
          const StepIcon = stepIcons[step]
          const isActive = index === currentStepIndex
          const isCompleted = index < currentStepIndex

          return (
            <div key={step} className="flex flex-col items-center relative flex-1">
              <motion.div
                className={cn(
                  "w-12 h-12 rounded-full flex items-center justify-center mb-3 transition-all duration-300 shadow-sm",
                  isActive ? "bg-primary text-primary-foreground shadow-primary/25" :
                  isCompleted ? "bg-primary/90 text-primary-foreground" :
                  "bg-muted text-muted-foreground"
                )}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isCompleted ? (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Check className="w-5 h-5" />
                  </motion.div>
                ) : (
                  <StepIcon className="w-5 h-5" />
                )}
              </motion.div>

              <div className="text-center h-16 flex flex-col justify-start">
                <span className={cn(
                  "text-sm font-semibold block mb-1",
                  isActive ? "text-primary" :
                  isCompleted ? "text-primary/90" :
                  "text-muted-foreground"
                )}>
                  {stepLabels[step]}
                </span>
                <span className={cn(
                  "text-xs leading-tight line-clamp-2",
                  isActive ? "text-foreground/80" :
                  isCompleted ? "text-foreground/60" :
                  "text-muted-foreground/70"
                )}>
                  {stepDescriptions[step]}
                </span>
              </div>

              {index < steps.length - 1 && (
                <div className="absolute top-6 left-[60%] right-[-40%] h-[2px] -translate-y-1/2">
                  <motion.div
                    className={cn(
                      "h-full bg-muted/50 rounded-full",
                      isCompleted && "bg-primary/30"
                    )}
                    initial={{ scaleX: 0 }}
                    animate={{ scaleX: isCompleted ? 1 : 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                    style={{ transformOrigin: "left" }}
                  />
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Mobile view - Enhanced */}
      <div className="md:hidden">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <motion.div
              className="w-10 h-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center shadow-sm"
              whileHover={{ scale: 1.05 }}
            >
              {React.createElement(stepIcons[currentStep], { className: "w-5 h-5" })}
            </motion.div>
            <div>
              <span className="font-semibold text-sm text-primary">{stepLabels[currentStep]}</span>
              <p className="text-xs text-muted-foreground leading-tight">
                {stepDescriptions[currentStep]}
              </p>
            </div>
          </div>

          <div className="text-right">
            <div className="text-xs font-medium text-muted-foreground">
              Adım {currentStepIndex + 1}/{steps.length}
            </div>
          </div>
        </div>

        {/* Step navigation dots for mobile */}
        <div className="flex items-center justify-center space-x-2">
          {steps.map((_, index) => (
            <motion.div
              key={index}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-300",
                index === currentStepIndex ? "bg-primary w-6" :
                index < currentStepIndex ? "bg-primary/60" :
                "bg-muted"
              )}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: index * 0.1 }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
