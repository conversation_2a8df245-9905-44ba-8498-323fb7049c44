# SalonFlow Referans Sistemi ve Ödeme Yönetimi İyileştirmeleri

Bu belge, SalonFlow referans sistemi ve ödeme yönetimi süreçlerinde yapılacak değişiklikleri ve iyileştirmeleri içermektedir.

## Mevcut Durum Analizi

### Referans Sistemi
- Mevcut sistemde referans veren kişi 1 ay ücretsiz abonelik kazanıyor.
- Referans alan kişi 30 gün uzatılmış deneme süresi kazanıyor.
- Referans kodları veritabanında `referral_codes` tablosunda saklanıyor.
- Referans faydaları `referral_benefits` tablosunda saklanıyor.
- `applyReferralCode` fonksiyonu, referans kodunu uygulayarak referans faydalarını oluşturuyor.

### Admin Ödeme Ekleme Süreci
- Admin, abonelik detay sayfasından ödeme ekleyebiliyor.
- Ödeme tutarı manuel olarak girilebiliyor.
- Ödeme tarihi ve fatura numarası girilebiliyor.
- Ödeme kaydı oluşturulduğunda "completed" durumunda kaydediliyor.
- Ödeme bilgileri `subscription_payments` tablosunda saklanıyor.

## İstenen Değişiklikler

### 1. Referans Sistemi Değişikliği
- Referans veren kişi 1 ay ücretsiz abonelik KAZANMASIN
- Bunun yerine, referans alan kişi ilk ödemesinde güncel en düşük paket ücreti kadar indirim kazansın (şu anda 750 TL)
- Referans indirimi, referans ile üye olan kişinin ödeme yapması durumunda uygulanacak
- Bu değişiklik için veritabanı yapısı ve uygulama akışında güncelleme gerekecek

### 2. Admin Ödeme Ekleme Sürecinin İyileştirilmesi
- Rastgele fiyat girilememelidir
- Admin, aboneliğin hangi dönemi için ödeme aldığını seçebilmelidir (ay veya tarih aralığı)
- Kullanıcının indirim hakkı varsa, bu otomatik olarak uygulanmalıdır
- Ödeme tutarı seçilen plan ve indirimler baz alınarak otomatik hesaplanmalıdır
- Admin sadece ödemenin alındığını onaylamalıdır
- Bu süreç hata yapma olasılığını azaltacak ve takibi kolaylaştıracaktır

## Geliştirme Görevleri

### 1. Veritabanı Değişiklikleri

#### 1.1. Referans Sistemi Veritabanı Değişiklikleri
- **Zorluk:** Orta
- **Tahmini Süre:** 4 saat

##### 1.1.1. `referral_benefits` Tablosunu Güncelleme
- SQL script hazırlama: `referral_benefits` tablosuna yeni alanlar ekleyecek SQL kodunu yazma
  - `discount_amount` (NUMERIC): İndirim tutarı
  - `discount_applied` (BOOLEAN): İndirimin uygulanıp uygulanmadığı
  - `discount_applied_payment_id` (UUID): İndirimin uygulandığı ödeme ID'si
- Mevcut kayıtları güncelleme: `benefit_type` değerini 'referred_discount' olarak güncelleme
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.2. Ödeme Yönetimi Veritabanı Değişiklikleri
- **Zorluk:** Orta
- **Tahmini Süre:** 4 saat

##### 1.2.1. `subscription_payments` Tablosunu Güncelleme
- SQL script hazırlama: `subscription_payments` tablosuna yeni alanlar ekleyecek SQL kodunu yazma
  - `period_start_date` (DATE): Ödeme döneminin başlangıç tarihi
  - `period_end_date` (DATE): Ödeme döneminin bitiş tarihi
  - `original_amount` (NUMERIC): İndirim öncesi orijinal tutar
  - `discount_amount` (NUMERIC): İndirim tutarı
  - `discount_type` (VARCHAR): İndirim tipi (referral, promo, vb.)
  - `discount_reference_id` (UUID): İndirim referans ID'si
- SQL scriptini Supabase'de çalıştırma ve test etme

### 2. Backend Değişiklikleri

#### 2.1. Referans Sistemi API Değişiklikleri
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** 1.1. Referans Sistemi Veritabanı Değişiklikleri

##### 2.1.1. `src/lib/db/types.ts` Dosyasını Güncelleme
- `ReferralBenefit` interface'ini güncelleme
- `ReferralBenefitInsert` ve `ReferralBenefitUpdate` tiplerini güncelleme

##### 2.1.2. `src/lib/db/referrals.ts` Dosyasını Güncelleme
- `applyReferralCode` fonksiyonunu güncelleme
- Yeni fonksiyon ekleme: `checkReferralDiscount`
- Yeni fonksiyon ekleme: `applyReferralDiscount`

#### 2.2. Ödeme Yönetimi API Değişiklikleri
- **Zorluk:** Zor
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** 1.2. Ödeme Yönetimi Veritabanı Değişiklikleri

##### 2.2.1. `src/lib/db/types.ts` Dosyasını Güncelleme
- `SubscriptionPayment` interface'ini güncelleme
- `SubscriptionPaymentInsert` ve `SubscriptionPaymentUpdate` tiplerini güncelleme

##### 2.2.2. `src/lib/db/subscription-payments.ts` Dosyasını Güncelleme
- Yeni fonksiyon ekleme: `calculatePaymentAmount`
- Yeni fonksiyon ekleme: `checkAvailableDiscounts`
- `createSubscriptionPayment` fonksiyonunu güncelleme

### 3. Frontend Değişiklikleri

#### 3.1. Referans Sistemi Frontend Değişiklikleri
- **Zorluk:** Kolay
- **Tahmini Süre:** 4 saat
- **Bağımlılıklar:** 2.1. Referans Sistemi API Değişiklikleri

##### 3.1.1. `src/app/dashboard/referrals/page.tsx` Dosyasını Güncelleme
- Referans sistemi açıklamalarını güncelleme
- Referans faydaları görüntüleme bileşenini güncelleme

#### 3.2. Admin Ödeme Ekleme Sayfası İyileştirmeleri
- **Zorluk:** Zor
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** 2.2. Ödeme Yönetimi API Değişiklikleri

##### 3.2.1. `src/app/admin/subscriptions/[id]/payment/page.tsx` Dosyasını Güncelleme
- Ödeme ekleme formunu güncelleme
- Tarih aralığı seçimi için DatePicker bileşenleri ekleme
- Otomatik tutar hesaplama mantığı ekleme
- İndirim kontrolü ve gösterimi ekleme
- Ödeme özeti bileşeni ekleme

### 4. Test ve Dokümantasyon

#### 4.1. Birim Testleri
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** Tüm geliştirme görevleri

##### 4.1.1. Referans Sistemi API Testleri
- `applyReferralCode` fonksiyonu için birim testleri yazma
- `checkReferralDiscount` fonksiyonu için birim testleri yazma
- `applyReferralDiscount` fonksiyonu için birim testleri yazma

##### 4.1.2. Ödeme Yönetimi API Testleri
- `calculatePaymentAmount` fonksiyonu için birim testleri yazma
- `checkAvailableDiscounts` fonksiyonu için birim testleri yazma
- `createSubscriptionPayment` fonksiyonu için birim testleri yazma

#### 4.2. Entegrasyon Testleri
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** Tüm geliştirme görevleri

##### 4.2.1. Referans Sistemi Entegrasyon Testleri
- Referans kodu oluşturma ve paylaşma akışı testi
- Referans kodu ile kayıt olma akışı testi
- Referans indirimi uygulama akışı testi (ilk ödeme yapıldığında)
- Güncel en düşük plan ücretinin doğru şekilde indirim olarak uygulandığını doğrulama testi

##### 4.2.2. Ödeme Yönetimi Entegrasyon Testleri
- Ödeme dönemi seçimi ve tutar hesaplama akışı testi
- İndirim uygulama akışı testi
- Ödeme onaylama akışı testi

#### 4.3. Dokümantasyon
- **Zorluk:** Kolay
- **Tahmini Süre:** 4 saat
- **Bağımlılıklar:** Tüm geliştirme görevleri

##### 4.3.1. Teknik Dokümantasyon
- API dokümantasyonu güncelleme
- Veritabanı şeması dokümantasyonu güncelleme
- Referans sistemi ve ödeme yönetimi akış diyagramları oluşturma

##### 4.3.2. Kullanıcı Dokümantasyonu
- Salon sahibi için referans sistemi kılavuzu güncelleme
- Admin için ödeme ekleme kılavuzu güncelleme

## Uygulama Aşamaları ve Öncelikler

### Aşama 1: Veritabanı Değişiklikleri (1 gün)
- **Öncelik:** Yüksek
- **Görevler:**
  - 1.1. Referans Sistemi Veritabanı Değişiklikleri
  - 1.2. Ödeme Yönetimi Veritabanı Değişiklikleri

### Aşama 2: Backend Değişiklikleri (3 gün)
- **Öncelik:** Yüksek
- **Görevler:**
  - 2.1. Referans Sistemi API Değişiklikleri
  - 2.2. Ödeme Yönetimi API Değişiklikleri

### Aşama 3: Frontend Değişiklikleri (3 gün)
- **Öncelik:** Orta
- **Görevler:**
  - 3.1. Referans Sistemi Frontend Değişiklikleri
  - 3.2. Admin Ödeme Ekleme Sayfası İyileştirmeleri

### Aşama 4: Test ve Dokümantasyon (2 gün)
- **Öncelik:** Düşük
- **Görevler:**
  - 4.1. Birim Testleri
  - 4.2. Entegrasyon Testleri
  - 4.3. Dokümantasyon

## Geliştirme Süreci Takibi

### Görev Durumu Takibi
- Görevlerin durumunu takip etmek için bir Kanban board oluşturma
- Görevleri "Yapılacak", "Devam Ediyor", "Test Ediliyor", "Tamamlandı" durumlarında izleme
- Haftalık ilerleme raporları oluşturma

### Kod İnceleme Süreci
- Her görev için kod incelemesi yapma
- Kod kalitesi ve standartlara uygunluk kontrolü
- Performans ve güvenlik değerlendirmesi
