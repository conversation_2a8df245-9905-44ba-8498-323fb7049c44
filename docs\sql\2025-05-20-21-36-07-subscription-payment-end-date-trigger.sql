-- Abonelik ödeme bitiş tarihi güncelleme trigger'ı
-- Tarih: 2025-05-20

-- Bu trigger, bir ödeme kaydı oluşturulduğunda veya güncellendiğinde
-- abonelik bitiş tarihini otomatik olarak günceller.

-- Create a function that will be called by the trigger
CREATE OR REPLACE FUNCTION update_subscription_end_date()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if the payment status is 'completed'
  IF NEW.status = 'completed' THEN
    -- Update the subscription end date and status
    UPDATE salon_subscriptions
    SET 
      end_date = NEW.period_end_date,
      status = CASE 
                WHEN status IN ('trial', 'past_due', 'suspended') THEN 'active'
                ELSE status
              END,
      updated_at = NOW()
    WHERE id = NEW.subscription_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
DROP TRIGGER IF EXISTS update_subscription_end_date_trigger ON subscription_payments;

CREATE TRIGGER update_subscription_end_date_trigger
AFTER INSERT OR UPDATE OF status
ON subscription_payments
FOR EACH ROW
EXECUTE FUNCTION update_subscription_end_date();
