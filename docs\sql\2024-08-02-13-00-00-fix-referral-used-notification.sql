-- Referral Used Notification Sorunu <PERSON>ü<PERSON>ü
-- Tarih: 2024-08-02

-- 1. Mevcut notifications_type_check kı<PERSON><PERSON>tlamasını kaldır
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS notifications_type_check;

-- 2. <PERSON><PERSON> k<PERSON> (referral_used değerini de kabul edecek şekilde)
ALTER TABLE notifications ADD CONSTRAINT notifications_type_check 
  CHECK (type IN ('new_booking', 'cancellation', 'update', 'subscription_reminder', 'payment_reminder', 'referral_used'));

-- 3. Notifications tablosundaki kısıtlamaları kontrol et
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'notifications'::regclass;

-- 4. notify_referral_used fonksiyonunu kontrol et
SELECT pg_get_functiondef(oid) 
FROM pg_proc 
WHERE proname = 'notify_referral_used';

-- 5. Test bildirim ekleme
DO $$
DECLARE
  test_salon_id UUID := '00000000-0000-0000-0000-000000000001';
  test_user_id UUID := '00000000-0000-0000-0000-000000000002';
BEGIN
  -- Test bildirimini ekle
  INSERT INTO notifications (
    salon_id, 
    user_id, 
    type, 
    title, 
    message, 
    read, 
    data
  ) VALUES (
    test_salon_id, 
    test_user_id, 
    'referral_used', 
    'Test Bildirim', 
    'Bu bir test bildirimidir.', 
    false, 
    jsonb_build_object('test', true)
  );
  
  -- Test bildirimini sil
  DELETE FROM notifications 
  WHERE salon_id = test_salon_id AND user_id = test_user_id AND title = 'Test Bildirim';
  
  RAISE NOTICE 'Test başarılı: Notifications tablosuna referral_used tipinde bildirim eklenebiliyor.';
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Test başarısız: %', SQLERRM;
END;
$$;
