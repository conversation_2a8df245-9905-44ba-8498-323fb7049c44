"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { AppointmentForm } from "@/components/appointment-form-new"
import { useUser } from "@/contexts/UserContext"

export default function NewAppointmentPage() {
  // UserContext'ten salon bilgilerini al
  const { salonId, salonLoading } = useUser()

  if (salonLoading) {
    return (
      <div className="p-3 sm:p-4">
        <div className="flex justify-center items-center h-40 sm:h-64">
          <p>Yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (!salonId) {
    return (
      <div className="p-3 sm:p-4">
        <header className="flex h-12 sm:h-16 shrink-0 items-center gap-1 sm:gap-2 mb-2 sm:mb-4">
          <div className="flex items-center gap-1 sm:gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-1 sm:mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-xl sm:text-2xl font-bold">Yeni Randevu</h1>
          </div>
        </header>

        <div className="flex flex-col items-center justify-center h-40 sm:h-64 space-y-3 sm:space-y-4">
          <p className="text-center text-sm sm:text-base">Salon bulunamadı. Lütfen önce bir salon oluşturun.</p>
          <Button asChild size="sm" className="w-full sm:w-auto">
            <Link href="/dashboard/settings">Salon Oluştur</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-3 sm:p-4 space-y-4 sm:space-y-6">
      <header className="flex h-12 sm:h-16 shrink-0 items-center gap-1 sm:gap-2 mb-2 sm:mb-4">
        <div className="flex items-center gap-1 sm:gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-1 sm:mr-2 data-[orientation=vertical]:h-4"
          />
          <Button variant="outline" size="icon" asChild className="mr-1 sm:mr-2 h-8 w-8 sm:h-9 sm:w-9">
            <Link href="/dashboard/appointments">
              <ArrowLeft className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            </Link>
          </Button>
          <h1 className="text-xl sm:text-2xl font-bold">Yeni Randevu</h1>
        </div>
      </header>

      <div className="bg-card rounded-lg border p-3 sm:p-4 md:p-6">
        <AppointmentForm salonId={salonId} />
      </div>
    </div>
  )
}
