"use client"


import Link from "next/link"
import { Plus } from "lucide-react"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { But<PERSON> } from "@/components/ui/button"
import { AppointmentCalendar } from "@/components/appointment-calendar"
import { useUser } from "@/contexts/UserContext"
import { AppointmentsOnlySkeleton } from "@/components/ui/skeleton-loaders"

export default function AppointmentsPage() {
  // UserContext'ten salon bilgilerini al
  const { salonId, salonLoading } = useUser()

  if (salonLoading) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center justify-between gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Randevular</h1>
          </div>
          <Button asChild>
            <Link href="/dashboard/appointments/new">
              <Plus className="mr-2 h-4 w-4" />
              Yeni Randevu
            </Link>
          </Button>
        </header>

        <AppointmentsOnlySkeleton viewType="weekly" />
      </div>
    )
  }

  if (!salonId) {
    return (
      <div className="p-4">
        <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <h1 className="text-2xl font-bold">Randevular</h1>
          </div>
        </header>

        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <p>Salon bulunamadı. Lütfen önce bir salon oluşturun.</p>
          <Button asChild>
            <Link href="/dashboard/settings">Salon Oluştur</Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Randevular</h1>
        </div>
        <Button asChild>
          <Link href="/dashboard/appointments/new">
            <Plus className="mr-2 h-4 w-4" />
            Yeni Randevu
          </Link>
        </Button>
      </header>

      <AppointmentCalendar salonId={salonId} />
    </div>
  )
}
