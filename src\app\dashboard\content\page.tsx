"use client"

import { useState, useEffect } from "react"
import { FileText, Eye, Save, RotateCcw } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"

import { useUser } from "@/contexts/UserContext"
import { ContentProvider, useContent } from "@/contexts/ContentContext"
import { HeroContentEditor } from "@/components/cms/hero-content-editor"
import { AboutContentEditor } from "@/components/cms/about-content-editor"
import { ServicesContentEditor } from "@/components/cms/services-content-editor"
import { TestimonialsManager } from "@/components/cms/testimonials-manager"
import { ContactContentEditor } from "@/components/cms/contact-content-editor"
import { ContentPreview } from "@/components/cms/content-preview"
import {useMediaQuery} from "react-responsive";

// Main content component that uses ContentProvider
function ContentManagementContent() {
  const { userRole } = useUser()
  const {
    isLoading,
    hasChanges,
    saveContent,
    resetChanges,
  } = useContent()

  // Preview modal state
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  // Sadece salon sahibi erişebilir
  useEffect(() => {
    if (userRole !== "owner") {
      toast.error("Bu sayfaya erişim yetkiniz yok.")
      return
    }
  }, [userRole])

  // Önizleme modal'ını aç
  const handlePreview = () => {
    setIsPreviewOpen(true)
  }

  // Değişiklikleri kaydet
  const handleSave = async () => {
    if (!hasChanges) {
      toast.info("Kaydedilecek değişiklik yok")
      return
    }

    try {
      await saveContent()
    } catch (error) {
      // Error is already handled in saveContent
    }
  }

  // Değişiklikleri geri al
  const handleReset = () => {
    if (!hasChanges) {
      toast.info("Geri alınacak değişiklik yok")
      return
    }

    resetChanges()
  }

  // İçerik değişikliği callback'i
  const handleContentChange = () => {
    // Bu fonksiyon child component'ler tarafından çağrılır
    // Şu anda herhangi bir işlem yapmıyor, ama gelecekte
    // real-time preview güncellemeleri için kullanılabilir
  }

  if (userRole !== "owner") {
    return (
      <div className="p-4">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold">Erişim Yetkisi Yok</h3>
            <p className="text-muted-foreground">Bu sayfaya sadece salon sahipleri erişebilir.</p>
          </div>
        </div>
      </div>
    )
  }

  const isMobile = useMediaQuery({query: '(max-width: 640px)'})


  return (
    <div className="p-4">
      {/* Header */}
      <header className="flex h-16 shrink-0 items-center gap-2 mb-6">
        <div className="flex items-center gap-2 flex-1">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
          <div className="flex items-center gap-2">
            <FileText className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold">İçerik Yönetimi</h1>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          {!isMobile && <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              disabled={isLoading}
          >
            <Eye className="mr-2 h-4 w-4" />
            Önizleme
          </Button>}

          {hasChanges && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                disabled={isLoading}
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                Geri Al
              </Button>

              <Button
                size="sm"
                onClick={handleSave}
                disabled={isLoading}
              >
                <Save className="mr-2 h-4 w-4" />
                {isLoading ? "Kaydediliyor..." : "Kaydet"}
              </Button>
            </>
          )}
        </div>
      </header>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle>Salon Landing Sayfası İçerik Yönetimi</CardTitle>
            <CardDescription>
              Müşterilerinizin göreceği landing sayfasındaki tüm içerikleri buradan düzenleyebilirsiniz.
              Değişiklikleriniz anında yayına alınır.
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Content Tabs */}
        {isLoading ? (
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue="hero" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="hero">Ana Bölüm</TabsTrigger>
              <TabsTrigger value="about">Hakkımızda</TabsTrigger>
              <TabsTrigger value="services">Hizmetler</TabsTrigger>
              <TabsTrigger value="testimonials">Yorumlar</TabsTrigger>
              <TabsTrigger value="contact">İletişim</TabsTrigger>
            </TabsList>

            <TabsContent value="hero">
              <Card>
                <CardHeader>
                  <CardTitle>Ana Bölüm (Hero Section)</CardTitle>
                  <CardDescription>
                    Sayfanızın en üst kısmında görünen ana başlık, açıklama ve istatistikler
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <HeroContentEditor />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="about">
              <Card>
                <CardHeader>
                  <CardTitle>Hakkımızda Bölümü</CardTitle>
                  <CardDescription>
                    Salonunuz hakkında bilgiler, özellikler ve istatistikler
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <AboutContentEditor />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="services">
              <ServicesContentEditor />
            </TabsContent>

            <TabsContent value="testimonials">
              <TestimonialsManager onContentChange={handleContentChange} />
            </TabsContent>

            <TabsContent value="contact">
              <ContactContentEditor onContentChange={handleContentChange} />
            </TabsContent>
          </Tabs>
        )}

        {/* Content Preview Modal */}
        <ContentPreview
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
        />
      </div>
    </div>
  )
}

// Main page component with ContentProvider wrapper
export default function ContentManagementPage() {
  return (
    <ContentProvider>
      <ContentManagementContent />
    </ContentProvider>
  )
}
