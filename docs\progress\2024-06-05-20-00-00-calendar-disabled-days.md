# Takvimde Çalışılmayan Günleri Devre Dışı Bırakma - 2024-06-05

## Yap<PERSON>lan İşlemler

1. DatePicker bileşeni güncellendi:
   - `disabledDates` prop'u eklendi (tarih dizisi veya fonksiyon olabilir)
   - Calendar bileşenine `disabled` prop'u geçirildi

2. Booking-form.tsx dosyası güncellendi:
   - Berber çalışma saatlerini saklamak için yeni bir state eklendi
   - Bir tarihin devre dışı bırakılıp bırakılmayacağını kontrol eden `isDateDisabled` fonksiyonu eklendi
   - Berber çalışma saatlerini yükleyen `loadBarberWorkingHours` fonksiyonu eklendi
   - Berber seçildiğinde çalışma saatlerini yüklemek için `handleBarberChange` fonksiyonu güncellendi
   - DatePicker bileşenine `disabledDates` prop'u geçirildi

## Teknik Detaylar

1. DatePicker bileşeni (`src/components/ui/date-picker.tsx`):
   ```typescript
   interface DatePickerProps {
     date: Date | undefined
     setDate: (date: Date | undefined) => void
     className?: string
     disabled?: boolean
     disabledDates?: Date[] | ((date: Date) => boolean)
   }
   ```

2. Booking-form.tsx dosyasında:
   ```typescript
   // Berber çalışma saatlerini saklamak için state
   const [barberWorkingHours, setBarberWorkingHours] = useState<{day_of_week: number, is_closed: boolean}[]>([])

   // Bir tarihin devre dışı bırakılıp bırakılmayacağını kontrol eden fonksiyon
   const isDateDisabled = (date: Date) => {
     // Geçmiş tarihleri devre dışı bırakma - calendar bileşeni zaten bunu yapıyor
     if (date < new Date()) return false

     // Haftanın gününü al (0 = Pazar, 6 = Cumartesi)
     const dayOfWeek = date.getDay()

     // Berberin bu gün çalışıp çalışmadığını kontrol et
     const workingHoursForDay = barberWorkingHours.find(day => day.day_of_week === dayOfWeek)
     
     // Bu gün için çalışma saati verisi yoksa, devre dışı bırakma
     if (!workingHoursForDay) return false
     
     // Berber bu gün çalışmıyorsa devre dışı bırak
     return workingHoursForDay.is_closed
   }
   ```

## Sonuç

Bu değişikliklerle birlikte, müşteriler artık berberin çalışmadığı günleri takvimde seçemeyecekler. Bu, kullanıcı deneyimini iyileştirecek ve kullanıcıların müsait olmayan günleri seçmesini önleyecek.
