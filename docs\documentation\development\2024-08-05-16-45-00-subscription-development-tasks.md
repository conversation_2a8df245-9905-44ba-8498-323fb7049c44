# SalonFlow Abonelik Sistemi Geliştirme Görevleri

**Tarih:** 5 Ağustos 2024
**Saat:** 16:45

## <PERSON><PERSON>ış

B<PERSON> be<PERSON>, SalonFlow abonelik sistemi için detaylı geliştirme görevlerini içermektedir. Görevler, [Abonelik Sistemi Özeti](../../../progress/subs/2024-07-25-10-00-00-subscriptions.md) ve [Detaylı Geliştirme Planı](../../../progress/subs/2024-07-25-15-30-00-subscriptions-detailed.md) dokümanlarına dayanmaktadır.

## Hata Düzeltmeleri
- [2024-07-28] Next.js 15 params.id erişim hatası ve ambiguous salon_id hatası düzeltildi. [Detaylar](../../../progress/subs/2024-07-28-10-00-00-subscription-bugfixes.md) ✅

## Yeni İyileştirmeler
- [2024-08-05] Referans sistemi ve ödeme yönetimi iyileştirmeleri planlandı. [Detaylar](../../../progress/subs/2024-08-05-10-00-00-referral-payment-improvements.md)

## 1. Veritabanı Yapısı Geliştirme

### 1.1. Abonelik Tabloları Oluşturma ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün

#### 1.1.1. subscription_plans Tablosu Oluşturma ✅
- SQL script hazırlama: `subscription_plans` tablosunu oluşturacak SQL kodunu yazma ✅
- Varsayılan planları ekleme: Solo, Small Team ve Pro Salon planlarını tanımlama ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.2. salon_subscriptions Tablosu Güncelleme ✅
- Mevcut `salon_subscriptions` tablosunu güncelleyecek SQL kodunu yazma ✅
- Yeni sütunlar ekleme: `plan_id`, `status`, `trial_end_date`, `payment_method`, `is_yearly` ✅
- Mevcut verileri yeni yapıya uygun şekilde dönüştürme ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.3. subscription_payments Tablosu Oluşturma ✅
- SQL script hazırlama: `subscription_payments` tablosunu oluşturacak SQL kodunu yazma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.4. referral_codes Tablosu Oluşturma ✅
- SQL script hazırlama: `referral_codes` tablosunu oluşturacak SQL kodunu yazma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.1.5. referral_benefits Tablosu Oluşturma ✅
- SQL script hazırlama: `referral_benefits` tablosunu oluşturacak SQL kodunu yazma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

### 1.2. RLS Politikaları Oluşturma ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 4 saat
- **Bağımlılıklar:** 1.1. Abonelik Tabloları Oluşturma

#### 1.2.1. subscription_plans Tablosu için RLS ✅
- Herkesin görüntüleyebileceği, ancak sadece admin'in düzenleyebileceği politikalar oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.2.2. salon_subscriptions Tablosu için RLS ✅
- Salon sahiplerinin kendi aboneliklerini görebileceği politika oluşturma ✅
- Admin'in tüm abonelikleri görebileceği politika oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.2.3. subscription_payments Tablosu için RLS ✅
- Salon sahiplerinin kendi ödemelerini görebileceği politika oluşturma ✅
- Admin'in tüm ödemeleri görebileceği politika oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 1.2.4. referral_codes ve referral_benefits Tabloları için RLS ✅
- Salon sahiplerinin kendi referans kodlarını ve faydalarını görebileceği politikalar oluşturma ✅
- Admin'in tüm referans kodlarını ve faydalarını görebileceği politikalar oluşturma ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

### 1.3. TypeScript Tip Tanımlamaları Güncelleme ✅
- **Zorluk:** Kolay
- **Tahmini Süre:** 2 saat
- **Bağımlılıklar:** 1.1. Abonelik Tabloları Oluşturma

#### 1.3.1. Yeni Tip Tanımlamaları Oluşturma ✅
- `SubscriptionPlan` interface'i oluşturma ✅
- Güncellenmiş `SalonSubscription` interface'i oluşturma ✅
- `SubscriptionPayment` interface'i oluşturma ✅
- `ReferralCode` ve `ReferralBenefit` interface'leri oluşturma ✅

#### 1.3.2. Insert ve Update Tipleri Oluşturma ✅
- Tüm yeni tipler için Insert ve Update tiplerini tanımlama ✅
- Mevcut `src/lib/db/types.ts` dosyasını güncelleme ✅

## 2. Backend İşlevselliği Geliştirme

### 2.1. Abonelik Yönetimi API'leri ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 2 gün
- **Bağımlılıklar:** 1.3. TypeScript Tip Tanımlamaları Güncelleme

#### 2.1.1. Abonelik Planları API'si Geliştirme ✅
- `src/lib/db/subscription-plans.ts` dosyasını oluşturma ✅
- `getSubscriptionPlans()` fonksiyonunu geliştirme ✅
- `getSubscriptionPlanById()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

#### 2.1.2. Salon Abonelikleri API'si Güncelleme ✅
- Mevcut `src/lib/db/subscriptions.ts` dosyasını güncelleme ✅
- `getActiveSalonSubscription()` fonksiyonunu güncelleme (plan detaylarını içerecek şekilde) ✅
- `createTrialSubscription()` fonksiyonunu geliştirme ✅
- `upgradeSubscription()` fonksiyonunu geliştirme ✅
- `isTrialExpired()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

#### 2.1.3. Ödeme Yönetimi API'si Geliştirme ✅
- `src/lib/db/subscription-payments.ts` dosyasını oluşturma ✅
- `getSubscriptionPayments()` fonksiyonunu geliştirme ✅
- `createSubscriptionPayment()` fonksiyonunu geliştirme ✅
- `updateSubscriptionPayment()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

#### 2.1.4. Referans Sistemi API'si Geliştirme ✅
- `src/lib/db/referrals.ts` dosyasını oluşturma ✅
- `getSalonReferralCode()` fonksiyonunu geliştirme ✅
- `createReferralCode()` fonksiyonunu geliştirme ✅
- `applyReferralCode()` fonksiyonunu geliştirme ✅
- Birim testleri yazma ve test etme

### 2.2. Abonelik Durumu Kontrolü ve Kısıtlama Mekanizması ✅
- **Zorluk:** Zor
- **Tahmini Süre:** 3 gün
- **Bağımlılıklar:** 2.1. Abonelik Yönetimi API'leri

#### 2.2.1. Abonelik Durumu Kontrolü Middleware Geliştirme ✅
- `src/middleware/subscription-check.ts` dosyasını oluşturma ✅
- Kullanıcı oturumu ve salon ID'si kontrolü ✅
- Abonelik durumu kontrolü (trial, active, past_due, suspended) ✅
- Deneme süresi kontrolü ✅
- Ödeme gecikmesi kontrolü ✅
- Middleware'i `src/middleware.ts` dosyasına entegre etme ✅
- Test etme

#### 2.2.2. Özellik Erişim Kontrolü Hook Geliştirme ✅
- `src/hooks/useSubscriptionFeatures.ts` dosyasını oluşturma ✅
- Abonelik planına göre özellikleri yükleme ✅
- Özellik durumlarını (maxStaff, hasAnalytics, hasFinance, hasCustomDomain) belirleme ✅
- Hook'u test etme

### 2.3. Bildirim Sistemi Entegrasyonu ✅
- **Zorluk:** Orta
- **Tahmini Süre:** 1 gün
- **Bağımlılıklar:** 2.1. Abonelik Yönetimi API'leri

#### 2.3.1. Abonelik Bildirimleri Trigger'ı Geliştirme ✅
- SQL script hazırlama: `create_subscription_notification()` fonksiyonunu oluşturma ✅
- Farklı abonelik durumları için bildirim içeriklerini tanımlama ✅
- Trigger'ı `salon_subscriptions` tablosuna bağlama ✅
- SQL scriptini Supabase'de çalıştırma ve test etme

#### 2.3.2. Deneme Süresi Bitimine Yaklaşma Bildirimi Geliştirme ✅
- `src/lib/cron/subscription-reminders.ts` dosyasını oluşturma ✅
- Deneme süresi yaklaşan abonelikleri bulma ✅
- Bildirim oluşturma ✅
- Cron job'ı ayarlama (Supabase Edge Functions veya harici bir servis kullanarak)
- Test etme
