"use client"

import { useState, useEffect } from "react"
import { Co<PERSON>, Check } from "lucide-react"
import { toast } from "sonner"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { supabase } from "@/lib/supabase"

interface BookingLinkProps {
  salonId: string
}

export function BookingLink({ salonId }: BookingLinkProps) {
  const [copied, setCopied] = useState(false)
  const [slug, setSlug] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Salon slug'ını al
  useEffect(() => {
    async function fetchSalonSlug() {
      try {
        setIsLoading(true)
        const { data, error } = await supabase
          .from('salons')
          .select('slug')
          .eq('id', salonId)
          .single()

        if (error) throw error
        setSlug(data.slug)
      } catch (error) {
        console.error("Salon slug'ı alınırken hata:", error)
        toast.error("Salon bilgileri alınamadı.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchSalonSlug()
  }, [salonId])

  // Generate booking link
  const bookingLink = slug
    ? `${window.location.origin}/${slug}`
    : `${window.location.origin}/booking/${salonId}`

  // Copy link to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(bookingLink)
      setCopied(true)
      toast.success("Bağlantı panoya kopyalandı.")

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false)
      }, 2000)
    } catch (error) {
      console.error("Error copying to clipboard:", error)
      toast.error("Bağlantı kopyalanırken bir hata oluştu.")
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Randevu Bağlantısı</CardTitle>
        <CardDescription>
          Bu bağlantıyı müşterilerinizle paylaşarak randevu almalarını sağlayabilirsiniz.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2">
          <Input value={bookingLink} readOnly disabled={isLoading} />
          <Button variant="outline" size="icon" onClick={copyToClipboard} disabled={isLoading}>
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
