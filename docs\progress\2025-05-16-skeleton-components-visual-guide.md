# Skeleton Bileşenleri Görsel Rehberi

Bu rehber, SalonFlow uygulamasında kullanılan iskelet (skeleton) yükleme bileşenlerinin görsel örneklerini ve gerçek içerikle karşılaştırmalarını içermektedir.

## 1. Haftalık Görünüm Karşılaştırması

### Skeleton Yükleme Durumu

```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ ▒▒▒▒▒▒  ▒▒▒▒▒   │ │ ▒▒▒▒▒▒  ▒▒▒▒▒   │ │ ▒▒▒▒▒▒  ▒▒▒▒▒   │
│ ▒▒▒▒▒▒          │ │ ▒▒▒▒▒▒          │ │ ▒▒▒▒▒▒          │
│                 │ │                 │ │                 │
│ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │ │ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │ │ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │
│                 │ │                 │ │                 │
│ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │ │ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │ │ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │
│ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │ │ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │ │                 │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

### Gerç<PERSON> İçerik

```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ Pazartesi  Tatil│ │ Salı            │ │ Çarşamba        │
│ 15 Mayıs        │ │ 16 Mayıs        │ │ 17 Mayıs        │
│                 │ │                 │ │                 │
│ Yeni randevu    │ │ Yeni randevu    │ │ Yeni randevu    │
│ eklemek için    │ │ eklemek için    │ │ eklemek için    │
│ tıklayın        │ │ tıklayın        │ │ tıklayın        │
│                 │ │                 │ │                 │
└─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 2. Günlük Görünüm Karşılaştırması

### Skeleton Yükleme Durumu

```
┌───────────────────────────────────────────────────┐
│ ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒    ▒▒▒▒▒▒▒▒           │
│                                                   │
├───────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────┐   │
│ │ ▒▒▒▒▒▒    ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │   │
│ └─────────────────────────────────────────────┘   │
│                                                   │
│ ┌─────────────────────────────────────────────┐   │
│ │ ▒▒▒▒▒▒    ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │   │
│ └─────────────────────────────────────────────┘   │
│                                                   │
│ ┌─────────────────────────────────────────────┐   │
│ │ ▒▒▒▒▒▒    ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒ │   │
│ └─────────────────────────────────────────────┘   │
└───────────────────────────────────────────────────┘
```

### Gerçek İçerik

```
┌───────────────────────────────────────────────────┐
│ Perşembe, 18 Mayıs 2025                           │
│                                                   │
├───────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────┐   │
│ │ 09:00    Müsait - Randevu eklemek için tıkla│   │
│ └─────────────────────────────────────────────┘   │
│                                                   │
│ ┌─────────────────────────────────────────────┐   │
│ │ 09:30    Ahmet Yılmaz - Saç Kesimi          │   │
│ └─────────────────────────────────────────────┘   │
│                                                   │
│ ┌─────────────────────────────────────────────┐   │
│ │ 10:00    Müsait - Randevu eklemek için tıkla│   │
│ └─────────────────────────────────────────────┘   │
└───────────────────────────────────────────────────┘
```

## 3. Aylık Görünüm Karşılaştırması

### Skeleton Yükleme Durumu

```
┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐
│▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│
└─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘

┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐
│▒ ▒▒ │ │▒ ▒▒ │ │▒ ▒▒ │ │▒ ▒▒ │ │▒ ▒▒ │ │▒ ▒▒ │ │▒ ▒▒ │
│     │ │     │ │     │ │     │ │     │ │     │ │     │
│▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│ │▒▒▒▒▒│
└─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘
```

### Gerçek İçerik

```
┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐
│Pzt  │ │Sal  │ │Çar  │ │Per  │ │Cum  │ │Cmt  │ │Paz  │
└─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘

┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐
│1 Tat│ │2    │ │3    │ │4    │ │5    │ │6    │ │7    │
│     │ │     │ │     │ │     │ │     │ │     │ │     │
│2 apt│ │     │ │1 apt│ │     │ │3 apt│ │     │ │     │
└─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘
```

## 4. Başlık Bileşeni Karşılaştırması

### Skeleton Yükleme Durumu

```
┌───────────────────────────────────────────────────────────────┐
│                                                               │
│  ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒                                  │
│  ▒▒▒▒▒▒▒▒▒▒▒▒▒▒                                              │
│                                                               │
│                          ▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒▒                │
│                          ▒▒▒▒▒ ▒▒▒▒▒ ▒▒▒▒▒ ▒▒▒▒▒ ▒▒▒▒▒       │
│                                                               │
└───────────────────────────────────────────────────────────────┘
```

### Gerçek İçerik

```
┌───────────────────────────────────────────────────────────────┐
│                                                               │
│  15 - 21 Mayıs 2025                                           │
│  Randevu takvimi                                              │
│                                                               │
│                          [D][H][A]  ◀ Bugün ▶  📅            │
│                                                               │
│                                                               │
└───────────────────────────────────────────────────────────────┘
```

## Kod Örneği: Haftalık Görünüm Skeleton Bileşeni

```tsx
export function WeeklyCalendarSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
      {Array.from({ length: 7 }).map((_, index) => (
        <Card key={index} className="h-full border">
          <CardHeader className="p-3">
            <div className="flex justify-between items-center">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
            <Skeleton className="h-4 w-24 mt-1" />
          </CardHeader>
          <CardContent className="p-3 space-y-2">
            <Skeleton className="h-10 w-full" />
            {Array.from({ length: Math.floor(Math.random() * 3) + 1 }).map((_, idx) => (
              <Skeleton key={idx} className="h-20 w-full" />
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
```

## Uygulama Örneği

```tsx
// src/app/dashboard/appointments/page.tsx
export default function AppointmentsPage() {
  const { salonId, salonLoading } = useUser()

  if (salonLoading) {
    return (
      <div className="p-4">
        <AppointmentsPageSkeleton />
      </div>
    )
  }

  return (
    <div className="p-4">
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 mb-4">
        {/* ... */}
      </header>
      <AppointmentCalendar salonId={salonId} />
    </div>
  )
}
```

## Önemli Noktalar

1. **Gerçekçi Boyutlar**: Skeleton bileşenler, gerçek içeriğin boyutlarını yansıtacak şekilde tasarlanmıştır.
2. **Rastgele Varyasyonlar**: Bazı bileşenlerde, daha gerçekçi bir görünüm için rastgele sayıda öğe oluşturulur.
3. **Hiyerarşik Yapı**: Skeleton bileşenler, gerçek içeriğin hiyerarşik yapısını takip eder.
4. **Duyarlı Tasarım**: Tüm skeleton bileşenler, farklı ekran boyutlarına uyum sağlayacak şekilde tasarlanmıştır.
5. **Animasyon**: Tüm skeleton bileşenler, Shadcn UI'ın `animate-pulse` sınıfını kullanarak hafif bir nabız animasyonuna sahiptir.
