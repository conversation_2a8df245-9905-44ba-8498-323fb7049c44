-- Update the record_appointment_income function to use actual service price
CREATE OR <PERSON><PERSON>LACE FUNCTION record_appointment_income() RETURNS TRIGGER AS $$
DECLARE
  v_service_name TEXT;
  v_service_id UUID;
  v_salon_id UUID;
  v_category_id UUID;
  v_amount DECIMAL(10, 2);
BEGIN
  -- Only proceed if status is changing to 'completed'
  IF (TG_OP = 'UPDATE' AND NEW.status = 'completed' AND OLD.status != 'completed') THEN
    -- Get service details including price
    SELECT s.name, s.id, a.salon_id, s.price
    INTO v_service_name, v_service_id, v_salon_id, v_amount
    FROM appointments a
    JOIN services s ON a.service_id = s.id
    WHERE a.id = NEW.id;
    
    -- Find the default service income category
    SELECT id INTO v_category_id
    FROM finance_categories
    WHERE salon_id = v_salon_id AND name = 'Hizmet Geliri' AND is_system_default = TRUE;
    
    -- If no default category exists, use the first income category
    IF v_category_id IS NULL THEN
      SELECT id INTO v_category_id
      FROM finance_categories
      WHERE salon_id = v_salon_id AND type = 'income'
      LIMIT 1;
    END IF;
    
    -- Record the transaction with actual service price
    INSERT INTO finance_transactions (
      salon_id,
      category_id,
      amount,
      transaction_date,
      description,
      appointment_id,
      service_id,
      created_by
    ) VALUES (
      v_salon_id,
      v_category_id,
      v_amount,
      NEW.date,
      'Otomatik kaydedildi: ' || v_service_name,
      NEW.id,
      v_service_id,
      auth.uid()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;