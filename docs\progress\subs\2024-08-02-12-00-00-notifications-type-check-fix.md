# Notifications Type Check Constraint <PERSON><PERSON><PERSON>

**Tarih:** 2 Ağustos 2024
**Saat:** 12:00

## 1. Tespit Edilen Sorun

Abonelik bildirimleri oluşturma sürecinde aşağıdaki hata tespit edildi:

```
{
    "code": "23514",
    "details": null,
    "hint": null,
    "message": "new row for relation \"notifications\" violates check constraint \"notifications_type_check\""
}
```

## 2. <PERSON><PERSON><PERSON>runun ana kaynağı şudur:

- `notifications` tablosundaki `type` sütunu için check constraint, sadece 'new_booking', 'cancellation' ve 'update' değerlerine izin veriyor.
- <PERSON><PERSON><PERSON>, abonelik bildirimleri için 'subscription_reminder' ve 'payment_reminder' değerleri kullanılmaya çalışılıyor.

## 3. Uygulanan Çözümler

### 3.1. Veritabanı Şeması Güncellemeleri

`notifications` tablosundaki `type` sütunu için check constraint'i güncelledik:

```sql
-- Mevcut kısıtlamayı kaldır
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS notifications_type_check;

-- Yeni kısıtlama ekle (subscription_reminder ve payment_reminder değerlerini de kabul edecek şekilde)
ALTER TABLE notifications ADD CONSTRAINT notifications_type_check 
  CHECK (type IN ('new_booking', 'cancellation', 'update', 'subscription_reminder', 'payment_reminder'));
```

### 3.2. Subscription Notification Trigger Fonksiyonu Güncelleme

Abonelik bildirimleri oluşturan trigger fonksiyonunu güncelledik:

```sql
CREATE OR REPLACE FUNCTION create_subscription_notification()
RETURNS TRIGGER AS $$
DECLARE
  salon_owner_id UUID;
  notification_title TEXT;
  notification_message TEXT;
  notification_type TEXT;
BEGIN
  -- Salon sahibini bul
  SELECT owner_id INTO salon_owner_id
  FROM salons
  WHERE id = NEW.salon_id;
  
  -- Bildirim içeriğini belirle
  IF TG_OP = 'INSERT' THEN
    -- Yeni abonelik oluşturulduğunda
    IF NEW.status = 'trial' THEN
      notification_title := 'Deneme Aboneliği Başladı';
      notification_message := 'Deneme aboneliğiniz başarıyla oluşturuldu. Deneme süreniz ' || NEW.trial_end_date || ' tarihinde sona erecek.';
      notification_type := 'subscription_reminder';
    ELSIF NEW.status = 'active' THEN
      notification_title := 'Aboneliğiniz Aktifleştirildi';
      notification_message := 'Aboneliğiniz başarıyla aktifleştirildi. Teşekkür ederiz!';
      notification_type := 'subscription_reminder';
    END IF;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Abonelik durumu değiştiğinde
    IF OLD.status != NEW.status THEN
      IF NEW.status = 'active' THEN
        notification_title := 'Aboneliğiniz Aktifleştirildi';
        notification_message := 'Aboneliğiniz başarıyla aktifleştirildi. Teşekkür ederiz!';
        notification_type := 'subscription_reminder';
      ELSIF NEW.status = 'past_due' THEN
        notification_title := 'Ödeme Hatırlatması';
        notification_message := 'Abonelik ödemeniz gecikti. Lütfen en kısa sürede ödemenizi yapın.';
        notification_type := 'payment_reminder';
      ELSIF NEW.status = 'suspended' THEN
        notification_title := 'Aboneliğiniz Askıya Alındı';
        notification_message := 'Ödeme yapılmadığı için aboneliğiniz askıya alındı. Hizmetlerinize tekrar erişmek için lütfen ödemenizi yapın.';
        notification_type := 'payment_reminder';
      ELSIF NEW.status = 'cancelled' THEN
        notification_title := 'Aboneliğiniz İptal Edildi';
        notification_message := 'Aboneliğiniz iptal edildi. Tekrar abone olmak için lütfen bizimle iletişime geçin.';
        notification_type := 'subscription_reminder';
      END IF;
    -- Plan değiştiğinde
    ELSIF OLD.plan_id != NEW.plan_id THEN
      notification_title := 'Abonelik Planınız Güncellendi';
      notification_message := 'Abonelik planınız başarıyla güncellendi.';
      notification_type := 'subscription_reminder';
    END IF;
  END IF;
  
  -- Bildirim oluştur
  IF notification_title IS NOT NULL THEN
    INSERT INTO notifications (
      salon_id,
      user_id,
      type,
      title,
      message,
      read,
      data
    ) VALUES (
      NEW.salon_id,
      salon_owner_id,
      notification_type,
      notification_title,
      notification_message,
      FALSE,
      jsonb_build_object('subscription_id', NEW.id, 'status', NEW.status)
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 3.3. NotificationsContext.tsx Dosyası Güncelleme

`src/contexts/NotificationsContext.tsx` dosyasında `NotificationType` tipini güncelledik:

```typescript
// Notification types
export type NotificationType = "new_booking" | "cancellation" | "update" | "subscription_reminder" | "payment_reminder"
```

Ayrıca, bildirim tipine göre toast tipini belirleyen kodu güncelledik:

```typescript
// Toast mesajı göster
let toastType = 'success';

// Bildirim tipine göre toast tipini belirle
if (notification.type === 'cancellation') {
  toastType = 'error';
} else if (notification.type === 'payment_reminder') {
  toastType = 'warning';
} else if (notification.type === 'subscription_reminder') {
  toastType = 'info';
}
```

Son olarak, bildirim tıklandığında yönlendirme yapılan kodu güncelledik:

```typescript
onClick: () => {
  if (notification.type === 'subscription_reminder' || notification.type === 'payment_reminder') {
    window.location.href = `/dashboard/subscription`
  } else if (notification.data?.id) {
    window.location.href = `/dashboard/appointments/${notification.data.id}`
  }
},
```

## 4. Sonuç

Bu değişikliklerle, abonelik bildirimleri oluşturma sürecindeki sorunlar çözülmüştür. Artık:

1. Abonelik bildirimleri 'subscription_reminder' ve 'payment_reminder' tipleriyle oluşturulabilecek
2. Bildirim tipine göre farklı toast tipleri gösterilecek (success, error, warning, info)
3. Abonelik bildirimleri tıklandığında kullanıcı abonelik sayfasına yönlendirilecek

Bu değişiklikler, kullanıcıların abonelik durumlarıyla ilgili bildirimleri sorunsuz bir şekilde alabilmelerini sağlayacaktır.
