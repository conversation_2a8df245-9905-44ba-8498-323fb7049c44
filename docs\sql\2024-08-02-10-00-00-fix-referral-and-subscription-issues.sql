-- Re<PERSON><PERSON> ve Abonelik Sistemi Sorunlarını Düzeltme
-- Tarih: 2024-08-02

-- 1. salon_subscriptions tablosu için RLS politikalarını düzeltme
-- is_admin() fonksiyonu yerine doğrudan e-posta kontrolü kullanma

-- Mevcut politikaları kaldır
DROP POLICY IF EXISTS "Admin abonelikleri ekleyebilir" ON salon_subscriptions;
DROP POLICY IF EXISTS "Admin abonelikleri güncelleyebilir" ON salon_subscriptions;
DROP POLICY IF EXISTS "Admin abonelikleri silebilir" ON salon_subscriptions;
DROP POLICY IF EXISTS "Admin tüm abonelikleri görebilir" ON salon_subscriptions;

-- Yeni politikalar oluştur
CREATE POLICY "Admin abonelikleri ekleyebilir" ON salon_subscriptions
  FOR INSERT WITH CHECK (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin abonelikleri güncelleyebilir" ON salon_subscriptions
  FOR UPDATE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin abonelikleri silebilir" ON salon_subscriptions
  FOR DELETE USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

CREATE POLICY "Admin tüm abonelikleri görebilir" ON salon_subscriptions
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- 2. Salon sahipleri için abonelik ekleme politikası
-- Salon sahipleri kendi salonları için abonelik oluşturabilmeli
CREATE POLICY "Salon sahipleri kendi aboneliklerini ekleyebilir" ON salon_subscriptions
  FOR INSERT WITH CHECK (salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()));

-- 3. Referans kodu doğrulama ve kullanma politikası
-- Herkes referans kodlarını görebilmeli (doğrulama için)
CREATE POLICY "Herkes referans kodlarını doğrulayabilir" ON referral_codes
  FOR SELECT USING (is_active = true);

-- 4. Referral benefits tablosunu kontrol et
-- Referral benefits tablosunun RLS politikalarını düzelt
DROP POLICY IF EXISTS "Salon sahipleri kendi referans faydalarını görebilir" ON referral_benefits;
DROP POLICY IF EXISTS "Admin tüm referans faydalarını görebilir" ON referral_benefits;

CREATE POLICY "Salon sahipleri kendi referans faydalarını görebilir" ON referral_benefits
  FOR SELECT USING (
    referrer_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid()) OR
    referred_salon_id IN (SELECT id FROM salons WHERE owner_id = auth.uid())
  );

CREATE POLICY "Admin tüm referans faydalarını görebilir" ON referral_benefits
  FOR SELECT USING (auth.uid() IN (SELECT id FROM auth.users WHERE email = '<EMAIL>'));

-- 5. Servis rolü için bypass politikaları
-- Servis rolü tüm tablolara erişebilmeli
CREATE POLICY "Service role bypass for salon_subscriptions" ON salon_subscriptions
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role bypass for referral_codes" ON referral_codes
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role bypass for referral_benefits" ON referral_benefits
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Service role bypass for pending_referrals" ON pending_referrals
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- 6. Referans kodu 6QLBODRI'nin aktif olduğunu doğrula
UPDATE referral_codes SET is_active = true WHERE code = '6QLBODRI';

-- 7. Salon oluşturma sırasında abonelik oluşturma için politika
-- Herkes salon_subscriptions tablosuna veri ekleyebilmeli
CREATE POLICY "Herkes salon_subscriptions ekleyebilir" ON salon_subscriptions
  FOR INSERT WITH CHECK (true);

-- 8. Salon oluşturma sırasında referans kodu uygulama için politika
-- Herkes referral_benefits tablosuna veri ekleyebilmeli
CREATE POLICY "Herkes referral_benefits ekleyebilir" ON referral_benefits
  FOR INSERT WITH CHECK (true);

-- 9. Referans kodu kullanım sayısını artırma için politika
-- Herkes referral_codes tablosunu güncelleyebilmeli
CREATE POLICY "Herkes referral_codes güncelleyebilir" ON referral_codes
  FOR UPDATE USING (true);

-- 10. Referans faydalarını uygulama için politika
-- Herkes referral_benefits tablosunu güncelleyebilmeli
CREATE POLICY "Herkes referral_benefits güncelleyebilir" ON referral_benefits
  FOR UPDATE USING (true);
