// Integration tests for subscription functionality
require('dotenv').config({ path: '.env.local' });
const { expect } = require('chai');
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create a Supabase client for integration tests
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Import the API functions
const subscriptionPlans = require('../../src/lib/db/subscription-plans');
const subscriptions = require('../../src/lib/db/subscriptions');
const subscriptionPayments = require('../../src/lib/db/subscription-payments');

// Test data
const testSalonId = process.env.TEST_SALON_ID || '00000000-0000-0000-0000-000000000000';
let testSubscriptionId = null;
let testPlanId = null;

describe('Subscription Integration Tests', function() {
  // Increase timeout for integration tests
  this.timeout(10000);
  
  before(async function() {
    // Skip tests if no TEST_SALON_ID is provided
    if (process.env.TEST_SALON_ID === undefined) {
      console.warn('Skipping integration tests: TEST_SALON_ID not provided in .env.local');
      this.skip();
    }
    
    // Get a valid plan ID for testing
    try {
      const plans = await subscriptionPlans.getSubscriptionPlans();
      if (plans && plans.length > 0) {
        testPlanId = plans[0].id;
      } else {
        console.warn('No subscription plans found for testing');
        this.skip();
      }
    } catch (error) {
      console.error('Error getting subscription plans:', error);
      this.skip();
    }
  });
  
  describe('Abonelik İşlemleri Testleri', function() {
    it('should create a trial subscription', async function() {
      // First, check if there's already an active subscription
      try {
        const existingSubscription = await subscriptions.getActiveSalonSubscription(testSalonId);
        
        // If there's an active subscription, deactivate it for testing
        if (existingSubscription) {
          await supabase
            .from('salon_subscriptions')
            .update({ is_active: false })
            .eq('id', existingSubscription.id);
        }
      } catch (error) {
        // Ignore errors, we'll create a new subscription anyway
      }
      
      // Create a trial subscription
      const trialSubscription = {
        salon_id: testSalonId,
        plan_id: testPlanId,
        status: 'trial',
        is_active: true,
        start_date: new Date().toISOString(),
        trial_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
        is_yearly: false
      };
      
      try {
        const newSubscription = await subscriptions.createSalonSubscription(trialSubscription);
        expect(newSubscription).to.be.an('object');
        expect(newSubscription.salon_id).to.equal(testSalonId);
        expect(newSubscription.plan_id).to.equal(testPlanId);
        expect(newSubscription.status).to.equal('trial');
        expect(newSubscription.is_active).to.be.true;
        
        // Save the subscription ID for later tests
        testSubscriptionId = newSubscription.id;
      } catch (error) {
        console.error('Error creating trial subscription:', error);
        throw error;
      }
    });
    
    it('should get the active salon subscription', async function() {
      try {
        const activeSubscription = await subscriptions.getActiveSalonSubscription(testSalonId);
        expect(activeSubscription).to.be.an('object');
        expect(activeSubscription.salon_id).to.equal(testSalonId);
        expect(activeSubscription.is_active).to.be.true;
        expect(activeSubscription.id).to.equal(testSubscriptionId);
      } catch (error) {
        console.error('Error getting active subscription:', error);
        throw error;
      }
    });
    
    it('should check if trial is expired', async function() {
      try {
        const isExpired = await subscriptions.isTrialExpired(testSalonId);
        expect(isExpired).to.be.a('boolean');
        // Since we just created a trial with a future end date, it should not be expired
        expect(isExpired).to.be.false;
      } catch (error) {
        console.error('Error checking trial expiration:', error);
        throw error;
      }
    });
    
    it('should upgrade subscription to a new plan', async function() {
      // Get another plan for upgrade if available
      let upgradePlanId = testPlanId;
      try {
        const plans = await subscriptionPlans.getSubscriptionPlans();
        if (plans && plans.length > 1) {
          // Use a different plan for upgrade
          upgradePlanId = plans[1].id;
        }
      } catch (error) {
        // Ignore errors, we'll use the same plan if needed
      }
      
      try {
        const updatedSubscription = await subscriptions.upgradeSubscription(testSubscriptionId, upgradePlanId, false);
        expect(updatedSubscription).to.be.an('object');
        expect(updatedSubscription.id).to.equal(testSubscriptionId);
        expect(updatedSubscription.plan_id).to.equal(upgradePlanId);
        expect(updatedSubscription.status).to.equal('active');
        expect(updatedSubscription.is_yearly).to.be.false;
      } catch (error) {
        console.error('Error upgrading subscription:', error);
        throw error;
      }
    });
  });
  
  describe('Ödeme İşleme Akışı Testi', function() {
    it('should create a subscription payment', async function() {
      const payment = {
        subscription_id: testSubscriptionId,
        amount: 750,
        payment_date: new Date().toISOString(),
        payment_method: 'bank_transfer',
        status: 'completed',
        notes: 'Integration test payment'
      };
      
      try {
        const newPayment = await subscriptionPayments.createSubscriptionPayment(payment);
        expect(newPayment).to.be.an('object');
        expect(newPayment.subscription_id).to.equal(testSubscriptionId);
        expect(newPayment.amount).to.equal(750);
        expect(newPayment.status).to.equal('completed');
      } catch (error) {
        console.error('Error creating subscription payment:', error);
        throw error;
      }
    });
    
    it('should get subscription payments', async function() {
      try {
        const payments = await subscriptionPayments.getSubscriptionPayments(testSubscriptionId);
        expect(payments).to.be.an('array');
        expect(payments.length).to.be.at.least(1);
        expect(payments[0].subscription_id).to.equal(testSubscriptionId);
      } catch (error) {
        console.error('Error getting subscription payments:', error);
        throw error;
      }
    });
  });
  
  after(async function() {
    // Clean up - deactivate the test subscription
    if (testSubscriptionId) {
      try {
        await supabase
          .from('salon_subscriptions')
          .update({ is_active: false })
          .eq('id', testSubscriptionId);
      } catch (error) {
        console.error('Error cleaning up test subscription:', error);
      }
    }
  });
});
