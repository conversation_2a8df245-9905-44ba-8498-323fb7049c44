"use client"

import { useMemo } from "react"
import { CustomAppSidebar } from "@/components/custom-app-sidebar"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { UserProvider, useUser } from "@/contexts/UserContext"
import { NotificationsProvider } from "@/contexts/NotificationsContext"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { useEffect } from "react"

// Inner component that uses the useUser hook
function AdminLayoutContent({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const { user, isLoading, isAdminUser } = useUser()
  const router = useRouter()

  // Admin kontrolü
  useEffect(() => {
    if (!isLoading && !isAdminUser) {
      router.push("/dashboard")
      toast.error("Bu sayfaya erişim yetkiniz yok.")
    }
  }, [isLoading, isAdminUser, router])

  // Sadece gerçekten yükleme durumundaysa loading göster
  const showLoading = useMemo(() => {
    return isLoading && !user;
  }, [isLoading, user]);

  if (showLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Admin değilse içeriği gösterme
  if (!isAdminUser) {
    return null;
  }

  return (
    <SidebarProvider>
      <NotificationsProvider>
        <CustomAppSidebar />
        <SidebarInset>
          {children}
        </SidebarInset>
      </NotificationsProvider>
    </SidebarProvider>
  )
}

export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <UserProvider>
      <AdminLayoutContent>
        {children}
      </AdminLayoutContent>
    </UserProvider>
  )
}
