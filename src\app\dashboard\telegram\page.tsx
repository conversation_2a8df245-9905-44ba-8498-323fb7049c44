"use client"

import { useEffect, useState } from "react"
import { useUser } from "@/contexts/UserContext"
import { TelegramSettingsForm } from "@/components/telegram/telegram-settings-form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, AlertTriangle, MessageSquare } from "lucide-react"

export default function TelegramPage() {
  const { user, salon, isLoading: userLoading } = useUser()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!userLoading) {
      setIsLoading(false)
    }
  }, [userLoading])

  if (isLoading || userLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Yükleniyor...</span>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Bu sayfaya erişmek için giriş yapmanız gerekir.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!salon) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Salon bilgileri bulunamadı. Lütfen sayfayı yenileyin.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Page Header */}
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Telegram Bildirimleri</h1>
        </div>
        <p className="text-muted-foreground">
          Randevu olayları için Telegram bildirimlerini yapılandırın ve yönetin.
        </p>
      </div>

      {/* Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>Telegram Bildirimleri Hakkında</CardTitle>
          <CardDescription>
            Telegram bildirimleri nasıl çalışır ve ne zaman gönderilir?
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">✅ Bildirim Gönderilir</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Yeni randevu oluşturulduğunda</li>
                <li>• Randevu iptal edildiğinde</li>
                <li>• Randevu tarihi/saati değiştirildiğinde</li>
                <li>• Randevu berberi değiştirildiğinde</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-blue-600">ℹ️ Bildirim İçeriği</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Müşteri adı ve telefonu</li>
                <li>• Berber adı</li>
                <li>• Hizmet türü</li>
                <li>• Randevu tarihi ve saati</li>
                <li>• Salon adı</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings Form */}
      <TelegramSettingsForm 
        salonId={salon.id} 
        salonName={salon.name}
      />

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Yardım ve Sorun Giderme</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <h4 className="font-medium">Bildirimler gelmiyor mu?</h4>
              <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                <li>• Botunuzun kanalınızda yönetici olduğundan emin olun</li>
                <li>• Kanal ID'sinin doğru olduğunu kontrol edin</li>
                <li>• Test bildirimi göndererek bağlantıyı test edin</li>
                <li>• Telegram bildirimleri etkin olduğundan emin olun</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">Kanal ID nasıl bulunur?</h4>
              <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                <li>• Genel kanallar için: @kanaladi formatını kullanın</li>
                <li>• Özel kanallar için: @userinfobot'u kanalınıza ekleyin</li>
                <li>• Bot size kanal ID'sini verecektir (-1001234567890 formatında)</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium">Güvenlik</h4>
              <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                <li>• Kanal ID'leri şifreli olarak saklanır</li>
                <li>• Bot tokenları güvenli sunucularda tutulur</li>
                <li>• Sadece salon sahipleri ayarları değiştirebilir</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
