"use client"

import React, { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useUser } from "@/contexts/UserContext"
import { useSubscriptionFeatures } from "@/hooks/useSubscriptionFeatures"
import { toast } from "sonner"
import { Globe, ExternalLink, ArrowRight } from "lucide-react"

export default function CustomDomainPage() {
  const router = useRouter()
  const { salon } = useUser()
  const salonId = salon?.id
  const [domain, setDomain] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Abonelik özelliklerini kontrol et
  const { features, hasFeature } = useSubscriptionFeatures()

  // Özel alan adı özelliğine erişim kontrolü
  useEffect(() => {
    if (!hasFeature('hasCustomDomain')) {
      router.push('/dashboard/subscription/upgrade')
      toast.error("Bu özelliği kullanmak için abonelik planınızı yükseltmeniz gerekiyor.")
    }
  }, [hasFeature, router])

  // Form gönderme işlemi
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!domain) {
      toast.error("Lütfen bir alan adı girin.")
      return
    }

    setIsSubmitting(true)
    
    // Burada özel alan adı ayarlama API'si çağrılacak
    setTimeout(() => {
      toast.success("Özel alan adı ayarları kaydedildi. DNS ayarlarınızı yapılandırmanız gerekiyor.")
      setIsSubmitting(false)
    }, 1500)
  }

  return (
    <div className="p-4 space-y-6">
      <header className="flex h-16 shrink-0 items-center gap-2 mb-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <h1 className="text-2xl font-bold">Özel Alan Adı</h1>
        </div>
      </header>

      {/* Özel alan adı özelliği erişim kontrolü */}
      {!hasFeature('hasCustomDomain') && (
        <Alert className="mb-4">
          <AlertTitle>Abonelik Planı Gerekli</AlertTitle>
          <AlertDescription>
            Özel alan adı özelliğini kullanmak için abonelik planınızı yükseltmeniz gerekiyor.
            <Link href="/dashboard/subscription/upgrade" className="ml-1 font-medium underline">
              Planınızı yükseltin
            </Link>
          </AlertDescription>
        </Alert>
      )}

      {hasFeature('hasCustomDomain') && (
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-primary" />
                Özel Alan Adı Ayarları
              </CardTitle>
              <CardDescription>
                Salonunuz için özel bir alan adı yapılandırın
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="domain">Alan Adı</Label>
                  <Input
                    id="domain"
                    placeholder="salonunuz.com"
                    value={domain}
                    onChange={(e) => setDomain(e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    Örnek: salonunuz.com (www. olmadan)
                  </p>
                </div>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Kaydediliyor..." : "Kaydet"}
                </Button>
              </form>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>DNS Ayarları</CardTitle>
              <CardDescription>
                Alan adınızı SalonFlow'a yönlendirmek için DNS ayarlarınızı yapılandırın
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">CNAME Kaydı</h3>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm font-mono">
                      <span className="text-muted-foreground">Ad:</span> @<br />
                      <span className="text-muted-foreground">Hedef:</span> salonflow-custom.vercel.app
                    </p>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-2">TXT Kaydı</h3>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm font-mono">
                      <span className="text-muted-foreground">Ad:</span> @<br />
                      <span className="text-muted-foreground">Değer:</span> salonflow-verification={salon?.slug || 'your-salon-slug'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="https://docs.salonflow.com/custom-domains" target="_blank">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Detaylı Kurulum Kılavuzu
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </>
      )}
    </div>
  )
}
