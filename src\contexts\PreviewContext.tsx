"use client"

import { create<PERSON>ontext, use<PERSON>ontext, ReactNode } from "react"
import { <PERSON><PERSON>onte<PERSON>, SalonTestimonial } from "@/lib/db/types"
import { parseSalonContent, getDefaultSalonContent } from "@/lib/db/public"

interface PreviewContextType {
  isPreviewMode: boolean
  previewContent: ReturnType<typeof parseSalonContent>
  previewTestimonials: SalonTestimonial[]
}

const PreviewContext = createContext<PreviewContextType | undefined>(undefined)

interface PreviewProviderProps {
  children: ReactNode
  isPreviewMode?: boolean
  content?: SalonContent[]
  testimonials?: SalonTestimonial[]
}

export function PreviewProvider({ 
  children, 
  isPreviewMode = false,
  content = [],
  testimonials = []
}: PreviewProviderProps) {
  const previewContent = content.length > 0 
    ? parseSalonContent(content)
    : getDefaultSalonContent()

  const value: PreviewContextType = {
    isPreviewMode,
    previewContent,
    previewTestimonials: testimonials
  }

  return (
    <PreviewContext.Provider value={value}>
      {children}
    </PreviewContext.Provider>
  )
}

export function usePreview() {
  const context = useContext(PreviewContext)
  if (context === undefined) {
    throw new Error('usePreview must be used within a PreviewProvider')
  }
  return context
}

// Hook to check if we're in preview mode
export function useIsPreview() {
  const context = useContext(PreviewContext)
  return context?.isPreviewMode || false
}
