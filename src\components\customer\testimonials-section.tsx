"use client"

import { motion } from "framer-motion"
import { MessageCir<PERSON>, Star } from "lucide-react"
import { TestimonialCard } from "./testimonial-card"
import { SalonTestimonial } from "@/lib/db/types"
import { usePreview, useIsPreview } from "@/contexts/PreviewContext"
import { useSalon } from "@/contexts/SalonContext"

interface TestimonialsSectionProps {
  salonName: string
  salonId: string
}

export function TestimonialsSection({ salonName, salonId }: TestimonialsSectionProps) {
  // Preview context
  const isPreview = useIsPreview()
  const previewContext = isPreview ? usePreview() : null

  // Salon context for content
  const { testimonials: salonTestimonials, contentLoading } = useSalon()

  // Get testimonials based on context
  const testimonials = isPreview && previewContext
    ? previewContext.previewTestimonials
    : salonTestimonials

  const isLoading = isPreview ? false : contentLoading

  // Fallback testimonials if no real testimonials exist
  const fallbackTestimonials = [
    {
      id: "fallback-1",
      customer_name: "<PERSON><PERSON>ılmaz",
      rating: 5,
      comment: "<PERSON><PERSON> bir deneyim yaşadım. Berber<PERSON> çok profesyoneldi ve istediğim tarzı mükemmel bir şekilde yaptı. Kesinlikle tekrar geleceğim.",
      service_name: "Saç Kesimi & Sakal Düzenleme",
      date_text: "2 hafta önce"
    },
    {
      id: "fallback-2",
      customer_name: "Mehmet Kaya",
      rating: 5,
      comment: "Randevu sistemi çok pratik, salon temiz ve hijyenik. Ekip çok güler yüzlü ve işini bilen. Herkese tavsiye ederim.",
      service_name: "Saç Kesimi",
      date_text: "1 ay önce"
    },
    {
      id: "fallback-3",
      customer_name: "Emre Demir",
      rating: 4,
      comment: "Kaliteli hizmet ve uygun fiyatlar. Saç kesimi sonrası çok memnun kaldım. Tek eksik biraz daha hızlı olabilirdi.",
      service_name: "Saç Kesimi & Yıkama",
      date_text: "3 hafta önce"
    }
  ]

  // Use real testimonials if available, otherwise use fallback
  const displayTestimonials = testimonials.length > 0 ? testimonials : fallbackTestimonials

  const averageRating = displayTestimonials.length > 0
    ? displayTestimonials.reduce((acc, t) => acc + t.rating, 0) / displayTestimonials.length
    : 0

  return (
    <section className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center space-y-6 mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
              <MessageCircle className="w-4 h-4" />
              <span>Müşteri Yorumları</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-foreground">
              Müşterilerimiz
              <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Ne Diyor?
              </span>
            </h2>

            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center space-x-1">
                {Array.from({ length: 5 }, (_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(averageRating)
                        ? "text-yellow-400 fill-current"
                        : "text-gray-300 dark:text-gray-600"
                    }`}
                  />
                ))}
              </div>
              <span className="text-lg font-semibold text-foreground">
                {averageRating.toFixed(1)}/5
              </span>
              <span className="text-muted-foreground">
                ({displayTestimonials.length} değerlendirme)
              </span>
            </div>

            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {salonName} ailesine katılan müşterilerimizin deneyimlerini okuyun.
              Sizin de memnuniyetiniz bizim önceliğimiz.
            </p>
          </motion.div>

          {/* Testimonials Grid */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 3 }, (_, i) => (
                <div key={i} className="bg-background border border-border rounded-2xl p-6 animate-pulse">
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="w-12 h-12 bg-muted rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-5/6"></div>
                    <div className="h-3 bg-muted rounded w-4/6"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {displayTestimonials.map((testimonial, index) => (
                <TestimonialCard
                  key={testimonial.id}
                  testimonial={{
                    id: testimonial.id,
                    name: testimonial.customer_name,
                    rating: testimonial.rating,
                    comment: testimonial.comment,
                    service: testimonial.service_name || undefined,
                    date: testimonial.date_text || undefined
                  }}
                  index={index}
                />
              ))}
            </div>
          )}

          {/* Bottom Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="mt-16 text-center"
          >
            <div className="bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-8 border border-primary/20">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-8">
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-primary">500+</div>
                  <div className="text-sm text-muted-foreground">Mutlu Müşteri</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-primary">4.9/5</div>
                  <div className="text-sm text-muted-foreground">Ortalama Puan</div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-primary">98%</div>
                  <div className="text-sm text-muted-foreground">Memnuniyet Oranı</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
