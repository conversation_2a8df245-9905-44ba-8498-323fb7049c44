"use client"

import { useEffect, useRef, useState } from "react"
import { useInView } from "framer-motion"

interface UseScrollAnimationOptions {
  threshold?: number
  triggerOnce?: boolean
  rootMargin?: string
}

export function useScrollAnimation(options: UseScrollAnimationOptions = {}) {
  const ref = useRef(null)
  const isInView = useInView(ref, {
    threshold: options.threshold || 0.1,
    once: options.triggerOnce !== false,
    margin: options.rootMargin || "0px 0px -100px 0px"
  })

  return { ref, isInView }
}

export function useStaggeredAnimation(itemCount: number, delay: number = 0.1) {
  const [visibleItems, setVisibleItems] = useState<number[]>([])
  const { ref, isInView } = useScrollAnimation()

  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        const items = Array.from({ length: itemCount }, (_, i) => i)
        items.forEach((item, index) => {
          setTimeout(() => {
            setVisibleItems(prev => [...prev, item])
          }, index * delay * 1000)
        })
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [isInView, itemCount, delay])

  return { ref, visibleItems, isInView }
}

export function useParallaxScroll() {
  const [scrollY, setScrollY] = useState(0)

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return scrollY
}

export function useMousePosition() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  return mousePosition
}
