"use client"

import { createContext, useContext, useState, useEffect, ReactNode, useMemo } from "react"
import { toast } from "sonner"

import { useUser } from "@/contexts/UserContext"
import {
  getSalonContent,
  bulkUpsertSalonContent
} from "@/lib/db/salon-content"
import {
  getSalonTestimonials,
  createSalonTestimonial,
  updateSalonTestimonial,
  deleteSalonTestimonial,
  toggleTestimonialStatus
} from "@/lib/db/salon-testimonials"
import { SalonContent, SalonTestimonial, SalonContentInsert, SalonTestimonialInsert } from "@/lib/db/types"

// Content Context Types
interface ContentContextType {
  // Data
  content: SalonContent[]
  testimonials: SalonTestimonial[]
  isLoading: boolean
  hasChanges: boolean

  // Content Operations
  getContentValue: (section: string, key: string, fallback?: string) => string
  updateContentValue: (section: string, key: string, value: string, type?: 'text' | 'number' | 'boolean' | 'json') => void
  saveContent: () => Promise<void>
  resetChanges: () => void

  // Testimonial Operations
  addTestimonial: (testimonial: Omit<SalonTestimonialInsert, 'salon_id'>) => Promise<void>
  updateTestimonial: (id: string, updates: Partial<SalonTestimonial>) => Promise<void>
  deleteTestimonial: (id: string) => Promise<void>
  toggleTestimonial: (id: string, isActive: boolean) => Promise<void>

  // Utility
  refreshData: () => Promise<void>
}

const ContentContext = createContext<ContentContextType | undefined>(undefined)

// Content Provider Component
interface ContentProviderProps {
  children: ReactNode
}

export function ContentProvider({ children }: ContentProviderProps) {
  const { salon } = useUser()
  const [content, setContent] = useState<SalonContent[]>([])
  const [testimonials, setTestimonials] = useState<SalonTestimonial[]>([])
  const [originalContent, setOriginalContent] = useState<SalonContent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  // Check if there are unsaved changes
  const hasChanges = useMemo(() => {
    if (content.length !== originalContent.length) return true

    return content.some(item => {
      const original = originalContent.find(orig =>
        orig.section === item.section && orig.content_key === item.content_key
      )
      return !original || original.content_value !== item.content_value
    })
  }, [content, originalContent])

  // Load content data
  const loadData = async () => {
    if (!salon?.id) return

    try {
      setIsLoading(true)
      const [contentData, testimonialsData] = await Promise.all([
        getSalonContent(salon.id),
        getSalonTestimonials(salon.id)
      ])

      setContent(contentData)
      setOriginalContent(contentData)
      setTestimonials(testimonialsData)
    } catch (error) {
      console.error("İçerik yüklenirken hata:", error)
      toast.error("İçerik yüklenirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  // Load data on mount and salon change
  useEffect(() => {
    loadData()
  }, [salon?.id])

  // Get content value by section and key
  const getContentValue = (section: string, key: string, fallback: string = ''): string => {
    const item = content.find(c => c.section === section && c.content_key === key)
    return item?.content_value || fallback
  }

  // Update content value locally (not saved until saveContent is called)
  const updateContentValue = (
    section: string,
    key: string,
    value: string,
    type: 'text' | 'number' | 'boolean' | 'json' = 'text'
  ) => {
    setContent(prev => {
      const existingIndex = prev.findIndex(c => c.section === section && c.content_key === key)

      if (existingIndex >= 0) {
        // Update existing item
        const updated = [...prev]
        updated[existingIndex] = {
          ...updated[existingIndex],
          content_value: value,
          content_type: type
        }
        return updated
      } else {
        // Add new item
        const newItem: SalonContent = {
          id: `temp-${Date.now()}`, // Temporary ID
          salon_id: salon?.id || '',
          section: section as any,
          content_key: key,
          content_value: value,
          content_type: type,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        return [...prev, newItem]
      }
    })
  }

  // Save all content changes
  const saveContent = async () => {
    if (!salon?.id || !hasChanges) return

    try {
      setIsSaving(true)

      // Prepare content items for bulk upsert
      const contentItems = content.map(item => ({
        section: item.section,
        content_key: item.content_key,
        content_value: item.content_value || '',
        content_type: item.content_type
      }))

      // Bulk upsert content
      const savedContent = await bulkUpsertSalonContent(salon.id, contentItems)

      // Update local state with saved data
      setContent(savedContent)
      setOriginalContent(savedContent)

      toast.success("İçerik başarıyla kaydedildi")
    } catch (error) {
      console.error("İçerik kaydetme hatası:", error)
      toast.error("İçerik kaydedilirken bir hata oluştu")
      throw error
    } finally {
      setIsSaving(false)
    }
  }

  // Reset changes to original state
  const resetChanges = () => {
    setContent([...originalContent])
    toast.info("Değişiklikler geri alındı")
  }

  // Add new testimonial
  const addTestimonial = async (testimonialData: Omit<SalonTestimonialInsert, 'salon_id'>) => {
    if (!salon?.id) return

    try {
      const newTestimonial = await createSalonTestimonial({
        ...testimonialData,
        salon_id: salon.id
      })

      setTestimonials(prev => [...prev, newTestimonial])
      toast.success("Yorum başarıyla eklendi")
    } catch (error) {
      console.error("Yorum ekleme hatası:", error)
      toast.error("Yorum eklenirken bir hata oluştu")
      throw error
    }
  }

  // Update testimonial
  const updateTestimonial = async (id: string, updates: Partial<SalonTestimonial>) => {
    try {
      const updatedTestimonial = await updateSalonTestimonial(id, updates)

      setTestimonials(prev =>
        prev.map(t => t.id === id ? updatedTestimonial : t)
      )
      toast.success("Yorum başarıyla güncellendi")
    } catch (error) {
      console.error("Yorum güncelleme hatası:", error)
      toast.error("Yorum güncellenirken bir hata oluştu")
      throw error
    }
  }

  // Delete testimonial
  const deleteTestimonial = async (id: string) => {
    try {
      await deleteSalonTestimonial(id)

      setTestimonials(prev => prev.filter(t => t.id !== id))
      toast.success("Yorum başarıyla silindi")
    } catch (error) {
      console.error("Yorum silme hatası:", error)
      toast.error("Yorum silinirken bir hata oluştu")
      throw error
    }
  }

  // Toggle testimonial active status
  const toggleTestimonial = async (id: string, isActive: boolean) => {
    try {
      const updatedTestimonial = await toggleTestimonialStatus(id, isActive)

      setTestimonials(prev =>
        prev.map(t => t.id === id ? updatedTestimonial : t)
      )
      toast.success(`Yorum ${isActive ? 'aktif' : 'pasif'} hale getirildi`)
    } catch (error) {
      console.error("Yorum durumu değiştirme hatası:", error)
      toast.error("Yorum durumu değiştirilirken bir hata oluştu")
      throw error
    }
  }

  // Refresh all data
  const refreshData = async () => {
    await loadData()
  }

  const value: ContentContextType = {
    // Data
    content,
    testimonials,
    isLoading: isLoading || isSaving,
    hasChanges,

    // Content Operations
    getContentValue,
    updateContentValue,
    saveContent,
    resetChanges,

    // Testimonial Operations
    addTestimonial,
    updateTestimonial,
    deleteTestimonial,
    toggleTestimonial,

    // Utility
    refreshData
  }

  return (
    <ContentContext.Provider value={value}>
      {children}
    </ContentContext.Provider>
  )
}

// Hook to use Content Context
export function useContent() {
  const context = useContext(ContentContext)
  if (context === undefined) {
    throw new Error('useContent must be used within a ContentProvider')
  }
  return context
}

// Export types for external use
export type { ContentContextType }
