"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Search, ShoppingBag } from "lucide-react"
import { toast } from "sonner"
import { motion, AnimatePresence } from "framer-motion"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { ClientBookingModal } from "@/components/client/client-booking-modal"
import { ClientHeader } from "@/components/client/client-header"
import { useSalon } from "@/contexts/SalonContext"
import { publicAccess } from "@/lib/db"
import { Product } from "@/lib/db/types"
import { Spotlight } from "@/components/ui/spotlight"
import { ProductSpotlightCard } from "@/components/client/product-spotlight-card"
import { FeaturedProductsCarousel } from "@/components/client/featured-products-carousel"
import { AnimatedCategoryTabs } from "@/components/client/animated-category-tabs"
import { ProductDetailModal } from "@/components/client/product-detail-modal"

export default function SalonProductsPage() {
  const { salon, salonId, slug, isLoading: salonLoading, dataLoaded } = useSalon()

  const [productList, setProductList] = useState<Product[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isProductModalOpen, setIsProductModalOpen] = useState(false)

  // Ürünleri yükle
  useEffect(() => {
    async function loadProducts() {
      if (!salonId) return

      try {
        setIsLoading(true)
        const data = await publicAccess.getPublicActiveProductsBySalonId(salonId)
        setProductList(data)

        // Kategorileri çıkar
        const uniqueCategories = [...new Set(data.map(p => p.category).filter(Boolean))]
        setCategories(uniqueCategories as string[])
      } catch (error) {
        console.error("Ürünler yüklenirken hata:", error)
        toast.error("Ürünler yüklenirken bir hata oluştu")
      } finally {
        setIsLoading(false)
      }
    }

    if (salonId) {
      loadProducts()
    }
  }, [salonId])

  // Sadece ilk yüklemede salon loading state göster
  // Sayfalar arası geçişlerde gösterme
  if (salonLoading && !dataLoaded) {
    return (
      <div className="min-h-screen flex flex-col">
        <div className="flex justify-center items-center h-screen">
          <div className="flex flex-col items-center gap-4">
            <div className="h-12 w-12 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
            <p className="text-lg font-medium">Yükleniyor...</p>
          </div>
        </div>
      </div>
    )
  }

  // Salon bilgileri yüklendiyse ama henüz mevcut değilse
  if (!salon || !salonId) {
    return (
      <div className="min-h-screen flex flex-col">
        <div className="flex justify-center items-center h-screen">
          <div className="flex flex-col items-center gap-4">
            <h1 className="text-2xl font-bold">Salon Bulunamadı</h1>
            <p className="text-muted-foreground">
              Aradığınız salon bulunamadı veya artık mevcut değil.
            </p>
            <Button asChild size="lg">
              <Link href="/">Ana Sayfaya Dön</Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Filtrelenmiş ürünleri hesapla
  const filteredProducts = productList.filter(product => {
    const matchesCategory = selectedCategory === "all" || product.category === selectedCategory
    const matchesSearch = searchQuery === "" ||
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()))

    return matchesCategory && matchesSearch
  })

  // Öne çıkan ürünleri seç (en fazla 10 ürün)
  const featuredProducts = [...productList]
    .sort(() => Math.random() - 0.5) // Rastgele sırala
    .slice(0, Math.min(10, productList.length))

  // Ürün detaylarını göster
  const handleProductClick = (product: Product) => {
    setSelectedProduct(product)
    setIsProductModalOpen(true)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <ClientHeader currentPage="products" />

      <AnimatePresence mode="wait">
        <motion.div
          key={`${slug}-products`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
        >
          {/* Hero Section */}
          <section className="relative overflow-hidden bg-gradient-to-b from-black to-background py-20">
            {/* Spotlight effect */}
            <Spotlight
              className="-top-40 left-0 md:left-60"
              fill="rgba(var(--primary-rgb), 0.15)"
            />

            <div className="container relative z-10 mx-auto px-4">
              <div className="flex flex-col items-center text-center space-y-6">
                <motion.h1
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="text-4xl md:text-5xl font-bold tracking-tight"
                >
                  {salon.name} Ürünleri
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-xl text-muted-foreground max-w-2xl"
                >
                  Salonumuzda kullandığımız ve satışa sunduğumuz kaliteli ürünleri keşfedin.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="flex flex-col sm:flex-row gap-4 mt-4"
                >
                  <ClientBookingModal
                    salonId={salonId || ""}
                    buttonText="Randevu Al"
                    buttonSize="lg"
                    buttonClassName="text-lg px-8"
                  />
                  <Button size="lg" variant="outline" className="text-lg px-8" asChild>
                    <Link href={`/${slug}`}>
                      Salona Dön
                    </Link>
                  </Button>
                </motion.div>
              </div>
            </div>
          </section>

          {/* Featured Products Carousel */}
          {!isLoading && featuredProducts.length > 0 && (
            <FeaturedProductsCarousel
              products={featuredProducts}
              onProductClick={handleProductClick}
            />
          )}

          {/* Products Section */}
          <section className="py-16">
            <div className="container mx-auto px-4">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
                <motion.h2
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="text-3xl font-bold"
                >
                  Ürünlerimiz
                </motion.h2>
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="relative w-full md:w-64"
                >
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Ürün ara..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </motion.div>
              </div>

              {isLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="flex flex-col space-y-3">
                      <Skeleton className="h-64 w-full rounded-lg" />
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  ))}
                </div>
              ) : productList.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="flex flex-col items-center justify-center py-16 text-center"
                >
                  <ShoppingBag className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-2xl font-bold mb-2">Henüz Ürün Yok</h3>
                  <p className="text-muted-foreground max-w-md">
                    Bu salon henüz ürün eklememişti. Daha sonra tekrar kontrol edin.
                  </p>
                </motion.div>
              ) : (
                <>
                  {categories.length > 0 && (
                    <AnimatedCategoryTabs
                      categories={categories}
                      activeCategory={selectedCategory}
                      onCategoryChange={setSelectedCategory}
                    />
                  )}

                  {filteredProducts.length === 0 ? (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5 }}
                      className="flex flex-col items-center justify-center py-16 text-center"
                    >
                      <Search className="h-16 w-16 text-muted-foreground mb-4" />
                      <h3 className="text-2xl font-bold mb-2">Sonuç Bulunamadı</h3>
                      <p className="text-muted-foreground max-w-md">
                        Aramanıza uygun ürün bulunamadı. Lütfen farklı bir arama terimi deneyin.
                      </p>
                    </motion.div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                      {filteredProducts.map((product, index) => (
                        <ProductSpotlightCard
                          key={product.id}
                          product={product}
                          onClick={() => handleProductClick(product)}
                          index={index}
                        />
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          </section>

          {/* CTA Section */}
          <section className="py-16 bg-muted/30">
            <div className="container mx-auto px-4 text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold mb-6">Randevu Alın</h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
                  Ürünlerimiz hakkında daha fazla bilgi almak ve profesyonel bakım için hemen randevu alın.
                </p>
                <ClientBookingModal
                  salonId={salonId || ""}
                  buttonText="Hemen Randevu Al"
                  buttonSize="lg"
                  buttonClassName="text-lg px-8"
                />
              </motion.div>
            </div>
          </section>

          {/* Product Detail Modal */}
          <ProductDetailModal
            product={selectedProduct}
            open={isProductModalOpen}
            onOpenChange={setIsProductModalOpen}
            salonId={salonId || ""}
          />
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
