-- SalonFlow Ürün Özelliği Kontrol RPC Fonksiyonu
-- Oluşturulma Tarihi: 2025-05-23

-- B<PERSON> fonksiyon, bir salonun:
-- 1. <PERSON><PERSON><PERSON><PERSON> yönetimi özelliğine sahip bir abonelik planında olup olmadığını
-- 2. En az bir ürüne sahip olup olmadığını
-- kontrol eder ve her iki koşul da sağlanıyorsa true, aksi takdirde false döndürür.

CREATE OR REPLACE FUNCTION check_salon_has_products_feature(p_salon_id UUID)
RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_has_product_feature BOOLEAN;
  v_has_products BOOLEAN;
BEGIN
  -- 1. Salonun ürün yönetimi özelliğine sahip bir abonelik planında olup olmadığını kontrol et
  SELECT EXISTS (
    SELECT 1
    FROM salon_subscriptions ss
    JOIN subscription_plans sp ON ss.plan_id = sp.id
    WHERE ss.salon_id = p_salon_id
      AND ss.status = 'active'
      AND sp.features->>'product_management' = 'true'
  ) INTO v_has_product_feature;

  -- 2. Salonun en az bir ürüne sahip olup olmadığını kontrol et
  SELECT EXISTS (
    SELECT 1
    FROM products
    WHERE salon_id = p_salon_id
      AND is_active = TRUE
  ) INTO v_has_products;

  -- Her iki koşul da sağlanıyorsa true, aksi takdirde false döndür
  RETURN v_has_product_feature AND v_has_products;
END;
$$ LANGUAGE plpgsql;

-- anon rolüne EXECUTE izni ver
GRANT EXECUTE ON FUNCTION check_salon_has_products_feature(UUID) TO anon;

COMMENT ON FUNCTION check_salon_has_products_feature(UUID) IS 
'Bir salonun ürün yönetimi özelliğine sahip bir abonelik planında olup olmadığını ve 
en az bir ürüne sahip olup olmadığını kontrol eder. Her iki koşul da sağlanıyorsa true döndürür.';
