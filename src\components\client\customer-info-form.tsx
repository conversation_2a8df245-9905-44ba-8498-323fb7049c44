"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { motion } from "framer-motion"
import { UserCircle, Mail, Phone, ArrowRight, ArrowLeft } from "lucide-react"
import * as z from "zod"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"

// Form schema
const formSchema = z.object({
  name: z.string().min(2, {
    message: "İsim en az 2 karakter olmalıdır.",
  }),
  surname: z.string().min(2, {
    message: "Soyisim en az 2 karakter olmalıdır.",
  }),
  phone: z.string().min(10, {
    message: "<PERSON><PERSON><PERSON><PERSON><PERSON> bir telefon numarası giriniz.",
  }),
  email: z.string().email({
    message: "Geçerli bir e-posta adresi giriniz.",
  }).optional().or(z.literal("")),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: "Gizlilik politikasını kabul etmelisiniz.",
  }),
})

interface CustomerInfoFormProps {
  defaultValues: {
    name: string
    surname: string
    phone: string
    email: string
  }
  onSubmit: (data: { name: string, surname: string, phone: string, email: string }) => void
  onBack: () => void
}

export function CustomerInfoForm({ defaultValues, onSubmit, onBack }: CustomerInfoFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...defaultValues,
      acceptTerms: false
    },
  })

  // Handle form submission
  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)

    try {
      onSubmit({
        name: values.name,
        surname: values.surname,
        phone: values.phone,
        email: values.email || ""
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Kişisel Bilgiler</h2>
        <p className="text-muted-foreground">
          Randevunuz için iletişim bilgilerinizi girin
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>İsim</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="İsminiz"
                        {...field}
                        className="pl-10"
                      />
                      <UserCircle className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="surname"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Soyisim</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="Soyisminiz"
                        {...field}
                        className="pl-10"
                      />
                      <UserCircle className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Telefon</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="Telefon numaranız"
                      {...field}
                      className="pl-10"
                    />
                    <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>E-posta (İsteğe bağlı)</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      placeholder="E-posta adresiniz"
                      {...field}
                      className="pl-10"
                    />
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="acceptTerms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    Kişisel verilerimin işlenmesine ilişkin <a href="#" className="text-primary underline">gizlilik politikasını</a> okudum ve kabul ediyorum.
                  </FormLabel>
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />

          <div className="pt-4 flex flex-col sm:flex-row gap-3">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="w-full sm:w-1/2"
              size="lg"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Geri
            </Button>

            <Button
              type="submit"
              className="w-full sm:w-1/2"
              size="lg"
              disabled={isSubmitting}
            >
              {isSubmitting ? "İşleniyor..." : "Devam Et"}
              {!isSubmitting && <ArrowRight className="ml-2 h-4 w-4" />}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
