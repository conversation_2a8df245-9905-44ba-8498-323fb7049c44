-- Salon tablosuna slug alanı ekle
ALTER TABLE salons ADD COLUMN slug TEXT UNIQUE;

-- Mevcut salonlar için slug oluştur
-- Salon adını küçük harfe çevir, boşlukları tire ile değiştir ve Türkçe karakterleri İngilizce karakterlere dönüştür
UPDATE salons
SET slug = LOWER(
  REPLACE(
    REPLACE(
      REPLACE(
        REPLACE(
          REPLACE(
            REPLACE(
              REPLACE(
                REPLACE(
                  REPLACE(
                    REPLACE(
                      REPLACE(
                        REPLACE(name, ' ', '-'),
                        'ğ', 'g'),
                      'ü', 'u'),
                    'ş', 's'),
                  'ı', 'i'),
                'ö', 'o'),
              'ç', 'c'),
            'Ğ', 'g'),
          'Ü', 'u'),
        'Ş', 's'),
      'İ', 'i'),
    'Ö', 'o'),
  'Ç', 'c')
);

-- Aynı slug'a sahip salonlar için slug'ın sonuna rastgele bir sayı ekle
DO $$
DECLARE
  duplicate_slug RECORD;
BEGIN
  FOR duplicate_slug IN (
    SELECT slug, COUNT(*) as count
    FROM salons
    GROUP BY slug
    HAVING COUNT(*) > 1
  ) LOOP
    UPDATE salons
    SET slug = slug || '-' || floor(random() * 1000)::text
    WHERE slug = duplicate_slug.slug
    AND id NOT IN (
      SELECT id
      FROM salons
      WHERE slug = duplicate_slug.slug
      LIMIT 1
    );
  END LOOP;
END $$;

-- Slug alanını NOT NULL yap
ALTER TABLE salons ALTER COLUMN slug SET NOT NULL;

-- Slug için index oluştur
CREATE INDEX idx_salons_slug ON salons(slug);

-- Slug için trigger oluştur (yeni salon eklendiğinde otomatik slug oluşturma)
CREATE OR REPLACE FUNCTION generate_salon_slug()
RETURNS TRIGGER AS $$
DECLARE
  base_slug TEXT;
  new_slug TEXT;
  counter INTEGER := 0;
BEGIN
  -- Salon adından slug oluştur
  base_slug := LOWER(
    REPLACE(
      REPLACE(
        REPLACE(
          REPLACE(
            REPLACE(
              REPLACE(
                REPLACE(
                  REPLACE(
                    REPLACE(
                      REPLACE(
                        REPLACE(
                          REPLACE(NEW.name, ' ', '-'),
                          'ğ', 'g'),
                        'ü', 'u'),
                      'ş', 's'),
                    'ı', 'i'),
                  'ö', 'o'),
                'ç', 'c'),
              'Ğ', 'g'),
            'Ü', 'u'),
          'Ş', 's'),
        'İ', 'i'),
      'Ö', 'o'),
    'Ç', 'c')
  );
  
  -- Özel karakterleri kaldır (sadece harfler, rakamlar ve tire kalacak)
  base_slug := REGEXP_REPLACE(base_slug, '[^a-z0-9\-]', '', 'g');
  
  -- Başlangıç slug'ı
  new_slug := base_slug;
  
  -- Slug benzersiz olana kadar sayı ekle
  WHILE EXISTS (SELECT 1 FROM salons WHERE slug = new_slug) LOOP
    counter := counter + 1;
    new_slug := base_slug || '-' || counter;
  END LOOP;
  
  NEW.slug := new_slug;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER salon_slug_trigger
BEFORE INSERT ON salons
FOR EACH ROW
WHEN (NEW.slug IS NULL)
EXECUTE FUNCTION generate_salon_slug();