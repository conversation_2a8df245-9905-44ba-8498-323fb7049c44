"use client"

import { motion } from "framer-motion"
import { Award, Users, Clock, Heart, CheckCircle, Star, TrendingUp, Shield, Zap, Target, Sparkles } from "lucide-react"
import { BookingButton } from "./booking-button"
import { getDefaultSalonContent } from "@/lib/db/public"
import { usePreview, useIsPreview } from "@/contexts/PreviewContext"
import { useSalon } from "@/contexts/SalonContext"

interface AboutSectionProps {
  salonName: string
  salonId: string
}

export function AboutSection({ salonName, salonId }: AboutSectionProps) {
  // Preview context
  const isPreview = useIsPreview()
  const previewContext = isPreview ? usePreview() : null

  // Salon context for content
  const { salonContent, contentLoading } = useSalon()

  // Icon mapping
  const iconMap: Record<string, any> = {
    Award, Users, Clock, Heart, CheckCircle, Star, TrendingUp, Shield, Zap, Target, Sparkles
  }

  // Get about content based on context
  const content = isPreview && previewContext
    ? previewContext.previewContent.about
    : salonContent?.about || getDefaultSalonContent().about

  const isLoading = isPreview ? false : contentLoading

  // Map features with icons
  const features = content.features?.map((feature: any) => ({
    ...feature,
    icon: iconMap[feature.icon] || Award
  })) || []

  const stats = content.stats || []
  const trustItems = content.trustItems || []

  return (
    <section id="about" className="py-20 bg-gradient-to-b from-muted/20 to-background">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div className="space-y-6">
                <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
                  <Heart className="w-4 h-4" />
                  <span>{content.badgeText}</span>
                </div>

                <h2 className="text-4xl md:text-5xl font-bold text-foreground">
                  <span className="block">{salonName}</span>
                  <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    {content.title}
                  </span>
                </h2>

                <p className="text-xl text-muted-foreground leading-relaxed">
                  {content.description}
                </p>
              </div>

              {/* Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {features.map((feature, index) => (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start space-x-3"
                  >
                    <div className="p-2 bg-primary/10 rounded-lg flex-shrink-0">
                      <feature.icon className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-foreground mb-1">
                        {feature.title}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {feature.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* CTA */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="pt-4"
              >
                <BookingButton
                  buttonText={content.ctaButtonText || 'Hemen Randevu Al'}
                  buttonSize="lg"
                  buttonClassName="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 text-white shadow-lg hover:shadow-xl transition-all duration-300 px-8"
                />
              </motion.div>
            </motion.div>

            {/* Right Content */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              {/* Image Placeholder */}
              <div className="relative">
                <div className="aspect-[4/3] bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl overflow-hidden shadow-xl">
                  <div className="w-full h-full bg-gradient-to-br from-muted/50 to-muted/30 flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <div className="w-20 h-20 bg-primary/20 rounded-full mx-auto flex items-center justify-center">
                        <Award className="w-10 h-10 text-primary" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-xl font-semibold text-foreground">Kalite Garantisi</h3>
                        <p className="text-muted-foreground">Profesyonel hizmet anlayışı</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Floating Stats */}
                <motion.div
                  animate={{ y: [-5, 5, -5] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -top-4 -right-4 bg-background shadow-lg rounded-xl p-4 border"
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">4.9/5</div>
                    <div className="text-xs text-muted-foreground">Müşteri Puanı</div>
                  </div>
                </motion.div>
              </div>

              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-4">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, scale: 0.9 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-background border border-border rounded-xl p-6 text-center hover:shadow-lg transition-shadow duration-300"
                  >
                    <div className="text-3xl font-bold text-primary mb-2">
                      {stat.number}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stat.label}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Trust Indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
                className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                  <h3 className="font-semibold text-foreground">{content.trustTitle || 'Güvenilir Hizmet'}</h3>
                </div>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  {trustItems.map((item: any, index: number) => (
                    <li key={index} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                      <span>{item.text}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
