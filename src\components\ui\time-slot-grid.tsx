"use client"

import { useState, useEffect } from "react"
import { format, parse, addMinutes } from "date-fns"

import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { BarberWorkingHours } from "@/lib/db/types"
import { HolidayDate } from "@/components/ui/holiday-calendar"

export interface TimeSlot {
  time: string;
  isAvailable: boolean;
  reason?: string;
}

export interface TimeSlotGridProps {
  date: Date;
  availableTimes: string[];
  selectedTime: string;
  onTimeSelect: (time: string) => void;
  holidayDates?: HolidayDate[];
  barberWorkingHours?: BarberWorkingHours[];
  existingAppointmentTimes?: string[];
}

export function TimeSlotGrid({
  date,
  availableTimes,
  selectedTime,
  onTimeSelect,
  holidayDates = [],
  barberWorkingHours = [],
  existingAppointmentTimes = []
}: TimeSlotGridProps) {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])

  // Generate time slots based on barber working hours with 30-minute intervals
  useEffect(() => {


    const slots: TimeSlot[] = []

    // Varsayılan başlangıç ve bitiş saatleri
    let startTimeStr = "09:00"
    let endTimeStr = "22:00"

    // Eğer berber çalışma saatleri varsa, onları kullan
    if (barberWorkingHours.length > 0) {
      const dayOfWeek = date.getDay()
      const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

      if (workingHoursForDay && !workingHoursForDay.is_closed) {
        startTimeStr = workingHoursForDay.open_time.substring(0, 5)
        endTimeStr = workingHoursForDay.close_time.substring(0, 5)
      }
    }

    const startTime = parse(startTimeStr, "HH:mm", new Date())
    const endTime = parse(endTimeStr, "HH:mm", new Date())
    let currentTime = startTime

    // Function to check if a date is a holiday
    const isHoliday = (date: Date) => {
      return holidayDates.some(
        (holiday) => format(holiday.date, "yyyy-MM-dd") === format(date, "yyyy-MM-dd")
      )
    }

    // Function to check if a barber works on a specific day
    const isBarberWorkingDay = (date: Date) => {
      if (!barberWorkingHours.length) return true

      const dayOfWeek = date.getDay() // 0 = Sunday, 6 = Saturday
      const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

      // If no working hours found for this day or is_closed is true, barber doesn't work
      return workingHoursForDay && !workingHoursForDay.is_closed
    }

    // Function to check if a time is within barber working hours
    const isTimeWithinWorkingHours = (time: string) => {
      if (!barberWorkingHours.length) {
        return true
      }

      const dayOfWeek = date.getDay()
      const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

      if (!workingHoursForDay || workingHoursForDay.is_closed) {
        return false
      }

      // Format time strings to ensure proper comparison (HH:mm format)
      const formattedTime = time.substring(0, 5)
      const formattedOpenTime = workingHoursForDay.open_time.substring(0, 5)
      const formattedCloseTime = workingHoursForDay.close_time.substring(0, 5)

      // Check if time is within working hours
      const isWithinWorkingHours = formattedTime >= formattedOpenTime && formattedTime <= formattedCloseTime

      // Check if time is during lunch break
      const isDuringLunchBreak = workingHoursForDay.has_lunch_break &&
        workingHoursForDay.lunch_start_time &&
        workingHoursForDay.lunch_end_time &&
        formattedTime >= workingHoursForDay.lunch_start_time.substring(0, 5) &&
        formattedTime <= workingHoursForDay.lunch_end_time.substring(0, 5)

      return isWithinWorkingHours && !isDuringLunchBreak
    }

    // Function to check if a time is during lunch break
    const isTimeDuringLunchBreak = (time: string) => {
      if (!barberWorkingHours.length) return false

      const dayOfWeek = date.getDay()
      const workingHoursForDay = barberWorkingHours.find(wh => wh.day_of_week === dayOfWeek)

      if (!workingHoursForDay || !workingHoursForDay.has_lunch_break) return false

      // Format time strings to ensure proper comparison (HH:mm format)
      const formattedTime = time.substring(0, 5)

      return workingHoursForDay.lunch_start_time &&
        workingHoursForDay.lunch_end_time &&
        formattedTime >= workingHoursForDay.lunch_start_time.substring(0, 5) &&
        formattedTime <= workingHoursForDay.lunch_end_time.substring(0, 5)
    }

    // Check if the date is a holiday
    const isHolidayDate = isHoliday(date)

    // Check if the barber works on this day
    const isBarberWorking = isBarberWorkingDay(date)

    while (currentTime <= endTime) {
      const timeString = format(currentTime, "HH:mm")

      // Determine if the time slot is available
      // If the time is the selected time, always mark it as available
      const isAvailable = timeString === selectedTime || availableTimes.includes(timeString)
      let reason = ""

      // Eğer müsait değilse, nedenini belirle (sadece bilgi amaçlı)
      if (!isAvailable) {
        if (isHolidayDate) {
          reason = "Tatil günü"
        } else if (!isBarberWorking) {
          reason = "Berber çalışmıyor"
        } else if (isTimeDuringLunchBreak(timeString)) {
          reason = "Öğle arası"
        } else if (!isTimeWithinWorkingHours(timeString)) {
          reason = "Çalışma saatleri dışında"
        } else if (existingAppointmentTimes.includes(timeString)) {
          reason = "Dolu randevu"
        } else {
          reason = "Müsait değil"
        }
      }

      slots.push({
        time: timeString,
        isAvailable,
        reason
      })

      currentTime = addMinutes(currentTime, 30)
    }



    setTimeSlots(slots)
  }, [date, availableTimes, holidayDates, barberWorkingHours, existingAppointmentTimes, selectedTime])

  return (
    <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 gap-2 max-h-[40vh] overflow-y-auto p-1 scrollbar-hide">
      {timeSlots.map((slot) => (
        <TooltipProvider key={slot.time}>
          <Tooltip>
            <TooltipTrigger asChild>
              <div
                className={cn(
                  "p-2 rounded-md border text-center cursor-pointer transition-colors text-sm",
                  slot.isAvailable && "hover:bg-muted",
                  !slot.isAvailable && "opacity-50 bg-muted/30 cursor-not-allowed",
                  selectedTime === slot.time && "bg-primary text-primary-foreground"
                )}
                onClick={() => {
                  if (slot.isAvailable) {
                    onTimeSelect(slot.time)
                  }
                }}
              >
                {slot.time}
              </div>
            </TooltipTrigger>
            <TooltipContent side="top">
              {slot.isAvailable ? (
                <p>Müsait</p>
              ) : (
                <p>{slot.reason}</p>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
    </div>
  )
}
