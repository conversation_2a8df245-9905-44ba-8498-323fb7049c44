import { supabaseClient } from '../supabase-singleton';
import {
  SalonSubscription,
  SalonSubscriptionInsert,
  SalonSubscriptionUpdate,
  SubscriptionPlan
} from './types';

const supabase = supabaseClient;

/**
 * Get the active subscription for a salon with plan details
 */
export async function getActiveSalonSubscription(salonId: string) {
  console.log("🔍 getActiveSalonSubscription çağrıldı, salonId:", salonId);

  try {
    const { data, error } = await supabase
      .from('salon_subscriptions')
      .select('*, plans:plan_id(*)')
      .eq('salon_id', salonId)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // PGRST116 is "No rows returned" error
        console.log("⚠️ Aktif abonelik bulunamadı");
        return null;
      }
      console.error("❌ Abonelik sorgusunda hata:", error);
      throw error;
    }

    console.log("✅ Aktif abonelik bulundu:", data ? `ID: ${data.id}, Status: ${data.status}` : "null");
    return data as SalonSubscription | null;
  } catch (error) {
    console.error("❌ getActiveSalonSubscription beklenmeyen hata:", error);
    throw error;
  }
}

/**
 * Get all subscriptions for a salon with plan details
 */
export async function getSalonSubscriptions(salonId: string) {
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .select('*, plans:plan_id(*)')
    .eq('salon_id', salonId)
    .order('start_date', { ascending: false });

  if (error) throw error;
  return data as SalonSubscription[];
}

/**
 * Create a new subscription for a salon
 */
export async function createSalonSubscription(subscription: SalonSubscriptionInsert) {
  // First, deactivate any active subscriptions
  await supabase
    .from('salon_subscriptions')
    .update({ is_active: false })
    .eq('salon_id', subscription.salon_id)
    .eq('is_active', true);

  // Then create the new subscription
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .insert(subscription)
    .select()
    .single();

  if (error) throw error;
  return data as SalonSubscription;
}

/**
 * Update a subscription
 */
export async function updateSalonSubscription({ id, ...subscription }: SalonSubscriptionUpdate) {
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .update(subscription)
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SalonSubscription;
}

/**
 * Cancel a subscription
 */
export async function cancelSalonSubscription(id: string) {
  const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD

  const { data, error } = await supabase
    .from('salon_subscriptions')
    .update({
      is_active: false,
      end_date: today
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SalonSubscription;
}

/**
 * Check if a salon has an active subscription
 */
export async function hasSalonActiveSubscription(salonId: string) {
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .select('*')
    .eq('salon_id', salonId)
    .eq('is_active', true);

  if (error) throw error;
  return data.length > 0;
}

/**
 * Get the subscription plan for a salon
 */
export async function getSalonSubscriptionPlan(salonId: string) {
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .select('plan_id, plans:plan_id(*)')
    .eq('salon_id', salonId)
    .eq('is_active', true)
    .single();

  if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "No rows returned" error
  return data ? (data.plans as unknown as SubscriptionPlan) : null;
}

/**
 * Create a trial subscription for a salon
 * @param salonId Salon ID
 * @param planId Plan ID
 * @param isReferred Whether the user was referred (extends trial period)
 */
export async function createTrialSubscription(salonId: string, planId: string, isReferred: boolean = false) {
  const today = new Date();
  const trialEndDate = new Date();

  // Referans ile gelen kullanıcılar için 30 gün, diğerleri için 14 gün deneme süresi
  const trialDays = isReferred ? 30 : 14;
  trialEndDate.setDate(today.getDate() + trialDays);

  console.log(`Creating trial subscription for salon ${salonId} with ${trialDays} days trial period (isReferred: ${isReferred})`);

  const subscription: SalonSubscriptionInsert = {
    salon_id: salonId,
    plan_id: planId,
    plan: 'basic', // Eski plan sütunu için varsayılan değer
    status: 'trial',
    start_date: today.toISOString().split('T')[0],
    trial_end_date: trialEndDate.toISOString().split('T')[0],
    payment_method: 'manual',
    is_yearly: false,
    is_active: true
  };

  return createSalonSubscription(subscription);
}

/**
 * Upgrade a subscription to a new plan
 */
export async function upgradeSubscription(subscriptionId: string, newPlanId: string, isYearly: boolean) {
  const { data: currentSub, error: fetchError } = await supabase
    .from('salon_subscriptions')
    .select('*')
    .eq('id', subscriptionId)
    .single();

  if (fetchError) throw fetchError;

  // Aboneliği güncelle
  const updateData: SalonSubscriptionUpdate = {
    id: subscriptionId,
    plan_id: newPlanId,
    is_yearly: isYearly,
    status: 'active'
  };

  return updateSalonSubscription(updateData);
}

/**
 * Check if a salon's trial is expired
 */
export async function isTrialExpired(salonId: string) {
  console.log("🔍 isTrialExpired çağrıldı, salonId:", salonId);

  try {
    const subscription = await getActiveSalonSubscription(salonId);

    if (!subscription) {
      console.log("⚠️ Abonelik bulunamadı, deneme süresi bitmiş kabul ediliyor");
      return true;
    }

    if (subscription.status !== 'trial') {
      console.log("ℹ️ Abonelik deneme sürecinde değil, durumu:", subscription.status);
      return true;
    }

    if (!subscription.trial_end_date) {
      console.log("⚠️ Deneme bitiş tarihi bulunamadı, deneme süresi bitmiş kabul ediliyor");
      return true;
    }

    const today = new Date();
    const trialEndDate = new Date(subscription.trial_end_date);
    const isExpired = today > trialEndDate;

    console.log("ℹ️ Deneme süresi kontrolü:",
      isExpired ? "Süresi dolmuş" : "Aktif",
      "- Bitiş tarihi:", subscription.trial_end_date);

    return isExpired;
  } catch (error) {
    console.error("❌ isTrialExpired beklenmeyen hata:", error);
    return true; // Hata durumunda güvenli tarafta kalmak için true döndür
  }
}

/**
 * Suspend a subscription due to payment failure
 */
export async function suspendSubscription(id: string) {
  const { data, error } = await supabase
    .from('salon_subscriptions')
    .update({
      status: 'suspended'
    })
    .eq('id', id)
    .select()
    .single();

  if (error) throw error;
  return data as SalonSubscription;
}
