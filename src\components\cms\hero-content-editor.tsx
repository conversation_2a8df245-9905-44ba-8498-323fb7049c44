"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Star, Users, Clock, TrendingUp } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"

import { useContent } from "@/contexts/ContentContext"

interface HeroContentEditorProps {
  onContentChange?: () => void
}

export function HeroContentEditor({ onContentChange }: HeroContentEditorProps) {
  const { getContentValue, updateContentValue } = useContent()
  
  // Form state
  const [formData, setFormData] = useState({
    badgeText: '',
    tagline: '',
    description: '',
    ctaPrimary: '',
    ctaSecondary: '',
    statsCustomers: '',
    statsCustomersLabel: '',
    statsExperience: '',
    statsExperienceLabel: '',
    statsRating: '',
    statsRatingLabel: '',
    statsSupport: '',
    statsSupportLabel: ''
  })

  // Load initial data
  useEffect(() => {
    setFormData({
      badgeText: getContentValue('hero', 'badge_text', 'Profesyonel Berber Hizmetleri'),
      tagline: getContentValue('hero', 'tagline', 'ile tarzını yansıt.'),
      description: getContentValue('hero', 'description', 'Uzman ekibimizle kaliteli hizmet garantisi. Modern teknikler ve kişisel yaklaşımla tarzınızı yenileyin.'),
      ctaPrimary: getContentValue('hero', 'cta_primary', 'Randevu Al'),
      ctaSecondary: getContentValue('hero', 'cta_secondary', 'Hizmetlerimizi Keşfet'),
      statsCustomers: getContentValue('hero', 'stats_customers', '500+'),
      statsCustomersLabel: getContentValue('hero', 'stats_customers_label', 'Mutlu Müşteri'),
      statsExperience: getContentValue('hero', 'stats_experience', '5+'),
      statsExperienceLabel: getContentValue('hero', 'stats_experience_label', 'Yıl Deneyim'),
      statsRating: getContentValue('hero', 'stats_rating', '4.9'),
      statsRatingLabel: getContentValue('hero', 'stats_rating_label', 'Müşteri Puanı'),
      statsSupport: getContentValue('hero', 'stats_support', '24/7'),
      statsSupportLabel: getContentValue('hero', 'stats_support_label', 'Online Randevu')
    })
  }, [getContentValue])

  // Handle input changes
  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }))
    
    // Map form keys to content keys
    const contentKeyMap: Record<string, string> = {
      badgeText: 'badge_text',
      tagline: 'tagline',
      description: 'description',
      ctaPrimary: 'cta_primary',
      ctaSecondary: 'cta_secondary',
      statsCustomers: 'stats_customers',
      statsCustomersLabel: 'stats_customers_label',
      statsExperience: 'stats_experience',
      statsExperienceLabel: 'stats_experience_label',
      statsRating: 'stats_rating',
      statsRatingLabel: 'stats_rating_label',
      statsSupport: 'stats_support',
      statsSupportLabel: 'stats_support_label'
    }

    const contentKey = contentKeyMap[key]
    if (contentKey) {
      updateContentValue('hero', contentKey, value)
      onContentChange?.()
    }
  }

  return (
    <div className="space-y-6">
      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Ana İçerik</CardTitle>
          <CardDescription>
            Hero bölümünün ana başlık, açıklama ve buton metinleri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label htmlFor="badgeText">Rozet Metni</Label>
              <Input
                id="badgeText"
                value={formData.badgeText}
                onChange={(e) => handleInputChange('badgeText', e.target.value)}
                placeholder="Profesyonel Berber Hizmetleri"
              />
              <p className="text-xs text-muted-foreground">
                Sayfanın üst kısmında görünen küçük rozet metni
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tagline">Slogan</Label>
              <Input
                id="tagline"
                value={formData.tagline}
                onChange={(e) => handleInputChange('tagline', e.target.value)}
                placeholder="ile tarzını yansıt."
              />
              <p className="text-xs text-muted-foreground">
                Salon adından sonra gelen slogan metni
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Açıklama</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Uzman ekibimizle kaliteli hizmet garantisi..."
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                Ana başlığın altında görünen açıklama metni
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ctaPrimary">Ana Buton</Label>
                <Input
                  id="ctaPrimary"
                  value={formData.ctaPrimary}
                  onChange={(e) => handleInputChange('ctaPrimary', e.target.value)}
                  placeholder="Randevu Al"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ctaSecondary">İkincil Buton</Label>
                <Input
                  id="ctaSecondary"
                  value={formData.ctaSecondary}
                  onChange={(e) => handleInputChange('ctaSecondary', e.target.value)}
                  placeholder="Hizmetlerimizi Keşfet"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>İstatistikler</CardTitle>
          <CardDescription>
            Hero bölümünde görünen istatistik kartları
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Customers Stat */}
          <div className="flex items-center gap-4 p-4 border rounded-lg">
            <div className="p-2 bg-primary/10 rounded-full">
              <Users className="w-5 h-5 text-primary" />
            </div>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="statsCustomers">Değer</Label>
                <Input
                  id="statsCustomers"
                  value={formData.statsCustomers}
                  onChange={(e) => handleInputChange('statsCustomers', e.target.value)}
                  placeholder="500+"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="statsCustomersLabel">Etiket</Label>
                <Input
                  id="statsCustomersLabel"
                  value={formData.statsCustomersLabel}
                  onChange={(e) => handleInputChange('statsCustomersLabel', e.target.value)}
                  placeholder="Mutlu Müşteri"
                />
              </div>
            </div>
          </div>

          {/* Experience Stat */}
          <div className="flex items-center gap-4 p-4 border rounded-lg">
            <div className="p-2 bg-primary/10 rounded-full">
              <TrendingUp className="w-5 h-5 text-primary" />
            </div>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="statsExperience">Değer</Label>
                <Input
                  id="statsExperience"
                  value={formData.statsExperience}
                  onChange={(e) => handleInputChange('statsExperience', e.target.value)}
                  placeholder="5+"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="statsExperienceLabel">Etiket</Label>
                <Input
                  id="statsExperienceLabel"
                  value={formData.statsExperienceLabel}
                  onChange={(e) => handleInputChange('statsExperienceLabel', e.target.value)}
                  placeholder="Yıl Deneyim"
                />
              </div>
            </div>
          </div>

          {/* Rating Stat */}
          <div className="flex items-center gap-4 p-4 border rounded-lg">
            <div className="p-2 bg-primary/10 rounded-full">
              <Star className="w-5 h-5 text-primary" />
            </div>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="statsRating">Değer</Label>
                <Input
                  id="statsRating"
                  value={formData.statsRating}
                  onChange={(e) => handleInputChange('statsRating', e.target.value)}
                  placeholder="4.9"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="statsRatingLabel">Etiket</Label>
                <Input
                  id="statsRatingLabel"
                  value={formData.statsRatingLabel}
                  onChange={(e) => handleInputChange('statsRatingLabel', e.target.value)}
                  placeholder="Müşteri Puanı"
                />
              </div>
            </div>
          </div>

          {/* Support Stat */}
          <div className="flex items-center gap-4 p-4 border rounded-lg">
            <div className="p-2 bg-primary/10 rounded-full">
              <Clock className="w-5 h-5 text-primary" />
            </div>
            <div className="flex-1 grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="statsSupport">Değer</Label>
                <Input
                  id="statsSupport"
                  value={formData.statsSupport}
                  onChange={(e) => handleInputChange('statsSupport', e.target.value)}
                  placeholder="24/7"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="statsSupportLabel">Etiket</Label>
                <Input
                  id="statsSupportLabel"
                  value={formData.statsSupportLabel}
                  onChange={(e) => handleInputChange('statsSupportLabel', e.target.value)}
                  placeholder="Online Randevu"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
