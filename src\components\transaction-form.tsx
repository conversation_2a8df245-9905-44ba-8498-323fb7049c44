"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { format } from "date-fns"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DatePicker } from "@/components/ui/date-picker"
import { Textarea } from "@/components/ui/textarea"
import { Loader2 } from "lucide-react"
import { useUser } from "@/contexts/UserContext"
import { FinanceCategory, FinanceTransaction, FinanceTransactionInsert, FinanceTransactionUpdate } from "@/lib/db/types"
import { financeCategories, financeTransactions, appointments } from "@/lib/db"

// Form schema
const formSchema = z.object({
  category_id: z.string({
    required_error: "Kategori seçmelisiniz.",
  }),
  amount: z.coerce.number({
    required_error: "Tutar girmelisiniz.",
  }).positive("Tutar pozitif olmalıdır."),
  transaction_date: z.date({
    required_error: "Tarih seçmelisiniz.",
  }),
  description: z.string().optional(),
  appointment_id: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface TransactionFormProps {
  transaction?: FinanceTransaction
  onSuccess?: () => void
}

export function TransactionForm({ transaction, onSuccess }: TransactionFormProps) {
  const router = useRouter()
  const { salon } = useUser()
  const salonId = salon?.id

  const [isLoading, setIsLoading] = useState(false)
  const [categories, setCategories] = useState<FinanceCategory[]>([])
  const [appointments, setAppointments] = useState<any[]>([])
  const [isLoadingCategories, setIsLoadingCategories] = useState(false)
  const [isLoadingAppointments, setIsLoadingAppointments] = useState(false)

  // Initialize form with default values or transaction values if editing
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: transaction
      ? {
          category_id: transaction.category_id,
          amount: transaction.amount,
          transaction_date: transaction.transaction_date ? new Date(transaction.transaction_date) : undefined,
          description: transaction.description || "",
          appointment_id: transaction.appointment_id || "no_appointment",
        }
      : {
          category_id: "",
          amount: undefined,
          transaction_date: new Date(),
          description: "",
          appointment_id: "no_appointment",
        },
  })

  // Fetch categories and appointments when component mounts
  useEffect(() => {
    if (salonId) {
      fetchCategories()
      fetchAppointments()
    }
  }, [salonId])

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setIsLoadingCategories(true)
      const data = await financeCategories.getFinanceCategories(salonId);
      setCategories(data)
    } catch (error) {
      console.error("Error fetching categories:", error)
      toast.error("Kategoriler yüklenirken bir hata oluştu")
    } finally {
      setIsLoadingCategories(false)
    }
  }

  // Fetch appointments
  const fetchAppointments = async () => {
    try {
      setIsLoadingAppointments(true)
      const data = await appointments.getAppointmentsBySalon(salonId);
      setAppointments(data)
    } catch (error) {
      console.error("Error fetching appointments:", error)
      toast.error("Randevular yüklenirken bir hata oluştu")
    } finally {
      setIsLoadingAppointments(false)
    }
  }

  // Form submission handler
  const onSubmit = async (values: FormValues) => {
    if (!salonId) {
      toast.error("Salon bilgisi bulunamadı")
      return
    }

    setIsLoading(true)

    try {
      // Format the date
      const formattedDate = format(values.transaction_date, "yyyy-MM-dd");

      // If appointment_id is "no_appointment" or empty, set it to null
      const appointmentId = (values.appointment_id === "no_appointment" || !values.appointment_id)
        ? null
        : values.appointment_id;

      if (transaction) {
        // Update existing transaction
        const updateData: FinanceTransactionUpdate = {
          id: transaction.id,
          category_id: values.category_id,
          amount: values.amount,
          transaction_date: formattedDate,
          description: values.description || null,
          appointment_id: appointmentId,
        };

        await financeTransactions.updateFinanceTransaction(updateData);
      } else {
        // Create new transaction
        const newTransaction: FinanceTransactionInsert = {
          salon_id: salonId,
          category_id: values.category_id,
          amount: values.amount,
          transaction_date: formattedDate,
          description: values.description || null,
          appointment_id: appointmentId,
          service_id: null,
          created_by: null, // Will be set by the database function
        };

        await financeTransactions.createFinanceTransaction(newTransaction);
      }

      toast.success(transaction ? "İşlem güncellendi" : "İşlem eklendi")

      if (onSuccess) {
        onSuccess()
      } else {
        router.push("/dashboard/finance/transactions")
        router.refresh()
      }
    } catch (error) {
      console.error("Error saving transaction:", error)
      toast.error(error instanceof Error ? error.message : "İşlem kaydedilirken bir hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  if (!salonId) {
    return <div>Salon bilgisi yükleniyor...</div>
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="category_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Kategori</FormLabel>
                <Select
                  disabled={isLoading || isLoadingCategories}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Kategori seçin" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id}
                      >
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: category.color || "#ccc" }}
                          />
                          {category.name} ({category.type === "income" ? "Gelir" : "Gider"})
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="amount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tutar (₺)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="transaction_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tarih</FormLabel>
                <DatePicker
                  date={field.value}
                  setDate={field.onChange}
                  disabled={isLoading}
                  compact={true}
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="appointment_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>İlişkili Randevu (Opsiyonel)</FormLabel>
                <Select
                  disabled={isLoading || isLoadingAppointments}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Randevu seçin (opsiyonel)" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="no_appointment">Randevu seçilmedi</SelectItem>
                    {appointments.map((appointment) => (
                      <SelectItem
                        key={appointment.id}
                        value={appointment.id}
                      >
                        {format(new Date(appointment.date), "dd.MM.yyyy")} - {appointment.start_time} - {appointment.fullname}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Açıklama (Opsiyonel)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="İşlem hakkında açıklama girin"
                  disabled={isLoading}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" disabled={isLoading}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {transaction ? "Güncelle" : "Kaydet"}
        </Button>
      </form>
    </Form>
  )
}
