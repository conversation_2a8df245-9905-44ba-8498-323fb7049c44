// Test script to check Supabase connection and RLS policies
require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Error: Supabase credentials not found in environment variables.');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test connection
async function testConnection() {
  try {
    console.log('Testing Supabase connection...');

    // Try to query the salons table
    const { data: salons, error: salonsError } = await supabase
      .from('salons')
      .select('*')
      .limit(5);

    if (salonsError) {
      console.error('Error querying salons table:', salonsError);
      return false;
    }

    console.log('Database connection successful!');
    console.log('Salons found:', salons.length);
    console.log('Salons:', salons);

    // Try to query the customers table
    const { data: customers, error: customersError } = await supabase
      .from('customers')
      .select('*')
      .limit(5);

    if (customersError) {
      console.error('Error querying customers table:', customersError);
      console.error('This might be due to RLS policies. This is expected for unauthenticated users.');
    } else {
      console.log('Customers found:', customers.length);
      console.log('Customers:', customers);
    }

    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

// Run the test
testConnection()
  .then(success => {
    if (success) {
      console.log('Supabase connection test passed!');
    } else {
      console.error('Supabase connection test failed!');
    }
  })
  .catch(error => {
    console.error('Error running test:', error);
  });
