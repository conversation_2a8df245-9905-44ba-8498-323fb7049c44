# Public Access RPC Fonksiyonları Uygulaması ve Düzeltmeleri

**Tarih:** 25 Ağustos 2024
**Saat:** 19:00

## Genel Bakış

Bu dokümantasyon, kimlik doğrulaması olmayan kullanıcılar için güvenli veri erişimi sağlayan SECURITY DEFINER fonksiyonlarının uygulanmasını ve karşılaşılan sorunların çözümlerini açıklamaktadır. Bu değişiklikler, client-side kodun doğrudan veritabanı tablolarına erişmek yerine RPC fonksiyonlarını kullanmasını sağlar.

## 1. İlk Uygulama

### Public Access Function Wrapper Dosyaları

Aşağıdaki dosyalar oluşturuldu:

- `src/lib/db/public/salons.ts` - Salon bilgilerini getiren fonksiyonlar
- `src/lib/db/public/barbers.ts` - Berber bilgilerini getiren fonksiyonlar
- `src/lib/db/public/services.ts` - Hizmet bilgilerini getiren fonksiyonlar
- `src/lib/db/public/working-hours.ts` - Çalışma saatlerini getiren fonksiyonlar
- `src/lib/db/public/holidays.ts` - Tatil günlerini getiren fonksiyonlar
- `src/lib/db/public/products.ts` - Ürün bilgilerini getiren fonksiyonlar
- `src/lib/db/public/appointments.ts` - Randevu bilgilerini getiren ve oluşturan fonksiyonlar
- `src/lib/db/public/index.ts` - Tüm public access fonksiyonlarını export eden dosya

### Ana index.ts Dosyası Güncellendi

`src/lib/db/index.ts` dosyası, public access fonksiyonlarını export edecek şekilde güncellendi:

```typescript
// Export public access functions
export * as publicAccess from './public';
```

### Client-Side Kodlar Güncellendi

Aşağıdaki dosyalar, doğrudan veritabanı erişimi yerine RPC fonksiyonlarını kullanacak şekilde güncellendi:

- `src/app/[slug]/products/page.tsx`
- `src/app/booking/[salonId]/page.tsx`
- `src/components/client/client-booking-form.tsx`

## 2. Karşılaşılan Sorunlar ve Çözümleri

### Sorun 1: Working Hours RPC Fonksiyonu Hatası

```json
{
    "code": "42703",
    "details": null,
    "hint": null,
    "message": "column w.barber_id does not exist"
}
```

**Analiz:**
- `working_hours` tablosunda `barber_id` sütunu yok
- `barber_working_hours` tablosunda `barber_id` sütunu var
- İki farklı tablo için ayrı RPC fonksiyonları gerekiyor

**Çözüm:**
1. `get_public_working_hours_by_salon_id` fonksiyonu düzeltildi
2. `get_public_barber_working_hours_by_barber_id` ve `get_public_barber_working_hours_by_salon_id` fonksiyonları eklendi
3. `src/lib/db/public/barber-working-hours.ts` dosyası oluşturuldu
4. `src/lib/db/public/index.ts` dosyası güncellendi

### Sorun 2: RPC Fonksiyonu Bulunamadı Hatası

```json
{
    "code": "PGRST202",
    "details": "Searched for the function public.get_public_barber_working_hours_by_barber_id with parameter p_barber_id or with a single unnamed json/jsonb parameter, but no matches were found in the schema cache.",
    "hint": "Perhaps you meant to call the function public.get_public_working_hours_by_salon_id",
    "message": "Could not find the function public.get_public_barber_working_hours_by_barber_id(p_barber_id) in the schema cache"
}
```

**Analiz:**
- SQL fonksiyonları Supabase'de çalıştırılmamış

**Geçici Çözüm:**
Berber çalışma saatlerini çekme mantığını korumak için, doğrudan veritabanı erişimi kullanarak geçici bir çözüm uygulandı:

```typescript
// Berber çalışma saatlerini getir
let barberWorkingHours = []
try {
  const { data, error } = await supabaseClient
    .from('barber_working_hours')
    .select('*')
    .eq('barber_id', barberId)
    .order('day_of_week')
  
  if (!error) {
    barberWorkingHours = data || []
  }
} catch (err) {
  console.error("Error fetching barber working hours:", err)
}

// Salon çalışma saatlerini getir
const salonWorkingHours = await publicAccess.getPublicWorkingHoursBySalonId(salonId)

// Berber çalışma saatleri varsa onları, yoksa salon çalışma saatlerini kullan
const hoursToUse = barberWorkingHours.length > 0
  ? barberWorkingHours.map(bwh => ({
      id: bwh.id,
      barber_id: bwh.barber_id,
      salon_id: salonId,
      day_of_week: bwh.day_of_week,
      open_time: bwh.open_time,
      close_time: bwh.close_time,
      is_closed: bwh.is_closed,
      lunch_start_time: bwh.lunch_start_time,
      lunch_end_time: bwh.lunch_end_time,
      has_lunch_break: bwh.has_lunch_break
    }))
  : salonWorkingHours.map(wh => ({
      id: wh.id,
      barber_id: barberId,
      salon_id: wh.salon_id,
      day_of_week: wh.day_of_week,
      open_time: wh.open_time,
      close_time: wh.close_time,
      is_closed: wh.is_closed,
      lunch_start_time: null,
      lunch_end_time: null,
      has_lunch_break: false
    }))
```

## 3. Kalıcı Çözüm İçin Talimatlar

SQL fonksiyonlarını Supabase'de çalıştırmak için aşağıdaki adımları izleyin:

1. Supabase Dashboard'a giriş yapın
2. Projenizi seçin
3. Sol menüden "SQL Editor" seçeneğine tıklayın
4. "New Query" butonuna tıklayın
5. `docs/sql/public_access_rpc/2024-08-25-19-00-00-working-hours-functions-fix.sql` dosyasındaki SQL kodunu kopyalayıp yapıştırın
6. "Run" butonuna tıklayın
7. SQL fonksiyonları başarıyla çalıştırıldıktan sonra, uygulamayı yeniden başlatın

SQL fonksiyonları başarıyla çalıştırıldıktan sonra, doğrudan veritabanı erişimi yerine RPC fonksiyonlarını kullanacak şekilde kodu güncelleyebilirsiniz:

```typescript
// Get both salon working hours and barber working hours
const salonWorkingHours = await publicAccess.getPublicWorkingHoursBySalonId(salonId)
const barberWorkingHours = await publicAccess.getPublicBarberWorkingHoursByBarberId(barberId)

// Use barber working hours if available, otherwise use salon working hours
const hoursToUse = barberWorkingHours.length > 0 
  ? barberWorkingHours.map(bwh => ({
      ...bwh,
      salon_id: salonId // Ensure salon_id is set for consistency
    }))
  : salonWorkingHours
```

## 4. Sonuç

Bu değişiklikler sayesinde:

1. Client-side kod artık doğrudan veritabanı tablolarına erişmek yerine güvenli RPC fonksiyonlarını kullanmaktadır.
2. Salon çalışma saatleri ve berber çalışma saatleri için ayrı RPC fonksiyonları oluşturulmuştur.
3. Berber çalışma saatlerini çekme mantığı korunmuştur.

Bu iyileştirmeler, uygulamanın güvenliğini ve erişim kontrolünü artırırken, mevcut işlevselliği korumaktadır.
