-- Fix recursive policy for barbers table

-- Drop the problematic policy
DROP POLICY IF EXISTS "Staff can view salon barbers" ON barbers;

-- Create a new policy that avoids recursion
CREATE POLICY "Staff can view salon barbers" ON barbers
  FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM barbers AS staff_barber
    WHERE staff_barber.user_id = auth.uid()
    AND staff_barber.salon_id = barbers.salon_id
  ));
