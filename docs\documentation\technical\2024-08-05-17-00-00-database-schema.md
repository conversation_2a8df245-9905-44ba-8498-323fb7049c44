# SalonFlow Veritabanı Şeması

**Tarih:** 5 Ağustos 2024
**Saat:** 17:00

## Genel Bakış

Bu dokümantasyon, SalonFlow veritabanı şemasını detaylı olarak açıklamaktadır. SalonFlow, PostgreSQL veritabanı kullanmaktadır ve Supabase üzerinde barındırılmaktadır.

## Tablolar

### 1. salons

Kuaför salonlarının bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| name | TEXT | Salon adı |
| address | TEXT | Salon adresi |
| phone | TEXT | Salon telefon numarası |
| description | TEXT | Salon açıklaması |
| logo_url | TEXT | Salon logo URL'si |
| owner_id | UUID | Salon sahibinin kullanıcı ID'si |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |
| slug | TEXT | Salon URL slug'ı |
| is_active | BOOLEAN | Salon aktif mi? |

### 2. barbers

Berberlerin bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| user_id | UUID | Kullanıcı ID'si (auth.users tablosuna referans) |
| name | TEXT | Berber adı |
| email | TEXT | Berber e-posta adresi |
| phone | TEXT | Berber telefon numarası |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |
| is_active | BOOLEAN | Berber aktif mi? |

### 3. services

Salonların sunduğu hizmetlerin bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| name | TEXT | Hizmet adı |
| description | TEXT | Hizmet açıklaması |
| price | NUMERIC | Hizmet fiyatı |
| duration | INTEGER | Hizmet süresi (dakika) |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |
| is_active | BOOLEAN | Hizmet aktif mi? |

### 4. appointments

Randevu bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| barber_id | UUID | Berber ID'si (barbers tablosuna referans) |
| service_id | UUID | Hizmet ID'si (services tablosuna referans) |
| start_time | TIMESTAMP | Başlangıç zamanı |
| end_time | TIMESTAMP | Bitiş zamanı |
| status | TEXT | Randevu durumu (active, completed, cancelled, no-show) |
| notes | TEXT | Randevu notları |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |
| created_by | UUID | Randevuyu oluşturan kullanıcı ID'si |
| fullname | TEXT | Müşteri adı |
| phonenumber | TEXT | Müşteri telefon numarası |
| email | TEXT | Müşteri e-posta adresi |

### 5. working_hours

Salon ve berberlerin çalışma saatlerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| barber_id | UUID | Berber ID'si (barbers tablosuna referans, NULL ise salon çalışma saati) |
| day_of_week | INTEGER | Haftanın günü (0-6, 0=Pazar) |
| start_time | TIME | Başlangıç saati |
| end_time | TIME | Bitiş saati |
| is_day_off | BOOLEAN | Tatil günü mü? |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |

### 6. holidays

Salon ve berberlerin tatil günlerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| barber_id | UUID | Berber ID'si (barbers tablosuna referans, NULL ise salon tatili) |
| date | DATE | Tatil tarihi |
| description | TEXT | Tatil açıklaması |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |

### 7. notifications

Bildirim bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| user_id | UUID | Kullanıcı ID'si (auth.users tablosuna referans) |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| title | TEXT | Bildirim başlığı |
| message | TEXT | Bildirim mesajı |
| type | TEXT | Bildirim tipi (appointment, system, etc.) |
| is_read | BOOLEAN | Okundu mu? |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| created_by | UUID | Bildirimi oluşturan kullanıcı ID'si |

### 8. subscription_plans

Abonelik planlarının bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| name | TEXT | Plan adı |
| description | TEXT | Plan açıklaması |
| price_monthly | NUMERIC | Aylık fiyat |
| price_yearly | NUMERIC | Yıllık fiyat |
| max_barbers | INTEGER | Maksimum berber sayısı |
| features | JSONB | Plan özellikleri |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |

### 9. salon_subscriptions

Salon aboneliklerinin bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| plan_id | UUID | Plan ID'si (subscription_plans tablosuna referans) |
| start_date | DATE | Başlangıç tarihi |
| end_date | DATE | Bitiş tarihi |
| status | TEXT | Abonelik durumu (active, trial, expired, cancelled) |
| is_yearly | BOOLEAN | Yıllık abonelik mi? |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |

### 10. subscription_payments

Abonelik ödemelerinin bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| subscription_id | UUID | Abonelik ID'si (salon_subscriptions tablosuna referans) |
| amount | NUMERIC | Ödeme tutarı |
| payment_date | DATE | Ödeme tarihi |
| payment_method | TEXT | Ödeme yöntemi |
| status | TEXT | Ödeme durumu (completed, pending, failed) |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |

### 11. referral_codes

Referans kodlarının bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| salon_id | UUID | Salon ID'si (salons tablosuna referans) |
| code | TEXT | Referans kodu |
| is_active | BOOLEAN | Kod aktif mi? |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |

### 12. referral_benefits

Referans faydalarının bilgilerini içerir.

| Alan | Tip | Açıklama |
|------|-----|----------|
| id | UUID | Birincil anahtar |
| referrer_salon_id | UUID | Referans veren salon ID'si |
| referred_salon_id | UUID | Referans alan salon ID'si |
| benefit_type | TEXT | Fayda tipi (discount, extended_trial) |
| benefit_value | NUMERIC | Fayda değeri |
| is_used | BOOLEAN | Fayda kullanıldı mı? |
| created_at | TIMESTAMP | Oluşturulma tarihi |
| updated_at | TIMESTAMP | Güncellenme tarihi |

## İlişkiler

- `salons.owner_id` -> `auth.users.id`
- `barbers.salon_id` -> `salons.id`
- `barbers.user_id` -> `auth.users.id`
- `services.salon_id` -> `salons.id`
- `appointments.salon_id` -> `salons.id`
- `appointments.barber_id` -> `barbers.id`
- `appointments.service_id` -> `services.id`
- `working_hours.salon_id` -> `salons.id`
- `working_hours.barber_id` -> `barbers.id`
- `holidays.salon_id` -> `salons.id`
- `holidays.barber_id` -> `barbers.id`
- `notifications.user_id` -> `auth.users.id`
- `notifications.salon_id` -> `salons.id`
- `salon_subscriptions.salon_id` -> `salons.id`
- `salon_subscriptions.plan_id` -> `subscription_plans.id`
- `subscription_payments.subscription_id` -> `salon_subscriptions.id`
- `referral_codes.salon_id` -> `salons.id`
- `referral_benefits.referrer_salon_id` -> `salons.id`
- `referral_benefits.referred_salon_id` -> `salons.id`
