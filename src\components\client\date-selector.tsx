"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Calendar as CalendarIcon, ArrowRight, ArrowLeft, Info } from "lucide-react"
import { format, addDays, isSameDay, isToday, isTomorrow, isAfter, isBefore } from "date-fns"
import { tr } from "date-fns/locale"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface DateSelectorProps {
  selectedDate: Date | null
  onSelect: (date: Date) => void
  disabledDates?: Date[]
  holidayDates?: any[]
  onBack: () => void
}

export function DateSelector({
  selectedDate,
  onSelect,
  disabledDates = [],
  holidayDates = [],
  onBack
}: DateSelectorProps) {
  const today = new Date()

  // Generate next 14 days for quick selection
  const nextDays = Array.from({ length: 14 }, (_, i) => addDays(today, i))

  // Check if a date is a holiday
  const isHoliday = (date: Date) => {
    return holidayDates.some(holiday =>
      isSameDay(new Date(holiday.date), date)
    )
  }

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    if (isToday(date)) return "Bugün"
    if (isTomorrow(date)) return "Yarın"
    return format(date, "d MMM", { locale: tr })
  }

  // Format day name
  const formatDayName = (date: Date) => {
    return format(date, "EEEE", { locale: tr })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">Tarih Seçin</h2>
        <p className="text-muted-foreground">
          Randevunuz için uygun bir tarih seçin
        </p>
      </div>

      {/* Quick date selection */}
      <div className="grid grid-cols-3 sm:grid-cols-7 gap-2">
        {nextDays.map((date, index) => {
          const isDisabled = disabledDates.some(d => isSameDay(d, date)) ||
                            isHoliday(date) ||
                            isBefore(date, today);

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, delay: index * 0.03 }}
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      onClick={() => !isDisabled && onSelect(date)}
                      className={cn(
                        "border rounded-lg p-2 text-center cursor-pointer transition-all",
                        selectedDate && isSameDay(selectedDate, date)
                          ? "border-primary bg-primary/5 shadow-sm"
                          : isDisabled
                            ? "opacity-50 cursor-not-allowed bg-muted/30"
                            : "hover:border-primary/50 hover:bg-muted/50"
                      )}
                    >
                      <div className="text-xs font-medium mb-1">
                        {formatDayName(date)}
                      </div>
                      <div className="text-sm font-semibold">
                        {formatDateForDisplay(date)}
                      </div>
                      <div className="text-xs mt-1">
                        {format(date, "d MMMM", { locale: tr })}
                      </div>

                      {isHoliday(date) && (
                        <div className="mt-1 text-xs text-destructive flex items-center justify-center">
                          <Info className="h-3 w-3 mr-1" />
                          Tatil
                        </div>
                      )}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    {isDisabled
                      ? isHoliday(date)
                        ? "Tatil günü"
                        : "Bu tarih seçilemez"
                      : "Müsait"}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </motion.div>
          )
        })}
      </div>

      {/* Calendar for selecting other dates */}
      <div className="flex justify-center pt-4">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full sm:w-auto">
              <CalendarIcon className="mr-2 h-4 w-4" />
              Diğer Tarihleri Göster
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 z-[10000]" align="center">
            <Calendar
              mode="single"
              selected={selectedDate || undefined}
              onSelect={(date) => date && onSelect(date)}
              disabled={[
                ...disabledDates,
                ...holidayDates.map(h => new Date(h.date)),
                { before: today }
              ]}
              locale={tr}
              weekStartsOn={1}
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="pt-4 flex flex-col sm:flex-row gap-3">
        <Button
          onClick={onBack}
          variant="outline"
          className="w-full sm:w-1/2"
          size="lg"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Geri
        </Button>

        {selectedDate && (
          <Button
            onClick={() => onSelect(selectedDate)}
            className="w-full sm:w-1/2"
            size="lg"
          >
            Devam Et
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}
